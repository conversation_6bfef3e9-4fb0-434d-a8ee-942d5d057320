package usersettings

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
)

func (s *service) UpdateUserSettings(ctx context.Context, settings *models.UpdateSettingsInput) (*models.UserSettings, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}
	zlog.Info(ctx, "Updating user settings", zap.String("userID", userID.Hex()))
	updatedSettings, err := s.updateUserSettingFromObject(ctx, settings, userID)
	if err != nil {
		zlog.Info(ctx, "Error updating user settings", zap.String("userID", userID.Hex()), zap.Error(err))
		return nil, err
	}

	return updatedSettings, nil
}

func (s *service) updateUserSettingFromObject(ctx context.Context, settingsInput *models.UpdateSettingsInput, userID primitive.ObjectID) (*models.UserSettings, error) {
	settings := &models.UserSettings{
		UserID: userID,
	}
	updateFields := bson.M{}
	if settingsInput == nil {
		return settings, nil
	}
	existingSettings, _ := s.userSettingsRepo.GetUserSettings(ctx, userID)
	if existingSettings != nil {
		settings.PlaySound = existingSettings.PlaySound
		settings.HapticFeedback = existingSettings.HapticFeedback
		settings.KeyboardType = existingSettings.KeyboardType
	}
	if settingsInput.PlaySound != nil {
		settings.PlaySound = *settingsInput.PlaySound
		updateFields["playSound"] = settings.PlaySound
	}
	if settingsInput.HapticFeedback != nil {
		settings.HapticFeedback = *settingsInput.HapticFeedback
		updateFields["hapticFeedback"] = settings.HapticFeedback
	}
	if settingsInput.KeyboardType != nil {
		settings.KeyboardType = *settingsInput.KeyboardType
		updateFields["keyboardType"] = settings.KeyboardType
	}
	filter := bson.M{"userId": userID}
	update := bson.M{"$set": updateFields}
	opts := options.Update().SetUpsert(true)
	err := s.userSettingsRepo.UpdateOne(ctx, filter, update, opts)
	if err != nil {
		return nil, err
	}
	return settings, nil
}
