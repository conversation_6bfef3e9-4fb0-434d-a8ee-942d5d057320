package scheduler

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

const (
	retryInitialDelay = 1 * time.Second
	retryMaxDelay     = 1 * time.Minute
	pollInterval      = 500 * time.Millisecond
)

func (s *service) ProcessDueTasks() {
	go s.runTaskJobQueueWithRestart(s.runningCtx)
	go s.processTasksCron()
}

func (s *service) runTaskJobQueueWithRestart(ctx context.Context) {
	backoff := retryInitialDelay

	for {
		if ctx.Err() != nil {
			zlog.Info(ctx, "stopping task job queue processor due to shutdown")
			return
		}

		err := s.processTasksJobQueue(ctx)
		if err != nil {
			zlog.Error(ctx, "task job queue processor exited with error, retrying with backoff", err)
			select {
			case <-time.After(backoff):
				backoff *= 2
				if backoff > retryMaxDelay {
					backoff = retryMaxDelay
				}
				continue
			case <-ctx.Done():
				zlog.Info(ctx, "context cancelled during backoff, exiting")
				return
			}
		} else {
			backoff = retryInitialDelay
		}
	}
}

func (s *service) processTasksJobQueue(ctx context.Context) error {
	ticker := time.NewTicker(pollInterval)
	defer ticker.Stop()
	for {
		if err := ctx.Err(); err != nil {
			zlog.Error(ctx, "context cancelled during backoff, exiting", err)
			return err
		}
		now := time.Now().UnixMilli()

		tasks, err := s.sortedSet.ZRangeByScore(ctx, constants.SCHEDULER_SET, &redis.ZRangeBy{
			Min: "-inf",
			Max: fmt.Sprintf("%d", now),
		})
		if err != nil && !errors.Is(err, redis.Nil) {
			zlog.Error(ctx, "failed to get due tasks", err)
			return fmt.Errorf("failed to get due tasks: %w", err)
		}

		for _, taskStr := range tasks {
			var task models.SchedulerIntermediatePayload
			if err := json.Unmarshal([]byte(taskStr), &task); err != nil {
				zlog.Error(ctx, "failed to unmarshal due tasks", err)
				continue
			}

			err := s.sortedSet.ZRem(ctx, constants.SCHEDULER_SET, taskStr)
			if err != nil {
				zlog.Error(ctx, "failed to remove due tasks", err)
				return err
			}
			go func(task models.SchedulerIntermediatePayload) {
				taskCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
				defer cancel()
				err := s.handleTask(taskCtx, &task)
				if err != nil {
					zlog.Error(ctx, fmt.Sprint("failed to handle due tasks", task.Type), err, zap.Int("taskType", int(task.Type)))
				}
			}(task)
		}
		select {
		case <-ticker.C:
			continue
		case <-ctx.Done():
			return ctx.Err()
		}
	}
}

func (s *service) processTasksCron() {
	s.scheduleWeeklyContest()
	s.scheduleDailyChallenges()
	s.scheduleDailyPuzzle()
	s.scheduleWeeklyLeagueProcess()
}
