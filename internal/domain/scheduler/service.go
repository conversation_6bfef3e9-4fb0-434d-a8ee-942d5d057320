package scheduler

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/domain/swissTournament"

	"matiksOfficial/matiks-server-go/internal/domain"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	"matiksOfficial/matiks-server-go/internal/infrastructure/sortedset"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.uber.org/fx"
)

type service struct {
	sortedSet              sortedset.SortedSet
	gameService            domain.GameStore
	puzzleGameService      domain.PuzzleGameStore
	showdownService        domain.ShowdownStore
	contestService         domain.ContestStore
	dailyChallengeService  domain.DailyChallengeStore
	weeklyLeagueService    domain.WeeklyLeagueStore
	contestRepo            repository.ContestRepository
	puzzleService          domain.PuzzleStore
	coreService            domain.CoreLogicStore
	runningCtx             context.Context
	cancelContextFunc      context.CancelFunc
	showdownFixtureService swissTournament.ShowdownFixturesStore
}

func NewSchedulerService(
	lc fx.Lifecycle,
	gameService domain.GameStore,
	sortedSet sortedset.SortedSet,
	showdownService domain.ShowdownStore,
	puzzleGameService domain.PuzzleGameStore,
	contestService domain.ContestStore,
	dailyChallengeService domain.DailyChallengeStore,
	weeklyLeagueService domain.WeeklyLeagueStore,
	contestRepo repository.ContestRepository,
	puzzleService domain.PuzzleStore,
	coreService domain.CoreLogicStore,
	showdownFixtureService swissTournament.ShowdownFixturesStore,
) domain.SchedulerStore {
	ctx, cancel := context.WithCancel(context.Background())
	s := &service{
		gameService:            gameService,
		sortedSet:              sortedSet,
		showdownService:        showdownService,
		puzzleGameService:      puzzleGameService,
		contestService:         contestService,
		dailyChallengeService:  dailyChallengeService,
		weeklyLeagueService:    weeklyLeagueService,
		contestRepo:            contestRepo,
		puzzleService:          puzzleService,
		coreService:            coreService,
		runningCtx:             ctx,
		cancelContextFunc:      cancel,
		showdownFixtureService: showdownFixtureService,
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			zlog.Debug(ctx, "Starting scheduler service")
			s.ProcessDueTasks()
			return nil
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down scheduler service")
			s.cancelContextFunc()
			return nil
		},
	})

	return s
}
