package user

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/domain/game/utils/botutils"
	"matiksOfficial/matiks-server-go/internal/models"
)

// GetBotUser wraps the botutils.GetBotUser function and provides access to storage
func (s *service) GetBotUser(ctx context.Context, currentUser *models.User, gameConfig models.GameConfigInterface) (*models.User, error) {
	return botutils.GetBotUser(ctx, currentUser, s.userRepo, s, gameConfig, s.storage.Storage)
}
