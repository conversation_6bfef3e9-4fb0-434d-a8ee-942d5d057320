package user

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"matiksOfficial/matiks-server-go/utils/systemErrors"

	"github.com/redis/go-redis/v9"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.uber.org/zap"
)

func (s *service) GetUserByID(ctx context.Context, id models.ObjectID) (*models.User, error) {
	zlog.Info(ctx, "Getting user by ID", zap.String("userID to get", id.Hex()))

	var user *models.User
	data, err := s.redisCache.Get(ctx, constants.HumanBotKey+id.Hex())
	if err != nil && !errors.Is(err, redis.Nil) {
		return nil, err
	}
	if err == nil && data != nil {
		err := json.Unmarshal(data, &user)
		if err != nil {
			return nil, err
		}
	}

	if user == nil {
		user, err = s.userRepo.GetByID(ctx, id)
		if err != nil {
			zlog.Error(ctx, "User not found", err, zap.String("userId", id.Hex()))
			return nil, err
		}
		if user == nil {
			return nil, systemErrors.ErrUserNotFound
		}
	}

	if user.IsDeleted != nil && *user.IsDeleted {
		return nil, systemErrors.ErrUserNotFound
	}

	if user.ID != id {
		return nil, systemErrors.ErrUserNotFound
	}

	return user, nil
}

func (s *service) GetUserByEmail(ctx context.Context, email string) (*models.User, error) {
	zlog.Info(ctx, "Getting user by email", zap.String("email", email))
	user, err := s.userRepo.GetByEmail(ctx, email)
	if err != nil {
		zlog.Error(ctx, "User not found", err, zap.String("email", email))
		return nil, err
	}
	return user, nil
}

func (s *service) GetUserByUsername(ctx context.Context, username *string) (*models.SearchUserOutput, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("unauthorized: user not authenticated")
	}

	if username == nil {
		return nil, fmt.Errorf("username is nil")
	}

	searchedUser, err := s.userRepo.GetByUsername(ctx, *username)
	if err != nil {
		return nil, fmt.Errorf("user not found: %w", err)
	}

	isFollowing, friendshipStatus, err := s.friendsAndFollowersRepo.CheckFollowingAndFriendsStatusConcurrent(ctx, userID, searchedUser.ID)
	if err != nil {
		return nil, err
	}

	searchedUserOutput := models.SearchUserOutput{
		UserPublicDetails: searchedUser,
		IsFollowing:       &isFollowing,
		FriendshipStatus:  &friendshipStatus,
	}

	return &searchedUserOutput, nil
}

func (s *service) GetCurrentUser(ctx context.Context) (*models.User, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("unauthorized: user not authenticated")
	}

	zlog.Info(ctx, "Getting current user", zap.String("userID to get", userID.Hex()))

	var user *models.User

	user, err = s.GetUserByID(ctx, userID)
	if err != nil {
		zlog.Error(ctx, "User not found", err, zap.String("userId", userID.Hex()))
		return nil, err
	}
	if user == nil {
		return nil, systemErrors.ErrUserNotFound
	}

	return user, nil
}
