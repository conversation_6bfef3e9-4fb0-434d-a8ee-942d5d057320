package user

import (
	"context"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

const StatikCoinsThresholdForMatiksWeeklyLeague = 25

func (s *service) UpdateUserParticipationInMatiksWeeklyLeague(ctx context.Context, user *models.User) error {
	if user == nil {
		return nil
	}

	userLeague := user.League
	userTotalStatikCoins := user.StatikCoins

	if userLeague == nil {
		zlog.Info(ctx, "User has not joined any league", zap.String("UserId", user.ID.Hex()))
		if userTotalStatikCoins >= StatikCoinsThresholdForMatiksWeeklyLeague {
			newUserLeague := models.LeagueInfo{
				League:            utils.AllocPtr(models.LeagueTypeBronze),
				GroupID:           utils.AllocPtr(1),
				UpdatedAt:         utils.AllocPtr(time.Now().UTC()),
				CoinsTillLastWeek: utils.AllocPtr(userTotalStatikCoins),
				ProgressState:     utils.AllocPtr(models.WeeklyLeagueProgressStateNoChange),
				HasParticipated:   utils.AllocPtr(true),
			}
			err := s.AssignUserToLeagueGroup(ctx, newUserLeague, user.ID)
			if err != nil {
				zlog.Info(ctx, "Error assigning user to league group", zap.Error(err))
				return err
			}
			zlog.Info(ctx, "No Error in assigning user to league group", zap.Error(err))
			err = s.coreService.PublishUserEvent(ctx, user.ID, &models.JoinedWeeklyLeagueEvent{
				LeagueInfo: newUserLeague,
			})
			if err != nil {
				zlog.Info(ctx, "Error in PublishUserEvent", zap.Error(err))
				return err
			}
		}
		return nil
	}

	coinsTillLastWeek := userLeague.CoinsTillLastWeek
	if coinsTillLastWeek == nil {
		return nil
	}

	if userLeague.HasParticipated != nil && *userLeague.HasParticipated {
		return nil
	}

	coinsEarnedThisWeekTillNow := userTotalStatikCoins - *coinsTillLastWeek

	if coinsEarnedThisWeekTillNow >= StatikCoinsThresholdForMatiksWeeklyLeague {
		userLeague.HasParticipated = utils.AllocPtr(true)
		err := s.userRepo.UpdateOne(ctx, bson.M{"_id": user.ID}, bson.M{"$set": bson.M{"league": userLeague}})
		if err != nil {
			return err
		}

		err = s.coreService.PublishUserEvent(ctx, user.ID, &models.JoinedWeeklyLeagueEvent{
			LeagueInfo: *userLeague,
		})
		if err != nil {
			return err
		}
	}

	return nil
}
