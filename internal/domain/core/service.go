package core

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/domain"
	"matiksOfficial/matiks-server-go/internal/infrastructure/cache"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	"matiksOfficial/matiks-server-go/internal/infrastructure/websocket"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.uber.org/fx"
)

type service struct {
	userRepo  repository.UserRepository
	userCache cache.UserCache
	ws        websocket.Websocket
}

func NewCoreService(
	lc fx.Lifecycle,
	repositoryFactory *repository.RepositoryFactory,
	cacheInstance cache.Cache,
	wsInstance websocket.Websocket,
) domain.CoreLogicStore {
	s := &service{
		userRepo:  repositoryFactory.UserRepository,
		userCache: cache.NewUserCacheWrapper(cacheInstance),
		ws:        wsInstance,
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			zlog.Debug(ctx, "Starting core service")
			return nil
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down core service")
			return nil
		},
	})

	return s
}
