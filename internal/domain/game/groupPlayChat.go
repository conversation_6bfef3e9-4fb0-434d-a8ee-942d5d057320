package game

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/models"
)

func (s *service) HandleGroupPlayChat(ctx context.Context, message *models.GroupPlayChatMessage) error {
	err := s.publishMessageEvent(ctx, message)
	if err != nil {
		return err
	}

	return nil
}

func (s *service) publishMessageEvent(ctx context.Context, message *models.GroupPlayChatMessage) error {
	gameId := message.GameID
	channel := fmt.Sprintf("%s_%s_%s", constants.SubscriptionPrefixEnum.GROUP_CHAT_EVENT, gameId, constants.SocketV2)

	err := s.ws.Publish(ctx, channel, message)
	if err != nil {
		return err
	}
	return nil
}
