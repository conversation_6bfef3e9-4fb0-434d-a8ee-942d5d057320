package game

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/constants"
	gameutils "matiksOfficial/matiks-server-go/internal/domain/game/utils"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

func (s *service) CreateGame(ctx context.Context, gameConfigInput *models.GameConfigInput) (*models.Game, error) {
	zlog.Info(ctx, "Creating Game", zap.Any("gameConfigInput", gameConfigInput))
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		zlog.Error(ctx, "Error retrieving active games:", err)
		return nil, err
	}

	if gameConfigInput == nil {
		return nil, fmt.Errorf("gameConfigInput cannot be nil")
	}

	if err := s.validateGameConfig(gameConfigInput); err != nil {
		return nil, fmt.Errorf("invalid game configuration: %w", err)
	}

	gameConfig, timeLimit, err := gameutils.GetNewGameConfigFields(gameConfigInput)
	if err != nil || gameConfig == nil {
		return nil, fmt.Errorf("failed to get new game config fields: %w", err)
	}

	zlog.Debug(ctx, "CreateGame", zap.String("userID", userID.Hex()))

	gameCategory, gameType, gameMode := gameutils.GetGameCategoryTypeAndMode(gameConfig)

	if gameMode == models.GameModePractice {
		return s.GetPracticeGame(ctx, gameConfigInput)
	} else if gameMode == models.GameModeOnlineSearch {
		zlog.Warn(ctx, "CreateGame called for GameTypePlayOnline, which should be handled by StartSearching")
		return nil, fmt.Errorf("cannot directly create 'PLAY_ONLINE' game, use StartSearching")
	} else if gameMode == models.GameModeSumdayShowdown {
		zlog.Warn(ctx, "CreateGame called for GameTypeSumdayShowdown, specific logic might be needed")
	}

	activeGames, err := s.gameRepo.Find(ctx, bson.M{
		"gameStatus": bson.M{"$nin": []string{string(constants.GameStatusEnum.ENDED), string(constants.GameStatusEnum.CANCELLED)}},
		"players": bson.M{
			"$elemMatch": bson.M{
				"userId": userID,
				"status": constants.PlayerStatusEnum.ACCEPTED,
			},
		},
	})
	if err != nil {
		zlog.Error(ctx, "Error finding games:", err)
		return nil, err
	}

	if len(activeGames) > 1 {
		for _, game := range activeGames {
			if gameMode != models.GameModeSumdayShowdown {
				game.GameStatus = models.GameStatusCancelled
				game.Questions = nil

				err := s.gameRepo.UpdateGame(ctx, game)
				if err != nil {
					zlog.Error(ctx, "Error cancelling game:", err)
				}

				err = s.gameCache.SetGame(ctx, game)
				if err != nil {
					zlog.Error(ctx, "Error caching game:", err)
				}
			}
		}
	}

	currentUser, err := s.userService.GetUserByID(ctx, userID)
	if err != nil {
		return nil, err
	}

	currentUserRating := gameutils.GetUserRatingByGameCategory(gameCategory, currentUser)

	players := []*models.Player{
		{
			UserID:      userID,
			Status:      models.PlayerStatusAccepted,
			Rating:      utils.AllocPtr(currentUserRating),
			StatikCoins: currentUser.StatikCoins,
			TimeLeft:    timeLimit,
		},
	}

	newGame := models.Game{
		CreatedBy:    userID,
		GameCategory: gameCategory,
		GameType:     gameType,
		GameMode:     gameMode,
		GameStatus:   models.GameStatusCreated,
		Players:      players,
		Config:       gameConfig,
	}

	createdGame, gameCreationError := s.gameRepo.CreateGame(ctx, &newGame)
	if gameCreationError != nil {
		zlog.Error(ctx, "Error creating game:", gameCreationError)
		return nil, gameCreationError
	}

	err = s.gameCache.SetGame(ctx, createdGame)
	if err != nil {
		zlog.Error(ctx, "Failed to save game in cache :", err)
		return nil, err
	}

	return &newGame, nil
}
