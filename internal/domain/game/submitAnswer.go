package game

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/utils/systemErrors"

	"matiksOfficial/matiks-server-go/internal/constants"
	gameutils "matiksOfficial/matiks-server-go/internal/domain/game/utils"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

const (
	POSITIVE_POINTS = 4
	NEGATIVE_POINTS = -1
)

func (s *service) SubmitAnswer(ctx context.Context, answerInput *models.SubmitAnswerInput) (*models.Game, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	gameID := answerInput.GameID.Hex()

	game, err := s.GetGameByID(ctx, &answerInput.GameID)
	if err != nil {
		zlog.Error(ctx, "Failed to get game", err,
			zap.String("gameID", gameID))
		return nil, fmt.Errorf("failed to get game: %w", err)
	}

	if game.GameStatus == models.GameStatusCreated {
		zlog.Warn(ctx, "Attempted to submit answer for a game that hasn't started")
		return nil, systemErrors.ErrGameNotStarted
	}

	if game.GameStatus == models.GameStatusEnded {
		return nil, systemErrors.ErrGameAlreadyEnded
	}

	if game.GameStatus == models.GameStatusPaused {
		zlog.Info(ctx, "Resuming paused game",
			zap.String("gameID", gameID))
		game.GameStatus = models.GameStatusStarted
	}
	if game.StartTime == nil {
		zlog.Warn(ctx, "Game startTime is nil",
			zap.String("gameID", gameID))
		return nil, fmt.Errorf("game startTime cannot be nil")
	}

	submissionTime := answerInput.TimeOfSubmission.Time()

	totalTimeOfLiveGame := int(submissionTime.Sub(*(game.StartTime)).Milliseconds())

	//var submissionTimes []int
	//for _, q := range game.Questions {
	//	for _, s := range q.Submissions {
	//		if s.UserID != nil && *s.UserID == userID && s.TimeTaken != nil {
	//			submissionTimes = append(submissionTimes, *s.TimeTaken)
	//		}
	//	}
	//}
	//
	//submissionTimes = append(submissionTimes, totalTimeOfLiveGame)
	//
	//isBotBehavior, _, err := s.botDetectionService.HandleSubmissionBotDetection(ctx, userID, game.ID, submissionTimes)
	//if err != nil {
	//	zlog.Error(ctx, "Failed to handle bot detection", err,
	//		zap.String("gameID", gameID),
	//		zap.String("userID", userID.Hex()))
	//}
	//

	questionEntry := findQuestionByID(game.Questions, answerInput.QuestionID)
	if questionEntry == nil {
		zlog.Warn(ctx, "Question not found in game",
			zap.String("gameID", gameID),
			zap.String("questionID", answerInput.QuestionID))
		return game, nil
	}

	isCorrect := isAnswerCorrect(questionEntry.Question, &answerInput.SubmittedValue)

	if game.GameType == models.GameTypeAbilityDuels {
		isCorrect = answerInput.IsCorrect
	}

	if game.Config.TimeLimit != nil && *game.Config.TimeLimit*1000 <= totalTimeOfLiveGame {
		zlog.Info(ctx, "Game time limit reached, ending game",
			zap.String("gameID", gameID))
		game.EndTime = &submissionTime
		return game, nil
	}

	if hasUserSubmitted(questionEntry.Submissions, userID) {
		zlog.Warn(ctx, "User has already submitted an answer for this question",
			zap.String("gameID", gameID))
		return game, nil
	}

	changeInMarks := calculateChangeInMarks(isCorrect)
	if game.GameType == models.GameTypeGroupPlay {
		changeInMarks = calculateChangeInMarksForGroupPlay(isCorrect, questionEntry.Submissions, len(game.Players))
	}
	zlog.Debug(ctx, "Calculated change in marks", zap.Float64("changeInMarks", changeInMarks))

	//if isBotBehavior && isCorrect {
	//	var leaderboardEntry *models.LeaderBoardEntry
	//	for i, entry := range game.LeaderBoard {
	//		if entry.UserID != nil && *entry.UserID == userID {
	//			leaderboardEntry = game.LeaderBoard[i]
	//			break
	//		}
	//	}
	//
	//	if leaderboardEntry == nil {
	//		leaderboardEntry = &models.LeaderBoardEntry{
	//			UserID:      &userID,
	//			TotalPoints: utils.AllocPtr(0.0),
	//			Correct:     utils.AllocPtr(0),
	//			Incorrect:   utils.AllocPtr(0),
	//		}
	//		game.LeaderBoard = append(game.LeaderBoard, leaderboardEntry)
	//	}
	//
	//	game.LeaderBoard = gameutils.UpdateLeaderboardRanks(game)
	//
	//	zlog.Info(ctx, "Bot behavior detected - not increasing opponent's score",
	//		zap.String("gameID", gameID),
	//		zap.String("userID", userID.Hex()))
	//
	//	return nil, nil
	//} else {
	//	gameutils.UpdateGameLeaderboard(game, userID, isCorrect, changeInMarks)
	//}

	gameutils.UpdateGameLeaderboard(game, userID, isCorrect, changeInMarks)

	points := NEGATIVE_POINTS
	if isCorrect {
		points = POSITIVE_POINTS
	}

	newSubmission := &models.Submission{
		UserID:            utils.AllocPtr(userID),
		TimeTaken:         &totalTimeOfLiveGame,
		Points:            utils.AllocPtr(int(changeInMarks)),
		SubmissionTime:    &answerInput.TimeOfSubmission,
		IsCorrect:         &isCorrect,
		InCorrectAttempts: &answerInput.IncorrectAttempts,
		SubmittedValues:   []*string{&answerInput.SubmittedValue},
	}

	questionEntry.Submissions = append(questionEntry.Submissions, newSubmission)

	if game.GameType == models.GameTypeFastestFinger {
		for _, player := range game.Players {
			if player.UserID != userID {
				incorrectSubmission := &models.Submission{
					UserID:            utils.AllocPtr(player.UserID),
					TimeTaken:         &totalTimeOfLiveGame,
					Points:            utils.AllocPtr(0),
					SubmissionTime:    &answerInput.TimeOfSubmission,
					IsCorrect:         utils.AllocPtr(false),
					InCorrectAttempts: &answerInput.IncorrectAttempts,
					SubmittedValues:   []*string{},
				}
				questionEntry.Submissions = append(questionEntry.Submissions, incorrectSubmission)
			}
		}
	}

	// if game.GameType == models.GameTypeGroupPlay {
	// 	currentUserPlayerIndex := -1
	// 	for i, player := range game.Players {
	// 		if player.UserID == userID {
	// 			currentUserPlayerIndex = i
	// 		}
	// 	}

	// 	if currentUserPlayerIndex != -1 && game.Players[currentUserPlayerIndex].TimeLeft != nil {
	// 		remainingTime := *game.Players[currentUserPlayerIndex].TimeLeft
	// 		if isCorrect {
	// 			remainingTime += constants.ADDITIONAL_TIME_FOR_CORRECT_ANSWER
	// 		} else {
	// 			remainingTime -= constants.TIME_DEDUCTION_FOR_WRONG_ANSWER
	// 		}
	// 		game.Players[currentUserPlayerIndex].TimeLeft = utils.AllocPtr(remainingTime)

	// 		err := s.gameRepo.UpdateGame(ctx, game)

	// 		if err != nil {
	// 			zlog.Error(ctx, "Failed to update game in db", err)
	// 		}

	// 		err = s.gameCache.SetGame(ctx, game)
	// 		if err != nil {
	// 			zlog.Error(ctx, "Failed to update cached game", err)
	// 		}
	// 	}
	// }

	eventType := string(constants.GameEventEnum.INCORRECT_MOVE_MADE)
	if isCorrect {
		eventType = string(constants.GameEventEnum.CORRECT_MOVE_MADE)
	}

	err = s.addEncryptedQuestionsInGame(game)
	if err != nil {
		zlog.Error(ctx, "Failed to Encrypt Que", err)
	}

	var answerResponseModel models.AnswerResponseModel
	mapGameModelToAnswerResponseModel(game, &answerResponseModel)

	err = s.publishSubmitAnswerGameEvent(ctx, game, constants.GameEventEnum.CORRECT_MOVE_MADE.String())
	if err != nil {
		zlog.Error(ctx, "Failed to publish game ended event", err)
	}
	// saving game in cache.
	err = s.gameCache.SetGame(ctx, game)
	if err != nil {
		zlog.Error(ctx, "Failed to update cached game", err)
	}

	zlog.Info(ctx, "Answer submission completed successfully",
		zap.Bool("isCorrect", isCorrect),
		zap.Int("points", points),
		zap.String("eventType", eventType))

	return game, nil
}

func findQuestionByID(questions []*models.GameQuestion, questionID string) *models.GameQuestion {
	for _, q := range questions {
		if q.Question.ID != nil && *q.Question.ID == questionID {
			return q
		}
	}
	return nil
}

func isAnswerCorrect(question *models.Question, submittedValue *string) bool {
	if len(question.Answers) > 0 && question.Answers[0] == *submittedValue {
		return true
	}
	return false
}

func hasUserSubmitted(submissions []*models.Submission, userID primitive.ObjectID) bool {
	for _, s := range submissions {
		if s.UserID != nil && *s.UserID == userID {
			return true
		}
	}
	return false
}

func calculateChangeInMarks(isCorrect bool) float64 {
	if !isCorrect {
		return float64(NEGATIVE_POINTS)
	}
	return POSITIVE_POINTS
}

func calculateChangeInMarksForGroupPlay(isCorrect bool, submissions []*models.Submission, noOfPlayers int) float64 {
	if !isCorrect {
		return 0
	}

	correctSubmissions := 0
	for _, submission := range submissions {
		if submission.IsCorrect != nil && *submission.IsCorrect {
			correctSubmissions++
		}
	}

	score := float64(noOfPlayers - correctSubmissions)
	if score < 0 {
		return 1
	}
	return score
}
