package game

import (
	"context"
	"fmt"
	"math/rand/v2"
	"time"

	gameutils "matiksOfficial/matiks-server-go/internal/domain/game/utils"

	"go.uber.org/zap"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) ChallengeUser(ctx context.Context, challengeUserInput *models.ChallengeUserInput) (*models.Game, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	zlog.Debug(ctx, "Challenging user", zap.String("userID", userID.Hex()))

	if challengeUserInput == nil {
		return nil, fmt.Errorf("challengeUserInput cannot be nil")
	}

	if err := s.validateGameConfig(challengeUserInput.GameConfig); err != nil {
		return nil, fmt.Errorf("invalid game configuration: %w", err)
	}

	gameConfig, _, err := gameutils.GetNewGameConfigFields(challengeUserInput.GameConfig)
	if err != nil || gameConfig == nil {
		return nil, fmt.Errorf("failed to get game config: %w", err)
	}

	currentUser, err := s.userService.GetUserByID(ctx, userID)
	if err != nil || currentUser == nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	gameCategory, gameType, gameMode := gameutils.GetGameCategoryTypeAndMode(gameConfig)

	currentUserRating := gameutils.GetUserRatingByGameCategory(gameCategory, currentUser)

	players := []*models.Player{
		{UserID: userID, Rating: utils.AllocPtr(currentUserRating), Status: models.PlayerStatusInvited, StatikCoins: currentUser.StatikCoins},
	}
	game := gameutils.CreateGameInstance(userID, gameCategory, gameType, gameMode, gameConfig, players)

	if challengeUserInput.UserID != nil {
		opponentUser, err := s.userService.GetUserByID(ctx, *challengeUserInput.UserID)
		if err != nil {
			return nil, err
		}

		opponentUserRating := gameutils.GetUserRatingByGameCategory(gameCategory, opponentUser)

		players := []*models.Player{
			{UserID: userID, Rating: utils.AllocPtr(currentUserRating), Status: models.PlayerStatusInvited, StatikCoins: currentUser.StatikCoins},
			{UserID: *challengeUserInput.UserID, Rating: utils.AllocPtr(opponentUserRating), StatikCoins: opponentUser.StatikCoins},
		}
		game = gameutils.CreateGameInstance(userID, gameCategory, gameType, gameMode, gameConfig, players)
	}

	savedGame, err := s.gameRepo.CreateGame(ctx, game)
	if err != nil {
		zlog.Error(ctx, "Error creating game:", err)
		return nil, err
	}

	err = s.PublishChallengeEvent(ctx, savedGame, string(models.ChallengeStatusChallengeSent))
	if err != nil {
		zlog.Error(ctx, "Error publishing challenge event:", err)
	}

	if challengeUserInput.UserID != nil {
		if challengeUserInput.GameConfig.GameType == models.GameTypeFlashAnzan {
			go s.respondToChallengeIfBot(utils.DeriveContextWithoutCancel(ctx), savedGame, *challengeUserInput.UserID, false)
		} else {
			go s.respondToChallengeIfBot(utils.DeriveContextWithoutCancel(ctx), savedGame, *challengeUserInput.UserID, true)
		}
		go s.handleChallengeExpiration(utils.DeriveContextWithoutCancel(ctx), *savedGame.ID)
	}

	return savedGame, nil
}

func (s *service) handleChallengeExpiration(ctx context.Context, gameID primitive.ObjectID) {
	timer := time.NewTimer(20 * time.Second)
	defer timer.Stop()

	select {
	case <-timer.C:
		game, err := s.gameRepo.GetGameByID(ctx, gameID)
		if err != nil {
			zlog.Error(ctx, "Error retrieving game for expiration check:", err)
			return
		}

		if game.GameStatus == models.GameStatusCreated {
			game.GameStatus = models.GameStatusCancelled

			err = s.gameRepo.UpdateGame(ctx, game)
			if err != nil {
				zlog.Error(ctx, "Error updating expired game:", err)
				return
			}

			err = s.gameCache.SetGame(ctx, game)
			if err != nil {
				zlog.Error(ctx, "Error updating expired game cache:", err)
				return
			}

			err = s.PublishChallengeEvent(ctx, game, string(models.ChallengeStatusChallengeExpired))
			if err != nil {
				zlog.Error(ctx, "Error publishing challenge expiration event:", err)
			}
		}
	case <-ctx.Done():
		return
	}
}

func (s *service) respondToChallengeIfBot(ctx context.Context, game *models.Game, userID primitive.ObjectID, accept bool) {
	gameID := *game.ID
	user, err := s.userService.GetUserByID(ctx, userID)
	if err != nil {
		zlog.Error(ctx, "Error retrieving user:", err)
		return
	}
	if (user.IsBot == nil && user.IsHumanBot == nil) ||
		(user.IsBot == nil && user.IsHumanBot != nil && !*user.IsHumanBot) ||
		(user.IsBot != nil && !*user.IsBot && user.IsHumanBot == nil) ||
		(user.IsBot != nil && !*user.IsBot && user.IsHumanBot != nil && !*user.IsHumanBot) {
		return
	}

	if user.HumanBotConfig != nil && user.HumanBotConfig.InGame != nil && *user.HumanBotConfig.InGame {
		return
	}

	randT := 1.5 + 6*rand.Float64()
	timer := time.NewTimer(time.Duration(randT) * time.Second)
	defer timer.Stop()

	select {
	case <-timer.C:
		botCtx := context.WithValue(utils.DeriveContextWithoutCancel(ctx), constants.UserContextKey, user.ID.Hex())
		if accept {
			probAcceptance := rand.Float64()
			if probAcceptance < 0.4 && (user.HumanBotConfig == nil || (user.HumanBotConfig != nil && len(user.HumanBotConfig.LastOneHourOpponents[userID]) > 5)) {
				_, err := s.RejectChallenge(botCtx, gameID)
				if err != nil {
					zlog.Error(ctx, "Error rejecting challenge if bot:", err)
					return
				}
				return
			}

			_, err := s.AcceptChallenge(botCtx, gameID)
			if err != nil {
				zlog.Error(ctx, "Error accepting challenge if bot:", err)
				return
			}
		} else {
			_, err := s.RejectChallenge(botCtx, gameID)
			if err != nil {
				zlog.Error(ctx, "Error rejecting challenge if bot:", err)
				return
			}
		}
	case <-ctx.Done():
		zlog.Info(ctx, "Accepting rematch if bot context cancelled", zap.String("gameID", gameID.Hex()), zap.String("userID", userID.Hex()))
	}
}
