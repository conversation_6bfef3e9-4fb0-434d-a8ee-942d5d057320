package gameutils

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/models"
)

func GetQuestionsForGame(ctx context.Context, game *models.Game) ([]*models.GameQuestion, error) {
	if game == nil {
		return nil, fmt.Errorf("game is nil")
	}

	if game.Config == nil {
		return nil, fmt.Errorf("game config is nil")
	}

	_, gameType, _ := GetGameCategoryTypeAndMode(game.Config)
	switch gameType {
	case models.GameTypeFlashAnzan:
		return nil, nil
	case models.GameTypeAbilityDuels:
		return GetDMASAbilityQuestionsForGame(ctx, game.ID.Hex(), game.Players, game.Config, nil)
	case models.GameTypeDMASAbility:
		return GetDMASAbilityQuestionsForGame(ctx, game.ID.Hex(), game.Players, game.Config, nil)
	case models.GameTypePlayOnline:
		return GetDMASQuestionsForGame(ctx, game.ID.Hex(), game.Players, game.Config)
	case models.GameTypePlayWithFriend:
		return GetDMASQuestionsForGame(ctx, game.ID.Hex(), game.Players, game.Config)
	case models.GameTypeGroupPlay:
		return GetDMASQuestionsForGame(ctx, game.ID.Hex(), game.Players, game.Config)
	case models.GameTypeOnlineChallenge:
		return GetDMASQuestionsForGame(ctx, game.ID.Hex(), game.Players, game.Config)
	case models.GameTypeDMAS:
		return GetDMASQuestionsForGame(ctx, game.ID.Hex(), game.Players, game.Config)
	case models.GameTypeFastestFinger:
		return GetDMASQuestionsForGame(ctx, game.ID.Hex(), game.Players, game.Config)
	case models.GameTypeCrossMath:
		return GetCrossMathPuzzleGameQuestions(ctx, game.ID.Hex(), game.Players, game.Config)
	case models.GameTypeKenKen:
		return GetKenKenPuzzleGameQuestions(ctx, game.ID.Hex(), game.Players, game.Config)
	case models.GameTypeSumdayShowdown:
		return GetDMASQuestionsForGame(ctx, game.ID.Hex(), game.Players, game.Config)
	case models.GameTypeDMASTimeBank:
		return GetDMASQuestionsForGame(ctx, game.ID.Hex(), game.Players, game.Config)
	case models.GameTypePractice:
		return GetDMASQuestionsForGame(ctx, game.ID.Hex(), game.Players, game.Config)

	default:
		return nil, fmt.Errorf("question generator not implemented for game type %s", gameType)
	}
}
