package gameutils

import "matiksOfficial/matiks-server-go/internal/models"

func GetUserRatingByGameCategory(gameCategory models.GameCategory, user *models.User) int {
	switch gameCategory {
	case models.GameCategoryClassical:
		return getUserAbilityRatingWithFallback(user)
	case models.GameCategoryBlitz:
		return getUserGlobalRatingWithFallback(user)
	case models.GameCategoryPuzzle:
		return getUserPuzzleRatingWithFallback(user)
	case models.GameCategoryMemory:
		return getUserFlashAnzanRatingWithFallback(user)
	default:
		return getUserGlobalRatingWithFallback(user)
	}
}
