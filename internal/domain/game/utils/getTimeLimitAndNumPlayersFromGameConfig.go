package gameutils

import (
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
)

func GetGameModeFromGameType(gameType models.GameType) models.GameMode {
	switch gameType {
	case models.GameTypePlayOnline:
		return models.GameModeOnlineSearch
	case models.GameTypePlayWithFriend:
		return models.GameModePlayViaLink
	case models.GameTypeOnlineChallenge:
		return models.GameModeOnlineChallenge
	case models.GameTypePractice:
		return models.GameModePractice
	case models.GameTypeSumdayShowdown:
		return models.GameModeSumdayShowdown
	case models.GameTypeGroupPlay:
		return models.GameModeGroupPlay
	default:
		return models.GameModeOnlineSearch
	}
}

func GetGameCategoryFromGameType(gameType models.GameType) models.GameCategory {
	switch gameType {
	case models.GameTypeFlashAnzan:
		return models.GameCategoryMemory
	case models.GameTypeDMASAbility:
		return models.GameCategoryClassical
	case models.GameTypeCrossMath, models.GameTypeKenKen:
		return models.GameCategoryPuzzle
	case models.GameTypeDMAS, models.GameTypeDMASTimeBank, models.GameTypeFastestFinger:
		return models.GameCategoryBlitz

	// Deprecated
	case models.GameTypeAbilityDuels:
		return models.GameCategoryClassical
	case models.GameTypePlayOnline, models.GameTypeGroupPlay, models.GameTypePractice, models.GameTypePlayWithFriend, models.GameTypeOnlineChallenge:
		return models.GameCategoryBlitz
	case models.GameTypeSumdayShowdown:
		return models.GameCategoryBlitz

	default:
		return models.GameCategoryBlitz
	}
}

func GetGameModeFromGameConfig(gameConfig *models.GameConfig) models.GameMode {
	if gameConfig == nil {
		return models.GameModeOnlineSearch
	}
	if gameConfig.ModeSpecificConfig != nil {
		return gameConfig.ModeSpecificConfig.Mode
	}
	return GetGameModeFromGameType(gameConfig.GameType)
}

func GetMaxTimePerQuestionFromGameConfig(gameConfig *models.GameConfig) int {
	maxTimePerQuestionFallback := 60
	if gameConfig == nil {
		return maxTimePerQuestionFallback
	}

	if gameConfig.ModeSpecificConfig != nil && gameConfig.ModeSpecificConfig.GroupPlay != nil {
		if gameConfig.ModeSpecificConfig.GroupPlay.MaxTimePerQuestion == nil || *gameConfig.ModeSpecificConfig.GroupPlay.MaxTimePerQuestion == 0 {
			return maxTimePerQuestionFallback
		}
		return *gameConfig.ModeSpecificConfig.GroupPlay.MaxTimePerQuestion
	}

	if gameConfig.MaxTimePerQuestion != nil {
		return *gameConfig.MaxTimePerQuestion
	}

	return maxTimePerQuestionFallback
}

func GetTimeLimitFromGameConfig(gameConfig *models.GameConfig) int {
	if gameConfig == nil {
		return 0
	}

	var gameCategory models.GameCategory
	if gameConfig.CategorySpecificConfig != nil {
		gameCategory = gameConfig.CategorySpecificConfig.Category
	} else {
		gameCategory = GetGameCategoryFromGameType(gameConfig.GameType)
	}

	switch gameCategory {
	case models.GameCategoryBlitz:
		if gameConfig.CategorySpecificConfig != nil && gameConfig.CategorySpecificConfig.Blitz != nil && gameConfig.CategorySpecificConfig.Blitz.TimeLimit != nil {
			return *gameConfig.CategorySpecificConfig.Blitz.TimeLimit
		}
	case models.GameCategoryClassical:
		if gameConfig.CategorySpecificConfig != nil && gameConfig.CategorySpecificConfig.Classical != nil && gameConfig.CategorySpecificConfig.Classical.TimeLimit != nil {
			return *gameConfig.CategorySpecificConfig.Classical.TimeLimit
		}
	case models.GameCategoryMemory:
		if gameConfig.CategorySpecificConfig != nil && gameConfig.CategorySpecificConfig.Memory != nil && gameConfig.CategorySpecificConfig.Memory.TimeLimit != nil {
			return *gameConfig.CategorySpecificConfig.Memory.TimeLimit
		}
	case models.GameCategoryPuzzle:
		if gameConfig.CategorySpecificConfig != nil && gameConfig.CategorySpecificConfig.Puzzle != nil && gameConfig.CategorySpecificConfig.Puzzle.TimeLimit != nil {
			return *gameConfig.CategorySpecificConfig.Puzzle.TimeLimit
		}
	}

	if gameConfig.TimeLimit == nil {
		return 0
	}

	return *gameConfig.TimeLimit
}

func GetNumPlayersFromGameConfig(gameConfig *models.GameConfig) *int {
	if gameConfig == nil {
		return nil
	}
	gameMode := GetGameModeFromGameConfig(gameConfig)

	switch gameMode {
	case models.GameModeOnlineSearch:
		if gameConfig.ModeSpecificConfig != nil && gameConfig.ModeSpecificConfig.OnlineSearch != nil {
			return gameConfig.ModeSpecificConfig.OnlineSearch.NumPlayers
		}
	case models.GameModeOnlineChallenge:
		if gameConfig.ModeSpecificConfig != nil && gameConfig.ModeSpecificConfig.OnlineChallenge != nil {
			return gameConfig.ModeSpecificConfig.OnlineChallenge.NumPlayers
		}
	case models.GameModePractice:
		if gameConfig.ModeSpecificConfig != nil && gameConfig.ModeSpecificConfig.Practice != nil {
			return gameConfig.ModeSpecificConfig.Practice.NumPlayers
		}
	case models.GameModePlayViaLink:
		if gameConfig.ModeSpecificConfig != nil && gameConfig.ModeSpecificConfig.PlayViaLink != nil {
			return gameConfig.ModeSpecificConfig.PlayViaLink.NumPlayers
		}
	case models.GameModeGroupPlay:
		if gameConfig.ModeSpecificConfig != nil && gameConfig.ModeSpecificConfig.GroupPlay != nil {
			return gameConfig.ModeSpecificConfig.GroupPlay.MinPlayers
		}
	case models.GameModeRushWithoutTime:
		if gameConfig.ModeSpecificConfig != nil && gameConfig.ModeSpecificConfig.RushWithoutTime != nil {
			return gameConfig.ModeSpecificConfig.RushWithoutTime.NumPlayers
		}
	case models.GameModeRushWithTime:
		if gameConfig.ModeSpecificConfig != nil && gameConfig.ModeSpecificConfig.RushWithTime != nil {
			return gameConfig.ModeSpecificConfig.RushWithTime.NumPlayers
		}
	case models.GameModeSumdayShowdown:
		return utils.AllocPtr(2)
	case models.GameModeSurvivalSaturday:
		if gameConfig.ModeSpecificConfig != nil && gameConfig.ModeSpecificConfig.SurvivalSaturday != nil {
			return gameConfig.ModeSpecificConfig.SurvivalSaturday.NumPlayers
		}
	}
	return gameConfig.NumPlayers
}
