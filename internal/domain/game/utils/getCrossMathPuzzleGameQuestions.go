package gameutils

import (
	"context"
	"fmt"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"matiksOfficial/matiks-server-go/internal/domain/questions/questionsGenerator"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
)

const (
	MinGameRating     = 500
	MaxGameRating     = 3500
	DefaultGameRating = 1000
	SmallGridSize     = 5
	LargeGridSize     = 7
)

func GetCrossMathPuzzleGameQuestions(ctx context.Context, gameId string, players []*models.Player, config *models.GameConfig) ([]*models.GameQuestion, error) {
	if config == nil {
		return nil, fmt.Errorf("config is nil")
	}

	gameID, err := primitive.ObjectIDFromHex(gameId)
	if err != nil {
		return nil, fmt.Errorf("invalid game id")
	}

	var totalRating int
	validPlayerCount := 0

	for _, player := range players {
		if player == nil || player.Rating == nil {
			continue
		}
		totalRating += *player.Rating
		validPlayerCount++
	}

	gameRating := DefaultGameRating

	if validPlayerCount > 0 {
		gameRating = totalRating / validPlayerCount
	}

	if gameRating < MinGameRating {
		gameRating = MinGameRating
	} else if gameRating > MaxGameRating {
		gameRating = MaxGameRating
	}

	hiddenDistribution := questionsGenerator.GetHideCellsDistributionAccToUserRating(gameRating)

	questions := make([]*models.GameQuestion, 0, len(hiddenDistribution[SmallGridSize])+len(hiddenDistribution[LargeGridSize]))

	questions = append(questions, GetGameQuestions(ctx, hiddenDistribution, SmallGridSize, gameID)...)

	questions = append(questions, GetGameQuestions(ctx, hiddenDistribution, LargeGridSize, gameID)...)

	return questions, nil
}

func GetGameQuestions(ctx context.Context, hiddenDistribution map[int][]int, gridSize int, gameID primitive.ObjectID) []*models.GameQuestion {
	questions := make([]*models.GameQuestion, 0, len(hiddenDistribution[gridSize]))
	for i := range hiddenDistribution[gridSize] {
		grid, answers := questionsGenerator.GeneratePuzzleWithSize(gridSize, hiddenDistribution[gridSize][i])
		questions = append(questions, &models.GameQuestion{
			Stats: models.GameQuestionStats{},
			Question: &models.Question{
				ID: utils.AllocPtr(fmt.Sprintf("%s_%d", gameID.Hex(), i)),
				Expression: []string{
					questionsGenerator.MinifyPuzzleGrid(grid, answers, gridSize),
				},
				Tags:        []string{"CROSS-MATH", "PUZZLE"},
				Description: utils.AllocPtr(fmt.Sprintf("Cross Math Puzzle %d", i)),
			},
			Submissions: []*models.Submission{},
		})
	}
	return questions
}
