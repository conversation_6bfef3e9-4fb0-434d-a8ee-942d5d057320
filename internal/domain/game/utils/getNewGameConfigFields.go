package gameutils

import (
	"fmt"

	"matiksOfficial/matiks-server-go/internal/models"
)

func ConvertDefaultGameConfigInputToModel(input *models.DefaultGameConfigInput) *models.DefaultGameConfig {
	return &models.DefaultGameConfig{
		TimeLimit: input.TimeLimit,
	}
}

func ConvertPuzzleGameConfigInputToModel(input *models.PuzzleGameConfigInput) *models.PuzzleGameConfig {
	return &models.PuzzleGameConfig{
		TimeLimit:          input.TimeLimit,
		NumPlayers:         input.NumPlayers,
		NumOfQuestions:     input.NumOfQuestions,
		GameType:           input.GameType,
		DifficultyLevel:    input.DifficultyLevel,
		MaxTimePerQuestion: input.MaxTimePerQuestion,
	}
}

func ConvertShowdownGameConfigInputToModel(input *models.ShowdownGameConfigInput) *models.ShowdownGameConfig {
	// TODO: Implement this
	return nil
}

func ConvertDefaultGameModeConfigInputToModel(input *models.DefaultGameModeConfigInput) *models.DefaultGameModeConfig {
	return &models.DefaultGameModeConfig{
		NumPlayers: input.NumPlayers,
	}
}

func ConvertGroupPlayGameConfigInputToModel(input *models.GroupPlayGameConfigInput) *models.GroupPlayGameConfig {
	return &models.GroupPlayGameConfig{
		MaxTimePerQuestion: input.MaxTimePerQuestion,
		DifficultyLevel:    input.DifficultyLevel,
		MaxGapBwGame:       input.MaxGapBwGame,
		MaxPlayers:         input.MaxPlayers,
		MinPlayers:         input.MinPlayers,
		QuestionTags:       input.QuestionTags,
	}
}

func GetNewGameConfigFields(gameConfigInput *models.GameConfigInput) (*models.GameConfig, *int, error) {
	if gameConfigInput == nil {
		return nil, nil, fmt.Errorf("gameConfigInput is nil")
	}

	gameConfig := &models.GameConfig{}
	var timeLimit *int
	isNewConfig := gameConfigInput.CategorySpecificConfig != nil || gameConfigInput.GameTypeSpecificConfig != nil || gameConfigInput.ModeSpecificConfig != nil

	if isNewConfig {
		if gameConfigInput.CategorySpecificConfig != nil {
			gameConfig.CategorySpecificConfig = &models.GameCategorySpecificConfig{
				Category: gameConfigInput.CategorySpecificConfig.Category,
			}
			if gameConfigInput.CategorySpecificConfig.Blitz != nil {
				gameConfig.CategorySpecificConfig.Blitz = ConvertDefaultGameConfigInputToModel(gameConfigInput.CategorySpecificConfig.Blitz)
				timeLimit = gameConfigInput.CategorySpecificConfig.Blitz.TimeLimit
			}
			if gameConfigInput.CategorySpecificConfig.Classical != nil {
				gameConfig.CategorySpecificConfig.Classical = ConvertDefaultGameConfigInputToModel(gameConfigInput.CategorySpecificConfig.Classical)
				timeLimit = gameConfigInput.CategorySpecificConfig.Classical.TimeLimit
			}
			if gameConfigInput.CategorySpecificConfig.Memory != nil {
				gameConfig.CategorySpecificConfig.Memory = ConvertDefaultGameConfigInputToModel(gameConfigInput.CategorySpecificConfig.Memory)
				timeLimit = gameConfigInput.CategorySpecificConfig.Memory.TimeLimit
			}
			if gameConfigInput.CategorySpecificConfig.Puzzle != nil {
				gameConfig.CategorySpecificConfig.Puzzle = ConvertPuzzleGameConfigInputToModel(gameConfigInput.CategorySpecificConfig.Puzzle)
				timeLimit = gameConfigInput.CategorySpecificConfig.Puzzle.TimeLimit
			}

		}

		if gameConfigInput.GameTypeSpecificConfig != nil {
			gameConfig.GameTypeSpecificConfig = &models.GameTypeSpecificConfig{
				Type: gameConfigInput.GameTypeSpecificConfig.Type,
			}
		}

		if gameConfigInput.ModeSpecificConfig != nil {
			gameConfig.ModeSpecificConfig = &models.GameModeSpecificConfig{
				Mode: gameConfigInput.ModeSpecificConfig.Mode,
			}

			if gameConfigInput.ModeSpecificConfig.SumdayShowdown != nil {
				gameConfig.ModeSpecificConfig.SumdayShowdown = ConvertShowdownGameConfigInputToModel(gameConfigInput.ModeSpecificConfig.SumdayShowdown)
			}
			if gameConfigInput.ModeSpecificConfig.OnlineSearch != nil {
				gameConfig.ModeSpecificConfig.OnlineSearch = ConvertDefaultGameModeConfigInputToModel(gameConfigInput.ModeSpecificConfig.OnlineSearch)
			}
			if gameConfigInput.ModeSpecificConfig.OnlineChallenge != nil {
				gameConfig.ModeSpecificConfig.OnlineChallenge = ConvertDefaultGameModeConfigInputToModel(gameConfigInput.ModeSpecificConfig.OnlineChallenge)
			}
			if gameConfigInput.ModeSpecificConfig.Practice != nil {
				gameConfig.ModeSpecificConfig.Practice = ConvertDefaultGameModeConfigInputToModel(gameConfigInput.ModeSpecificConfig.Practice)
			}
			if gameConfigInput.ModeSpecificConfig.RushWithTime != nil {
				gameConfig.ModeSpecificConfig.RushWithTime = ConvertDefaultGameModeConfigInputToModel(gameConfigInput.ModeSpecificConfig.RushWithTime)
			}
			if gameConfigInput.ModeSpecificConfig.RushWithoutTime != nil {
				gameConfig.ModeSpecificConfig.RushWithoutTime = ConvertDefaultGameModeConfigInputToModel(gameConfigInput.ModeSpecificConfig.RushWithoutTime)
			}
			if gameConfigInput.ModeSpecificConfig.GroupPlay != nil {
				gameConfig.ModeSpecificConfig.GroupPlay = ConvertGroupPlayGameConfigInputToModel(gameConfigInput.ModeSpecificConfig.GroupPlay)
			}
			if gameConfigInput.ModeSpecificConfig.PlayViaLink != nil {
				gameConfig.ModeSpecificConfig.PlayViaLink = ConvertDefaultGameModeConfigInputToModel(gameConfigInput.ModeSpecificConfig.PlayViaLink)
			}
			if gameConfigInput.ModeSpecificConfig.SurvivalSaturday != nil {
				gameConfig.ModeSpecificConfig.SurvivalSaturday = ConvertDefaultGameModeConfigInputToModel(gameConfigInput.ModeSpecificConfig.SurvivalSaturday)
			}
		}
	} else {
		gameConfig = &models.GameConfig{
			TimeLimit:          &gameConfigInput.TimeLimit,
			NumPlayers:         &gameConfigInput.NumPlayers,
			GameType:           gameConfigInput.GameType,
			QuestionTags:       gameConfigInput.QuestionTags,
			DifficultyLevel:    gameConfigInput.DifficultyLevel,
			MaxTimePerQuestion: gameConfigInput.MaxTimePerQuestion,
		}
		timeLimit = &gameConfigInput.TimeLimit
	}
	return gameConfig, timeLimit, nil
}
