package gameutils

import (
	"context"
	"fmt"
	"strings"

	"go.uber.org/zap"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"matiksOfficial/matiks-server-go/internal/domain/questions/questionsGenerator"
	"matiksOfficial/matiks-server-go/internal/models"
)

const QuestionRatingAdjustment = 400

func GetDMASQuestionsForGame(ctx context.Context, questionIdPrefix string, players []*models.Player, config *models.GameConfig) ([]*models.GameQuestion, error) {
	playerRatings := make([]int, 0, len(players))
	for _, player := range players {
		if player == nil {
			continue
		}

		if player.Rating == nil {
			continue
		}

		playerRatings = append(playerRatings, *player.Rating)
	}

	if config.DifficultyLevel != nil && len(config.DifficultyLevel) > 0 {
		playerRatings = config.DifficultyLevel
	}

	if len(playerRatings) == 0 {
		return nil, fmt.Errorf("cannot generate questions: no player ratings available")
	}

	minRating := playerRatings[0]
	maxRating := playerRatings[0]
	for _, rating := range playerRatings {
		if rating < minRating {
			minRating = rating
		}
		if rating > maxRating {
			maxRating = rating
		}
	}

	startRating := max(0, minRating-QuestionRatingAdjustment)
	endRating := maxRating + QuestionRatingAdjustment
	var numberOfQuestions int
	timeLimit := GetTimeLimitFromGameConfig(config)
	if timeLimit > 0 {
		numberOfQuestions = (NUMBER_OF_QUESTIONS_PER_MIN*timeLimit + 30) / 60
	} else {
		numberOfQuestions = 0
	}

	ratings := questionsGenerator.GenerateUniformRatings(startRating, endRating, numberOfQuestions)

	questions := make([]*models.GameQuestion, 0, numberOfQuestions)
	uniqueExpressions := make(map[string]struct{})

	for i, rating := range ratings {
		var question *models.Question
		const maxAttempts = 10
		attempts := maxAttempts

		for attempts > 0 {
			question = questionsGenerator.GetRandomArithmeticQuestion(rating, config.QuestionTags)

			if question == nil {
				zlog.Warn(ctx, "Failed to generate question", zap.Int("rating", rating), zap.Int("attempts", attempts))
				attempts--
				continue
			}

			expressionKey := strings.Join(question.Expression, " ")
			if _, exists := uniqueExpressions[expressionKey]; !exists {
				uniqueExpressions[expressionKey] = struct{}{}
				break
			}
			attempts--
		}

		if question == nil {
			zlog.Warn(ctx, "Failed to generate question", zap.Int("rating", rating))
			continue
		}

		questionID := fmt.Sprintf("%s_%d", questionIdPrefix, i)
		question.ID = &questionID
		questions = append(questions, &models.GameQuestion{
			Question: question,
		})
	}

	return questions, nil
}
