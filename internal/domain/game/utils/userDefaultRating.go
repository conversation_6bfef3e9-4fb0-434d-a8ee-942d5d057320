package gameutils

import (
	"fmt"
	"math"

	"matiksOfficial/matiks-server-go/internal/domain/utils"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/models"
)

const abilityRatingMultiplier = 0.7

func GetUserDefaultRating(user *models.User) *models.UserRating {
	return &models.UserRating{
		GlobalRating:       utils.AllocPtr(getUserGlobalRatingWithFallback(user)),
		FlashAnzanRating:   utils.AllocPtr(getUserFlashAnzanRatingWithFallback(user)),
		AbilityDuelsRating: utils.AllocPtr(getUserAbilityRatingWithFallback(user)),
		PuzzleRating:       utils.AllocPtr(getUserPuzzleRatingWithFallback(user)),
	}
}

func GetPlayerRatingByGameType(user *models.User, gameType interface{}) int {
	switch gameType {
	case models.PuzzleGameTypeCrossMathPuzzleDuel:
		return getUserPuzzleRatingWithFallback(user)
	case models.GameTypeFlashAnzan:
		return getUserFlashAnzanRatingWithFallback(user)
	case models.GameTypeAbilityDuels:
		return getUserAbilityRatingWithFallback(user)
	default:
		return getUserGlobalRatingWithFallback(user)
	}
}

func GetRatingFieldByGameType(gameType interface{}) string {
	gameTypeStr := fmt.Sprintf("%v", gameType)
	if gameTypeStr == models.PuzzleGameTypeCrossMathPuzzleDuel.String() {
		return constants.PuzzleRatingField
	}
	switch gameType {
	case models.GameTypeFlashAnzan:
		return constants.MemoryRatingField
	case models.GameTypeAbilityDuels:
		return constants.AbilityRatingField
	default:
		return constants.GlobalRatingField
	}
}

func getUserGlobalRatingWithFallback(user *models.User) int {
	if user != nil && user.RatingV2 != nil && user.RatingV2.GlobalRating != nil {
		return *user.RatingV2.GlobalRating
	}
	if user != nil && user.Rating != nil {
		return *user.Rating
	}
	return constants.DefaultRating
}

func getUserFlashAnzanRatingWithFallback(user *models.User) int {
	if user != nil && user.RatingV2 != nil && user.RatingV2.FlashAnzanRating != nil {
		return *user.RatingV2.FlashAnzanRating
	}
	return constants.DefaultFlashAnzanRating
}

func getUserAbilityRatingWithFallback(user *models.User) int {
	if user != nil && user.RatingV2 != nil && user.RatingV2.AbilityDuelsRating != nil {
		return *user.RatingV2.AbilityDuelsRating
	}
	return int(math.Max(1000, float64(getUserGlobalRatingWithFallback(user))*abilityRatingMultiplier))
}

func getUserPuzzleRatingWithFallback(user *models.User) int {
	if user != nil && user.RatingV2 != nil && user.RatingV2.PuzzleRating != nil {
		return *user.RatingV2.PuzzleRating
	}
	return constants.DefaultPuzzleRating
}
