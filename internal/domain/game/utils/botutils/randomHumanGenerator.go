package botutils

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"time"

	"cloud.google.com/go/storage"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"

	"matiksOfficial/matiks-server-go/internal/constants"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
)

const (
	// URL for generating random human faces
	RandomFaceURL = "https://thispersondoesnotexist.com"

	// Timeout for HTTP requests
	HTTPTimeout = 30 * time.Second

	// Maximum retries for downloading face image
	MaxRetries = 3
)

// DownloadRandomFace downloads a random human face from thispersondoesnotexist.com
func DownloadRandomFace(ctx context.Context) ([]byte, error) {
	client := &http.Client{
		Timeout: HTTPTimeout,
	}

	var lastErr error
	for attempt := 1; attempt <= MaxRetries; attempt++ {
		zlog.Debug(ctx, "Attempting to download random face",
			zap.Int("attempt", attempt), zap.Int("maxRetries", MaxRetries))

		req, err := http.NewRequestWithContext(ctx, "GET", RandomFaceURL, nil)
		if err != nil {
			lastErr = fmt.Errorf("failed to create request: %w", err)
			continue
		}

		// Add headers to mimic a real browser request
		req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
		req.Header.Set("Accept", "image/webp,image/apng,image/*,*/*;q=0.8")
		req.Header.Set("Accept-Language", "en-US,en;q=0.9")
		req.Header.Set("Cache-Control", "no-cache")
		req.Header.Set("Pragma", "no-cache")

		resp, err := client.Do(req)
		if err != nil {
			lastErr = fmt.Errorf("failed to download face (attempt %d): %w", attempt, err)
			if attempt < MaxRetries {
				time.Sleep(time.Duration(attempt) * time.Second) // Exponential backoff
			}
			continue
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			lastErr = fmt.Errorf("unexpected status code %d (attempt %d)", resp.StatusCode, attempt)
			if attempt < MaxRetries {
				time.Sleep(time.Duration(attempt) * time.Second)
			}
			continue
		}

		imageData, err := io.ReadAll(resp.Body)
		if err != nil {
			lastErr = fmt.Errorf("failed to read response body (attempt %d): %w", attempt, err)
			if attempt < MaxRetries {
				time.Sleep(time.Duration(attempt) * time.Second)
			}
			continue
		}

		if len(imageData) == 0 {
			lastErr = fmt.Errorf("received empty image data (attempt %d)", attempt)
			if attempt < MaxRetries {
				time.Sleep(time.Duration(attempt) * time.Second)
			}
			continue
		}

		zlog.Debug(ctx, "Successfully downloaded random face",
			zap.Int("imageSize", len(imageData)), zap.Int("attempt", attempt))
		return imageData, nil
	}

	return nil, fmt.Errorf("failed to download random face after %d attempts: %w", MaxRetries, lastErr)
}

// UploadFaceToStorage uploads the face image to Google Cloud Storage and returns the URL
func UploadFaceToStorage(ctx context.Context, imageData []byte, userID primitive.ObjectID, storageClient *storage.Client) (string, error) {
	if storageClient == nil {
		return "", fmt.Errorf("storage client is nil")
	}

	bucket := storageClient.Bucket(constants.StorageBucket)
	objectName := fmt.Sprintf("bot_profile_pictures/%s_bot-profile.jpeg", userID.Hex())
	object := bucket.Object(objectName)

	writer := object.NewWriter(ctx)
	writer.ContentType = "image/jpeg"
	writer.CacheControl = "public, max-age=86400" // Cache for 24 hours

	_, err := writer.Write(imageData)
	if err != nil {
		writer.Close()
		return "", fmt.Errorf("failed to write image data: %w", err)
	}

	if err := writer.Close(); err != nil {
		return "", fmt.Errorf("failed to close writer: %w", err)
	}

	// Generate URL with timestamp to ensure CDN cache invalidation
	timestamp := time.Now().Unix()
	url := fmt.Sprintf("https://cdn.matiks.com/%s?timestamp=%d", objectName, timestamp)

	zlog.Debug(ctx, "Successfully uploaded bot profile picture",
		zap.String("objectName", objectName), zap.String("url", url))

	return url, nil
}

// GenerateRandomBotProfile generates a random human name and downloads a random face
// Returns name and profileImageURL, with graceful fallbacks for any failures
func GenerateRandomBotProfile(ctx context.Context, userID primitive.ObjectID, storageClient *storage.Client) (name string, profileImageURL string, err error) {
	// Generate random human name - this should never fail
	name = GenerateRandomHumanName()

	// Ensure we have a valid name
	if name == "" {
		name = "Guest User" // Ultimate fallback
	}

	// Try to download and upload random face image
	profileImageURL = tryGenerateProfileImage(ctx, userID, storageClient, name)

	// Always return success - we have graceful fallbacks for everything
	return name, profileImageURL, nil
}

// GenerateRandomBotProfileWithCollisionHandling generates a bot profile with collision handling
// Used when username generation needs multiple attempts
func GenerateRandomBotProfileWithCollisionHandling(ctx context.Context, userID primitive.ObjectID, storageClient *storage.Client, attempt int) (name string, profileImageURL string, err error) {
	// Generate name with variation based on attempt number
	name = GenerateRandomHumanNameWithVariation(attempt)

	// Ensure we have a valid name
	if name == "" {
		name = fmt.Sprintf("Guest User %d", attempt) // Fallback with attempt number
	}

	// Try to generate profile image
	profileImageURL = tryGenerateProfileImage(ctx, userID, storageClient, name)

	// Always return success
	return name, profileImageURL, nil
}

// tryGenerateProfileImage attempts to generate a profile image with multiple fallback strategies
func tryGenerateProfileImage(ctx context.Context, userID primitive.ObjectID, storageClient *storage.Client, name string) string {
	// Strategy 1: Try to download and upload random face
	if imageData, err := DownloadRandomFace(ctx); err == nil {
		if profileURL, uploadErr := UploadFaceToStorage(ctx, imageData, userID, storageClient); uploadErr == nil {
			return profileURL
		} else {
			zlog.Warn(ctx, "Failed to upload face image, trying fallback",
				zap.Error(uploadErr), zap.String("name", name))
		}
	} else {
		zlog.Warn(ctx, "Failed to download random face, trying fallback",
			zap.Error(err), zap.String("name", name))
	}

	// Strategy 2: Use initial-based fallback image
	if len(name) > 0 {
		initial := string(name[0])
		return fmt.Sprintf("https://storage.googleapis.com/matiks-go/%s.png", initial)
	}

	// Strategy 3: Return nil for default avatar handling
	// This allows the frontend to show a default avatar
	return ""
}
