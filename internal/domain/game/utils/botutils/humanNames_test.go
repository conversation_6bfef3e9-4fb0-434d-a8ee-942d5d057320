package botutils

import (
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestFirstNames_NotEmpty(t *testing.T) {
	assert.NotEmpty(t, FirstNames, "FirstNames should not be empty")
	assert.Greater(t, len(FirstNames), 50, "Should have a good variety of first names")
	
	// Verify all names are non-empty
	for i, name := range FirstNames {
		assert.NotEmpty(t, name, "First name at index %d should not be empty", i)
		assert.NotContains(t, name, " ", "First name should not contain spaces")
	}
}

func TestMiddleNames_NotEmpty(t *testing.T) {
	assert.NotEmpty(t, MiddleNames, "MiddleNames should not be empty")
	assert.Greater(t, len(MiddleNames), 10, "Should have a variety of middle names")
	
	// Verify all names are non-empty
	for i, name := range MiddleNames {
		assert.NotEmpty(t, name, "Middle name at index %d should not be empty", i)
		assert.NotContains(t, name, " ", "Middle name should not contain spaces")
	}
}

func TestLastNames_NotEmpty(t *testing.T) {
	assert.NotEmpty(t, LastNames, "LastNames should not be empty")
	assert.Greater(t, len(LastNames), 50, "Should have a good variety of last names")
	
	// Verify all names are non-empty
	for i, name := range LastNames {
		assert.NotEmpty(t, name, "Last name at index %d should not be empty", i)
		assert.NotContains(t, name, " ", "Last name should not contain spaces")
	}
}

func TestIndianNames_Authenticity(t *testing.T) {
	// Test that we have authentic Indian names
	expectedFirstNames := []string{"Aarav", "Priya", "Arjun", "Ananya", "Krishna", "Kavya"}
	expectedLastNames := []string{"Sharma", "Patel", "Singh", "Reddy", "Iyer", "Das"}
	expectedMiddleNames := []string{"Kumar", "Devi", "Singh", "Kaur", "Prakash"}
	
	for _, name := range expectedFirstNames {
		assert.Contains(t, FirstNames, name, "Should contain Indian first name: %s", name)
	}
	
	for _, name := range expectedLastNames {
		assert.Contains(t, LastNames, name, "Should contain Indian last name: %s", name)
	}
	
	for _, name := range expectedMiddleNames {
		assert.Contains(t, MiddleNames, name, "Should contain Indian middle name: %s", name)
	}
}

func TestGenerateRandomHumanName_Format(t *testing.T) {
	for i := 0; i < 100; i++ {
		name := GenerateRandomHumanName()
		
		// Basic format checks
		assert.NotEmpty(t, name, "Generated name should not be empty")
		assert.True(t, strings.Contains(name, " "), "Name should contain at least one space")
		
		// Should not start or end with space
		assert.False(t, strings.HasPrefix(name, " "), "Name should not start with space")
		assert.False(t, strings.HasSuffix(name, " "), "Name should not end with space")
		
		// Should not have multiple consecutive spaces
		assert.False(t, strings.Contains(name, "  "), "Name should not have multiple consecutive spaces")
		
		parts := strings.Split(name, " ")
		assert.GreaterOrEqual(t, len(parts), 2, "Name should have at least first and last name")
		assert.LessOrEqual(t, len(parts), 4, "Name should not have more than 4 parts")
		
		// Each part should be non-empty
		for j, part := range parts {
			assert.NotEmpty(t, part, "Name part %d should not be empty", j)
		}
	}
}

func TestGenerateRandomHumanNameWithVariation_Patterns(t *testing.T) {
	tests := []struct {
		name     string
		attempt  int
		expected func(string) bool
	}{
		{
			name:    "Standard format (attempt 0)",
			attempt: 0,
			expected: func(name string) bool {
				parts := strings.Split(name, " ")
				return len(parts) == 2 // First Last
			},
		},
		{
			name:    "With middle name (attempt 1)",
			attempt: 1,
			expected: func(name string) bool {
				parts := strings.Split(name, " ")
				return len(parts) == 3 // First Middle Last
			},
		},
		{
			name:    "With initial (attempt 2)",
			attempt: 2,
			expected: func(name string) bool {
				return strings.Contains(name, ".") && !strings.Contains(name, ". .")
			},
		},
		{
			name:    "With two initials (attempt 3)",
			attempt: 3,
			expected: func(name string) bool {
				dotCount := strings.Count(name, ".")
				return dotCount >= 2
			},
		},
		{
			name:    "Cycle back to standard (attempt 4)",
			attempt: 4,
			expected: func(name string) bool {
				parts := strings.Split(name, " ")
				return len(parts) == 2 // Should cycle back to standard format
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			name := GenerateRandomHumanNameWithVariation(tt.attempt)
			assert.True(t, tt.expected(name), "Name '%s' doesn't match expected pattern for attempt %d", name, tt.attempt)
		})
	}
}

func TestGenerateRandomHumanName_Diversity(t *testing.T) {
	const iterations = 1000
	names := make(map[string]int)
	firstNames := make(map[string]int)
	lastNames := make(map[string]int)
	
	for i := 0; i < iterations; i++ {
		name := GenerateRandomHumanName()
		names[name]++
		
		parts := strings.Split(name, " ")
		if len(parts) >= 2 {
			firstNames[parts[0]]++
			lastNames[parts[len(parts)-1]]++
		}
	}
	
	// Check diversity
	uniqueNames := len(names)
	uniqueFirstNames := len(firstNames)
	uniqueLastNames := len(lastNames)
	
	// We should get good diversity
	assert.Greater(t, uniqueNames, iterations/2, "Should generate diverse full names")
	assert.Greater(t, uniqueFirstNames, 20, "Should use diverse first names")
	assert.Greater(t, uniqueLastNames, 20, "Should use diverse last names")
	
	// No single name should dominate
	for name, count := range names {
		frequency := float64(count) / float64(iterations)
		assert.Less(t, frequency, 0.1, "Name '%s' appears too frequently (%.2f%%)", name, frequency*100)
	}
}

func TestGenerateRandomHumanNameWithVariation_Consistency(t *testing.T) {
	// Test that the same attempt number produces consistent patterns
	for attempt := 0; attempt < 8; attempt++ {
		names := make([]string, 10)
		for i := 0; i < 10; i++ {
			names[i] = GenerateRandomHumanNameWithVariation(attempt)
		}
		
		// All names for the same attempt should follow the same pattern
		expectedPattern := attempt % 4
		for i, name := range names {
			parts := strings.Split(name, " ")
			switch expectedPattern {
			case 0: // Standard format
				assert.Equal(t, 2, len(parts), "Attempt %d, name %d: '%s' should have 2 parts", attempt, i, name)
			case 1: // With middle name
				assert.Equal(t, 3, len(parts), "Attempt %d, name %d: '%s' should have 3 parts", attempt, i, name)
			case 2: // With initial
				assert.True(t, strings.Contains(name, "."), "Attempt %d, name %d: '%s' should contain initial", attempt, i, name)
			case 3: // With two initials
				dotCount := strings.Count(name, ".")
				assert.GreaterOrEqual(t, dotCount, 2, "Attempt %d, name %d: '%s' should have 2+ initials", attempt, i, name)
			}
		}
	}
}

func TestMiddleNameInclusion_Probability(t *testing.T) {
	const iterations = 1000
	namesWithMiddle := 0
	
	for i := 0; i < iterations; i++ {
		name := GenerateRandomHumanName()
		parts := strings.Split(name, " ")
		if len(parts) == 3 {
			namesWithMiddle++
		}
	}
	
	// Should be around 30% (with some tolerance for randomness)
	percentage := float64(namesWithMiddle) / float64(iterations)
	assert.Greater(t, percentage, 0.2, "Should have at least 20%% names with middle names")
	assert.Less(t, percentage, 0.4, "Should have at most 40%% names with middle names")
}

func BenchmarkGenerateRandomHumanNameHumanNames(b *testing.B) {
	for i := 0; i < b.N; i++ {
		GenerateRandomHumanName()
	}
}

func BenchmarkGenerateRandomHumanNameWithVariationHumanNames(b *testing.B) {
	for i := 0; i < b.N; i++ {
		GenerateRandomHumanNameWithVariation(i % 4)
	}
}

// Test edge cases
func TestGenerateRandomHumanName_EdgeCases(t *testing.T) {
	// Test with empty slices (should not happen in practice, but good to test)
	originalFirst := FirstNames
	originalLast := LastNames
	originalMiddle := MiddleNames
	
	defer func() {
		FirstNames = originalFirst
		LastNames = originalLast
		MiddleNames = originalMiddle
	}()
	
	// This test is more for ensuring the function doesn't panic
	// In real usage, the slices should never be empty
	t.Run("handles normal case", func(t *testing.T) {
		name := GenerateRandomHumanName()
		assert.NotEmpty(t, name)
	})
}

func TestNameComponents_NoSpecialCharacters(t *testing.T) {
	// Verify that names don't contain problematic characters
	problematicChars := []string{"@", "#", "$", "%", "^", "&", "*", "(", ")", "[", "]", "{", "}", "|", "\\", "/", "?", "<", ">", ",", ";", ":", "\"", "'"}
	
	allNames := append(append(FirstNames, LastNames...), MiddleNames...)
	
	for _, name := range allNames {
		for _, char := range problematicChars {
			assert.NotContains(t, name, char, "Name '%s' should not contain problematic character '%s'", name, char)
		}
	}
}
