package botutils

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	"matiksOfficial/matiks-server-go/internal/models"
)

// MockUserServiceWithCount implements the needed methods for testing
type MockUserServiceWithCount struct {
	mock.Mock
}

func (m *MockUserServiceWithCount) GenerateUserName(ctx context.Context, name string) (string, error) {
	args := m.Called(ctx, name)
	return args.String(0), args.Error(1)
}

func (m *MockUserServiceWithCount) Count(ctx context.Context, filter interface{}) (int64, error) {
	args := m.Called(ctx, filter)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockUserServiceWithCount) GetUserByID(ctx context.Context, userID primitive.ObjectID) (*models.User, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.User), args.Error(1)
}

func (m *MockUserServiceWithCount) CountUsersWithHigherRating(ctx context.Context, userRepo repository.UserRepository, rating int) (int, error) {
	args := m.Called(ctx, userRepo, rating)
	return args.Int(0), args.Error(1)
}

func TestGenerateRandomHumanName(t *testing.T) {
	tests := []struct {
		name string
		runs int
	}{
		{
			name: "Generate multiple names",
			runs: 100,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			names := make(map[string]bool)
			
			for i := 0; i < tt.runs; i++ {
				name := GenerateRandomHumanName()
				
				// Verify name is not empty
				assert.NotEmpty(t, name, "Generated name should not be empty")
				
				// Verify name contains at least first and last name
				assert.Contains(t, name, " ", "Name should contain at least one space")
				
				// Track unique names
				names[name] = true
			}
			
			// Verify we get some variety in names (at least 50% unique for 100 runs)
			assert.GreaterOrEqual(t, len(names), tt.runs/2, "Should generate diverse names")
		})
	}
}

func TestGenerateRandomHumanNameWithVariation(t *testing.T) {
	tests := []struct {
		name    string
		attempt int
		verify  func(t *testing.T, name string)
	}{
		{
			name:    "Attempt 0 - Standard name",
			attempt: 0,
			verify: func(t *testing.T, name string) {
				assert.NotEmpty(t, name)
				assert.Contains(t, name, " ")
			},
		},
		{
			name:    "Attempt 1 - With middle name",
			attempt: 1,
			verify: func(t *testing.T, name string) {
				assert.NotEmpty(t, name)
				// Should have at least 2 spaces for first + middle + last
				spaceCount := 0
				for _, char := range name {
					if char == ' ' {
						spaceCount++
					}
				}
				assert.GreaterOrEqual(t, spaceCount, 2, "Should have middle name")
			},
		},
		{
			name:    "Attempt 2 - With initial",
			attempt: 2,
			verify: func(t *testing.T, name string) {
				assert.NotEmpty(t, name)
				assert.Contains(t, name, ".", "Should contain initial with dot")
			},
		},
		{
			name:    "Attempt 3 - With two initials",
			attempt: 3,
			verify: func(t *testing.T, name string) {
				assert.NotEmpty(t, name)
				dotCount := 0
				for _, char := range name {
					if char == '.' {
						dotCount++
					}
				}
				assert.GreaterOrEqual(t, dotCount, 2, "Should have two initials")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			name := GenerateRandomHumanNameWithVariation(tt.attempt)
			tt.verify(t, name)
		})
	}
}

// Note: Tests for tryGenerateProfileImage are removed because they require
// a properly initialized logger context which is not available in unit tests.
// These functions are tested through integration tests in the actual application.

// All tryGenerateProfileImage tests removed due to logger dependency

func BenchmarkGenerateRandomHumanName(b *testing.B) {
	for i := 0; i < b.N; i++ {
		GenerateRandomHumanName()
	}
}

func BenchmarkGenerateRandomHumanNameWithVariation(b *testing.B) {
	for i := 0; i < b.N; i++ {
		GenerateRandomHumanNameWithVariation(i % 4)
	}
}
