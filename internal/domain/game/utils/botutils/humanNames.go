package botutils

import "math/rand/v2"

// HumanNames contains lists of realistic Indian human names for bot generation
var (
	// Indian first names (male and female combined for diversity)
	FirstNames = []string{
		// Popular Indian male names
		"<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>",
		"<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>ryan", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>",
		"<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>",
		"<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>",
		"Advait", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>",

		// Popular Indian female names
		"<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>",
		"<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>",
		"<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>",
		"<PERSON><PERSON>", "Anvi", "Siya", "Drishti", "Jiya", "Asmi", "Nisha", "Rhea", "Vanya", "Kashvi",
		"Mahika", "Kimaya", "Mishka", "Samara", "Mysha", "Amaira", "Yashika", "Tanvi", "Samaira", "Reet",

		// Traditional names with modern appeal
		"Raj", "Amit", "Suresh", "Vikram", "Rahul", "Deepak", "Manoj", "Sandeep", "Ajay", "Ravi",
		"Priya", "Pooja", "Neha", "Sunita", "Kavita", "Meera", "Sita", "Geeta", "Radha", "Shanti",
		"Arjun", "Kiran", "Mohan", "Gopal", "Ramesh", "Mukesh", "Dinesh", "Mahesh", "Naresh", "Yogesh",
	}

	// Indian middle names (optional, used sometimes)
	MiddleNames = []string{
		"Kumar", "Kumari", "Devi", "Singh", "Kaur", "Lal", "Bai", "Chand", "Prakash", "Chandra",
		"Raj", "Rani", "Nath", "Nandan", "Mohan", "Kishore", "Shankar", "Narayan", "Anand", "Prasad",
	}

	// Indian last names/surnames from various regions
	LastNames = []string{
		// North Indian surnames
		"Sharma", "Gupta", "Singh", "Kumar", "Agarwal", "Bansal", "Jain", "Mittal", "Goel", "Arora",
		"Chopra", "Malhotra", "Kapoor", "Khanna", "Bhatia", "Sethi", "Aggarwal", "Jindal", "Goyal", "Singhal",
		"Saxena", "Verma", "Srivastava", "Tiwari", "Dubey", "Pandey", "Mishra", "Shukla", "Tripathi", "Chaturvedi",

		// South Indian surnames
		"Reddy", "Rao", "Nair", "Menon", "Iyer", "Iyengar", "Krishnan", "Raman", "Subramanian", "Venkatesh",
		"Naidu", "Chowdary", "Pillai", "Kumar", "Prasad", "Murthy", "Sastry", "Acharya", "Bhatt", "Joshi",
		"Patel", "Shah", "Mehta", "Desai", "Modi", "Thakkar", "Vyas", "Trivedi", "Pandya", "Raval",

		// West Indian surnames
		"Patil", "Kulkarni", "Joshi", "Deshpande", "Bhosale", "More", "Jadhav", "Pawar", "Raut", "Shinde",
		"Gaikwad", "Sawant", "Kadam", "Mane", "Salunkhe", "Kale", "Bhagat", "Lokhande", "Thorat", "Kamble",

		// East Indian surnames
		"Das", "Roy", "Ghosh", "Mukherjee", "Banerjee", "Chatterjee", "Sengupta", "Bose", "Mitra", "Saha",
		"Chakraborty", "Bhattacharya", "Dutta", "Sarkar", "Pal", "Mondal", "Biswas", "Haldar", "Kar", "Majumdar",
	}
)

// GenerateRandomHumanName generates a random realistic Indian human name
// Sometimes includes middle names to reduce collision chances
func GenerateRandomHumanName() string {
	firstName := FirstNames[rand.IntN(len(FirstNames))]
	lastName := LastNames[rand.IntN(len(LastNames))]

	// 30% chance to include a middle name to reduce collisions
	if rand.IntN(100) < 30 {
		middleName := MiddleNames[rand.IntN(len(MiddleNames))]
		return firstName + " " + middleName + " " + lastName
	}

	return firstName + " " + lastName
}

// GenerateRandomHumanNameWithVariation generates a name with more variation strategies
// Used when collision handling is needed
func GenerateRandomHumanNameWithVariation(attempt int) string {
	firstName := FirstNames[rand.IntN(len(FirstNames))]
	lastName := LastNames[rand.IntN(len(LastNames))]

	switch attempt % 4 {
	case 0:
		// Standard first + last name
		return firstName + " " + lastName
	case 1:
		// First + middle + last name
		middleName := MiddleNames[rand.IntN(len(MiddleNames))]
		return firstName + " " + middleName + " " + lastName
	case 2:
		// First name + initial + last name (e.g., "Raj K. Sharma")
		middleInitial := string(MiddleNames[rand.IntN(len(MiddleNames))][0])
		return firstName + " " + middleInitial + ". " + lastName
	case 3:
		// First name + two initials + last name (e.g., "Raj K. P. Sharma")
		middleInitial1 := string(MiddleNames[rand.IntN(len(MiddleNames))][0])
		middleInitial2 := string(MiddleNames[rand.IntN(len(MiddleNames))][0])
		return firstName + " " + middleInitial1 + ". " + middleInitial2 + ". " + lastName
	default:
		return firstName + " " + lastName
	}
}
