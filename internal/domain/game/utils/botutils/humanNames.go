package botutils

import "math/rand/v2"

// HumanNames contains lists of realistic human names for bot generation (Indian and International)
var (
	// Indian first names (male and female combined for diversity)
	IndianFirstNames = []string{
		// Popular Indian male names
		"<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>",
		"<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>",
		"<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>",
		"<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>",
		"<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>",
		"<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>",
		"<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>",
		"<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>v", "Harsh", "Karthik",
		"Rohan", "Tanish", "Dhruv", "Arush", "Krish", "Moksh", "Veer", "Aakash", "Daksh", "Kishan",
		"Aayan", "Laksh", "Advait", "Samarth", "Vihang", "Parth", "Anvit", "Agastya", "Tejas", "Yash",
		"Raghav", "Shivansh", "Atharva", "Reyansh", "Aayansh", "Shaurya", "Vivaan", "Aditya", "Vihaan",
		"Arjun", "Sai", "Ayaan", "Krishna", "Ishaan", "Shaurya", "Atharv", "Advik", "Pranav", "Rishab",

		// Popular Indian female names
		"Aadhya", "Saanvi", "Aanya", "Diya", "Pihu", "Prisha", "Ananya", "Fatima", "Anika", "Myra",
		"Sara", "Pari", "Kavya", "Ira", "Riya", "Keya", "Zara", "Kiara", "Arya", "Tara",
		"Ishika", "Khushi", "Avni", "Aradhya", "Kiya", "Ahana", "Zoya", "Sia", "Aditi", "Shanaya",
		"Navya", "Anvi", "Siya", "Drishti", "Jiya", "Asmi", "Nisha", "Rhea", "Vanya", "Kashvi",
		"Mahika", "Kimaya", "Mishka", "Samara", "Mysha", "Amaira", "Yashika", "Tanvi", "Samaira", "Reet",
		"Aadhya", "Saanvi", "Aanya", "Diya", "Pihu", "Prisha", "Ananya", "Fatima", "Anika", "Myra",
		"Sara", "Pari", "Kavya", "Ira", "Riya", "Keya", "Zara", "Kiara", "Aarya", "Tara",
		"Ishika", "Khushi", "Avni", "Aradhya", "Kiya", "Ahana", "Zoya", "Sia", "Aditi", "Shanaya",
		"Navya", "Anvi", "Siya", "Drishti", "Jiya", "Asmi", "Nisha", "Rhea", "Vanya", "Kashvi",
		"Mahika", "Kimaya", "Mishka", "Samara", "Mysha", "Amaira", "Yashika", "Tanvi", "Samaira", "Reet",

		// Traditional Indian names with modern appeal
		"Raj", "Amit", "Suresh", "Vikram", "Rahul", "Deepak", "Manoj", "Sandeep", "Ajay", "Ravi",
		"Priya", "Pooja", "Neha", "Sunita", "Kavita", "Meera", "Sita", "Geeta", "Radha", "Shanti",
		"Arjun", "Kiran", "Mohan", "Gopal", "Ramesh", "Mukesh", "Dinesh", "Mahesh", "Naresh", "Yogesh",
		"Sanjay", "Vinod", "Ashok", "Rajesh", "Sunil", "Anil", "Prakash", "Santosh", "Raman", "Pawan",
		"Anita", "Rekha", "Sushma", "Usha", "Lata", "Asha", "Kamala", "Pushpa", "Sudha", "Vandana",
		"Rajesh", "Suresh", "Ramesh", "Mahesh", "Naresh", "Dinesh", "Mukesh", "Yogesh", "Hitesh", "Jitesh",
		"Preeti", "Seema", "Reena", "Veena", "Meena", "Leela", "Sheela", "Heera", "Mira", "Tina",
		"Rohit", "Mohit", "Sumit", "Amit", "Lalit", "Ajit", "Ranjit", "Sanjit", "Manjit", "Harjit",
		"Kavita", "Sunita", "Anita", "Mamta", "Smita", "Geeta", "Sita", "Rita", "Nita", "Lata",
		"Arun", "Tarun", "Varun", "Karan", "Charan", "Gagan", "Magan", "Ragan", "Sagan", "Jagan",
	}

	// International first names (Western/English names)
	InternationalFirstNames = []string{
		// Popular male names
		"Alexander", "William", "James", "Benjamin", "Lucas", "Henry", "Theodore", "Caleb", "Ryan", "Asher",
		"Nathan", "Thomas", "Leo", "Isaiah", "Charles", "Josiah", "Christopher", "Joshua", "Andrew", "Daniel",
		"Matthew", "Anthony", "Mark", "Paul", "Steven", "Kenneth", "Joseph", "Edward", "Brian", "Ronald",
		"Timothy", "Jason", "Jeffrey", "Frank", "Gary", "Nicholas", "Eric", "Jonathan", "Stephen", "Larry",
		"Justin", "Scott", "Brandon", "Benjamin", "Samuel", "Gregory", "Alexander", "Patrick", "Jack", "Dennis",
		"Jerry", "Tyler", "Aaron", "Jose", "Henry", "Adam", "Douglas", "Nathan", "Peter", "Zachary",
		"Kyle", "Noah", "Alan", "Ethan", "Jeremy", "Lionel", "Angel", "Wayne", "Carl", "Harold",
		"Jordan", "Jesse", "Bryan", "Lawrence", "Arthur", "Gabriel", "Bruce", "Logan", "Billy", "Willie",
		"Ralph", "Roy", "Eugene", "Louis", "Philip", "Bobby", "Johnny", "Mason", "Elijah", "Wayne",
		"Liam", "Oliver", "Elijah", "William", "James", "Benjamin", "Lucas", "Henry", "Alexander", "Mason",

		// Popular female names
		"Emma", "Olivia", "Ava", "Isabella", "Sophia", "Charlotte", "Mia", "Amelia", "Harper", "Evelyn",
		"Abigail", "Emily", "Elizabeth", "Mila", "Ella", "Avery", "Sofia", "Camila", "Aria", "Scarlett",
		"Victoria", "Madison", "Luna", "Grace", "Chloe", "Penelope", "Layla", "Riley", "Zoey", "Nora",
		"Lily", "Eleanor", "Hannah", "Lillian", "Addison", "Aubrey", "Ellie", "Stella", "Natalie", "Zoe",
		"Leah", "Hazel", "Violet", "Aurora", "Savannah", "Audrey", "Brooklyn", "Bella", "Claire", "Skylar",
		"Lucy", "Paisley", "Everly", "Anna", "Caroline", "Nova", "Genesis", "Emilia", "Kennedy", "Samantha",
		"Maya", "Willow", "Kinsley", "Naomi", "Aaliyah", "Elena", "Sarah", "Ariana", "Allison", "Gabriella",
		"Alice", "Madelyn", "Cora", "Ruby", "Eva", "Serenity", "Autumn", "Adeline", "Hailey", "Gianna",
		"Valentina", "Isla", "Eliana", "Quinn", "Nevaeh", "Ivy", "Sadie", "Piper", "Lydia", "Alexa",
		"Josephine", "Emery", "Julia", "Delilah", "Arianna", "Vivian", "Kaylee", "Sophie", "Brielle", "Madeline",
		"Peyton", "Rylee", "Clara", "Hadley", "Melanie", "Mackenzie", "Reagan", "Adalynn", "Liliana", "Aubree",
		"Jade", "Katherine", "Isabelle", "Natalia", "Raelynn", "Maria", "Athena", "Ximena", "Aria", "Leilani",
		"Taylor", "Faith", "Rose", "Kylie", "Alexandra", "Mary", "Margaret", "Lyla", "Ashley", "Amaya",
		"Eliza", "Brianna", "Bailey", "Andrea", "Khloe", "Jasmine", "Melody", "Iris", "Isabel", "Norah",
		"Annabelle", "Valeria", "Emerson", "Adalyn", "Ryleigh", "Eden", "Emersyn", "Anastasia", "Kayla", "Alyssa",
	}

	// Indian middle names (optional, used sometimes)
	IndianMiddleNames = []string{
		"Kumar", "Kumari", "Devi", "Singh", "Kaur", "Lal", "Bai", "Chand", "Prakash", "Chandra",
		"Raj", "Rani", "Nath", "Nandan", "Mohan", "Kishore", "Shankar", "Narayan", "Anand", "Prasad",
		"Bhushan", "Ranjan", "Kiran", "Mani", "Ratna", "Shree", "Shri", "Mata", "Nath", "Wati",
		"Veer", "Bir", "Pal", "Das", "Lal", "Mal", "Chand", "Dutt", "Nand", "Anand",
	}

	// International middle names/initials
	InternationalMiddleNames = []string{
		"James", "John", "Robert", "Michael", "William", "David", "Richard", "Charles", "Joseph", "Thomas",
		"Christopher", "Daniel", "Paul", "Mark", "Donald", "George", "Kenneth", "Steven", "Edward", "Brian",
		"Ronald", "Anthony", "Kevin", "Jason", "Matthew", "Gary", "Timothy", "Jose", "Larry", "Jeffrey",
		"Frank", "Scott", "Eric", "Stephen", "Andrew", "Raymond", "Gregory", "Joshua", "Jerry", "Dennis",
		"Walter", "Patrick", "Peter", "Harold", "Douglas", "Henry", "Carl", "Arthur", "Ryan", "Roger",
		"Marie", "Rose", "Ann", "Jean", "Elizabeth", "Nicole", "Michelle", "Lisa", "Angela", "Helen",
		"Sandra", "Donna", "Carol", "Ruth", "Sharon", "Michelle", "Laura", "Sarah", "Kimberly", "Deborah",
		"Dorothy", "Lisa", "Nancy", "Karen", "Betty", "Helen", "Sandra", "Donna", "Carol", "Ruth",
	}

	// Indian last names/surnames from various regions (expanded)
	IndianLastNames = []string{
		// North Indian surnames
		"Sharma", "Gupta", "Singh", "Kumar", "Agarwal", "Bansal", "Jain", "Mittal", "Goel", "Arora",
		"Chopra", "Malhotra", "Kapoor", "Khanna", "Bhatia", "Sethi", "Aggarwal", "Jindal", "Goyal", "Singhal",
		"Saxena", "Verma", "Srivastava", "Tiwari", "Dubey", "Pandey", "Mishra", "Shukla", "Tripathi", "Chaturvedi",
		"Agrawal", "Maheshwari", "Khandelwal", "Porwal", "Somani", "Kothari", "Bajaj", "Garg", "Saraf", "Oswal",
		"Bhargava", "Sinha", "Jha", "Thakur", "Yadav", "Chauhan", "Rajput", "Bisht", "Negi", "Rawat",
		"Bhatt", "Joshi", "Upadhyay", "Pathak", "Dwivedi", "Ojha", "Gaur", "Saxena", "Mathur", "Tandon",

		// South Indian surnames
		"Reddy", "Rao", "Nair", "Menon", "Iyer", "Iyengar", "Krishnan", "Raman", "Subramanian", "Venkatesh",
		"Naidu", "Chowdary", "Pillai", "Kumar", "Prasad", "Murthy", "Sastry", "Acharya", "Bhatt", "Joshi",
		"Patel", "Shah", "Mehta", "Desai", "Modi", "Thakkar", "Vyas", "Trivedi", "Pandya", "Raval",
		"Shetty", "Hegde", "Kamath", "Bhat", "Pai", "Kini", "Amin", "Jain", "Acharya", "Dixit",
		"Swamy", "Gowda", "Reddy", "Naidu", "Chary", "Varma", "Sastri", "Pandit", "Shastri", "Guruji",
		"Nambiar", "Thampi", "Warrier", "Panicker", "Kurup", "Nair", "Menon", "Pillai", "Kartha", "Namboothiri",

		// West Indian surnames
		"Patil", "Kulkarni", "Joshi", "Deshpande", "Bhosale", "More", "Jadhav", "Pawar", "Raut", "Shinde",
		"Gaikwad", "Sawant", "Kadam", "Mane", "Salunkhe", "Kale", "Bhagat", "Lokhande", "Thorat", "Kamble",
		"Deshmukh", "Chavan", "Jagtap", "Kumbhar", "Sutar", "Sonar", "Lohar", "Teli", "Mali", "Gavli",
		"Rane", "Naik", "Gaonkar", "Shirodkar", "Parsekar", "Mayekar", "Prabhugaonkar", "Shirodkar", "Kamat", "Borkar",

		// East Indian surnames
		"Das", "Roy", "Ghosh", "Mukherjee", "Banerjee", "Chatterjee", "Sengupta", "Bose", "Mitra", "Saha",
		"Chakraborty", "Bhattacharya", "Dutta", "Sarkar", "Pal", "Mondal", "Biswas", "Haldar", "Kar", "Majumdar",
		"Ganguly", "Chowdhury", "Bhowmik", "Adhikari", "Barman", "Basak", "Bera", "Ghosal", "Halder", "Jana",
		"Kundu", "Maity", "Naskar", "Pramanik", "Samanta", "Senapati", "Sikdar", "Singha", "Tarafder", "Adak",
	}

	// International last names (Western/English surnames) - expanded
	InternationalLastNames = []string{
		// Common English/American surnames
		"Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller", "Davis", "Rodriguez", "Martinez",
		"Hernandez", "Lopez", "Gonzalez", "Wilson", "Anderson", "Thomas", "Taylor", "Moore", "Jackson", "Martin",
		"Lee", "Perez", "Thompson", "White", "Harris", "Sanchez", "Clark", "Ramirez", "Lewis", "Robinson",
		"Walker", "Young", "Allen", "King", "Wright", "Scott", "Torres", "Nguyen", "Hill", "Flores",
		"Green", "Adams", "Nelson", "Baker", "Hall", "Rivera", "Campbell", "Mitchell", "Carter", "Roberts",
		"Gomez", "Phillips", "Evans", "Turner", "Diaz", "Parker", "Cruz", "Edwards", "Collins", "Reyes",
		"Stewart", "Morris", "Morales", "Murphy", "Cook", "Rogers", "Gutierrez", "Ortiz", "Morgan", "Cooper",
		"Peterson", "Bailey", "Reed", "Kelly", "Howard", "Ramos", "Kim", "Cox", "Ward", "Richardson",
		"Watson", "Brooks", "Chavez", "Wood", "James", "Bennett", "Gray", "Mendoza", "Ruiz", "Hughes",
		"Price", "Alvarez", "Castillo", "Sanders", "Patterson", "Myers", "Long", "Ross", "Foster", "Jimenez",

		// European surnames
		"Mueller", "Schmidt", "Schneider", "Fischer", "Weber", "Meyer", "Wagner", "Becker", "Schulz", "Hoffmann",
		"Rossi", "Russo", "Ferrari", "Esposito", "Bianchi", "Romano", "Colombo", "Ricci", "Marino", "Greco",
		"Bruno", "Gallo", "Conti", "DeLuca", "Mancini", "Costa", "Giordano", "Rizzo", "Lombardi", "Moretti",
		"Dubois", "Martin", "Bernard", "Durand", "Moreau", "Laurent", "Simon", "Michel", "Lefebvre", "Leroy",
		"Roux", "David", "Bertrand", "Morel", "Fournier", "Girard", "Bonnet", "Dupont", "Lambert", "Fontaine",
		"O'Connor", "O'Sullivan", "Murphy", "Kelly", "Walsh", "Ryan", "Byrne", "McCarthy", "O'Brien", "Doyle",
		"Wilson", "Campbell", "Stewart", "Thomson", "Robertson", "Anderson", "MacDonald", "Scott", "Reid", "Murray",

		// Additional diverse surnames
		"Chang", "Chen", "Li", "Wang", "Zhang", "Liu", "Yang", "Huang", "Zhao", "Wu",
		"Zhou", "Xu", "Sun", "Ma", "Zhu", "Hu", "Guo", "Lin", "He", "Gao",
		"Tanaka", "Suzuki", "Takahashi", "Watanabe", "Ito", "Yamamoto", "Nakamura", "Kobayashi", "Kato", "Yoshida",
		"Yamada", "Sasaki", "Yamaguchi", "Matsumoto", "Inoue", "Kimura", "Hayashi", "Shimizu", "Yamazaki", "Mori",
	}
)

// GenerateRandomHumanName generates a random realistic human name (Indian or International)
// Ensures proper matching: Indian first names with Indian surnames, International with International
// Sometimes includes middle names to reduce collision chances
func GenerateRandomHumanName() string {
	// 60% chance for Indian names, 40% chance for International names
	isIndian := rand.IntN(100) < 60

	if isIndian {
		return generateIndianName()
	}
	return generateInternationalName()
}

// generateIndianName creates an Indian name with proper surname matching
func generateIndianName() string {
	firstName := IndianFirstNames[rand.IntN(len(IndianFirstNames))]
	lastName := IndianLastNames[rand.IntN(len(IndianLastNames))]

	// 30% chance to include a middle name to reduce collisions
	if rand.IntN(100) < 30 {
		middleName := IndianMiddleNames[rand.IntN(len(IndianMiddleNames))]
		return firstName + " " + middleName + " " + lastName
	}

	return firstName + " " + lastName
}

// generateInternationalName creates an International name with proper surname matching
func generateInternationalName() string {
	firstName := InternationalFirstNames[rand.IntN(len(InternationalFirstNames))]
	lastName := InternationalLastNames[rand.IntN(len(InternationalLastNames))]

	// 25% chance to include a middle name to reduce collisions
	if rand.IntN(100) < 25 {
		middleName := InternationalMiddleNames[rand.IntN(len(InternationalMiddleNames))]
		return firstName + " " + middleName + " " + lastName
	}

	return firstName + " " + lastName
}

// GenerateRandomHumanNameWithVariation generates a name with more variation strategies
// Used when collision handling is needed
func GenerateRandomHumanNameWithVariation(attempt int) string {
	// Alternate between Indian and International names based on attempt
	isIndian := attempt%2 == 0

	if isIndian {
		return generateIndianNameWithVariation(attempt)
	}
	return generateInternationalNameWithVariation(attempt)
}

// generateIndianNameWithVariation creates Indian names with different patterns
func generateIndianNameWithVariation(attempt int) string {
	firstName := IndianFirstNames[rand.IntN(len(IndianFirstNames))]
	lastName := IndianLastNames[rand.IntN(len(IndianLastNames))]

	switch attempt % 4 {
	case 0:
		// Standard first + last name
		return firstName + " " + lastName
	case 1:
		// First + middle + last name
		middleName := IndianMiddleNames[rand.IntN(len(IndianMiddleNames))]
		return firstName + " " + middleName + " " + lastName
	case 2:
		// First name + initial + last name (e.g., "Raj K. Sharma")
		middleInitial := string(IndianMiddleNames[rand.IntN(len(IndianMiddleNames))][0])
		return firstName + " " + middleInitial + ". " + lastName
	case 3:
		// First name + two initials + last name (e.g., "Raj K. P. Sharma")
		middleInitial1 := string(IndianMiddleNames[rand.IntN(len(IndianMiddleNames))][0])
		middleInitial2 := string(IndianMiddleNames[rand.IntN(len(IndianMiddleNames))][0])
		return firstName + " " + middleInitial1 + ". " + middleInitial2 + ". " + lastName
	default:
		return firstName + " " + lastName
	}
}

// generateInternationalNameWithVariation creates International names with different patterns
func generateInternationalNameWithVariation(attempt int) string {
	firstName := InternationalFirstNames[rand.IntN(len(InternationalFirstNames))]
	lastName := InternationalLastNames[rand.IntN(len(InternationalLastNames))]

	switch attempt % 4 {
	case 0:
		// Standard first + last name
		return firstName + " " + lastName
	case 1:
		// First + middle + last name
		middleName := InternationalMiddleNames[rand.IntN(len(InternationalMiddleNames))]
		return firstName + " " + middleName + " " + lastName
	case 2:
		// First name + initial + last name (e.g., "John M. Smith")
		middleInitial := string(InternationalMiddleNames[rand.IntN(len(InternationalMiddleNames))][0])
		return firstName + " " + middleInitial + ". " + lastName
	case 3:
		// First name + two initials + last name (e.g., "John M. R. Smith")
		middleInitial1 := string(InternationalMiddleNames[rand.IntN(len(InternationalMiddleNames))][0])
		middleInitial2 := string(InternationalMiddleNames[rand.IntN(len(InternationalMiddleNames))][0])
		return firstName + " " + middleInitial1 + ". " + middleInitial2 + ". " + lastName
	default:
		return firstName + " " + lastName
	}
}
