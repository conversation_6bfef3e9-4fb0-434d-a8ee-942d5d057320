package botutils

import "math/rand/v2"

// HumanNames contains lists of realistic human names for bot generation
var (
	FirstNames = []string{
		"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
		"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
		"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
		"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
		"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>",
		"<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
		"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
		"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
		"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
		"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
		"<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
		"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Caroline", "Nolan", "Genesis", "Jeremiah",
		"Aaliyah", "Easton", "Kennedy", "Elias", "Kinsley", "Colton", "Allison", "Cameron", "Maya", "Carson",
		"Sarah", "Robert", "Madelyn", "Angel", "Adeline", "Maverick", "Alexa", "Nicholas", "Ariana", "Dominic",
		"Elena", "Jaxon", "Gabriella", "Greyson", "Naomi", "Adam", "Alice", "Ian", "Sadie", "Austin",
		"Sophie", "Santiago", "Hailey", "Jordan", "Eva", "Cooper", "Emilia", "Brayden", "Autumn", "Roman",
		"Quinn", "Evan", "Nevaeh", "Ezekiel", "Piper", "Xavier", "Ruby", "Jose", "Serenity", "Jace",
		"Willow", "Jameson", "Everly", "Leonardo", "Cora", "Bryson", "Kaylee", "Axel", "Lydia", "Everett",
		"Aubree", "Parker", "Arianna", "Kai", "Eliana", "Lincoln", "Peyton", "Silas", "Melanie", "Miles",
		"Gianna", "Bennett", "Isabelle", "Felix", "Julia", "Blake", "Valentina", "Ryder", "Nova", "Carlos",
		"Clara", "Declan", "Vivian", "Aiden", "Reagan", "Luca", "Mackenzie", "Knox", "Madeline", "Alan",
		"Brielle", "Kaden", "Delilah", "Holden", "Ivy", "Finn", "Josephine", "Antonio", "Liliana", "Wayne",
		"Iris", "Ashton", "Jade", "Timothy", "Maria", "Victor", "Emery", "Brody", "Ryleigh", "Zion",
		"Leilani", "Abel", "Kimberly", "Myles", "Jasmine", "Diego", "Adalynn", "Hayden", "Faith", "Jude",
		"Rose", "Francisco", "Kylie", "Jonah", "Alexandra", "Enzo", "Mary", "Tucker", "Margaret", "Ariel",
		"Lyla", "King", "Lilly", "Malachi", "Adalyn", "Emmanuel", "Raelynn", "Karter", "Paige", "Rhett",
		"Brooke", "Zayden", "Andrea", "Kaiden", "Megan", "Maximus", "Harmony", "Drew", "Annabelle", "Rowan",
		"Valeria", "Braxton", "Rachel", "Cole", "Alina", "George", "Cecilia", "Luis", "Nicole", "Archer",
		"Mariah", "Weston", "Daniela", "Dawson", "Brooklynn", "Zander", "Jordyn", "Jesus", "Esther", "Nathaniel",
		"Fiona", "Jayce", "Payton", "Aidan", "Keira", "Ford", "Cassandra", "Leon", "Aliyah", "Gael",
	}

	LastNames = []string{
		"Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller", "Davis", "Rodriguez", "Martinez",
		"Hernandez", "Lopez", "Gonzalez", "Wilson", "Anderson", "Thomas", "Taylor", "Moore", "Jackson", "Martin",
		"Lee", "Perez", "Thompson", "White", "Harris", "Sanchez", "Clark", "Ramirez", "Lewis", "Robinson",
		"Walker", "Young", "Allen", "King", "Wright", "Scott", "Torres", "Nguyen", "Hill", "Flores",
		"Green", "Adams", "Nelson", "Baker", "Hall", "Rivera", "Campbell", "Mitchell", "Carter", "Roberts",
		"Gomez", "Phillips", "Evans", "Turner", "Diaz", "Parker", "Cruz", "Edwards", "Collins", "Reyes",
		"Stewart", "Morris", "Morales", "Murphy", "Cook", "Rogers", "Gutierrez", "Ortiz", "Morgan", "Cooper",
		"Peterson", "Bailey", "Reed", "Kelly", "Howard", "Ramos", "Kim", "Cox", "Ward", "Richardson",
		"Watson", "Brooks", "Chavez", "Wood", "James", "Bennett", "Gray", "Mendoza", "Ruiz", "Hughes",
		"Price", "Alvarez", "Castillo", "Sanders", "Patel", "Myers", "Long", "Ross", "Foster", "Jimenez",
		"Powell", "Jenkins", "Perry", "Russell", "Sullivan", "Bell", "Coleman", "Butler", "Henderson", "Barnes",
		"Gonzales", "Fisher", "Vasquez", "Simmons", "Romero", "Jordan", "Patterson", "Alexander", "Hamilton", "Graham",
		"Reynolds", "Griffin", "Wallace", "Moreno", "West", "Cole", "Hayes", "Bryant", "Herrera", "Gibson",
		"Ellis", "Tran", "Medina", "Aguilar", "Stevens", "Murray", "Ford", "Castro", "Marshall", "Owens",
		"Harrison", "Fernandez", "Mcdonald", "Woods", "Washington", "Kennedy", "Wells", "Vargas", "Henry", "Chen",
		"Freeman", "Webb", "Tucker", "Guzman", "Burns", "Crawford", "Olson", "Simpson", "Porter", "Hunter",
		"Gordon", "Mendez", "Silva", "Shaw", "Snyder", "Mason", "Dixon", "Munoz", "Hunt", "Hicks",
		"Holmes", "Palmer", "Wagner", "Black", "Robertson", "Boyd", "Rose", "Stone", "Salazar", "Fox",
		"Warren", "Mills", "Meyer", "Rice", "Robertson", "Knight", "Lane", "Tan", "Harvey", "Robertson",
		"Daniels", "Ferguson", "Nichols", "Stephens", "Soto", "Weaver", "Ryan", "Gardner", "Payne", "Grant",
		"Dunn", "Kelley", "Spencer", "Hawkins", "Arnold", "Pierce", "Vazquez", "Hansen", "Peters", "Santos",
		"Hart", "Bradley", "Knight", "Elliott", "Cunningham", "Duncan", "Armstrong", "Hudson", "Carroll", "Lane",
		"Riley", "Andrews", "Alvarado", "Ray", "Delgado", "Berry", "Perkins", "Hoffman", "Johnston", "Matthews",
		"Pena", "Richards", "Contreras", "Willis", "Carpenter", "Lawrence", "Sandoval", "Guerrero", "George", "Chapman",
		"Rios", "Estrada", "Ortega", "Watkins", "Greene", "Nunez", "Wheeler", "Valdez", "Harper", "Burke",
		"Larson", "Santiago", "Maldonado", "Morrison", "Franklin", "Carlson", "Austin", "Dominguez", "Carr", "Lawson",
		"Jacobs", "Obrien", "Lynch", "Singh", "Vega", "Bishop", "Montgomery", "Oliver", "Jensen", "Harvey",
		"Williamson", "Gilbert", "Dean", "Sims", "Espinoza", "Howell", "Li", "Wong", "Reid", "Hanson",
		"Le", "Mccoy", "Garrett", "Burton", "Fuller", "Wang", "Weber", "Welch", "Rojas", "Lucas",
		"Marquez", "Fields", "Park", "Yang", "Little", "Banks", "Padilla", "Day", "Walsh", "Bowman",
	}
)

// GenerateRandomHumanName generates a random realistic human name
func GenerateRandomHumanName() string {
	firstName := FirstNames[rand.IntN(len(FirstNames))]
	lastName := LastNames[rand.IntN(len(LastNames))]
	return firstName + " " + lastName
}
