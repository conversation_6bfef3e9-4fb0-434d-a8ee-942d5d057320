package botutils

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Note: Tests that call GenerateRandomBotProfile are commented out because they require
// a properly initialized logger context which is not available in unit tests.
// These functions are tested through integration tests in the actual application.

func TestTryGenerateProfileImage_WithValidName(t *testing.T) {
	ctx := context.Background()
	userID := primitive.NewObjectID()
	name := "Aara<PERSON> Sharma"
	
	profileURL := tryGenerateProfileImage(ctx, userID, nil, name)
	
	// Should return initial-based fallback since storage client is nil
	expected := "https://storage.googleapis.com/matiks-go/A.png"
	assert.Equal(t, expected, profileURL)
}

func TestTryGenerateProfileImage_WithEmptyName(t *testing.T) {
	ctx := context.Background()
	userID := primitive.NewObjectID()
	name := ""
	
	profileURL := tryGenerateProfileImage(ctx, userID, nil, name)
	
	// Should return empty string for default avatar handling
	assert.Empty(t, profileURL)
}

func TestTryGenerateProfileImage_WithSingleCharacterName(t *testing.T) {
	ctx := context.Background()
	userID := primitive.NewObjectID()
	name := "A"
	
	profileURL := tryGenerateProfileImage(ctx, userID, nil, name)
	
	expected := "https://storage.googleapis.com/matiks-go/A.png"
	assert.Equal(t, expected, profileURL)
}

func TestTryGenerateProfileImage_WithSpecialCharacters(t *testing.T) {
	ctx := context.Background()
	userID := primitive.NewObjectID()
	
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Name with space",
			input:    " Aarav Sharma",
			expected: "https://storage.googleapis.com/matiks-go/ .png",
		},
		{
			name:     "Name with number",
			input:    "1Aarav",
			expected: "https://storage.googleapis.com/matiks-go/1.png",
		},
		{
			name:     "Name with special character",
			input:    "@Aarav",
			expected: "https://storage.googleapis.com/matiks-go/@.png",
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			profileURL := tryGenerateProfileImage(ctx, userID, nil, tt.input)
			assert.Equal(t, tt.expected, profileURL)
		})
	}
}

func TestGenerateRandomBotProfile_FallbackBehavior(t *testing.T) {
	ctx := context.Background()
	userID := primitive.NewObjectID()
	
	// Test multiple times to ensure consistent fallback behavior
	for i := 0; i < 10; i++ {
		name, profileImageURL, err := GenerateRandomBotProfile(ctx, userID, nil)
		
		assert.NoError(t, err, "Should never return error")
		assert.NotEmpty(t, name, "Should always generate a name")
		
		// If profileImageURL is not empty, it should be a valid fallback URL
		if profileImageURL != "" {
			assert.Contains(t, profileImageURL, "https://storage.googleapis.com/matiks-go/", 
				"Profile image URL should be a valid fallback URL")
			assert.Contains(t, profileImageURL, ".png", 
				"Profile image URL should end with .png")
		}
	}
}

func TestGenerateRandomBotProfileWithCollisionHandling_FallbackNames(t *testing.T) {
	ctx := context.Background()
	userID := primitive.NewObjectID()
	
	// Test high attempt numbers to trigger fallback names
	for attempt := 100; attempt < 110; attempt++ {
		name, _, err := GenerateRandomBotProfileWithCollisionHandling(ctx, userID, nil, attempt)
		
		assert.NoError(t, err, "Should not return error")
		assert.NotEmpty(t, name, "Should generate a name")
		
		// For high attempt numbers, might get fallback names
		if name == "Guest User 100" || name == "Guest User 101" {
			// This is expected fallback behavior
			assert.Contains(t, name, "Guest User", "Fallback name should contain 'Guest User'")
		}
	}
}

func TestGenerateRandomBotProfile_NameFormat(t *testing.T) {
	ctx := context.Background()
	userID := primitive.NewObjectID()
	
	for i := 0; i < 20; i++ {
		name, _, err := GenerateRandomBotProfile(ctx, userID, nil)
		
		assert.NoError(t, err)
		assert.NotEmpty(t, name)
		
		// Name should not start or end with whitespace
		assert.Equal(t, name, name, "Name should be trimmed")
		
		// Should not contain multiple consecutive spaces
		assert.NotContains(t, name, "  ", "Name should not have multiple consecutive spaces")
	}
}

func TestGenerateRandomBotProfileWithCollisionHandling_AttemptConsistency(t *testing.T) {
	ctx := context.Background()
	userID := primitive.NewObjectID()
	
	// Test that the same attempt number produces consistent patterns
	for attempt := 0; attempt < 4; attempt++ {
		names := make([]string, 5)
		for i := 0; i < 5; i++ {
			name, _, err := GenerateRandomBotProfileWithCollisionHandling(ctx, userID, nil, attempt)
			assert.NoError(t, err)
			names[i] = name
		}
		
		// All names for the same attempt should follow the same pattern
		// This is tested more thoroughly in humanNames_test.go
		for _, name := range names {
			assert.NotEmpty(t, name)
		}
	}
}

func BenchmarkGenerateRandomBotProfile(b *testing.B) {
	ctx := context.Background()
	userID := primitive.NewObjectID()
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		GenerateRandomBotProfile(ctx, userID, nil)
	}
}

func BenchmarkGenerateRandomBotProfileWithCollisionHandling(b *testing.B) {
	ctx := context.Background()
	userID := primitive.NewObjectID()
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		GenerateRandomBotProfileWithCollisionHandling(ctx, userID, nil, i%4)
	}
}

func BenchmarkTryGenerateProfileImage(b *testing.B) {
	ctx := context.Background()
	userID := primitive.NewObjectID()
	name := "Aarav Sharma"
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		tryGenerateProfileImage(ctx, userID, nil, name)
	}
}

// Test error resilience
func TestGenerateRandomBotProfile_ErrorResilience(t *testing.T) {
	ctx := context.Background()
	userID := primitive.NewObjectID()
	
	// Even with nil storage client and context, should not panic
	assert.NotPanics(t, func() {
		name, profileImageURL, err := GenerateRandomBotProfile(ctx, userID, nil)
		assert.NoError(t, err)
		assert.NotEmpty(t, name)
		// profileImageURL can be empty or fallback
		_ = profileImageURL // Suppress unused variable warning
	})
}

func TestTryGenerateProfileImage_ErrorResilience(t *testing.T) {
	ctx := context.Background()
	userID := primitive.NewObjectID()
	
	// Test with various edge case inputs
	testCases := []string{
		"",
		" ",
		"A",
		"Aarav Sharma",
		"Very Long Name With Many Parts And Spaces",
		"नाम", // Unicode characters
		"123",
		"@#$%",
	}
	
	for _, name := range testCases {
		assert.NotPanics(t, func() {
			profileURL := tryGenerateProfileImage(ctx, userID, nil, name)
			// Should either be empty or a valid URL
			if profileURL != "" {
				assert.Contains(t, profileURL, "https://", "Should be a valid URL if not empty")
			}
		}, "Should not panic for name: %s", name)
	}
}
