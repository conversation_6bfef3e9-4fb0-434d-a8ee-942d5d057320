package botutils

import (
	"context"
	"fmt"
	"math"
	"math/rand/v2"
	"time"

	"cloud.google.com/go/storage"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain"
	gameutils "matiksOfficial/matiks-server-go/internal/domain/game/utils"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
)

func GetRandomRatingForBot(userRating int) int {
	lowerRange := int(math.Min(math.Max(float64(userRating-50), 500), 4000))
	higherRange := int(math.Max(math.Min(float64(userRating+50), 4000), 500))
	if higherRange < lowerRange {
		return lowerRange
	}
	return rand.IntN(higherRange-lowerRange+1) + lowerRange
}

func GetRandomV2RatingForBot(userRating int) *models.UserRating {
	lowerRange := int(math.Min(math.Max(float64(userRating-50), 500), 4000))
	higherRange := int(math.Max(math.Min(float64(userRating+50), 4000), 500))
	if higherRange < lowerRange {
		return &models.UserRating{
			GlobalRating:       &lowerRange,
			FlashAnzanRating:   &lowerRange,
			AbilityDuelsRating: &lowerRange,
			PuzzleRating:       &lowerRange,
		}
	}
	randomRating := rand.IntN(higherRange-lowerRange+1) + lowerRange
	return &models.UserRating{
		GlobalRating:       &randomRating,
		FlashAnzanRating:   &randomRating,
		AbilityDuelsRating: &randomRating,
		PuzzleRating:       &randomRating,
	}
}

func GetBotUser(ctx context.Context, currentUser *models.User, userRepo repository.UserRepository, userService domain.UserStore, gameConfig models.GameConfigInterface, storageClient *storage.Client) (*models.User, error) {
	if currentUser == nil {
		return nil, fmt.Errorf("currentUser is nil")
	}
	if currentUser.Rating == nil {
		return nil, fmt.Errorf("currentUser.Rating is nil")
	}

	currentUserRating := gameutils.GetPlayerRatingByGameType(currentUser, gameConfig.GetGameType())

	var opponentIDs []primitive.ObjectID
	if currentUser.Stats != nil && currentUser.Stats.Last10BotGames != nil && len(currentUser.Stats.Last10BotGames) > 0 {
		numGames := min(5, len(currentUser.Stats.Last10BotGames))
		for i := 0; i < numGames; i++ {
			if currentUser.Stats.Last10BotGames[i].OpponentID != nil {
				opponentIDs = append(opponentIDs, *currentUser.Stats.Last10BotGames[i].OpponentID)
			}
		}
	}

	ratingField := gameutils.GetRatingFieldByGameType(gameConfig.GetGameType())
	// Find the bot user with the closest rating in the database
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{
			"isBot":     true,
			"_id":       bson.M{"$nin": opponentIDs},
			"ratingV2":  bson.M{"$exists": true},
			ratingField: bson.M{"$or": bson.A{bson.M{"$exists": true}, bson.M{"$ne": nil}}},
		}}},
		{{Key: "$addFields", Value: bson.M{
			"ratingDifference": bson.M{
				"$abs": bson.M{
					"$subtract": bson.A{bson.M{"$ifNull": []interface{}{"$" + ratingField, currentUserRating}}, currentUserRating},
				},
			},
		}}},
		{{Key: "$sort", Value: bson.M{"ratingDifference": 1}}},
		{{Key: "$limit", Value: 1}},
	}

	botUsers, err := userRepo.Aggregate(ctx, pipeline)
	if err == nil && len(botUsers) != 0 {
		botUser := botUsers[0]
		botRating := gameutils.GetPlayerRatingByGameType(botUser, gameConfig.GetGameType())
		if botUser != nil && math.Abs(float64(botRating-currentUserRating)) <= 100 {
			return botUser, nil
		}
	}
	// If no suitable bot user found, create a new one
	newBotUserID := primitive.NewObjectID()

	// Generate bot profile with robust collision handling
	botName, profileImageURL, username, err := generateBotUserProfile(ctx, newBotUserID, storageClient, userService)
	if err != nil {
		return nil, fmt.Errorf("failed to generate bot user profile: %w", err)
	}

	botRandomRatingV2 := GetRandomV2RatingForBot(currentUserRating)
	newBotUser := &models.User{
		ID:       newBotUserID,
		Name:     utils.AllocPtr(botName),
		Rating:   botRandomRatingV2.GlobalRating,
		RatingV2: botRandomRatingV2,
		Username: username,
		IsBot:    utils.AllocPtr(true),
		IsGuest:  utils.AllocPtr(true),
		CreatedAt: utils.AllocPtr(time.Now()),
		UpdatedAt: utils.AllocPtr(time.Now()),
	}

	// Set profile image URL only if it's not empty (graceful fallback)
	if profileImageURL != "" {
		newBotUser.ProfileImageURL = utils.AllocPtr(profileImageURL)
	}

	err = userRepo.Create(ctx, newBotUser)
	if err != nil {
		return nil, fmt.Errorf("failed to create bot user: %w", err)
	}

	return newBotUser, nil
}

// generateBotUserProfile generates a bot profile with robust collision handling and fallback mechanisms
func generateBotUserProfile(ctx context.Context, userID primitive.ObjectID, storageClient *storage.Client, userService domain.UserStore) (name, profileImageURL, username string, err error) {
	const maxAttempts = 10

	// Try to generate a unique username with collision handling
	for attempt := 0; attempt < maxAttempts; attempt++ {
		// Generate name and profile image with variation based on attempt
		if attempt == 0 {
			name, profileImageURL, err = GenerateRandomBotProfile(ctx, userID, storageClient)
		} else {
			name, profileImageURL, err = GenerateRandomBotProfileWithCollisionHandling(ctx, userID, storageClient, attempt)
		}

		if err != nil {
			zlog.Warn(ctx, "Failed to generate bot profile, trying next attempt",
				zap.Error(err), zap.Int("attempt", attempt))
			continue
		}

		// Try to generate username from the name
		username, err = userService.GenerateUserName(ctx, name)
		if err == nil {
			// Success! We have a unique username
			return name, profileImageURL, username, nil
		}

		zlog.Warn(ctx, "Failed to generate unique username, trying variation",
			zap.Error(err), zap.String("name", name), zap.Int("attempt", attempt))
	}

	// If all attempts failed, use fallback strategy with total user count
	return generateFallbackBotProfile(ctx, userID, storageClient, userService)
}

// generateFallbackBotProfile generates a fallback bot profile using guest${totalUserCount} pattern
func generateFallbackBotProfile(ctx context.Context, userID primitive.ObjectID, storageClient *storage.Client, userService domain.UserStore) (name, profileImageURL, username string, err error) {
	zlog.Warn(ctx, "Using fallback bot profile generation strategy")

	// Try to get total user count for fallback username
	if userRepo, ok := userService.(interface{ Count(context.Context, interface{}) (int64, error) }); ok {
		totalUsers, countErr := userRepo.Count(ctx, map[string]interface{}{})
		if countErr == nil {
			fallbackName := fmt.Sprintf("guest%d", totalUsers+1)
			username, err = userService.GenerateUserName(ctx, fallbackName)
			if err == nil {
				// Use a simple name for fallback
				name = "Guest User"
				profileImageURL = tryGenerateProfileImage(ctx, userID, storageClient, name)
				return name, profileImageURL, username, nil
			}
		}
	}

	// Ultimate fallback - use timestamp-based username
	timestamp := time.Now().Unix()
	fallbackName := fmt.Sprintf("guest%d", timestamp)
	username, err = userService.GenerateUserName(ctx, fallbackName)
	if err != nil {
		// If even this fails, use a simple incremental approach
		for i := 1; i <= 1000; i++ {
			fallbackName = fmt.Sprintf("guest%d", i)
			username, err = userService.GenerateUserName(ctx, fallbackName)
			if err == nil {
				break
			}
		}

		if err != nil {
			return "", "", "", fmt.Errorf("failed to generate fallback username after all attempts: %w", err)
		}
	}

	name = "Guest User"
	profileImageURL = tryGenerateProfileImage(ctx, userID, storageClient, name)
	return name, profileImageURL, username, nil
}

func isBot(ctx context.Context, opponentID *primitive.ObjectID, userService domain.UserStore) (bool, int) {
	if opponentID == nil {
		return false, constants.DefaultRating
	}
	botUser, err := userService.GetUserByID(ctx, *opponentID)
	if err != nil || botUser == nil {
		return false, constants.DefaultRating
	}
	return botUser.IsBot != nil && *botUser.IsBot, *botUser.Rating
}
