package gameutils

import (
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// CreateGameInstance creates a new game instance with the provided parameters.
// It initializes all required fields, generates a new ObjectID, and sets creation timestamps.
// Returns a pointer to the newly created Game struct.
func CreateGameInstance(
	creatorID primitive.ObjectID,
	gameCategory models.GameCategory,
	gameType models.GameType,
	gameMode models.GameMode,
	gameConfig *models.GameConfig,
	players []*models.Player,
) *models.Game {
	return &models.Game{
		ID:           utils.AllocPtr(primitive.NewObjectID()),
		Players:      players,
		GameCategory: gameCategory,
		GameType:     gameType,
		GameMode:     gameMode,
		Config:       gameConfig,
		GameStatus:   models.GameStatusCreated,
		CreatedBy:    creatorID,
		CreatedAt:    utils.AllocPtr(time.Now()),
		UpdatedAt:    utils.AllocPtr(time.Now()),
	}
}
