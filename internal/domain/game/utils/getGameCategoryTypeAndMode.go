package gameutils

import (
	"matiksOfficial/matiks-server-go/internal/models"
)

func GetGameCategoryTypeAndMode(gameConfig *models.GameConfig) (models.GameCategory, models.GameType, models.GameMode) {
	if gameConfig == nil {
		return models.GameCategoryBlitz, models.GameTypePlayOnline, models.GameModeOnlineSearch
	}
	var gameType models.GameType
	if gameConfig.GameTypeSpecificConfig != nil && gameConfig.GameTypeSpecificConfig.Type != "" {
		gameType = gameConfig.GameTypeSpecificConfig.Type
	} else {
		gameType = gameConfig.GameType
	}

	var gameMode models.GameMode
	if gameConfig.ModeSpecificConfig != nil && gameConfig.ModeSpecificConfig.Mode != "" {
		gameMode = gameConfig.ModeSpecificConfig.Mode
	} else {
		gameMode = GetGameModeFromGameType(gameType)
	}

	var gameCategory models.GameCategory
	if gameConfig.CategorySpecificConfig != nil && gameConfig.CategorySpecificConfig.Category != "" {
		gameCategory = gameConfig.CategorySpecificConfig.Category
	} else {
		gameCategory = GetGameCategoryFromGameType(gameType)
	}
	return gameCategory, gameType, gameMode
}
