package gameutils

import "matiksOfficial/matiks-server-go/internal/models"

func GetCategorySpecificConfigFromGameType(gameType models.GameType, gameDefaultConfig *models.DefaultGameConfig, puzzleConfig *models.PuzzleGameConfig) *models.GameCategorySpecificConfig {
	switch gameType {
	case models.GameTypeAbilityDuels:
		return &models.GameCategorySpecificConfig{
			Category:  models.GameCategoryClassical,
			Classical: gameDefaultConfig,
		}
	case models.GameTypePlayOnline:
		return &models.GameCategorySpecificConfig{
			Category: models.GameCategoryBlitz,
			Blitz:    gameDefaultConfig,
		}
	case models.GameTypePlayWithFriend:
		return &models.GameCategorySpecificConfig{
			Category: models.GameCategoryBlitz,
			Blitz:    gameDefaultConfig,
		}
	case models.GameTypeOnlineChallenge:
		return &models.GameCategorySpecificConfig{
			Category: models.GameCategoryBlitz,
			Blitz:    gameDefaultConfig,
		}
	case models.GameTypePractice:
		return &models.GameCategorySpecificConfig{
			Category: models.GameCategoryBlitz,
			Blitz:    gameDefaultConfig,
		}
	case models.GameTypeSumdayShowdown:
		return &models.GameCategorySpecificConfig{
			Category: models.GameCategoryBlitz,
			Blitz:    gameDefaultConfig,
		}
	case models.GameTypeGroupPlay:
		return &models.GameCategorySpecificConfig{
			Category: models.GameCategoryBlitz,
			Blitz:    gameDefaultConfig,
		}
	case models.GameTypeFastestFinger:
		return &models.GameCategorySpecificConfig{
			Category: models.GameCategoryBlitz,
			Blitz:    gameDefaultConfig,
		}
	case models.GameTypeDMAS:
		return &models.GameCategorySpecificConfig{
			Category: models.GameCategoryBlitz,
			Blitz:    gameDefaultConfig,
		}
	case models.GameTypeKenKen:
		return &models.GameCategorySpecificConfig{
			Category: models.GameCategoryPuzzle,
			Puzzle:   puzzleConfig,
		}
	case models.GameTypeCrossMath:
		return &models.GameCategorySpecificConfig{
			Category: models.GameCategoryPuzzle,
			Puzzle:   puzzleConfig,
		}
	case models.GameTypeFlashAnzan:
		return &models.GameCategorySpecificConfig{
			Category: models.GameCategoryMemory,
			Memory:   gameDefaultConfig,
		}
	case models.GameTypeDMASAbility:
		return &models.GameCategorySpecificConfig{
			Category:  models.GameCategoryClassical,
			Classical: gameDefaultConfig,
		}
	case models.GameTypeDMASTimeBank:
		return &models.GameCategorySpecificConfig{
			Category:  models.GameCategoryClassical,
			Classical: gameDefaultConfig,
		}
	default:
		return &models.GameCategorySpecificConfig{
			Category: models.GameCategoryBlitz,
			Blitz:    gameDefaultConfig,
		}
	}
}
