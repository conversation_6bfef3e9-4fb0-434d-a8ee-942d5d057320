package gameutils

import (
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
)

func getUserWithNewRatingAfterApplyingRatingChangeForBlitzGame(user *models.User, finalRating int) *models.User {
	if user.RatingV2 == nil {
		user.RatingV2 = &models.UserRating{
			GlobalRating: utils.AllocPtr(finalRating),
		}
	}

	if user.RatingV2.GlobalRating == nil {
		user.RatingV2.GlobalRating = utils.AllocPtr(finalRating)
	}
	user.Rating = utils.AllocPtr(finalRating)
	user.RatingV2.GlobalRating = utils.AllocPtr(finalRating)
	return user
}

func getUserWithNewRatingAfterApplyingRatingChangeForPuzzleGame(user *models.User, finalRating int) *models.User {
	if user.RatingV2 == nil {
		user.RatingV2 = &models.UserRating{
			PuzzleRating: utils.AllocPtr(finalRating),
		}
	}

	if user.RatingV2.PuzzleRating == nil {
		user.RatingV2.PuzzleRating = utils.AllocPtr(finalRating)
	}

	user.RatingV2.PuzzleRating = utils.AllocPtr(finalRating)

	return user
}

func getUserWithNewRatingAfterApplyingRatingChangeForMemoryGame(user *models.User, finalRating int) *models.User {
	if user.RatingV2 == nil {
		user.RatingV2 = &models.UserRating{
			FlashAnzanRating: utils.AllocPtr(finalRating),
		}
	}

	if user.RatingV2.FlashAnzanRating == nil {
		user.RatingV2.FlashAnzanRating = utils.AllocPtr(finalRating)
	}

	user.RatingV2.FlashAnzanRating = utils.AllocPtr(finalRating)

	return user
}

func getUserWithNewRatingAfterApplyingRatingChangeForClassicGame(user *models.User, finalRating int) *models.User {
	if user.RatingV2 == nil {
		user.RatingV2 = &models.UserRating{
			AbilityDuelsRating: utils.AllocPtr(finalRating),
		}
	}

	if user.RatingV2.AbilityDuelsRating == nil {
		user.RatingV2.AbilityDuelsRating = utils.AllocPtr(finalRating)
	}

	user.RatingV2.AbilityDuelsRating = utils.AllocPtr(finalRating)
	return user
}

func GetUserWithNewRatingAfterApplyingRatingChange(user *models.User, ratingChange int, gameType interface{}) *models.User {
	initialRating := GetPlayerRatingByGameType(user, gameType)
	finalRating := min(5000, max(100, initialRating+ratingChange))

	switch gameType {
	case models.GameTypeAbilityDuels, models.GameTypeDMASAbility, models.GameTypeDMASTimeBank:
		return getUserWithNewRatingAfterApplyingRatingChangeForClassicGame(user, finalRating)
	case models.GameTypeFlashAnzan:
		return getUserWithNewRatingAfterApplyingRatingChangeForMemoryGame(user, finalRating)
	case models.GameTypePlayOnline,
		models.GameTypePlayWithFriend,
		models.GameTypeOnlineChallenge,
		models.GameTypeSumdayShowdown,
		models.GameTypeFastestFinger,
		models.GameTypeDMAS,
		models.GameTypePractice:
		return getUserWithNewRatingAfterApplyingRatingChangeForBlitzGame(user, finalRating)
	case models.PuzzleGameTypeCrossMathPuzzleDuel, models.PuzzleGameTypeCrossMathPuzzleWithFriend, models.GameTypeCrossMath, models.GameTypeKenKen:
		return getUserWithNewRatingAfterApplyingRatingChangeForPuzzleGame(user, finalRating)
	default:
		return user
	}
}
