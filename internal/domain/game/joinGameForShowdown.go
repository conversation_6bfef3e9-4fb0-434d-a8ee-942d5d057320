package game

import (
	"context"
	"fmt"
	"sync"
	"time"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/scheduler"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
	"matiksOfficial/matiks-server-go/utils/slicesustils"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) joinGameForShowdown(ctx context.Context, gameId primitive.ObjectID, game *models.Game) (*models.Game, error) {
	if err := validatePayload(game); err != nil {
		zlog.Error(ctx, "Failed to validate payload", err, zap.String("gameID", gameId.Hex()))
		return nil, err
	}

	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		zlog.Error(ctx, "Failed to get user from context", err, zap.String("gameID", gameId.Hex()))
		return nil, fmt.Errorf("current user not found: %v", err)
	}
	currentPlayer, isFound := slicesustils.Find(game.Players, func(player *models.Player) bool {
		return player.UserID == userID
	})

	if !isFound || currentPlayer == nil {
		zlog.Error(ctx, "Current user not found in the game", nil, zap.String("gameID", gameId.Hex()))
		return nil, fmt.Errorf("current user not found in the game")
	}

	if currentPlayer.Status == models.PlayerStatusAccepted && !checkIfAllPlayersJoined(game.Players) {
		zlog.Debug(ctx, "Current user already joined the game", zap.String("gameID", gameId.Hex()))
		return game, nil
	}

	var showdown *models.Showdown
	showdown, err = s.showdownCache.GetShowdown(ctx, *game.ShowdownId)
	if err != nil {
		zlog.Error(ctx, "Failed to get showdown Details", err, zap.String("gameID", gameId.Hex()))
		return nil, fmt.Errorf("failed to get showdown Details")
	}

	mtx, _ := s.mx.LoadOrStore(game.ID.Hex(), &sync.Mutex{})
	mutex := mtx.(*sync.Mutex)
	mutex.Lock()
	defer (func() {
		mutex.Unlock()
		s.mx.Delete(game.ID.Hex())
	})()

	game, err = s.GetGameByID(ctx, &gameId)
	if err != nil || game == nil {
		zlog.Error(ctx, "Game not found", nil, zap.String("gameID", gameId.Hex()))
		return nil, fmt.Errorf("game not found")
	}

	isAllPlayersJoined := checkIfAllPlayersJoined(game.Players)
	if isAllPlayersJoined {
		if game.GameStatus != models.GameStatusStarted {
			go func(ctx context.Context, gameId primitive.ObjectID, userID primitive.ObjectID) {
				s.StartGameForShowdown(utils.DeriveContextWithoutCancel(ctx), userID, gameId)
			}(ctx, gameId, userID)
		}
		return game, nil
	}

	updatedPlayers := updatePlayerStatus(game.Players, userID, models.PlayerStatusAccepted)
	game.Players = updatedPlayers

	showdownPlayer, isFound := slicesustils.Find(game.ShowdownGameConfig.ShowdownGamePlayer, func(player *models.ShowdownGamePlayer) bool {
		return player.UserID == userID
	})

	if !isFound || showdownPlayer == nil {
		zlog.Error(ctx, "Showdown player not found", nil, zap.String("gameID", gameId.Hex()))
		return nil, fmt.Errorf("showdown player not found")
	}

	showdownParticipant, err := s.showdownService.GetShowdownParticipant(ctx, *game.ShowdownId, showdownPlayer.ParticipantID)
	if err != nil {
		zlog.Error(ctx, "Failed to get showdown participant", err, zap.String("gameID", gameId.Hex()))
		return nil, err
	}

	if showdownParticipant.Rounds == nil || len(showdownParticipant.Rounds) == 0 {
		return nil, fmt.Errorf("rounds are nil or empty")
	}

	for i, roundData := range showdownParticipant.Rounds {
		if roundData.Round == game.ShowdownGameConfig.Round {
			roundData.HasJoined = true
			showdownParticipant.Rounds[i] = roundData
		}
	}
	err = s.showdownService.UpdateShowdownParticipant(ctx, showdownParticipant, game.ShowdownGameConfig.Round)
	if err != nil {
		return nil, err
	}

	action := fmt.Sprintf(`JoinGame:%s:%s`, (*game.ShowdownId).Hex(), gameId.Hex())
	isAllPlayersJoined = checkIfAllPlayersJoined(updatedPlayers)
	if !isAllPlayersJoined {
		maxGameStartTime := showdown.StartTime.Add(time.Duration((showdown.RoundTime+showdown.GapBwRounds)*(game.ShowdownGameConfig.Round-1)+showdown.RoundConfig.MaxWaitTime) * time.Second)
		if err := scheduler.Schedule(ctx, s.sortedSet, &models.SchedulerPayload{
			Type:       models.JoinGameShowdownCheck,
			Action:     action,
			ContextMap: utils.GetContextValuesMap(ctx),
		}, maxGameStartTime); err != nil {
			zlog.Error(ctx, "Failed to schedule join game showdown check", err, zap.String("gameID", gameId.Hex()))
			return nil, err
		}
	} else {
		if err = scheduler.CancelScheduledTask(ctx, s.sortedSet, action); err != nil {
			zlog.Error(ctx, "Failed to cancel scheduled task", err, zap.String("gameID", gameId.Hex()))
			return nil, err
		}
	}

	err = s.publishGameEvent(ctx, game, constants.GameEventEnum.USER_JOINED.String())
	if err != nil {
		zlog.Error(ctx, "Failed to publish game event", err, zap.String("gameID", gameId.Hex()))
		return nil, err
	}

	err = s.gameCache.SetGame(ctx, game)
	if err != nil {
		zlog.Error(ctx, "Failed to set game in cache", err, zap.String("gameID", gameId.Hex()))
		return nil, err
	}

	ctx = context.WithValue(ctx, constants.GameIDKey, gameId.Hex())
	if len(game.Players) != 2 || !isAllPlayersJoined {
		return game, nil
	}
	go func(ctx context.Context, gameId primitive.ObjectID, userID primitive.ObjectID) {
		s.StartGameForShowdown(utils.DeriveContextWithoutCancel(ctx), userID, gameId)
	}(ctx, gameId, userID)

	return game, nil
}

func validatePayload(game *models.Game) error {
	if game == nil {
		return fmt.Errorf("game is nil")
	}
	if game.Config == nil || game.Config.NumPlayers == nil {
		return fmt.Errorf("invalid game config")
	}

	if game.ShowdownGameConfig == nil {
		return fmt.Errorf("showdown game config is nil")
	}

	if game.ShowdownGameConfig.ShowdownGamePlayer == nil || len(game.ShowdownGameConfig.ShowdownGamePlayer) == 0 {
		return fmt.Errorf("showdown game player is nil")
	}

	return nil
}

func checkIfAllPlayersJoined(players []*models.Player) bool {
	isAllPlayersJoined := slicesustils.Reduce(players, true, func(acc bool, player *models.Player) bool {
		return acc && player != nil && player.Status == models.PlayerStatusAccepted
	})
	return isAllPlayersJoined
}

func (s *service) CheckIfOpponentNotJoinedForShowdown(ctx context.Context, showdownId, gameId primitive.ObjectID) error {
	game, err := s.GetGameByID(ctx, &gameId)
	if err != nil {
		zlog.Error(ctx, "Failed to get game", err, zap.String("gameID", gameId.Hex()))
		return err
	}
	if game.ShowdownGameConfig == nil || game.ShowdownGameConfig.ShowdownGamePlayer == nil || len(game.ShowdownGameConfig.ShowdownGamePlayer) == 0 {
		zlog.Error(ctx, "Showdown game config not found", nil, zap.String("gameID", gameId.Hex()))
		return fmt.Errorf("game config is nil")
	}
	if checkIfAllPlayersJoined(game.Players) {
		return nil
	}

	currentRound := game.ShowdownGameConfig.Round

	for _, showdownPlayer := range game.ShowdownGameConfig.ShowdownGamePlayer {
		isJoined := false
		player, isFound := slicesustils.Find(game.Players, func(player *models.Player) bool {
			return player.UserID == showdownPlayer.UserID
		})
		if !isFound || player == nil {
			return fmt.Errorf("player not found")
		}
		if player.Status == models.PlayerStatusAccepted {
			isJoined = true
		}
		err := s.updateShowdownGamePlayersV2(ctx, currentRound, game, showdownId, showdownPlayer.ParticipantID, isJoined, game.ShowdownGameConfig)
		if err != nil {
			return err
		}
	}
	return nil
}

func (s *service) updateShowdownGamePlayersV2(ctx context.Context, currentRound int, game *models.Game, showdownId, participantId primitive.ObjectID, isJoined bool, showdownGameConfig *models.ShowdownGameConfig) error {
	participant, err := s.showdownService.GetShowdownParticipant(ctx, showdownId, participantId)
	if err != nil {
		return err
	}
	if participant == nil {
		return fmt.Errorf("showdown participant not found")
	}

	roundIndex := slicesustils.FindIndex(participant.Rounds, func(roundData *models.ShowdownRound) bool {
		return roundData.Round == currentRound
	})
	roundData := models.ShowdownRound{}
	if roundIndex == -1 {
		return fmt.Errorf("round not found")
	}

	if participant.Rounds[roundIndex] == nil {
		return fmt.Errorf("round not found")
	}

	roundData = *participant.Rounds[roundIndex]

	if isJoined {
		roundData.Score = 1
		roundData.PlayerStatus = models.RoundPlayerStatusOpponentAbsent
		participant.TotalScore += 1
	} else {
		roundData.PlayerStatus = models.RoundPlayerStatusDidNotPlay
	}
	participant.Rounds[roundIndex] = &roundData

	err = s.showdownService.UpdateShowdownParticipant(ctx, participant, currentRound)
	if err != nil {
		return err
	}

	return nil
}
