package game

import (
	"context"
	"fmt"
	"math/rand/v2"
	"time"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/game/utils/botutils"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) RequestRematch(ctx context.Context, gameID primitive.ObjectID) (bool, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return false, err
	}

	zlog.Debug(ctx, "Requesting rematch", zap.String("userID", userID.Hex()), zap.String("gameID", gameID.Hex()))

	game, err := s.GetGameByID(ctx, &gameID)
	if err != nil {
		return false, fmt.Errorf("game not found")
	}

	if game.GameStatus != models.GameStatusEnded {
		return false, fmt.Errorf("rematch can only be requested for ended games")
	}

	if game.RematchRequestedBy != nil {
		return false, fmt.Errorf("rematch already requested for this game")
	}

	isPlayerInGame := false
	for _, player := range game.Players {
		if player.UserID == userID {
			isPlayerInGame = true
			break
		}
	}

	if !isPlayerInGame {
		return false, fmt.Errorf("only players in the game can request a rematch")
	}

	game.RematchRequestedBy = &userID

	err = s.gameRepo.UpdateOne(ctx, bson.M{"_id": gameID}, bson.M{"$set": bson.M{"rematchRequestedBy": userID}})
	if err != nil {
		return false, err
	}

	err = s.gameCache.SetGame(ctx, game)
	if err != nil {
		return false, err
	}

	err = s.publishRematchEventToBothUsers(ctx, game, userID, constants.RematchGameEnum.REMATCH_REQUESTED.String(), nil)
	if err != nil {
		return false, err
	}

	go s.acceptRematchIfBot(utils.DeriveContextWithoutCancel(ctx), game, userID)
	go s.autoCloseRematchRequest(utils.DeriveContextWithoutCancel(ctx), gameID, userID)

	zlog.Info(ctx, "Rematch requested", zap.String("gameID", gameID.Hex()), zap.String("requestedBy", userID.Hex()))
	return true, nil
}

func (s *service) autoCloseRematchRequest(ctx context.Context, gameID, requestedByUserID primitive.ObjectID) {
	timer := time.NewTimer(constants.AutoCloseRematchRequestDuration * time.Second)
	defer timer.Stop()

	select {
	case <-timer.C:
		game, err := s.GetGameByID(ctx, &gameID)
		if err != nil {
			zlog.Info(ctx, "Error finding game for auto-close", zap.Error(err))
			return
		}

		if game.RematchRequestedBy != nil {
			game.RematchRequestedBy = nil
			update := bson.M{
				"$unset": bson.M{
					"rematchRequestedBy": 1,
				},
			}

			filter := bson.M{"_id": gameID}
			err = s.gameRepo.UpdateOne(ctx, filter, update)
			if err != nil {
				zlog.Info(ctx, "Error deleting RematchRequestedBy field after auto-closing rematch request", zap.Error(err))
				return
			}

			err = s.gameCache.SetGame(ctx, game)
			if err != nil {
				zlog.Info(ctx, "Error caching game after auto-close", zap.Error(err))
				return
			}

			err = s.publishRematchEventToBothUsers(ctx, game, requestedByUserID, constants.RematchGameEnum.REMATCH_AUTO_CLOSED.String(), nil)
			if err != nil {
				zlog.Info(ctx, "Error publishing auto-close event", zap.Error(err))
			}
		}
	case <-ctx.Done():
		return
	}
}

func (s *service) acceptRematchIfBot(ctx context.Context, game *models.Game, userID primitive.ObjectID) {
	gameID := *game.ID
	opponentID := s.GetOpponentID(game.Players, userID)
	user, err := s.userService.GetUserByID(ctx, userID)
	if err != nil {
		zlog.Error(ctx, "Failed to get user details", err)
		return
	}
	opponent, err := s.userService.GetUserByID(ctx, opponentID)
	if err != nil {
		zlog.Error(ctx, "Failed to get opponent user details", err)
		return
	}

	if (opponent.IsBot == nil && opponent.IsHumanBot == nil) ||
		(opponent.IsBot == nil && opponent.IsHumanBot != nil && !*opponent.IsHumanBot) ||
		(opponent.IsBot != nil && !*opponent.IsBot && opponent.IsHumanBot == nil) ||
		(opponent.IsBot != nil && !*opponent.IsBot && opponent.IsHumanBot != nil && !*opponent.IsHumanBot) {
		return
	}

	if opponent.HumanBotConfig != nil && opponent.HumanBotConfig.InGame != nil && *opponent.HumanBotConfig.InGame {
		return
	}

	probAcceptance := rand.Float64()
	if probAcceptance < 0.3 {
		return
	}

	randT := 1.5 + 3*rand.Float64()
	timer := time.NewTimer(time.Duration(randT) * time.Second)
	defer timer.Stop()

	select {
	case <-timer.C:
		zlog.Info(ctx, "Accepting rematch if bot", zap.String("gameID", gameID.Hex()), zap.String("userID", userID.Hex()))
		s.botRematch(ctx, game, user, opponent)
	case <-ctx.Done():
		zlog.Info(ctx, "Accepting rematch if bot context cancelled", zap.String("gameID", gameID.Hex()), zap.String("userID", userID.Hex()))
	}
}

func (s *service) botRematch(ctx context.Context, game *models.Game, user, botUser *models.User) {
	gameID := *game.ID
	var seriesID *primitive.ObjectID
	if game.SeriesID != nil {
		seriesID = game.SeriesID
	} else {
		// Create a new game series
		newSeries := &models.GameSeries{
			ID:        utils.AllocPtr(primitive.NewObjectID()),
			GameIds:   &[]primitive.ObjectID{gameID},
			PlayerIds: &[]primitive.ObjectID{user.ID, botUser.ID},
		}
		_, err := s.gameSeriesRepo.CreateGameSeries(ctx, newSeries)
		if err != nil {
			return
		}
		seriesID = newSeries.ID
	}

	config := models.GameConfig{
		TimeLimit:  game.Config.TimeLimit,
		NumPlayers: game.Config.NumPlayers,
		GameType:   game.Config.GameType,
	}

	user, err := s.userService.GetUserByID(ctx, user.ID)
	if err != nil {
		zlog.Error(ctx, "Failed to get user details", err)
	}
	botUser, err = s.userService.GetUserByID(ctx, botUser.ID)
	if err != nil {
		zlog.Error(ctx, "Failed to get bot user details", err)
	}

	updatedUsers := []*models.User{user, botUser}
	updatedPlayers := s.getPlayersFromUsers(updatedUsers, game.GameType)

	newGame, err := s.createGameWithPlayers(ctx, updatedPlayers, &config)
	if err != nil {
		return
	}

	// Update the game series document
	seriesUpdate := bson.M{
		"$addToSet": bson.M{
			"gameIds": newGame.ID,
		},
	}
	_, err = s.gameSeriesRepo.Collection().UpdateOne(ctx, bson.M{"_id": seriesID}, seriesUpdate)
	if err != nil {
		return
	}

	update := bson.M{
		"$unset": bson.M{
			"rematchRequestedBy": 1,
		},
		"$set": bson.M{"seriesId": seriesID},
	}

	filter := bson.M{"_id": gameID}
	err = s.gameRepo.UpdateOne(ctx, filter, update)
	if err != nil {
		zlog.Error(ctx, "Error deleting RematchRequestedBy field after auto-closing rematch request", err)
		return
	}
	rematchRequestedBy := *game.RematchRequestedBy
	game.RematchRequestedBy = nil

	err = s.gameCache.SetGame(ctx, game)
	if err != nil {
		return
	}

	newGameID := newGame.ID.Hex()

	// update game cache and set seriesId of game
	newGame.SeriesID = seriesID
	err = s.gameCache.SetGame(ctx, newGame)
	if err != nil {
		return
	}

	err = s.publishRematchEventToBothUsers(ctx, game, rematchRequestedBy, constants.RematchGameEnum.REMATCH_ACCEPTED.String(), &newGameID)
	if err != nil {
		return
	}

	go func() {
		err := botutils.RunBot(utils.DeriveContextWithoutCancel(ctx), newGame, botUser, s, s.cache, nil)
		if err != nil {
			zlog.Error(ctx, "Error running bot after auto-close rematch request", err)
		}
	}()
}
