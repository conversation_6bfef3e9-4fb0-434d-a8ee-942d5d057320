package game

import (
	"context"
	"fmt"
	"time"

	gameutils "matiksOfficial/matiks-server-go/internal/domain/game/utils"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) GetPracticeGame(ctx context.Context, gameConfigInput *models.GameConfigInput) (*models.Game, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	zlog.Debug(ctx, "GetPracticeGame", zap.String("userID", userID.Hex()))

	if gameConfigInput == nil {
		return nil, fmt.Errorf("invalid game configuration")
	}

	if err := s.validateGameConfig(gameConfigInput); err != nil {
		return nil, fmt.Errorf("invalid game configuration: %w", err)
	}

	zlog.Info(ctx, "GetPracticeGame", zap.Any("gameConfigInput", gameConfigInput))

	user, err := s.userService.GetUserByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to gt user details %w", err)
	}

	players := []*models.Player{
		{
			UserID:      userID,
			Status:      models.PlayerStatusAccepted,
			Rating:      user.Rating,
			StatikCoins: user.StatikCoins,
		},
	}

	gameConfig, _, err := gameutils.GetNewGameConfigFields(gameConfigInput)
	if err != nil || gameConfig == nil {
		return nil, fmt.Errorf("failed to get new game config fields: %w", err)
	}

	gameCategory, gameType, gameMode := gameutils.GetGameCategoryTypeAndMode(gameConfig)

	gameID := primitive.NewObjectID()

	leaderBoard := make([]*models.LeaderBoardEntry, len(players))
	for i, player := range players {
		leaderBoardEntry := gameutils.GetDefaultUserLeaderBoardStand(player.UserID)
		leaderBoard[i] = &leaderBoardEntry
	}

	timeAfter5sec := time.Now().Add(5 * time.Second)

	game := &models.Game{
		ID:           &gameID,
		CreatedBy:    userID,
		Config:       gameConfig,
		GameCategory: gameCategory,
		GameMode:     gameMode,
		GameType:     gameType,
		GameStatus:   models.GameStatusStarted,
		Players:      players,
		LeaderBoard:  leaderBoard,
		StartTime:    &timeAfter5sec,
	}

	generatedQuestions, err := gameutils.GetQuestionsForGame(ctx, game)
	if err != nil {
		return nil, err
	}
	game.Questions = generatedQuestions

	err = s.addEncryptedQuestionsInGame(game)
	if err != nil {
		return nil, err
	}

	err = s.gameCache.SetGame(ctx, game)
	if err != nil {
		zlog.Error(ctx, "Error caching game", err, zap.String("gameID", gameID.Hex()))
		return nil, err
	}

	return game, nil
}
