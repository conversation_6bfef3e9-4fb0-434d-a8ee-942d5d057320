package game

import (
	"context"
	"fmt"

	gameutils "matiksOfficial/matiks-server-go/internal/domain/game/utils"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) JoinGame(ctx context.Context, joinGameInput *models.JoinGameInput) (*models.Game, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		zlog.Error(ctx, "Error retrieving active games:", err)
		return nil, err
	}

	zlog.Debug(ctx, "Joining game", zap.String("gameID", joinGameInput.GameID.Hex()))

	gameId := joinGameInput.GameID

	zlog.Debug(ctx, "JoinGame", zap.String("userID", userID.Hex()), zap.String("gameID", gameId.Hex()))

	game, err := s.GetGameByID(ctx, &gameId)
	if err != nil || game == nil {
		return nil, fmt.Errorf("game not found")
	}

	zlog.Info(ctx, "JoinGame", zap.String("userID", userID.Hex()), zap.String("gameID", game.ID.Hex()))
	if game.Config == nil || game.Config.NumPlayers == nil {
		return nil, fmt.Errorf("invalid game config")
	}

	if game.GameType == models.GameTypeSumdayShowdown {
		return s.joinGameForShowdown(ctx, gameId, game)
	}

	numPlayers := game.Config.NumPlayers
	acceptedPlayers := filterAcceptedPlayers(game.Players)

	zlog.Info(ctx, "JoinGame Resolver", zap.Any("acceptedPlayers", acceptedPlayers))
	currentUser, err := s.userService.GetUserByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("current user not found: %v", err)
	}

	rating := gameutils.GetPlayerRatingByGameType(currentUser, game.GameType)

	currPlayer := findPlayerByID(game.Players, userID)

	if currPlayer == nil && len(acceptedPlayers) >= *numPlayers {
		zlog.Error(ctx, "JoinGame: ", fmt.Errorf("game is already full"))
		return nil, fmt.Errorf("game is already full")
	}
	if currPlayer != nil && currPlayer.Status == models.PlayerStatusAccepted {
		zlog.Error(ctx, "JoinGame: ", fmt.Errorf("player already joined this game"))
		return game, nil
	}

	var updatedPlayers []*models.Player

	if currPlayer != nil {
		updatedPlayers = updatePlayerStatus(game.Players, userID, models.PlayerStatusAccepted)
	} else {
		player := models.Player{
			UserID:      userID,
			Status:      models.PlayerStatusAccepted,
			Rating:      &rating,
			StatikCoins: currentUser.StatikCoins,
			TimeLeft:    game.Config.TimeLimit,
		}
		updatedPlayers = append(game.Players, &player)
	}

	game.Players = updatedPlayers

	if len(updatedPlayers) >= *numPlayers && game.GameType != models.GameTypeSumdayShowdown {
		game.GameStatus = models.GameStatusReady
	}

	err = s.publishGameEvent(ctx, game, constants.GameEventEnum.USER_JOINED.String())
	if err != nil {
		return nil, err
	}

	err = s.gameCache.SetGame(ctx, game)
	if err != nil {
		return nil, err
	}

	return game, nil
}

// TODO @Rishav
// func (s *service) CheckIfOpponentNotJoinedForShowdown(ctx context.Context, showdownId primitive.ObjectID, gameId primitive.ObjectID) error {
// game, err := s.GetGameByID(ctx, &gameId)
// if err != nil {
// 	return err
// }
// // check if all players have already joined
// if checkPlayersStatus(game.Players) {
// 	return nil
// }
// if game.ShowdownGameConfig == nil || game.ShowdownGameConfig.ShowdownGamePlayer == nil || len(game.ShowdownGameConfig.ShowdownGamePlayer) == 0 {
// 	return fmt.Errorf("game config is nil")
// }
// for _, player := range game.ShowdownGameConfig.ShowdownGamePlayer {
// 	statusJoined := true
// 	if player.UserID != game.Players[0].UserID {
// 		statusJoined = false
// 	}

// 	err := s.updateShowdownGamePlayers(ctx, game, showdownId, player.UserID, statusJoined, game.ShowdownGameConfig)
// 	if err != nil {
// 		return err
// 	}
// }
// 	return nil
// }

func filterAcceptedPlayers(players []*models.Player) []models.Player {
	var accepted []models.Player
	for _, player := range players {
		if player.Status == models.PlayerStatusAccepted {
			accepted = append(accepted, *player)
		}
	}
	return accepted
}

func findPlayerByID(players []*models.Player, userID primitive.ObjectID) *models.Player {
	for _, player := range players {
		if player.UserID == userID {
			return player
		}
	}
	return nil
}

func updatePlayerStatus(players []*models.Player, userID primitive.ObjectID, status models.PlayerStatus) []*models.Player {
	for i, player := range players {
		if player.UserID == userID {
			players[i].Status = status
			return players
		}
	}
	return players
}

func checkPlayersStatus(players []*models.Player) bool {
	hasAllPlayerActive := true
	for _, player := range players {
		if player.Status == models.PlayerStatusInvited {
			hasAllPlayerActive = false
		}
	}
	return hasAllPlayerActive
}
