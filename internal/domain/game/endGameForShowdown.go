package game

import (
	"context"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/constants"
	gameutils "matiksOfficial/matiks-server-go/internal/domain/game/utils"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
	"matiksOfficial/matiks-server-go/utils/slicesustils"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) EndGameForShowdown(ctx context.Context, gameID *primitive.ObjectID) (*models.Game, error) {
	game, showdown, err := s.getGameAndShowdownPayload(ctx, gameID)
	if err != nil {
		return nil, err
	}

	if game == nil || showdown == nil {
		return nil, fmt.Errorf("game or showdown is nil")
	}

	showdownId := *game.ShowdownId
	game.GameStatus = models.GameStatusEnded
	err = s.gameCache.SetGame(ctx, game)
	if err != nil {
		zlog.Error(ctx, "Failed to set game in cache", err, zap.String("gameID", gameID.Hex()), zap.String("showdownId", showdownId.Hex()))
		return nil, err
	}
	sortedLeaderboard := gameutils.UpdateLeaderboardRanks(game)

	if len(sortedLeaderboard) < 2 {
		zlog.Error(ctx, "Not enough players to end game", nil, zap.String("gameID", gameID.Hex()), zap.String("showdownId", showdownId.Hex()))
		return nil, fmt.Errorf("not enough players")
	}

	isTie := *sortedLeaderboard[0].TotalPoints == *sortedLeaderboard[1].TotalPoints

	winnerID := sortedLeaderboard[0].UserID
	loserID := sortedLeaderboard[1].UserID

	if winnerID == nil || loserID == nil {
		zlog.Error(ctx, "Invalid player IDs", nil, zap.String("gameID", gameID.Hex()), zap.String("showdownId", showdownId.Hex()))
		return nil, fmt.Errorf("invalid player IDs")
	}
	players := models.Players{}
	players = append(players, winnerID, loserID)

	game.LeaderBoard = sortedLeaderboard
	currentRound := game.ShowdownGameConfig.Round

	winnerUser, err := s.coreService.GetUserByID(ctx, *winnerID)
	if err != nil {
		zlog.Error(ctx, "Failed to get winner user", err, zap.String("gameID", gameID.Hex()), zap.String("showdownId", showdownId.Hex()))
		return nil, err
	}

	loserUser, err := s.coreService.GetUserByID(ctx, *loserID)
	if err != nil {
		zlog.Error(ctx, "Failed to get loser user", err, zap.String("gameID", gameID.Hex()), zap.String("showdownId", showdownId.Hex()))
		return nil, err
	}

	winnerShowdownParticipant, winnerParticipantIndex, err := s.getShowdownParticipantFromGame(ctx, game, *winnerID)
	if err != nil {
		zlog.Error(ctx, "Failed to get winner", err, zap.String("gameID", gameID.Hex()), zap.String("showdownId", showdownId.Hex()))
		return nil, err
	}
	winnerRoundData, winnerRoundIndex, err := getCurrentRoundDataAndIndex(winnerShowdownParticipant, currentRound)
	if err != nil {
		zlog.Error(ctx, "Failed to get winner round data", err, zap.String("gameID", gameID.Hex()), zap.String("showdownId", showdownId.Hex()))
		return nil, err
	}

	loserShowdownParticipant, loserParticipantIndex, err := s.getShowdownParticipantFromGame(ctx, game, *loserID)
	if err != nil {
		zlog.Error(ctx, "Failed to get loser", err, zap.String("gameID", gameID.Hex()), zap.String("showdownId", showdownId.Hex()))
		return nil, err
	}
	loserRoundData, loserRoundIndex, err := getCurrentRoundDataAndIndex(loserShowdownParticipant, currentRound)
	if err != nil {
		zlog.Error(ctx, "Failed to get loser round data", err, zap.String("gameID", gameID.Hex()), zap.String("showdownId", showdownId.Hex()))
		return nil, err
	}

	showdownGameWinner := game.ShowdownGameConfig.ShowdownGamePlayer[winnerParticipantIndex]
	showdownGameLoser := game.ShowdownGameConfig.ShowdownGamePlayer[loserParticipantIndex]

	winnerRoundData.TotalGamesPlayed++
	loserRoundData.TotalGamesPlayed++
	if isTie {
		winnerRoundData.Wins += 0.5
		loserRoundData.Wins += 0.5

		showdownGameWinner.IsTie, showdownGameLoser.IsTie = true, true
	} else {
		winnerRoundData.Wins += 1
		showdownGameWinner.IsWinner = true
	}

	showdownGameWinner.Wins, showdownGameLoser.Wins = winnerRoundData.Wins, loserRoundData.Wins
	winsOfWinner := showdownGameWinner.Wins
	if showdownGameLoser.Wins > showdownGameWinner.Wins {
		winsOfWinner = showdownGameLoser.Wins
	}

	isRoundEnded := false

	if (winnerRoundData.TotalGamesPlayed == showdown.RoundConfig.NumOfGames) || (winsOfWinner > (float64(showdown.RoundConfig.NumOfGames) / 2.0)) {
		isRoundEnded = true
		winnerRoundData.PlayerStatus, loserRoundData.PlayerStatus = models.RoundPlayerStatusRoundCompleted, models.RoundPlayerStatusRoundCompleted
		if showdownGameWinner.Wins > showdownGameLoser.Wins {
			winnerRoundData.Score = 1
			winnerShowdownParticipant.TotalScore += 1
		} else if showdownGameWinner.Wins == showdownGameLoser.Wins {
			winnerRoundData.Score, loserRoundData.Score = 0.5, 0.5
			winnerShowdownParticipant.TotalScore += 0.5
			loserShowdownParticipant.TotalScore += 0.5
		} else {
			loserRoundData.Score = 1
			loserShowdownParticipant.TotalScore += 1
		}
	}

	ratingChangeInfo, err := s.calculateRatingChanges(ctx, game, sortedLeaderboard, winnerUser, loserUser)
	if err != nil {
		zlog.Error(ctx, "Failed to get rating change", err, zap.String("gameID", gameID.Hex()), zap.String("showdownId", showdownId.Hex()))
		return nil, err
	}
	if ratingChangeInfo == nil {
		zlog.Error(ctx, "Rating change info is nil", nil, zap.String("gameID", gameID.Hex()), zap.String("showdownId", showdownId.Hex()))
		return nil, fmt.Errorf("rating change info is nil")
	}

	if showdown.IsRatedEvent == nil {
		showdown.IsRatedEvent = utils.AllocPtr(false)
	}

	if *showdown.IsRatedEvent {
		winnerShowdownParticipant.RatingChange += ratingChangeInfo.WinnerRatingChange
		loserShowdownParticipant.RatingChange += ratingChangeInfo.LoserRatingChange

		winnerOldRating, loserOldRating := *winnerUser.Rating, *loserUser.Rating
		gameutils.GetUserWithNewRatingAfterApplyingRatingChange(winnerUser, ratingChangeInfo.WinnerRatingChange, game.GameType)
		gameutils.GetUserWithNewRatingAfterApplyingRatingChange(loserUser, ratingChangeInfo.LoserRatingChange, game.GameType)
		winnerNewRating, loserNewRating := *winnerUser.Rating, *loserUser.Rating

		gameutils.UpdateBadge(winnerUser, winnerOldRating, winnerNewRating)
		gameutils.UpdateBadge(loserUser, loserOldRating, loserNewRating)

	}

	if err := s.userService.UpdateUserFromObject(ctx, winnerUser); err != nil {
		return nil, fmt.Errorf("failed to update winner: %w", err)
	}

	if err := s.userService.UpdateUserFromObject(ctx, loserUser); err != nil {
		return nil, fmt.Errorf("failed to update loser: %w", err)
	}

	showdownGameConfig := models.ShowdownGameConfig{}
	showdownGameConfig.TotalGamesPlayed = winnerRoundData.TotalGamesPlayed
	showdownGameConfig.NumOfGames = showdown.RoundConfig.NumOfGames

	game.ShowdownGameConfig = &showdownGameConfig

	if winnerRoundData.TotalGamesPlayed < showdown.RoundConfig.NumOfGames && !isRoundEnded {
		newGameID, newGameStartTime, err := s.createNewGameForShowdown(ctx, *game.ShowdownId,
			showdown.RoundConfig.GameDuration, winnerRoundData.TotalGamesPlayed, showdown.RoundConfig.NumOfGames,
			players, showdown.CurrentRound, showdownGameWinner, showdownGameLoser,
		)
		if err != nil {
			zlog.Error(ctx, "Failed to create new game", err, zap.String("showdownId", game.ShowdownId.Hex()),
				zap.Int("currentRound", showdown.CurrentRound))
			return nil, fmt.Errorf("unable to create newgame %w", err)
		}

		if newGameStartTime.IsZero() || newGameID == nil {
			zlog.Error(ctx, "Failed to start new game", nil,
				zap.String("showdownId", game.ShowdownId.Hex()), zap.Int("currentRound", showdown.CurrentRound))
			return nil, fmt.Errorf("unable to start newgame")
		}

		winnerRoundData.Games = append(winnerRoundData.Games, *newGameID)
		loserRoundData.Games = append(loserRoundData.Games, *newGameID)

		showdownGameConfig.NextGameID = newGameID
		showdownGameConfig.NextGameStartsAt = &newGameStartTime
	} else {
		showdownGameConfig.IsRoundEnded = true
	}

	winnerShowdownParticipant.Rounds[winnerRoundIndex] = winnerRoundData
	loserShowdownParticipant.Rounds[loserRoundIndex] = loserRoundData

	showdownGameConfig.ShowdownGamePlayer = append(showdownGameConfig.ShowdownGamePlayer, showdownGameWinner, showdownGameLoser)

	err = s.showdownService.UpdateShowdownParticipant(ctx, winnerShowdownParticipant, game.ShowdownGameConfig.Round)
	if err != nil {
		zlog.Error(ctx, "Failed to update showdown participant", err, zap.String("gameID", gameID.Hex()), zap.String("showdownId", showdownId.Hex()))
		return nil, err
	}

	err = s.showdownService.UpdateShowdownParticipant(ctx, loserShowdownParticipant, game.ShowdownGameConfig.Round)
	if err != nil {
		zlog.Error(ctx, "Failed to update showdown participant", err, zap.String("gameID", gameID.Hex()), zap.String("showdownId", showdownId.Hex()))
		return nil, err
	}

	err = s.handleUpdatesOfEntities(ctx, *gameID, showdownId, sortedLeaderboard, game, winnerUser, loserUser, showdownGameConfig, *ratingChangeInfo)
	if err != nil {
		zlog.Error(ctx, "Failed to update entities", err, zap.String("gameID", gameID.Hex()), zap.String("showdownId", showdownId.Hex()))
		return nil, err
	}
	s.triggerPostGameUpdates(ctx, gameID, winnerUser, loserUser, ratingChangeInfo)
	return game, nil
}

func (s *service) handleUpdatesOfEntities(ctx context.Context, gameID, showdownId primitive.ObjectID, sortedLeaderboard []*models.LeaderBoardEntry, game *models.Game, winnerUser, loserUser *models.User, showdownGameConfig models.ShowdownGameConfig, ratingChangeInfo ratingChangeInfo) error {
	sortedLeaderboard[0].RatingChange = &ratingChangeInfo.WinnerRatingChange
	sortedLeaderboard[1].RatingChange = &ratingChangeInfo.LoserRatingChange

	sortedLeaderboard[0].StatikCoinsEarned = gameutils.GetStatikCoinsEarnedForWinner(sortedLeaderboard[0])
	sortedLeaderboard[1].StatikCoinsEarned = gameutils.GetStatikCoinsEarnedForLoser(sortedLeaderboard[1])

	game.ShowdownGameConfig = &showdownGameConfig

	err := s.publishGameEvent(ctx, game, constants.GameEventEnum.GAME_ENDED.String())
	if err != nil {
		zlog.Error(ctx, "Failed to publish game ended event", err)
		return err
	}

	gameutils.SaveMinifiedQuestions(game)
	game.Questions = nil
	game.RematchRequestedBy = nil

	err = s.gameRepo.UpdateGame(ctx, game)
	if err != nil {
		zlog.Error(ctx, "Failed to update game", err, zap.String("gameID", gameID.Hex()), zap.String("showdownId", showdownId.Hex()))
		return err
	}

	err = s.gameCache.SetGame(ctx, game)
	if err != nil {
		zlog.Error(ctx, "Failed to cache game", err, zap.String("gameID", gameID.Hex()), zap.String("showdownId", showdownId.Hex()))
		return err
	}
	return nil
}

func (s *service) createNewGameForShowdown(ctx context.Context, showdownId primitive.ObjectID, gameDuration, totalGamesPlayed, totalGames int, players models.Players, currentRound int, showdownGameWinner, showdownGameLoser *models.ShowdownGamePlayer) (*primitive.ObjectID, time.Time, error) {
	newShowdownGameWinner := models.ShowdownGamePlayer{
		IsTie:         false,
		IsWinner:      false,
		UserID:        showdownGameWinner.UserID,
		ParticipantID: showdownGameWinner.ParticipantID,
		Wins:          showdownGameWinner.Wins,
		Score:         showdownGameWinner.Score,
	}
	newShowdownGameLoser := models.ShowdownGamePlayer{
		IsTie:         false,
		IsWinner:      false,
		UserID:        showdownGameLoser.UserID,
		ParticipantID: showdownGameLoser.ParticipantID,
		Wins:          showdownGameLoser.Wins,
		Score:         showdownGameLoser.Score,
	}
	newGame, err := s.CreateGameForShowdown(ctx, &models.ShowdownConfig{
		ShowdownId: showdownId,
		GameConfig: models.GameConfig{
			NumPlayers: utils.AllocPtr(constants.SHOWDOWN_PLAYERS),
			TimeLimit:  utils.AllocPtr(gameDuration),
			GameType:   models.GameTypeSumdayShowdown,
		},
		IsPlayerAlreeadyJoined: true,
		TotalGamesPlayed:       totalGamesPlayed,
		TotalGames:             totalGames,
		Players:                players,
		Round:                  currentRound,
		ShowdownGamePlayer:     []*models.ShowdownGamePlayer{&newShowdownGameWinner, &newShowdownGameLoser},
	})
	if err != nil || len(newGame) == 0 || newGame[0] == nil && newGame[0].ID == nil {
		zlog.Error(ctx, "Failed to create new game", err, zap.String("showdownId", showdownId.Hex()), zap.Int("currentRound", currentRound), zap.Any("players", players))
		return nil, time.Time{}, fmt.Errorf("unable to create newgame %w", err)
	}
	newGameStartTime := time.Now().Add(30 * time.Second)
	_, err = s.StartGameForShowdownInternal(ctx, &models.StartGameInput{GameID: newGame[0].ID}, newGameStartTime)
	if err != nil || len(newGame) == 0 || newGame[0] == nil || newGame[0].ID == nil {
		zlog.Error(ctx, "Failed to start game", err, zap.String("showdownId", showdownId.Hex()), zap.Int("currentRound", currentRound))
		return nil, time.Time{}, fmt.Errorf("unable to start game %w", err)
	}
	return newGame[0].ID, newGameStartTime, nil
}

func (s *service) getGameAndShowdownPayload(ctx context.Context, gameID *primitive.ObjectID) (*models.Game, *models.Showdown, error) {
	if gameID == nil {
		return nil, nil, fmt.Errorf("GameId is nil")
	}
	game, err := s.GetGameByID(ctx, gameID)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get game: %w", err)
	}
	if game == nil {
		zlog.Error(ctx, "Game not found", nil, zap.String("gameID", gameID.Hex()))
		return nil, nil, fmt.Errorf("game not found")
	}
	if game.GameType != models.GameTypeSumdayShowdown || game.ShowdownId == nil {
		zlog.Error(ctx, "Game is invalid", nil, zap.String("gameID", gameID.Hex()))
		return nil, nil, fmt.Errorf("game is invalid")
	}

	if game.GameStatus == models.GameStatusEnded {
		zlog.Info(ctx, "Game is already ended", zap.String("gameID", gameID.Hex()))
		return nil, nil, nil
	}

	if game.GameStatus == models.GameStatusCancelled {
		zlog.Info(ctx, "Game is cancelled, ending game", zap.String("gameID", gameID.Hex()), zap.String("showdownId", game.ShowdownId.Hex()))
		return nil, nil, fmt.Errorf("game is cancelled")
	}

	if game.GameStatus != models.GameStatusStarted && game.GameStatus != models.GameStatusCreated {
		zlog.Info(ctx, "Game is not in a started state, skipping end game", zap.String("gameID", gameID.Hex()))
		return nil, nil, nil
	}

	if game.ShowdownGameConfig == nil || game.ShowdownGameConfig.ShowdownGamePlayer == nil || len(game.ShowdownGameConfig.ShowdownGamePlayer) < 2 {
		zlog.Error(ctx, "Showdown game config not found", nil, zap.String("gameID", gameID.Hex()))
		return nil, nil, fmt.Errorf("showdown game config not found")
	}

	if game.ShowdownGameConfig.ShowdownGamePlayer[0] == nil || game.ShowdownGameConfig.ShowdownGamePlayer[1] == nil {
		zlog.Error(ctx, "Showdown game player IDs not found", nil, zap.String("gameID", gameID.Hex()))
		return nil, nil, fmt.Errorf("showdown game player IDs not found")
	}

	showdown, err := s.showdownCache.GetShowdown(ctx, *game.ShowdownId)
	if err != nil {
		zlog.Error(ctx, "Failed to get showdown", err, zap.String("gameID", gameID.Hex()), zap.String("showdownId", game.ShowdownId.Hex()))
		return nil, nil, err
	}

	if showdown == nil {
		zlog.Error(ctx, "Showdown not found", nil, zap.String("gameID", gameID.Hex()), zap.String("showdownId", game.ShowdownId.Hex()))
		return nil, nil, fmt.Errorf("showdown not found")
	}

	return game, showdown, nil
}

func (s *service) getShowdownParticipantFromGame(ctx context.Context, game *models.Game, userID primitive.ObjectID) (*models.ShowdownParticipant, int, error) {
	// get index instead of player
	playerIndex := slicesustils.FindIndex(game.ShowdownGameConfig.ShowdownGamePlayer, func(player *models.ShowdownGamePlayer) bool {
		if player != nil && player.UserID == userID {
			return true
		}
		return false
	})
	if playerIndex == -1 {
		return nil, 0, fmt.Errorf("player not found")
	}
	participantID := game.ShowdownGameConfig.ShowdownGamePlayer[playerIndex].ParticipantID
	participant, err := s.showdownService.GetShowdownParticipant(ctx, *game.ShowdownId, participantID)
	if err != nil || participant == nil {
		return nil, 0, fmt.Errorf("failed to get participant: %w", err)
	}
	return participant, playerIndex, nil
}

func getCurrentRoundDataAndIndex(participant *models.ShowdownParticipant, currentRound int) (*models.ShowdownRound, int, error) {
	for j, roundData := range participant.Rounds {
		if roundData == nil {
			return nil, j, fmt.Errorf("unable to get round data")
		}
		if roundData.Round == currentRound {
			return roundData, j, nil
		}
	}
	return nil, 0, fmt.Errorf("failed to get current round info")
}
