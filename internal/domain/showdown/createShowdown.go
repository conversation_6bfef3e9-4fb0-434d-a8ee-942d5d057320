package showdown

import (
	"context"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/scheduler"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) CreateShowdown(ctx context.Context, input models.CreateShowdownInput) (*models.Showdown, error) {
	currentTime := time.Now().UTC()
	zlog.Info(ctx, "Creating showdown", zap.String("showdownName", input.Name))
	newRegistrationForm, err := createRegistrationForm(input.RegistrationForm)
	if err != nil {
		zlog.Error(ctx, "Failed to create registration form", err)
		return nil, err
	}

	var defTime time.Time

	if input.RegistrationStartTime == defTime {
		input.RegistrationStartTime = currentTime
	}
	registrationEndTime := input.StartTime.Add(-70 * time.Second)

	isRegistrationStarted := (input.RegistrationStartTime).Before(currentTime)
	roundTime := input.RoundConfig.GameDuration*input.RoundConfig.NumOfGames + (input.RoundConfig.NumOfGames-1)*input.RoundConfig.MaxGapBwGame + input.RoundConfig.MaxWaitTime

	showdownDuration := roundTime*input.Rounds + (input.Rounds-1)*input.GapBwRounds
	showdownEndTime := input.StartTime.Add(time.Duration(showdownDuration) * time.Second)

	newShowdown := &models.Showdown{
		ID:                    utils.AllocPtr(primitive.NewObjectID()),
		Name:                  input.Name,
		Description:           input.Description,
		StartTime:             input.StartTime,
		EndTime:               showdownEndTime,
		IsRatedEvent:          input.IsRatedEvent,
		Rounds:                input.Rounds,
		GapBwRounds:           input.GapBwRounds,
		RegistrationStartTime: &input.RegistrationStartTime,
		RegistrationEndTime:   &registrationEndTime,
		RoundTime:             roundTime,
		RegistrationCount:     utils.AllocPtr(0),
		Status:                models.ShowdownContestStatusUpcoming,
		RegistrationForm:      newRegistrationForm,
		CreatedAt:             time.Now(),
		UpdatedAt:             time.Now(),
		Duration:              showdownDuration,
		CurrentRound:          1,
		Details: models.ShowdownDetails{
			Format: input.Details.Format,
			Rules:  input.Details.Rules,
		},
	}

	newShowdown.HostedBy = &models.HostInfo{
		HostType: input.HostedBy.HostType,
		ID:       input.HostedBy.ID,
		HostLogo: input.HostedBy.HostLogo,
	}

	newShowdown.RoundConfig = models.RoundConfig{
		GameDuration: input.RoundConfig.GameDuration,
		NumOfPlayer:  input.RoundConfig.NumOfPlayer,
		GameType:     input.RoundConfig.GameType,
		MaxGapBwGame: input.RoundConfig.MaxGapBwGame,
		MaxWaitTime:  input.RoundConfig.MaxWaitTime,
		NumOfGames:   input.RoundConfig.NumOfGames,
	}

	if isRegistrationStarted {
		newShowdown.Status = models.ShowdownContestStatusRegistrationOpen
	}

	newShowdown, err = s.showdownRepo.CreateNewShowdown(ctx, newShowdown)
	if err != nil {
		zlog.Error(ctx, "Failed to create new showdown", err, zap.String("showdownID", newShowdown.ID.Hex()))
		return nil, err
	}
	err = s.showdownCache.SetShowdown(ctx, newShowdown)
	if err != nil {
		zlog.Error(ctx, "Failed to set showdown in cache", err, zap.String("showdownID", newShowdown.ID.Hex()))
		return nil, err
	}
	// notificationTime := newShowdown.StartTime.Add(-time.Second * 5)
	createFixtureTime := newShowdown.StartTime.Add(-60 * time.Second)
	registrationStartsAt := *newShowdown.RegistrationStartTime
	showdownStartsAt := newShowdown.StartTime
	showdownEndsAt := newShowdown.EndTime

	if newShowdown.ID != nil {
		showdownId := *newShowdown.ID
		// registrationStatusUpdate
		zlog.Info(ctx, "Scheduling registration status update", zap.String("showdownID", showdownId.Hex()), zap.Time("registrationStartTime", registrationStartsAt))
		err := scheduler.Schedule(ctx, s.sortedSet, &models.SchedulerPayload{
			Type: models.UpdateShowdownStatus,
			Action: models.UpdateShowdownStatusActionPayload{
				ShowdownId: showdownId,
				Status:     models.ShowdownContestStatusRegistrationOpen,
			},
			ContextMap: utils.GetContextValuesMap(ctx),
		}, registrationStartsAt)
		if err != nil {
			zlog.Error(ctx, "Failed to schedule showdown status update to registration open", err, zap.String("showdownID", showdownId.Hex()), zap.Time("registrationStartTime", registrationStartsAt))
			return nil, err
		}

		zlog.Info(ctx, "Scheduling live status update", zap.String("showdownID", showdownId.Hex()), zap.Time("showdownStartTime", showdownStartsAt))
		err = scheduler.Schedule(ctx, s.sortedSet, &models.SchedulerPayload{
			Type: models.UpdateShowdownStatus,
			Action: models.UpdateShowdownStatusActionPayload{
				ShowdownId: showdownId,
				Status:     models.ShowdownContestStatusLive,
			},
			ContextMap: utils.GetContextValuesMap(ctx),
		}, showdownStartsAt)
		if err != nil {
			zlog.Error(ctx, "Failed to schedule showdown status update to live", err, zap.String("showdownID", showdownId.Hex()), zap.Time("showdownStartTime", showdownStartsAt))
			return nil, err
		}

		zlog.Info(ctx, "Scheduling ended status update", zap.String("showdownID", showdownId.Hex()), zap.Time("showdownEndTime", showdownEndsAt))
		err = scheduler.Schedule(ctx, s.sortedSet, &models.SchedulerPayload{
			Type: models.UpdateShowdownStatus,
			Action: models.UpdateShowdownStatusActionPayload{
				ShowdownId: showdownId,
				Status:     models.ShowdownContestStatusEnded,
			},
			ContextMap: utils.GetContextValuesMap(ctx),
		}, showdownEndsAt)
		if err != nil {
			zlog.Error(ctx, "Failed to schedule showdown status update to ended", err, zap.String("showdownID", showdownId.Hex()), zap.Time("showdownEndTime", showdownEndsAt))
			return nil, err
		}

		zlog.Info(ctx, "Scheduling fixture creation", zap.String("showdownID", showdownId.Hex()), zap.Time("fixtureCreationTime", createFixtureTime))
		err = scheduler.Schedule(ctx, s.sortedSet, &models.SchedulerPayload{
			Type: models.FixtureCreation,
			Action: models.ShowdownRoundActionPayload{
				ShowdownId: showdownId,
				Round:      1,
			},
			ContextMap: utils.GetContextValuesMap(ctx),
		}, createFixtureTime)
		if err != nil {
			zlog.Error(ctx, "Failed to schedule showdown fixture creation", err, zap.String("showdownID", showdownId.Hex()), zap.Time("fixtureCreationTime", createFixtureTime))
			return nil, err
		}

		err = scheduler.Schedule(ctx, s.sortedSet, &models.SchedulerPayload{
			Type: models.ScheduleShowdownEnd,
			Action: models.ShowdownRoundActionPayload{
				ShowdownId: showdownId,
			},
			ContextMap: utils.GetContextValuesMap(ctx),
		}, showdownEndsAt)
		if err != nil {
			zlog.Error(ctx, "Failed to schedule showdown end", err, zap.String("showdownID", showdownId.Hex()), zap.Time("showdownEndTime", showdownEndsAt))
			return nil, err
		}
	}
	zlog.Debug(ctx, "Created showdown", zap.String("showdownID", newShowdown.ID.Hex()))
	return newShowdown, nil
}

func createRegistrationForm(input models.RegistrationFormInput) (*models.RegistrationForm, error) {
	now := time.Now()
	form := &models.RegistrationForm{
		ID:        primitive.NewObjectID(),
		Fields:    make([]*models.FormField, len(input.Fields)),
		CreatedAt: now,
		UpdatedAt: now,
	}

	for i, field := range input.Fields {
		validation := &models.FieldValidation{
			Regex:         &field.Validation.Regex,
			Min:           &field.Validation.Min,
			Max:           &field.Validation.Max,
			EmailSuffix:   field.Validation.EmailSuffix,
			EmailSuffixes: field.Validation.EmailSuffixes,
		}

		form.Fields[i] = &models.FormField{
			ID:         primitive.NewObjectID(),
			Name:       field.Name,
			Type:       field.Type,
			Label:      field.Label,
			Required:   field.Required,
			Options:    field.Options,
			Validation: validation,
		}
	}

	return form, nil
}
