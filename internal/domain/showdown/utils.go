package showdown

import (
	"context"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) CheckShowdownPlayersStatus(ctx context.Context, showdownId primitive.ObjectID, currentRound int) error {
	fixtures, err := s.fixtureRepo.GetFixturesByShowdownIDAndRound(ctx, showdownId, currentRound)
	if err != nil {
		zlog.Error(ctx, "Failed to get fixtures", err, zap.String("showdownID", showdownId.Hex()))
		return err
	}
	if fixtures == nil {
		zlog.Error(ctx, "fixtures not found", nil, zap.String("showdownID", showdownId.Hex()))
		return fmt.Errorf("fixtures not found")
	}
	for _, fixture := range fixtures {
		if fixture == nil || len(fixture.Participants) != 2 {
			zlog.Error(ctx, "fixture found nil while checking for showdown players status", nil, zap.String("showdownID", showdownId.Hex()))
			continue
		}
		playerStatusData := make([]*models.ShowdownPlayerStatusPayload, 0, 2)
		participants := make([]*models.ShowdownParticipant, 0, 2)

		for _, participantId := range fixture.Participants {
			participant, err := s.GetShowdownParticipant(ctx, showdownId, participantId)
			if err != nil {
				zlog.Error(ctx, "Failed to update status of showdown with in cache ", err, zap.String("showdownID", showdownId.Hex()))
				return err
			}
			if participant == nil {
				return fmt.Errorf("participant found nil while checking for showdown players status")
			}
			participants = append(participants, participant)
			for i, roundInfo := range participant.Rounds {
				if roundInfo != nil && roundInfo.Round == currentRound {
					playerStatusData = append(playerStatusData, &models.ShowdownPlayerStatusPayload{
						UserId:       participantId,
						PlayerStatus: roundInfo.PlayerStatus,
						RoundIndex:   i,
					})
				}
			}
		}
		if playerStatusData[0].PlayerStatus == models.RoundPlayerStatusPendingJoin && playerStatusData[1].PlayerStatus == models.RoundPlayerStatusPendingJoin {
			participants[0].Rounds[playerStatusData[0].RoundIndex].PlayerStatus = models.RoundPlayerStatusBothDidNotPlay
			participants[1].Rounds[playerStatusData[1].RoundIndex].PlayerStatus = models.RoundPlayerStatusBothDidNotPlay
			err = s.UpdateShowdownParticipant(ctx, participants[0], currentRound)
			if err != nil {
				zlog.Error(ctx, "Failed to update status of showdown with in cache "+showdownId.Hex(), err)
				return err
			}
			err = s.UpdateShowdownParticipant(ctx, participants[1], currentRound)
			if err != nil {
				zlog.Error(ctx, "Failed to update status of showdown with in cache "+showdownId.Hex(), err)
				return err
			}
			// s.PublishShowdownUserEvent(ctx, showdown, participants[0].I)
		}
	}
	return nil
}

func (s *service) UpdateShowdownStatus(ctx context.Context, showdownId primitive.ObjectID, status models.ShowdownContestStatus) error {
	update := bson.M{"$set": bson.M{"status": status}}
	err := s.showdownRepo.UpdateOne(ctx, bson.M{"_id": showdownId}, update)
	if err != nil {
		zlog.Error(ctx, "Failed to update status of showdown with id ", err, zap.String("showdownID", showdownId.Hex()))
		return err
	}
	showdown, err := s.showdownCache.GetShowdown(ctx, showdownId)
	if err != nil {
		zlog.Error(ctx, "Failed to get showdown details with in cache "+showdownId.Hex(), err)
		return err
	}
	showdown.Status = status
	err = s.showdownCache.SetShowdown(ctx, showdown)
	if err != nil {
		zlog.Error(ctx, "Failed to update status of showdown with in cache "+showdownId.Hex(), err)
		return err
	}
	return nil
}

func (s *service) UpdateShowdownCurrentRound(ctx context.Context, showdownId primitive.ObjectID, round int) error {
	showdown, err := s.getShowdownById(ctx, showdownId)
	if err != nil {
		zlog.Error(ctx, "Failed to get showdown details", err, zap.String("showdownID", showdownId.Hex()))
		return err
	}
	showdown.CurrentRound = round
	err = s.updateShowdown(utils.DeriveContextWithoutCancel(ctx), showdown)
	if err != nil {
		zlog.Error(ctx, "Failed to update showdown round", err, zap.String("showdownID", showdownId.Hex()))
		return err
	}
	return nil
}

func (s *service) updateShowdownStatusByCurrentTime(ctx context.Context, showdown *models.Showdown) error {
	currentTime := time.Now().UTC()
	if showdown == nil {
		return fmt.Errorf("contest not found")
	}
	newStatus := showdown.Status

	if currentTime.After(showdown.StartTime) && currentTime.Before(showdown.EndTime) {
		newStatus = models.ShowdownContestStatusLive
	} else if currentTime.After(showdown.EndTime) {
		newStatus = models.ShowdownContestStatusEnded
	} else if currentTime.After(*showdown.RegistrationStartTime) && currentTime.Before(*showdown.RegistrationEndTime) {
		newStatus = models.ShowdownContestStatusRegistrationOpen
	} else if currentTime.Before(*showdown.RegistrationStartTime) {
		newStatus = models.ShowdownContestStatusUpcoming
	}

	if newStatus != showdown.Status {
		showdown.Status = newStatus
		showdown.UpdatedAt = time.Now()
		err := s.showdownRepo.UpdateOne(ctx, bson.M{"_id": showdown.ID}, bson.M{"$set": bson.M{"status": newStatus, "updatedAt": showdown.UpdatedAt}})
		if err != nil {
			zlog.Error(ctx, "Failed to update contest status", err, zap.String("contestID", showdown.ID.Hex()))
		}
		return err
	}

	return nil
}

func (s *service) updateShowdown(ctx context.Context, showdown *models.Showdown) error {
	if showdown == nil {
		return fmt.Errorf("showdown is nil")
	}
	err := s.showdownRepo.Update(ctx, showdown)
	if err != nil {
		zlog.Error(ctx, "Failed to update showdown", err, zap.String("showdownID", showdown.ID.Hex()))
		return err
	}
	err = s.showdownCache.SetShowdown(ctx, showdown)
	return err
}

func (s *service) getParticipantsFromLeaderboard(ctx context.Context, showdownID primitive.ObjectID, currentPage, limit int) ([]*models.ShowdownParticipantDetail, error) {
	leaderboardEntities, err := s.showdownCache.GetPaginatedLeaderboardRev(ctx, showdownID, currentPage, limit)
	if err != nil {
		return nil, err
	}
	participants := make([]*models.ShowdownParticipantDetail, 0, len(leaderboardEntities))
	for _, v := range leaderboardEntities {
		participantId, err := primitive.ObjectIDFromHex(v.ID)
		if err != nil {
			return nil, err
		}
		participant, err := s.GetShowdownParticipant(ctx, showdownID, participantId)
		if err != nil {
			// TODO: Handle error
			continue
		}
		participants = append(participants, &models.ShowdownParticipantDetail{
			ShowdownParticipant: *participant,
		})
	}
	return participants, nil
}

func (s *service) convertToShowdownParticipantDetail(
	participantsInfo []*models.ShowdownParticipant,
) ([]*models.ShowdownParticipantDetail, error) {
	var participants []*models.ShowdownParticipantDetail
	for _, participantInfo := range participantsInfo {
		if participantInfo == nil {
			// TODO: ERROR HANDLING
			continue
		}
		participants = append(participants, &models.ShowdownParticipantDetail{ShowdownParticipant: *participantInfo})
	}

	return participants, nil
}

func (s *service) updateShowdownRoundBatch(ctx context.Context, roundDtos map[primitive.ObjectID]*models.ShowdownRoundDto, showdownID primitive.ObjectID) error {
	var participant *models.ShowdownParticipant
	var err error

	for _, dto := range roundDtos {
		if dto == nil {
			return fmt.Errorf("roundDTO is nil")
		}
		participant, err = s.GetShowdownParticipant(ctx, showdownID, dto.UserId)
		if err != nil {
			return err
		}
		participant.Rounds = append(participant.Rounds, &dto.Round)
		_, err = s.showdownCache.SetShowdownParticipant(ctx, participant)
		if err != nil {
			return err
		}
	}
	err = s.showdownParticipantRepo.UpdateShowdownRoundsBatch(ctx, roundDtos, showdownID)
	return err
}

func (s *service) getRecentParticipants(ctx context.Context, contestID primitive.ObjectID) ([]*models.User, error) {
	participants, err := s.showdownParticipantRepo.GetTopParticipantsByShowdown(ctx, contestID, 3)
	if err != nil {
		return nil, err
	}

	var recentParticipants []*models.User
	for _, participant := range participants {
		user, err := s.coreService.GetUserByID(ctx, participant.UserID)
		if err != nil {
			zlog.Error(ctx, "Failed to fetch user", err, zap.String("userID", participant.UserID.Hex()))
			continue
		}
		recentParticipants = append(recentParticipants, user)
	}

	return recentParticipants, nil
}

func (s *service) GetShowdownParticipant(ctx context.Context, showdownID, participantID primitive.ObjectID) (*models.ShowdownParticipant, error) {
	showdownParticipant, err := s.showdownCache.GetShowdownParticipant(ctx, showdownID, participantID)
	if err != nil || showdownParticipant == nil {
		return s.showdownParticipantRepo.GetByID(ctx, participantID)
	}
	return showdownParticipant, nil
}

func (s *service) SetShowdownParticipant(ctx context.Context, participant *models.ShowdownParticipant) error {
	err := s.showdownParticipantRepo.Update(ctx, participant)
	if err != nil {
		return err
	}
	_, err = s.showdownCache.SetShowdownParticipant(ctx, participant)
	if err != nil {
		return err
	}
	return nil
}
