package showdown

import (
	"context"
	"errors"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

func (s *service) GetFixturesByShowdownId(ctx context.Context, showdownID primitive.ObjectID) (*models.FicturesCollection, error) {
	fixtures := models.FicturesCollection{}
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}
	showdown, err := s.getShowdownById(ctx, showdownID)
	if err != nil {
		zlog.Error(ctx, "showdown not found", err, zap.String("showdownID", showdownID.Hex()))
		return nil, err
	}
	if showdown == nil {
		zlog.Error(ctx, "showdown not found", nil, zap.String("showdownID", showdownID.Hex()))
		return &fixtures, fmt.Errorf("showdown is nil")
	}

	currentUserParticipantID, err := s.getShowdownParticipantIdFromUserId(ctx, showdownID, userID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			zlog.Warn(ctx, "Failed to get participant id from cache", zap.String("showdownID", showdownID.Hex()), zap.String("userID", userID.Hex()), zap.Error(err))
			return nil, nil
		}
		zlog.Error(ctx, "Failed to get participant ID", err, zap.String("showdownID", showdownID.Hex()))
		return nil, err
	}
	currentUserFixture, err := s.fixtureRepo.GetByShowdownParticipantIDAndRound(ctx, showdownID, currentUserParticipantID, showdown.CurrentRound)
	if err != nil {
		zlog.Error(ctx, "Failed to get fixture", err, zap.String("showdownID", showdownID.Hex()))
		return nil, err
	}

	if currentUserFixture == nil {
		zlog.Error(ctx, "Failed to get fixture", nil, zap.String("showdownID", showdownID.Hex()))
		return nil, nil
	}

	fixtures.CurrentUserFicture = currentUserFixture

	if fixtures.CurrentUserFicture.Participants == nil || len(fixtures.CurrentUserFicture.Participants) == 0 {
		return nil, fmt.Errorf("participants are nil or empty")
	}

	for i, participantID := range fixtures.CurrentUserFicture.Participants {
		if participantID == currentUserParticipantID && len(fixtures.CurrentUserFicture.Participants) == 2 && i == 1 {
			fixtures.CurrentUserFicture.Participants[0], fixtures.CurrentUserFicture.Participants[1] = fixtures.CurrentUserFicture.Participants[1], fixtures.CurrentUserFicture.Participants[0]
		}
	}

	if fixtures.CurrentUserFicture.Users == nil || len(fixtures.CurrentUserFicture.Users) != len(fixtures.CurrentUserFicture.Participants) {
		fixtures.CurrentUserFicture.Users = make([]*models.ShowdownParticipantDetail, len(fixtures.CurrentUserFicture.Participants))
	}
	zlog.Info(ctx, "Fetching participants details", zap.String("showdownID", showdownID.Hex()))

	for i, participantID := range fixtures.CurrentUserFicture.Participants {
		participantData, err := s.GetShowdownParticipant(ctx, showdownID, participantID)
		if err != nil {
			return nil, err
		}
		if participantData == nil {
			return nil, fmt.Errorf("participant is nil with id: %v", participantID.Hex())
		}
		if fixtures.CurrentUserFicture.Users[i] == nil {
			fixtures.CurrentUserFicture.Users[i] = &models.ShowdownParticipantDetail{}
		}
		fixtures.CurrentUserFicture.Users[i].ShowdownParticipant = *participantData
		for _, roundData := range participantData.Rounds {
			if roundData != nil && roundData.Round == showdown.CurrentRound {
				fixtures.CurrentUserFicture.Users[i].CurrentRound = *roundData
			}
		}
	}

	return &fixtures, nil
}
