package showdown

import (
	"fmt"

	"matiksOfficial/matiks-server-go/internal/models"
)

func MapShowdownParticipantToCurrentParticipant(participant *models.ShowdownParticipant, currentParticipant *models.CurrentShowdonParticipant, currentRound int) error {
	if participant == nil {
		return fmt.Errorf("participant is nil")
	}
	if currentParticipant == nil {
		return fmt.<PERSON><PERSON><PERSON>("current participant is nil")
	}
	currentParticipant.ShowdownID = &participant.ShowdownID
	currentParticipant.UserID = &participant.UserID
	currentParticipant.Rounds = participant.Rounds
	currentParticipant.TotalScore = participant.TotalScore
	for _, roundData := range participant.Rounds {
		if roundData.Round == currentRound {
			currentParticipant.CurrentRound = roundData
			gamesLen := len(roundData.Games)
			if gamesLen > 0 {
				currentParticipant.CurrentGame = &roundData.Games[gamesLen-1]
			}
			break
		}
	}
	return nil
}
