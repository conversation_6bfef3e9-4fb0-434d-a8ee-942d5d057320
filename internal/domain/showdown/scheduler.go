package showdown

import (
	"time"

	"matiksOfficial/matiks-server-go/internal/models"
)

func (s *service) calculateCurrentRoundTime(showdown *models.Showdown, currentRound int) time.Time {
	return showdown.StartTime.Add(time.Duration(
		(currentRound-1)*showdown.RoundTime+
			(currentRound-2)*showdown.GapBwRounds,
	) * time.Second)
}

func (s *service) calculateCurrentRoundEndTime(showdown *models.Showdown, currentRoundStartTime time.Time) time.Time {
	return currentRoundStartTime.Add(time.Duration(
		showdown.RoundTime,
	) * time.Second)
}

func (s *service) calculateNextRoundTime(showdown *models.Showdown, currentRound int) time.Time {
	return showdown.StartTime.Add(time.Duration(
		currentRound*showdown.RoundTime+
			(currentRound-1)*showdown.GapBwRounds+
			15,
	) * time.Second)
}
