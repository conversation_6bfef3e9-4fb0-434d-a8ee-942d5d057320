package showdown

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) OnShowdownEnd(ctx context.Context, showdownId primitive.ObjectID) error {
	showdown, err := s.getShowdownById(ctx, showdownId)
	if err != nil {
		return err
	}
	err = s.showdownRepo.Update(ctx, showdown)
	if err != nil {
		return err
	}

	err = s.insertLeaderboardAndParticipantEntries(utils.DeriveContextWithoutCancel(ctx), showdownId)
	if err != nil {
		return err
	}

	return nil
}

func (s *service) insertLeaderboardAndParticipantEntries(ctx context.Context, showdownId primitive.ObjectID) error {
	var page int64 = 1
	totalCount, err := s.showdownCache.GetLeaderboardEntitiesCount(ctx, showdownId)
	if err != nil {
		return err
	}
	leaderBoardParticipants := make([]*models.LeaderParticipantEntity, 0, constants.LEADERBOARD_PAGE_SIZE)
	showdownParticipants := make([]*models.ShowdownParticipant, 0, constants.LEADERBOARD_PAGE_SIZE)

	totalPage := int64(totalCount / (constants.LEADERBOARD_PAGE_SIZE))
	if totalCount%constants.LEADERBOARD_PAGE_SIZE != 0 {
		totalPage += 1
	}

	for page <= totalPage {
		leaderboardEntities, err := s.showdownCache.GetPaginatedLeaderboard(ctx, showdownId, int(page), constants.LEADERBOARD_PAGE_SIZE)
		if err != nil {
			return err
		}
		for _, v := range leaderboardEntities {

			participantId, err := primitive.ObjectIDFromHex(v.ID)
			if err != nil {
				return err
			}
			participant, err := s.GetShowdownParticipant(ctx, showdownId, participantId)
			if err != nil {
				return err
			}
			if participant == nil {
				return fmt.Errorf("participant from leaderboard is nil")
			}
			showdownParticipants = append(showdownParticipants, participant)
			leaderBoardParticipants = append(leaderBoardParticipants, &models.LeaderParticipantEntity{
				ShowdownId: showdownId,
				Score:      v.Score,
				Rank:       int(v.Rank),
				Participant: models.ParticipantBasicInfo{
					ID:         participant.ID,
					UserID:     participant.UserID,
					ShowdownID: participant.ShowdownID,
					Rounds:     participant.Rounds,
					UserInfo:   participant.UserInfo,
				},
				UserId: participant.UserID,
			})
		}

		if len(showdownParticipants) > 0 {
			if err = s.showdownParticipantRepo.BulkUpdate(ctx, showdownParticipants); err != nil {
				return err
			}
		}

		if len(leaderBoardParticipants) > 0 {
			if err = s.showdownLeaderboardRepo.BulkInsert(ctx, leaderBoardParticipants); err != nil {
				return err
			}
		}
		page += 1
	}
	return nil
}
