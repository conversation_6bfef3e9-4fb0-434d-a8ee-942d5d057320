package showdown

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) UpdateShowdownParticipant(ctx context.Context, participant *models.ShowdownParticipant, round int) error {
	rank, err := s.showdownCache.SetShowdownParticipant(ctx, participant)
	if err != nil {
		zlog.Error(ctx, "UpdateShowdownParticipant: Failed to cache participant", err)
		return err
	}

	var currentUserParticipantDto models.CurrentShowdonParticipant
	err = MapShowdownParticipantToCurrentParticipant(participant, &currentUserParticipantDto, round)
	if err != nil {
		return err
	}

	err = s.coreService.PublishUserEvent(ctx, participant.UserID, &models.ShowdownParticipantUpdatedEvent{
		Participant: &currentUserParticipantDto,
	})
	if err != nil {
		zlog.Error(ctx, "Failed to publish ShowdownParticipantUpdatedEvent ", err)
		return err
	}

	showdownId := participant.ShowdownID
	page := int((rank)/constants.LEADERBOARD_PAGE_SIZE) + 1

	err = s.publishShowdownLeaderboardEvent(ctx, showdownId, &models.ShowdownEventPayload{Type: models.LEADERBOARD_UPDATE, Payload: &models.LeaderboardUpdatePayload{Page: page, ShowdownID: showdownId}})
	if err != nil {
		return err
	}

	err = s.showdownParticipantRepo.Update(ctx, participant)
	return err
}

func (s *service) publishShowdownLeaderboardEvent(ctx context.Context, showdownId primitive.ObjectID, payload *models.ShowdownEventPayload) error {
	channel := fmt.Sprintf("%s_%s", constants.SubscriptionPrefixEnum.SHOWDOWN_EVENT, showdownId.Hex())
	err := s.ws.Publish(ctx, channel, payload)
	return err
}
