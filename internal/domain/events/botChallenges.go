package events

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sync"
	"time"

	gameutils "matiksOfficial/matiks-server-go/internal/domain/game/utils"

	"github.com/redis/go-redis/v9"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
)

const (
	maxBotChallenges  = 2
	challengeCooldown = 24 * time.Hour
	publishInterval   = 20 * time.Second
)

var (
	challengePublishMutex sync.Mutex
	lastPublishTime       time.Time
)

func (s *service) triggerBotChallenges(ctx context.Context, userID string) error {
	userObjID, err := primitive.ObjectIDFromHex(userID)
	if err != nil {
		return fmt.Errorf("error converting userID %s to ObjectID: %w", userID, err)
	}

	challenges, err := s.getChallengesCount(ctx, userObjID)
	if err != nil {
		return fmt.Errorf("error getting challenges count: %w", err)
	}

	if challenges >= maxBotChallenges {
		return fmt.Errorf("max challenges reached: %d", maxBotChallenges)
	}

	go func() {
		err := s.publishChallengeEventRateLimited(ctx, userObjID)
		if err != nil {
			zlog.Error(ctx, "Error publishing challenge event", err)
		}
	}()

	return nil
}

func (s *service) publishChallengeEventRateLimited(ctx context.Context, userId primitive.ObjectID) error {
	challengePublishMutex.Lock()
	defer challengePublishMutex.Unlock()

	currentTime := time.Now()
	if currentTime.Sub(lastPublishTime) < publishInterval {
		time.Sleep(publishInterval - currentTime.Sub(lastPublishTime))
	}

	gameConfig := &models.GameConfig{
		TimeLimit:  utils.AllocPtr(60),
		NumPlayers: utils.AllocPtr(2),
		GameType:   models.GameTypePlayOnline,
	}

	gameCategory, gameType, gameMode := gameutils.GetGameCategoryTypeAndMode(gameConfig)

	currentUser, err := s.userService.GetUserByID(ctx, userId)
	if err != nil || currentUser == nil {
		return fmt.Errorf("error finding current user: %w", err)
	}

	if currentUser.Stats != nil && currentUser.Stats.Ngp > 0 {
		return nil
	}

	botUser, err := s.userService.GetBotUser(ctx, currentUser, gameConfig)
	if err != nil || botUser == nil {
		return fmt.Errorf("error getting bot user: %w", err)
	}

	game := gameutils.CreateGameInstance(botUser.ID, gameCategory, gameType, gameMode, gameConfig, []*models.Player{
		{UserID: botUser.ID, Rating: botUser.Rating, StatikCoins: botUser.StatikCoins},
		{UserID: userId, Rating: currentUser.Rating, Status: models.PlayerStatusInvited, StatikCoins: currentUser.StatikCoins},
	})

	savedGame, err := s.gameRepo.CreateGame(ctx, game)
	if err != nil {
		return fmt.Errorf("error creating game: %w", err)
	}

	err = s.updateChallengesCount(ctx, userId)
	if err != nil {
		return fmt.Errorf("error updating challenges count: %w", err)
	}

	err = s.gameService.PublishChallengeEvent(ctx, savedGame, string(models.ChallengeStatusChallengeSent))
	if err != nil {
		return fmt.Errorf("error publishing challenge event: %w", err)
	}

	go func() {
		err := s.handleChallengeExpiration(context.Background(), *game.ID)
		if err != nil {
			zlog.Error(ctx, "Error handling challenge expiration", err)
		}
	}()

	lastPublishTime = time.Now()
	return nil
}

func (s *service) getChallengesCount(ctx context.Context, userID primitive.ObjectID) (int64, error) {
	key := fmt.Sprintf("bot_challenges:%s", userID.Hex())

	countInBytes, err := s.redisCache.Get(ctx, key)
	if err != nil && errors.Is(err, redis.Nil) {
		return 0, nil
	} else if err != nil {
		return 0, err
	}
	var count int64
	err = json.Unmarshal(countInBytes, &count)
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (s *service) updateChallengesCount(ctx context.Context, userID primitive.ObjectID) error {
	key := fmt.Sprintf("bot_challenges:%s", userID.Hex())

	_, err := s.redisCache.Incr(ctx, key)
	if err != nil {
		return err
	}

	return s.redisCache.Expire(ctx, key, challengeCooldown)
}

func (s *service) handleChallengeExpiration(ctx context.Context, gameID primitive.ObjectID) error {
	timer := time.NewTimer(20 * time.Second)
	<-timer.C

	game, err := s.gameRepo.GetGameByID(ctx, gameID)
	if err != nil {
		return fmt.Errorf("error retrieving game for expiration check: %w", err)
	}

	if game.GameStatus == models.GameStatusCreated {
		game.GameStatus = models.GameStatusCancelled

		err = s.gameRepo.UpdateGame(ctx, game)
		if err != nil {
			return fmt.Errorf("error updating expired game: %w", err)
		}

		err := s.gameCache.SetGame(ctx, game)
		if err != nil {
			return err
		}

		err = s.gameService.PublishChallengeEvent(ctx, game, string(models.ChallengeStatusChallengeExpired))
		if err != nil {
			return fmt.Errorf("error publishing challenge expiration event: %w", err)
		}
	}

	return nil
}
