package matchmaking

import (
	"context"
	"slices"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
)

func matchmakeUniquePair(ctx context.Context, entities []*models.MatchmakingEntity) ([]*models.MatchmakingPair, error) {
	zlog.Debug(ctx, "MatchMakingUniquePair", zap.Any("length", len(entities)))
	if err := validateUniquePairCase(entities); err != nil {
		zlog.Error(ctx, "Failed to validate unique pair case", err)
		return nil, err
	}

	pairLength := len(entities) / 2

	matched := make(map[primitive.ObjectID]bool)
	pairs := make([]*models.MatchmakingPair, 0, pairLength)

	for i := 0; i < len(entities); i++ {
		if matched[entities[i].ID] {
			continue
		}

		for j := i + 1; j < len(entities); j++ {
			if matched[entities[j].ID] {
				continue
			}

			if !slices.Contains(entities[i].PreviousMatchedEntities, entities[j].ID) && !slices.Contains(entities[j].PreviousMatchedEntities, entities[i].ID) {
				pair := &models.MatchmakingPair{
					Entity1: entities[i].ID,
					Entity2: entities[j].ID,
				}
				pairs = append(pairs, pair)

				matched[entities[i].ID] = true
				matched[entities[j].ID] = true

				zlog.Debug(ctx, "Matched pair", zap.Any("pair", pair))
				break
			}
		}
	}

	if len(pairs) < pairLength {
		unmatched := make([]*models.MatchmakingEntity, 0)
		for _, entity := range entities {
			if !matched[entity.ID] {
				unmatched = append(unmatched, entity)
			}
		}

		sequentialPairs, err := matchmakeSequentialPair(ctx, unmatched)
		if err != nil {
			return nil, err
		}
		pairs = append(pairs, sequentialPairs...)
	}

	return pairs, nil
}
