package matchmaking

import (
	"matiksOfficial/matiks-server-go/internal/models"
	"matiksOfficial/matiks-server-go/utils/systemErrors"
)

func validatePayload(payload *models.MatchmakingPayload) error {
	if payload == nil {
		return systemErrors.ErrPayloadIsNil
	}
	if len(payload.Entities) == 0 {
		return systemErrors.ErrNoEntities
	}
	return nil
}

func validateUniquePairCase(entities []*models.MatchmakingEntity) error {
	if entities == nil {
		return systemErrors.ErrNoEntities
	}
	entitiesLength := len(entities)
	if entitiesLength < 2 {
		return systemErrors.ErrNoEntities
	}
	if entitiesLength%2 != 0 {
		return systemErrors.ErrOddNumberOfEntities
	}
	return nil
}

func validateSequentialPairCase(entities []*models.MatchmakingEntity) error {
	if entities == nil {
		return systemErrors.ErrNoEntities
	}
	if len(entities) < 2 {
		return systemErrors.ErrNoEntities
	}
	return nil
}
