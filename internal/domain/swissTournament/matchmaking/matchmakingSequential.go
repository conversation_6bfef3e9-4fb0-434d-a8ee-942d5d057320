package matchmaking

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/models"
)

func matchmakeSequentialPair(ctx context.Context, entities []*models.MatchmakingEntity) ([]*models.MatchmakingPair, error) {
	if err := validateSequentialPairCase(entities); err != nil {
		return nil, err
	}

	length := len(entities)
	pairs := make([]*models.MatchmakingPair, 0, length/2)

	for i := 0; i < length; i += 2 {
		pairs = append(pairs, &models.MatchmakingPair{
			Entity1: entities[i].ID,
			Entity2: entities[i+1].ID,
		})
	}

	return pairs, nil
}
