package matchmaking

import (
	"context"
	"math/rand/v2"

	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
)

func getByeParticipant(ctx context.Context, entities []*models.MatchmakingEntity, totalParticipants, currentRound int) (*models.MatchmakingEntity, int) {
	if totalParticipants%2 == 0 {
		return nil, -1
	}
	if currentRound <= totalParticipants {
		return getByeParticipantUnique(ctx, entities, totalParticipants, currentRound)
	}
	return getByeParticipantRandom(ctx, entities, totalParticipants, currentRound)
}

func getByeParticipantUnique(ctx context.Context, entities []*models.MatchmakingEntity, totalParticipants, currentRound int) (*models.MatchmakingEntity, int) {
	for i := len(entities) - 1; i >= 0; i-- {
		if entities[i] == nil {
			zlog.Debug(ctx, "Bye participant not found")
			continue
		}
		entity := entities[i]
		if !entity.HadBye {
			return entity, i
		}
	}
	return nil, -1
}

func getByeParticipantRandom(ctx context.Context, entities []*models.MatchmakingEntity, totalParticipants, currentRound int) (*models.MatchmakingEntity, int) {
	length := len(entities)
	if length == 0 {
		zlog.Debug(ctx, "Bye participant not found")
		return nil, -1
	}
	randomIndex := rand.IntN(length)
	return entities[randomIndex], randomIndex
}
