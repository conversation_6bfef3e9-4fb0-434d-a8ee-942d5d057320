package matchmaking

import (
	"context"
	"sort"

	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
	"matiksOfficial/matiks-server-go/utils/slicesustils"
	"matiksOfficial/matiks-server-go/utils/systemErrors"

	"go.uber.org/zap"
)

func Matchmake(ctx context.Context, payload *models.MatchmakingPayload, isSorted bool, currentRound int) (*models.MatchmakingResponse, error) {
	zlog.Debug(ctx, "MatchMaking", zap.Any("length", len(payload.Entities)))
	if err := validatePayload(payload); err != nil {
		zlog.Error(ctx, "Failed to validate payload", err)
		return nil, err
	}

	zlog.Debug(ctx, "MatchMaking sorting of payload entities")
	if !isSorted {
		sort.Slice(payload.Entities, func(i, j int) bool {
			return payload.Entities[i].MatchmakingScore > payload.Entities[j].MatchmakingScore
		})
	}

	var matchmakingResponse models.MatchmakingResponse
	totalParticipants := len(payload.Entities)
	zlog.Debug(ctx, "Bye participant calculation", zap.Int("totalParticipants", totalParticipants))

	byeEntity, byeIndex := getByeParticipant(ctx, payload.Entities, totalParticipants, currentRound)
	if len(payload.Entities)%2 != 0 && byeEntity == nil {
		zlog.Error(ctx, "Bye participant calculation", systemErrors.ErrOddNumberOfEntitiesAfterRemovingByeEntity, zap.Int("totalParticipants", totalParticipants))
		return nil, systemErrors.ErrOddNumberOfEntitiesAfterRemovingByeEntity
	}

	if byeEntity != nil {
		payload.Entities = slicesustils.RemoveAt(payload.Entities, byeIndex)
		matchmakingResponse.ByeEntity = &byeEntity.ID
	}

	pairs, err := matchmakeUniquePair(ctx, payload.Entities)
	if err != nil {
		zlog.Error(ctx, "Failed to match make unique pair", err)
		return nil, err
	}
	matchmakingResponse.Pairs = pairs

	return &matchmakingResponse, nil
}
