package scheduledTasks

import (
	"context"
	"fmt"
	"sync"

	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
	"matiksOfficial/matiks-server-go/utils/slicesustils"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

// TODO: fix and use this
func (s *showdownScheduledTasks) NotifyJoinGameTimeoutBatched(ctx context.Context, showdownId primitive.ObjectID, round int) error {
	fixtures, err := s.getFixtures(ctx, showdownId, round)
	if err != nil {
		zlog.Error(ctx, "Failed to get fixtures", err, zap.String("showdownID", showdownId.Hex()), zap.Int("round", round))
		return err
	}

	slices := slicesustils.SplitSlice(fixtures, 25) // TODO: make this a constant
	numOfGoRoutines := len(slices)
	var wg sync.WaitGroup
	wg.Add(numOfGoRoutines)
	for i := range numOfGoRoutines {
		go func() {
			defer wg.Done()
			fixtures := slices[i]
			if err := s.notifyJoinGameTimeout(ctx, showdownId, fixtures); err != nil {
				zlog.Error(ctx, "failed to notify users", err, zap.String("showdownID", showdownId.Hex()))
			}
		}()
	}
	wg.Wait()
	return nil
}

func (s *showdownScheduledTasks) notifyJoinGameTimeout(ctx context.Context, showdownId primitive.ObjectID, fixtures []*models.Fictures) error {
	// TODO: Implement

	// for _, fixture := range fixtures {
	// 	if fixture == nil {
	// 		zlog.Error(ctx, "fixture found nil while notifying users", nil, zap.String("showdownID", showdownId.Hex()))
	// 		continue
	// 	}

	// 	fixture.
	// }
	return nil
}

func (s *showdownScheduledTasks) getFixtures(ctx context.Context, showdownID primitive.ObjectID, round int) ([]*models.Fictures, error) {
	fixtures, err := s.fixtureRepo.GetFixturesByShowdownIDAndRound(ctx, showdownID, round)
	if err != nil {
		zlog.Error(ctx, "Failed to get fixtures", err, zap.String("showdownID", showdownID.Hex()), zap.Int("round", round))
		return nil, err
	}

	if fixtures == nil || len(fixtures) == 0 {
		zlog.Error(ctx, "No fixtures found", nil, zap.String("showdownID", showdownID.Hex()), zap.Int("round", round))
		return nil, fmt.Errorf("fixtures not found")
	}

	return fixtures, nil
}
