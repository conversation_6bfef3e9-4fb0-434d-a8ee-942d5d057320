package showdownFixtures

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
	"matiksOfficial/matiks-server-go/utils/systemErrors"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *showdownFixtures) batchInsertGames(
	ctx context.Context,
	showdownId primitive.ObjectID,
	round int,
	matchedPairs *models.MatchmakingResponse,
	participantsPayloadMap map[primitive.ObjectID]*models.ShowdownFixturesInternalPayload,
	totalGames int,
	gameDuration int,
) (map[primitive.ObjectID]*models.ShowdownFixturesInternalPayload, error) {
	if matchedPairs == nil {
		return participantsPayloadMap, systemErrors.ErrMatchMakingResponseNotFound
	}
	if len(matchedPairs.Pairs) == 0 {
		return participantsPayloadMap, systemErrors.ErrMatchMakingResponsePairsNotFound
	}
	if participantsPayloadMap == nil {
		return participantsPayloadMap, systemErrors.ErrParticipantsPayloadMapNotFound
	}
	zlog.Debug(ctx, "Batch inserting games", zap.Int("round", round), zap.Int("totalParticipants", len(matchedPairs.Pairs)))

	// create array of insert objects
	games := make([]*models.Game, 0, len(matchedPairs.Pairs))
	for _, pair := range matchedPairs.Pairs {
		if pair == nil {
			zlog.Debug(ctx, "Pair is nil", zap.Int("round", round))
			continue
		}

		player1, ok := participantsPayloadMap[pair.Entity1]
		if !ok {
			zlog.Debug(ctx, "Player 1 not found", zap.Int("round", round), zap.Any("player1", pair.Entity1))
			continue
		}
		player2, ok := participantsPayloadMap[pair.Entity2]
		if !ok {
			zlog.Debug(ctx, "Player 2 not found", zap.Int("round", round), zap.Any("player2", pair.Entity2))
			continue
		}
		if player1 == nil || player2 == nil {
			zlog.Debug(ctx, "Player 1 or 2 is nil", zap.Int("round", round), zap.Any("player1", pair.Entity1), zap.Any("player2", pair.Entity2))
			continue
		}

		playerGames, err := s.gameService.CreateGameForShowdown(ctx, &models.ShowdownConfig{
			ShowdownId: showdownId,
			TotalGames: totalGames,
			GameConfig: models.GameConfig{
				NumPlayers: utils.AllocPtr(constants.SHOWDOWN_PLAYERS),
				TimeLimit:  utils.AllocPtr(gameDuration),
				GameType:   models.GameTypeSumdayShowdown,
			},
			Players: models.Players{
				&player1.UserID,
				&player2.UserID,
			},
			Round: round,
			ShowdownGamePlayer: []*models.ShowdownGamePlayer{
				{
					IsTie:         false,
					IsWinner:      false,
					UserID:        player1.UserID,
					Wins:          0,
					Score:         0,
					ParticipantID: player1.ParticipantID,
				},
				{
					IsTie:         false,
					IsWinner:      false,
					UserID:        player2.UserID,
					Wins:          0,
					Score:         0,
					ParticipantID: player2.ParticipantID,
				},
			},
		})
		if err != nil {
			return participantsPayloadMap, fmt.Errorf("failed to create games for showdown: %w", err)
		}

		if playerGames == nil || len(playerGames) != 1 {
			return participantsPayloadMap, fmt.Errorf("player games is nil")
		}
		player1.InitialGameID = playerGames[0].ID
		player2.InitialGameID = playerGames[0].ID
		participantsPayloadMap[pair.Entity1] = player1
		participantsPayloadMap[pair.Entity2] = player2

		games = append(games, playerGames[0])
	}

	zlog.Debug(ctx, "Games created", zap.Any("games", games))

	zlog.Debug(ctx, "Batch inserting games", zap.Any("games", games))

	if err := s.gameRepo.CreateManyGames(ctx, games); err != nil {
		return participantsPayloadMap, fmt.Errorf("failed to bulk insert games: %w", err)
	}

	return participantsPayloadMap, nil
}
