package showdownFixtures

import (
	"context"
	"sort"

	"matiksOfficial/matiks-server-go/internal/domain/swissTournament/matchmaking"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
	"matiksOfficial/matiks-server-go/utils/systemErrors"

	"go.uber.org/zap"
)

func (s *showdownFixtures) getMatchedPairs(ctx context.Context, participantsPayload []*models.ShowdownFixturesInternalPayload, round int) (*models.MatchmakingResponse, error) {
	zlog.Debug(ctx, "Fetching matched pairs", zap.Int("round", round), zap.Int("totalParticipants", len(participantsPayload)))

	entities, err := parseParticipantsPayload(participantsPayload, round)
	if err != nil {
		zlog.Error(ctx, "Failed to parse participants payload", err, zap.Int("round", round))
		return nil, err
	}

	sort.Slice(entities, func(i, j int) bool {
		if entities[i] == nil || entities[j] == nil {
			return false
		}
		return entities[i].MatchmakingScore > entities[j].MatchmakingScore
	})

	matchMakingResponse, err := matchmaking.Matchmake(ctx, &models.MatchmakingPayload{
		Entities: entities,
	}, true, round)
	if err != nil {
		zlog.Error(ctx, "Failed to match make", err, zap.Int("round", round))
		return nil, err
	}

	if matchMakingResponse == nil {
		return nil, systemErrors.ErrMatchMakingResponseNotFound
	}

	zlog.Debug(ctx, "Matched pairs", zap.Int("round", round), zap.Any("lengthOfMatchMakingResponse", len(matchMakingResponse.Pairs)))
	return matchMakingResponse, nil
}
