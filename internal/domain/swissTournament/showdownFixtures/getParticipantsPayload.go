package showdownFixtures

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
	"matiksOfficial/matiks-server-go/utils/systemErrors"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *showdownFixtures) getParticipantsPayload(ctx context.Context, showdownId primitive.ObjectID, round int) ([]*models.ShowdownFixturesInternalPayload, map[primitive.ObjectID]*models.ShowdownFixturesInternalPayload, error) {
	participantsPayloadMap := make(map[primitive.ObjectID]*models.ShowdownFixturesInternalPayload)
	var participantsPayload []*models.ShowdownFixturesInternalPayload
	var err error
	if round == 1 {
		participantsPayload, err = s.showdownParticipantRepo.GetFixturesInternalPayload(ctx, showdownId)
		if err != nil {
			zlog.Error(ctx, "Failed to get fixtures internal payload", err, zap.String("showdownID", showdownId.Hex()))
			return nil, nil, err
		}
		zlog.Debug(ctx, "Participants payload", zap.String("showdownID", showdownId.Hex()), zap.Int("participantsPayloadXLen", len(participantsPayload)))
	} else {
		leaderboardEntities, err := s.showdownCache.GetAllLeaderboardEntities(ctx, showdownId)
		if err != nil {
			zlog.Error(ctx, "Failed to get leaderboard entities", err, zap.String("showdownID", showdownId.Hex()))
			return nil, nil, err
		}
		for _, leaderboardEntity := range leaderboardEntities {
			if leaderboardEntity == nil {
				zlog.Error(ctx, "Leaderboard entity is nil", nil, zap.String("showdownID", showdownId.Hex()))
				return nil, nil, systemErrors.ErrParticipantNotFound
			}
			participantID, err := primitive.ObjectIDFromHex(leaderboardEntity.ID)
			if err != nil {
				zlog.Error(ctx, "Failed to convert user id to primitive object id", err, zap.String("showdownID", showdownId.Hex()))
				return nil, nil, err
			}
			showdownParticipant, err := s.showdownService.GetShowdownParticipant(ctx, showdownId, participantID)
			if err != nil {
				zlog.Error(ctx, "Failed to get showdown participant", err, zap.String("showdownID", showdownId.Hex()))
				return nil, nil, err
			}
			if showdownParticipant == nil {
				zlog.Error(ctx, "Showdown participant is nil", nil, zap.String("showdownID", showdownId.Hex()))
				return nil, nil, systemErrors.ErrParticipantNotFound
			}
			participantsPayload = append(participantsPayload, &models.ShowdownFixturesInternalPayload{
				ParticipantID:           *showdownParticipant.ID,
				TotalScore:              showdownParticipant.TotalScore,
				PreviousMatchedEntities: showdownParticipant.RecentOpponents,
				HadBye:                  showdownParticipant.HadABye,
				UserID:                  showdownParticipant.UserID,
			})
		}
	}

	if len(participantsPayload) == 0 {
		zlog.Error(ctx, "Participants payload is empty", nil, zap.String("showdownID", showdownId.Hex()))
		return nil, nil, systemErrors.ErrParticipantNotFound
	}
	for _, participant := range participantsPayload {
		if participant == nil {
			zlog.Error(ctx, "Participant is nil", nil, zap.String("showdownID", showdownId.Hex()))
			return nil, nil, systemErrors.ErrParticipantNotFound
		}
		participantsPayloadMap[participant.ParticipantID] = participant
	}
	return participantsPayload, participantsPayloadMap, nil
}
