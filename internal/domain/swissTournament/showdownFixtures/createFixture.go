package showdownFixtures

import (
	"context"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/scheduler"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
	"matiksOfficial/matiks-server-go/utils/systemErrors"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *showdownFixtures) CreateFixture(ctx context.Context, showdownId primitive.ObjectID, currentRound int) error {
	zlog.Debug(ctx, "Creating fixture", zap.String("showdownID", showdownId.Hex()), zap.Int("round", currentRound))
	showdown, err := s.getShowdown(ctx, showdownId)
	if err != nil {
		zlog.Error(ctx, "Failed to get showdown", err, zap.String("showdownID", showdownId.Hex()))
		return err
	}

	if currentRound < 1 || currentRound > showdown.Rounds {
		return systemErrors.ErrInvalidRoundInputInFixture
	}

	if currentRound == 1 {
		err = s.UpdateShowdownStatus(ctx, showdownId, models.ShowdownContestStatusLive)
		if err != nil {
			zlog.Error(ctx, "Failed to update Showdown status", err, zap.String("showdownID", showdownId.Hex()))
			return err
		}
	}

	if currentRound != showdown.CurrentRound {
		err = s.UpdateShowdownRound(ctx, showdownId, currentRound)
		if err != nil {
			zlog.Error(ctx, "Failed to update Showdown current round", err, zap.String("showdownID", showdownId.Hex()))
			return err
		}
	}

	zlog.Debug(ctx, "Fetching participants payload", zap.String("showdownID", showdownId.Hex()))

	participantsPayload, participantsPayloadMap, err := s.getParticipantsPayload(ctx, showdownId, currentRound)
	if err != nil {
		zlog.Error(ctx, "Failed to get participants payload", err, zap.String("showdownID", showdownId.Hex()))
		return err
	}

	zlog.Debug(ctx, "Fetching matched pairs", zap.String("showdownID", showdownId.Hex()))

	matchedPairs, err := s.getMatchedPairs(ctx, participantsPayload, currentRound)
	if err != nil {
		zlog.Error(ctx, "Failed to get matched pairs", err, zap.String("showdownID", showdownId.Hex()))
		return err
	}

	zlog.Debug(ctx, "Batch inserting fixtures", zap.String("showdownID", showdownId.Hex()))

	fixtures, err := s.batchInsertFixtures(ctx, showdownId, currentRound, matchedPairs)
	if err != nil {
		zlog.Error(ctx, "Failed to batch insert fixtures", err, zap.String("showdownID", showdownId.Hex()))
		return err
	}
	if fixtures == nil || len(fixtures) == 0 {
		zlog.Error(ctx, "fixtures is nil", systemErrors.ErrFixturesNotFound, zap.String("showdownID", showdownId.Hex()))
		return systemErrors.ErrFixturesNotFound
	}

	zlog.Debug(ctx, "Batch inserting games", zap.String("showdownID", showdownId.Hex()))

	participantsPayloadMap, err = s.batchInsertGames(ctx, showdownId, currentRound, matchedPairs, participantsPayloadMap, showdown.RoundConfig.NumOfGames, showdown.RoundConfig.GameDuration)
	if err != nil {
		zlog.Error(ctx, "Failed to batch insert games", err, zap.String("showdownID", showdownId.Hex()))
		return err
	}

	zlog.Debug(ctx, "Batch updating participants", zap.String("showdownID", showdownId.Hex()))

	if err := s.batchUpdateMatchedParticipants(ctx, showdownId, currentRound, matchedPairs, participantsPayloadMap); err != nil {
		zlog.Error(ctx, "Failed to batch update participants", err, zap.String("showdownID", showdownId.Hex()))
		return err
	}

	zlog.Debug(ctx, "Notifying users", zap.String("showdownID", showdownId.Hex()))

	if err := s.NotifyUserBatched(ctx, showdownId, currentRound, fixtures); err != nil {
		zlog.Error(ctx, "Failed to notify users", err, zap.String("showdownID", showdownId.Hex()))
		return err
	}

	nextRound := currentRound + 1
	if nextRound > showdown.Rounds {
		return nil
	}

	nextRoundStarts := showdown.RoundTime*currentRound + showdown.GapBwRounds*(currentRound-1)
	createFixtureTime := showdown.StartTime.Add(time.Duration(nextRoundStarts) * time.Second)

	err = scheduler.Schedule(ctx, s.sortedSet, &models.SchedulerPayload{
		Type: models.FixtureCreation,
		Action: models.ShowdownRoundActionPayload{
			ShowdownId: showdownId,
			Round:      nextRound,
		},
		ContextMap: utils.GetContextValuesMap(ctx),
	}, createFixtureTime)
	if err != nil {
		zlog.Error(ctx, "Failed to schedule showdown fixture creation", err, zap.String("showdownID", showdownId.Hex()), zap.Time("fixtureCreationTime", createFixtureTime))
		return err
	}

	return nil
}
