package showdownFixtures

import (
	"context"
	"fmt"
	"sync"

	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
	"matiksOfficial/matiks-server-go/utils/slicesustils"
	"matiksOfficial/matiks-server-go/utils/systemErrors"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

const batchSize = 50

func (s *showdownFixtures) NotifyUserBatched(ctx context.Context, showdownId primitive.ObjectID, round int, fixtures []*models.Fictures) error {
	slices := slicesustils.SplitSlice(fixtures, batchSize)
	numOfGoRoutines := len(slices)
	var wg sync.WaitGroup
	wg.Add(numOfGoRoutines)
	for i := range numOfGoRoutines {
		go func() {
			defer wg.Done()
			fixtures := slices[i]
			if err := s.NotifyUsersAsFixturesCreated(ctx, showdownId, round, fixtures); err != nil {
				zlog.Error(ctx, "failed to notify users", err, zap.String("showdownID", showdownId.Hex()))
			}
		}()
	}
	wg.Wait()
	return nil
}

func (s *showdownFixtures) NotifyUsersAsFixturesCreated(ctx context.Context, showdownId primitive.ObjectID, round int, fixtures []*models.Fictures) error {
	if fixtures == nil || len(fixtures) == 0 {
		return systemErrors.ErrFixturesNotFound
	}

	showdown, err := s.GetShowdownById(ctx, showdownId)
	if err != nil || showdown == nil {
		return fmt.Errorf("showdow not found %w", err)
	}
	showdownNotificationPayload := models.ShowdownMinifiedNotificationPayload{
		ID:                showdown.ID,
		Name:              showdown.Name,
		Description:       showdown.Description,
		CurrentRound:      showdown.CurrentRound,
		StartTime:         showdown.StartTime,
		RegistrationCount: showdown.RegistrationCount,
	}

	for _, fixture := range fixtures {
		if fixture == nil {
			zlog.Error(ctx, "fixture found nil while notifying users", nil, zap.String("showdownID", showdownId.Hex()))
			continue
		}
		for _, participantId := range fixture.Participants {
			zlog.Debug(ctx, "Notifying matched players", zap.String("user", participantId.Hex()))
			participant, err := s.showdownService.GetShowdownParticipant(ctx, showdownId, participantId)
			if err != nil {
				zlog.Debug(ctx, "Failed to participant from cache", zap.Any("user", participantId))
				continue
			}
			if participant == nil {
				zlog.Debug(ctx, "Failed to participant from cache", zap.Any("user", participantId))
				continue
			}
			var currentUserParticipantDto models.CurrentShowdonParticipant
			err = MapShowdownParticipantToCurrentParticipant(participant, &currentUserParticipantDto, round)
			if err != nil {
				zlog.Debug(ctx, "Failed to map participant to currentUserParticipantDto", zap.Any("user", participantId))
				continue
			}
			data := models.ShowdownFicturesCreatedEvent{
				Showdown:       &showdownNotificationPayload,
				CurrentFixture: fixture,
				Participant:    &currentUserParticipantDto,
			}

			err = s.coreService.PublishUserEvent(ctx, participant.UserID, &data)
			if err != nil {
				zlog.Error(ctx, "failed to publish fixture creation event", err)
				return err
			}
		}
	}
	return nil
}
