package showdownFixtures

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
	"matiksOfficial/matiks-server-go/utils/systemErrors"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *showdownFixtures) batchUpdateMatchedParticipants(ctx context.Context, showdownId primitive.ObjectID, round int, matchedPairs *models.MatchmakingResponse, participantsPayloadMap map[primitive.ObjectID]*models.ShowdownFixturesInternalPayload) error {
	if matchedPairs == nil {
		return systemErrors.ErrMatchMakingResponseNotFound
	}
	if len(matchedPairs.Pairs) == 0 {
		return systemErrors.ErrMatchMakingResponsePairsNotFound
	}
	if participantsPayloadMap == nil {
		return systemErrors.ErrParticipantsPayloadMapNotFound
	}

	zlog.Debug(ctx, "Batch updating participants", zap.Int("round", round), zap.Any("matchedPairs", matchedPairs))
	for _, pair := range matchedPairs.Pairs {
		err := s.updatePair(ctx, showdownId, round, pair, participantsPayloadMap)
		if err != nil {
			return err
		}
	}

	if matchedPairs.ByeEntity != nil {
		err := s.updateByeParticipant(ctx, showdownId, round, *matchedPairs.ByeEntity, participantsPayloadMap)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *showdownFixtures) updatePair(ctx context.Context, showdownId primitive.ObjectID, currentRound int, matchedPair *models.MatchmakingPair, participantsPayloadMap map[primitive.ObjectID]*models.ShowdownFixturesInternalPayload) error {
	if matchedPair == nil {
		return systemErrors.ErrMatchMakingPairNotFound
	}

	err := s.updateParticipant(ctx, showdownId, currentRound, matchedPair.Entity1, matchedPair.Entity2, participantsPayloadMap)
	if err != nil {
		return err
	}
	err = s.updateParticipant(ctx, showdownId, currentRound, matchedPair.Entity2, matchedPair.Entity1, participantsPayloadMap)
	if err != nil {
		return err
	}

	return nil
}

func (s *showdownFixtures) updateParticipant(ctx context.Context, showdownId primitive.ObjectID, currentRound int, player, opponent primitive.ObjectID, participantsPayloadMap map[primitive.ObjectID]*models.ShowdownFixturesInternalPayload) error {
	participant, ok := participantsPayloadMap[player]
	if !ok {
		return systemErrors.ErrParticipantNotFound
	}

	if participant == nil {
		return systemErrors.ErrParticipantNotFound
	}
	showdownParticipant, err := s.showdownService.GetShowdownParticipant(ctx, showdownId, player)
	if err != nil {
		zlog.Error(ctx, "Failed to get showdown participant", err, zap.String("showdownID", showdownId.Hex()), zap.String("player", player.Hex()))
		return err
	}
	if showdownParticipant == nil {
		zlog.Error(ctx, "showdown participant not found", nil, zap.String("showdownID", showdownId.Hex()), zap.String("player", player.Hex()))
		return systemErrors.ErrShowdownParticipantNotFound
	}
	showdownParticipant.RecentOpponents = append(showdownParticipant.RecentOpponents, opponent)

	if participant.InitialGameID == nil {
		zlog.Error(ctx, "initial game ID not found", nil, zap.String("showdownID", showdownId.Hex()), zap.String("player", player.Hex()))
		return systemErrors.ErrInitialGameIDNotFound
	}

	showdownParticipant.Rounds = append(showdownParticipant.Rounds, &models.ShowdownRound{
		Round:        currentRound,
		PlayerStatus: models.RoundPlayerStatusPendingJoin,
		Score:        0,
		Games:        []models.ObjectID{*participant.InitialGameID},
	})
	err = s.UpdateShowdownParticipant(ctx, currentRound, showdownParticipant, false)
	if err != nil {
		zlog.Error(ctx, "Failed to update showdown participant", err, zap.String("showdownID", showdownId.Hex()), zap.String("player", player.Hex()))
		return err
	}

	return nil
}

func (s *showdownFixtures) updateByeParticipant(ctx context.Context, showdownId primitive.ObjectID, currentRound int, player primitive.ObjectID, participantsPayloadMap map[primitive.ObjectID]*models.ShowdownFixturesInternalPayload) error {
	participant, ok := participantsPayloadMap[player]
	if !ok {
		zlog.Error(ctx, "participant not found", nil, zap.String("showdownID", showdownId.Hex()), zap.String("player", player.Hex()))
		return systemErrors.ErrParticipantNotFound
	}

	if participant == nil {
		zlog.Error(ctx, "participant is nil", nil, zap.String("showdownID", showdownId.Hex()), zap.String("player", player.Hex()))
		return systemErrors.ErrParticipantNotFound
	}
	showdownParticipant, err := s.showdownService.GetShowdownParticipant(ctx, showdownId, player)
	if err != nil {
		zlog.Error(ctx, "Failed to get showdown participant", err, zap.String("showdownID", showdownId.Hex()), zap.String("player", player.Hex()))
		return err
	}
	showdownParticipant.HadABye = true
	showdownParticipant.TotalScore += 1
	showdownParticipant.Rounds = append(showdownParticipant.Rounds, &models.ShowdownRound{
		Round:        currentRound,
		PlayerStatus: models.RoundPlayerStatusBye,
		Score:        1,
		Games:        []models.ObjectID{},
		HasJoined:    true,
	})

	err = s.UpdateShowdownParticipant(ctx, currentRound, showdownParticipant, true)
	if err != nil {
		zlog.Error(ctx, "Failed to update showdown participant", err, zap.String("showdownID", showdownId.Hex()), zap.String("player", player.Hex()))
		return err
	}

	return nil
}
