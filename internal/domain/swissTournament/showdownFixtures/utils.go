package showdownFixtures

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/domain/showdown"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
	"matiksOfficial/matiks-server-go/utils/systemErrors"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *showdownFixtures) getShowdown(ctx context.Context, showdownId primitive.ObjectID) (*models.Showdown, error) {
	showdown, err := s.GetShowdownById(ctx, showdownId)
	if err != nil {
		zlog.Error(ctx, "Failed to get showdown", err, zap.String("showdownID", showdownId.Hex()))
		return nil, err
	}

	err = s.ValidateShowdown(showdown)
	if err != nil {
		zlog.Error(ctx, "Failed to validate showdown", err, zap.String("showdownID", showdownId.Hex()))
		return nil, err
	}
	return showdown, nil
}

func (s *showdownFixtures) UpdateShowdownStatus(ctx context.Context, showdownId primitive.ObjectID, status models.ShowdownContestStatus) error {
	update := bson.M{"$set": bson.M{"status": status}}
	err := s.showdownRepo.UpdateOne(ctx, bson.M{"_id": showdownId}, update)
	if err != nil {
		zlog.Error(ctx, "Failed to update status of showdown with id ", err, zap.String("showdownID", showdownId.Hex()))
		return err
	}
	showdown, err := s.showdownCache.GetShowdown(ctx, showdownId)
	if err != nil {
		zlog.Error(ctx, "Failed to get showdown details with in cache "+showdownId.Hex(), err)
		return err
	}
	showdown.Status = status
	err = s.showdownCache.SetShowdown(ctx, showdown)
	if err != nil {
		zlog.Error(ctx, "Failed to update status of showdown with in cache "+showdownId.Hex(), err)
		return err
	}
	return nil
}

func (s *showdownFixtures) UpdateShowdownRound(ctx context.Context, showdownId primitive.ObjectID, currentRound int) error {
	update := bson.M{"$set": bson.M{"currentRound": currentRound}}
	err := s.showdownRepo.UpdateOne(ctx, bson.M{"_id": showdownId}, update)
	if err != nil {
		zlog.Error(ctx, "Failed to update current round of showdown with id ", err, zap.String("showdownID", showdownId.Hex()))
		return err
	}
	showdown, err := s.showdownCache.GetShowdown(ctx, showdownId)
	if err != nil {
		zlog.Error(ctx, "Failed to get showdown details with in cache "+showdownId.Hex(), err)
		return err
	}
	showdown.CurrentRound = currentRound
	err = s.showdownCache.SetShowdown(ctx, showdown)
	if err != nil {
		zlog.Error(ctx, "Failed to update current round of showdown with in cache "+showdownId.Hex(), err)
		return err
	}
	return nil
}

func getIndexOfDivisionForRound(currentRound, numOfParticipants int) int {
	if currentRound >= numOfParticipants {
		return 0
	}
	if numOfParticipants%2 == 0 && currentRound%2 == 0 {
		return numOfParticipants - currentRound - 1
	}

	if numOfParticipants%2 == 1 && currentRound%2 == 1 {
		return numOfParticipants - currentRound - 1
	}

	return numOfParticipants - currentRound - 2
}

func parseParticipantsPayload(participantsPayload []*models.ShowdownFixturesInternalPayload, currentRound int) ([]*models.MatchmakingEntity, error) {
	matchMaking := matchMakingGetter(currentRound)
	entities := make([]*models.MatchmakingEntity, len(participantsPayload))
	for i, participant := range participantsPayload {
		score, err := matchMaking(participant)
		if err != nil {
			return nil, err
		}
		entities[i] = &models.MatchmakingEntity{
			ID:                      participant.ParticipantID,
			PreviousMatchedEntities: participant.PreviousMatchedEntities,
			MatchmakingScore:        score,
			HadBye:                  participant.HadBye,
		}
	}

	return entities, nil
}

func matchMakingGetter(currentRound int) func(*models.ShowdownFixturesInternalPayload) (int, error) {
	if currentRound == 1 {
		return func(participant *models.ShowdownFixturesInternalPayload) (int, error) {
			if participant == nil {
				return 0, fmt.Errorf("participant is nil")
			}
			return participant.Rating, nil
		}
	}
	return func(participant *models.ShowdownFixturesInternalPayload) (int, error) {
		if participant == nil {
			return 0, fmt.Errorf("participant is nil")
		}
		return int(participant.TotalScore), nil
	}
}

func MapMatchMakingPairToFixture(pair *models.MatchmakingPair, showdownId primitive.ObjectID, round int) *models.Fictures {
	if pair == nil {
		return nil
	}
	return &models.Fictures{
		ID:           primitive.NewObjectID(),
		ShowdownID:   showdownId,
		Round:        round,
		Participants: []primitive.ObjectID{pair.Entity1, pair.Entity2},
	}
}

func (s *showdownFixtures) GetShowdownById(ctx context.Context, id primitive.ObjectID) (*models.Showdown, error) {
	showdown, err := s.showdownCache.GetShowdown(ctx, id)
	if err != nil {
		showdown, err = s.showdownRepo.FindByID(ctx, id)
		if err != nil {
			return nil, err
		}
		err = s.showdownCache.SetShowdown(ctx, showdown)
		if err != nil {
			zlog.Error(ctx, "Failed to set showdown in cache", err, zap.String("showdownID", id.Hex()))
		}
	}
	return showdown, nil
}

func MapShowdownParticipantToCurrentParticipant(participant *models.ShowdownParticipant, currentParticipant *models.CurrentShowdonParticipant, currentRound int) error {
	if participant == nil {
		return fmt.Errorf("participant is nil")
	}
	if currentParticipant == nil {
		return fmt.Errorf("current participant is nil")
	}
	currentParticipant.ShowdownID = &participant.ShowdownID
	currentParticipant.UserID = &participant.UserID
	currentParticipant.Rounds = participant.Rounds
	currentParticipant.TotalScore = participant.TotalScore
	for _, roundData := range participant.Rounds {
		if roundData.Round == currentRound {
			currentParticipant.CurrentRound = roundData
			gamesLen := len(roundData.Games)
			if gamesLen > 0 {
				currentParticipant.CurrentGame = &roundData.Games[gamesLen-1]
			}
			break
		}
	}
	return nil
}

func (s *showdownFixtures) ValidateShowdown(showdown *models.Showdown) error {
	if showdown == nil {
		return systemErrors.ErrShowdownCannotBeNil
	}

	if showdown.Rounds < 1 {
		return systemErrors.ErrInvalidRoundInputInFixture
	}
	return nil
}

func (s *showdownFixtures) UpdateShowdownParticipant(ctx context.Context, currentRound int, showdownParticipant *models.ShowdownParticipant, shouldPublishUpdateEvent bool) error {
	if showdownParticipant == nil {
		return systemErrors.ErrShowdownParticipantNotFound
	}
	err := s.showdownService.SetShowdownParticipant(ctx, showdownParticipant)
	if err != nil {
		zlog.Error(ctx, "Failed to set showdown participant in cache", err, zap.String("showdownID", showdownParticipant.ShowdownID.Hex()), zap.String("userID", showdownParticipant.UserID.Hex()))
		return err
	}
	if shouldPublishUpdateEvent {
		currentUserParticipantDto := models.CurrentShowdonParticipant{}
		err = showdown.MapShowdownParticipantToCurrentParticipant(showdownParticipant, &currentUserParticipantDto, currentRound)
		if err != nil {
			return err
		}
		err = s.coreService.PublishUserEvent(ctx, showdownParticipant.UserID, &models.ShowdownParticipantUpdatedEvent{
			Participant: &currentUserParticipantDto,
		})
		if err != nil {
			zlog.Error(ctx, "Failed to publish ShowdownParticipantUpdatedEvent ", err)
			return err
		}
	}
	return nil
}
