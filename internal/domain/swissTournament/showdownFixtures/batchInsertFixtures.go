package showdownFixtures

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"
	"matiksOfficial/matiks-server-go/utils/systemErrors"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *showdownFixtures) batchInsertFixtures(ctx context.Context, showdownId primitive.ObjectID, currentRound int, matchedPairs *models.MatchmakingResponse) ([]*models.Fictures, error) {
	zlog.Debug(ctx, "Batch inserting fixtures", zap.String("fixtures", fmt.Sprint(matchedPairs)))

	if err := validateBatchInsertFixturesInput(matchedPairs); err != nil {
		return nil, err
	}

	fixtures := make([]*models.Fictures, 0, len(matchedPairs.Pairs))
	for _, pair := range matchedPairs.Pairs {
		if pair == nil {
			continue
		}
		fixture := &models.Fictures{
			ID:           primitive.NewObjectID(),
			ShowdownID:   showdownId,
			Round:        currentRound,
			Participants: []primitive.ObjectID{pair.Entity1, pair.Entity2},
		}
		fixtures = append(fixtures, fixture)
		zlog.Debug(ctx, "Batch inserted fixture", zap.Any("fixture", fixture))
	}
	if err := s.fixtureRepo.InsertMany(ctx, fixtures); err != nil {
		zlog.Error(ctx, "Failed to batch insert fixtures", err, zap.String("showdownID", showdownId.Hex()))
		return nil, err
	}
	return fixtures, nil
}

func validateBatchInsertFixturesInput(matchedPairs *models.MatchmakingResponse) error {
	if matchedPairs == nil {
		return systemErrors.ErrMatchMakingResponseNotFound
	}
	if len(matchedPairs.Pairs) == 0 {
		return systemErrors.ErrMatchMakingResponsePairsNotFound
	}
	return nil
}
