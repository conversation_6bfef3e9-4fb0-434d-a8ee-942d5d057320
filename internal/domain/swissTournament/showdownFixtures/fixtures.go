package showdownFixtures

import (
	"context"

	"matiksOfficial/matiks-server-go/internal/domain"
	"matiksOfficial/matiks-server-go/internal/domain/swissTournament"
	"matiksOfficial/matiks-server-go/internal/infrastructure/cache"
	"matiksOfficial/matiks-server-go/internal/infrastructure/repository"
	"matiksOfficial/matiks-server-go/internal/infrastructure/sortedset"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.uber.org/fx"
)

type showdownFixtures struct {
	showdownParticipantRepo repository.ShowdownParticipantRepository
	fixtureRepo             repository.FixturesRepository
	showdownRepo            repository.ShowdownRepository
	showdownCache           cache.ShowdownCache
	gameService             domain.GameStore
	gameRepo                repository.GameRepository
	coreService             domain.CoreLogicStore
	sortedSet               sortedset.SortedSet
	showdownService         domain.ShowdownStore
}

func NewShowdownFixture(
	lc fx.Lifecycle,
	showdownParticipantRepo repository.ShowdownParticipantRepository,
	fixtureRepo repository.FixturesRepository,
	showdownRepo repository.ShowdownRepository,
	cacheInstance cache.Cache,
	leaderboardCacheInstance cache.LeaderboardCache,
	gameService domain.GameStore,
	gameRepo repository.GameRepository,
	sortedSet sortedset.SortedSet,
	coreService domain.CoreLogicStore,
	showdownService domain.ShowdownStore,
) swissTournament.ShowdownFixturesStore {
	lc.Append(
		fx.Hook{
			OnStart: func(ctx context.Context) error {
				zlog.Info(ctx, "Showdown fixture started")
				return nil
			},
			OnStop: func(ctx context.Context) error {
				zlog.Info(ctx, "Showdown fixture stopped")
				return nil
			},
		},
	)

	return &showdownFixtures{
		showdownParticipantRepo: showdownParticipantRepo,
		fixtureRepo:             fixtureRepo,
		showdownRepo:            showdownRepo,
		showdownCache:           cache.NewShowdownCacheWrapper(cacheInstance, leaderboardCacheInstance),
		gameService:             gameService,
		coreService:             coreService,
		gameRepo:                gameRepo,
		sortedSet:               sortedSet,
		showdownService:         showdownService,
	}
}
