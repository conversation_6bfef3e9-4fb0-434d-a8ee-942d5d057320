package puzzleGame

import (
	"context"
	"fmt"
	"math/rand/v2"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/game/utils/botutils"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	puzzleGameUtils "matiksOfficial/matiks-server-go/internal/domain/puzzleGame/utils"
	"matiksOfficial/matiks-server-go/internal/domain/puzzleGame/utils/puzzleGameBotutils"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/middleware"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.uber.org/zap"
)

const (
	MAX_WAITING_TIME_FOR_USER = 12 * time.Second
	BOT_AFTER_SEC             = 9 * time.Second
)

type botMatchResult struct {
	matchedUsers []*models.User
}

func (s *service) StartSearchingForPuzzleGame(ctx context.Context, gameConfig *models.PuzzleGameConfigInput) (*bool, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get user ID from context: %w", err)
	}

	zlog.Debug(ctx, "Start Searching for opponent puzzle game...")

	if err := puzzleGameUtils.ValidatePuzzleGameConfig(gameConfig); err != nil {
		return nil, fmt.Errorf("invalid game configuration: %w", err)
	}

	currentUser, err := s.userService.GetUserByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	config := models.PuzzleGameConfig{
		TimeLimit:  gameConfig.TimeLimit,
		NumPlayers: gameConfig.NumPlayers,
		GameType:   gameConfig.GameType,
	}

	matchedUsers, err := s.playersQueue.AddUser(ctx, currentUser, config)
	if err != nil {
		return nil, fmt.Errorf("failed to add user to waiting queue: %w", err)
	}

	if matchedUsers == nil {
		searchCtx, cancel := context.WithTimeout(utils.DeriveContextWithoutCancel(ctx), MAX_WAITING_TIME_FOR_USER+2*time.Second)
		defer cancel()

		botMatchResultChan := make(chan *botMatchResult, 1)
		go s.handleSearchTimeout(searchCtx, currentUser, config, botMatchResultChan)

		select {
		case res := <-botMatchResultChan:
			if res != nil {
				game, err := s.startGameWithBot(ctx, res.matchedUsers, config)
				if err != nil {
					zlog.Error(ctx, "Failed to start game with bot", err)
					return nil, err
				}

				s.notifyMatchedPlayers(ctx, game, res.matchedUsers)
				s.scheduleEndPuzzleGame(ctx, game)
			} else {
				zlog.Debug(ctx, "Game not found, skipping notifications")
			}
		case <-ctx.Done():
			zlog.Debug(ctx, "Parent context cancelled")
		}

		success := true
		return &success, nil
	}

	players := s.getPlayersFromUsers(matchedUsers)
	game, err := s.createGameWithPlayers(ctx, players, config)
	if err != nil {
		return nil, fmt.Errorf("failed to create game: %w", err)
	}

	s.notifyMatchedPlayers(ctx, game, matchedUsers)

	s.scheduleEndPuzzleGame(ctx, game)

	zlog.Debug(ctx, "Game created successfully", zap.String("gameID", game.ID.Hex()))

	success := true
	return &success, nil
}

func (s *service) handleSearchTimeout(ctx context.Context, user *models.User, gameConfig models.PuzzleGameConfig, resultChan chan<- *botMatchResult) {
	zlog.Debug(ctx, "Entering handleSearchTimeout", zap.Any("gameConfig", gameConfig))

	botTimer := time.NewTimer(BOT_AFTER_SEC)
	defer botTimer.Stop()

	zlog.Debug(ctx, "Starting human bot timer", zap.Duration("duration", BOT_AFTER_SEC))

	select {
	case <-botTimer.C:
		zlog.Debug(ctx, "bot timer completed")

		zlog.Debug(ctx, "Attempting to start game with human bot")
		res := s.handleHumanBotGame(ctx, user, gameConfig)
		if res != nil {
			resultChan <- res
			zlog.Debug(ctx, "Exiting handleSearchTimeout after successful human bot match")
			return
		}
		zlog.Debug(ctx, "No suitable human bot found, proceeding with regular bot")
		zlog.Debug(ctx, "Attempting to handle regular bot game")
		res = s.handleBotGame(ctx, user, gameConfig)
		resultChan <- res
		zlog.Debug(ctx, "Regular bot game handling completed", zap.Bool("matchFound", res != nil))
		return

	case <-ctx.Done():
		// TODO: notify user to search again or add retry logic @deepak
		zlog.Debug(ctx, "Search context cancelled before bot attempt", zap.Error(ctx.Err()))
		resultChan <- nil
		zlog.Debug(ctx, "Exiting handleSearchTimeout due to context cancellation")
		return
	}
}

func (s *service) handleBotGame(ctx context.Context, user *models.User, gameConfig models.PuzzleGameConfig) *botMatchResult {
	zlog.Debug(ctx, "Entering handleBotGame", zap.Any("gameConfig", gameConfig))

	if ok, err := s.playersQueue.IsUserPresentInWaitingList(ctx, user); err != nil {
		zlog.Error(ctx, "Failed to check if user is in waiting list", err)
		return nil
	} else if ok {
		zlog.Debug(ctx, "User found in waiting list")

		zlog.Debug(ctx, "Attempting to get bot user")
		botUser, err := botutils.GetBotUser(ctx, user, s.userRepo, s.userService, gameConfig, s.storage.Storage)
		if err != nil {
			zlog.Error(ctx, "Failed to get bot user", err)
			return nil
		}
		if botUser == nil {
			zlog.Debug(ctx, "No suitable bot user found")
			return nil
		}
		zlog.Debug(ctx, "Bot user found", zap.String("botUserID", botUser.ID.Hex()))

		matchedUsers := []*models.User{user, botUser}
		zlog.Debug(ctx, "Matched users", zap.String("botUserID", botUser.ID.Hex()))

		if len(matchedUsers) != *gameConfig.NumPlayers {
			zlog.Warn(ctx, "Incorrect number of players for bot game", zap.Int("matchedUsers", len(matchedUsers)), zap.Int("requiredPlayers", *gameConfig.NumPlayers))
			return nil
		}

		zlog.Debug(ctx, "Removing user from waiting list")
		_, err = s.playersQueue.RemoveUser(ctx, user.ID.Hex())
		if err != nil {
			zlog.Error(ctx, "Failed to remove user from queue", err)
			return nil
		}
		zlog.Debug(ctx, "User successfully removed from waiting list")

		zlog.Debug(ctx, "Exiting handleBotGame with successful match", zap.String("botUserID", botUser.ID.Hex()))
		return &botMatchResult{matchedUsers}
	}

	zlog.Debug(ctx, "User not in waiting list, skipping bot game")
	zlog.Debug(ctx, "Exiting handleBotGame without match")
	return nil
}

func (s *service) handleHumanBotGame(ctx context.Context, user *models.User, gameConfig models.PuzzleGameConfig) *botMatchResult {
	zlog.Debug(ctx, "Entering handleHumanBotGame", zap.Any("gameConfig", gameConfig))
	if ok, err := s.playersQueue.IsUserPresentInWaitingList(ctx, user); err != nil {
		zlog.Error(ctx, "Failed to check if user is in waiting list", err)
		return nil
	} else if ok {
		zlog.Debug(ctx, "User found in waiting list")

		probHumanBot := rand.Float64()
		if probHumanBot >= 0.4 {
			zlog.Debug(ctx, "Random skip human bot game")
			return nil
		}

		zlog.Debug(ctx, "Attempting to match human bot", zap.Int("userRating", *user.Rating))
		botUser, err := puzzleGameBotutils.MatchHumanBot(ctx, s.userRepo, s.cache, *user.Rating, user.ID)
		if err != nil {
			zlog.Warn(ctx, "Failed to match human bot", zap.Error(err))
			return nil
		}

		if botUser == nil {
			zlog.Debug(ctx, "No suitable human bot found")
			return nil
		}

		zlog.Debug(ctx, "Human bot matched successfully", zap.String("botUserID", botUser.ID.Hex()))

		matchedUsers := []*models.User{user, botUser}

		if len(matchedUsers) != *gameConfig.NumPlayers {
			zlog.Warn(ctx, "Incorrect number of players for human bot game", zap.Int("matchedUsers", len(matchedUsers)), zap.Int("requiredPlayers", *gameConfig.NumPlayers))
			return nil
		}

		zlog.Debug(ctx, "User still in waiting list, removing")
		_, err = s.playersQueue.RemoveUser(ctx, user.ID.Hex())
		if err != nil {
			zlog.Error(ctx, "Failed to remove user from queue", err)
			return nil
		}
		zlog.Debug(ctx, "User successfully removed from waiting list")

		zlog.Debug(ctx, "Exiting handleHumanBotGame with successful match", zap.String("botUserID", botUser.ID.Hex()))
		return &botMatchResult{matchedUsers}
	}
	zlog.Debug(ctx, "User not in waiting list, skipping bot game")
	zlog.Debug(ctx, "Exiting handleHumanBotGame without match")
	return nil
}

func (s *service) startGameWithBot(ctx context.Context, matchedUsers []*models.User, gameConfig models.PuzzleGameConfig) (*models.PuzzleGame, error) {
	zlog.Debug(ctx, "Entering startGameWithBot", zap.Int("numUsers", len(matchedUsers)), zap.Any("gameConfig", gameConfig))

	players := s.getPlayersFromUsers(matchedUsers)
	zlog.Debug(ctx, "Players created", zap.Int("numPlayers", len(players)))

	game, err := s.createGameWithPlayers(ctx, players, gameConfig)
	if err != nil {
		zlog.Error(ctx, "Failed to create game with bot", err)
		return nil, fmt.Errorf("failed to create game with bot: %w", err)
	}
	zlog.Debug(ctx, "Game created successfully", zap.String("gameID", game.ID.Hex()))

	var botUser *models.User
	for _, u := range matchedUsers {
		if (u.IsHumanBot != nil && *u.IsHumanBot) || (u.IsBot != nil && *u.IsBot) {
			botUser = u
			break
		}
	}

	if botUser == nil {
		zlog.Debug(ctx, "No bot user found in matched users", zap.String("gameID", game.ID.Hex()))
		return nil, fmt.Errorf("no bot user found")
	}

	zlog.Debug(ctx, "Bot user identified", zap.String("botUserID", botUser.ID.Hex()), zap.Bool("isHumanBot", botUser.IsHumanBot != nil && *botUser.IsHumanBot))

	zlog.Debug(ctx, "Starting game with bot", zap.String("botUserID", botUser.ID.Hex()))

	// Run the bot
	go func() {
		traceCtx, span := middleware.WithSpan(utils.DeriveContextWithoutCancel(ctx), "startGameWithBot.runBot")
		defer span.End()
		zlog.Debug(traceCtx, "Starting bot goroutine", zap.String("botUserID", botUser.ID.Hex()))
		err := puzzleGameBotutils.RunPuzzleGameBot(traceCtx, game, botUser, s, s.cache)
		if err != nil {
			zlog.Error(traceCtx, "Failed to run bot", err, zap.String("botUserID", botUser.ID.Hex()))
		}
		zlog.Debug(traceCtx, "Bot goroutine completed", zap.String("botUserID", botUser.ID.Hex()))
	}()

	zlog.Debug(ctx, "Exiting startGameWithBot", zap.String("gameID", game.ID.Hex()), zap.String("botUserID", botUser.ID.Hex()))
	return game, nil
}
