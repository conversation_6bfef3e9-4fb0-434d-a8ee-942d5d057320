package constants

type SubscriptionPrefix string

type SubscriptionPrefixStruct struct {
	GAME_EVENT        SubscriptionPrefix
	PUZZLE_GAME_EVENT SubscriptionPrefix
	USER_EVENT        SubscriptionPrefix
	ONLINE_USERS      SubscriptionPrefix
	MESSAGE_EVENT     SubscriptionPrefix
	GROUP_CHAT_EVENT  SubscriptionPrefix
	SHOWDOWN_EVENT    SubscriptionPrefix
}

var SubscriptionPrefixEnum = SubscriptionPrefixStruct{
	GAME_EVENT:        "GAME_EVENT",
	PUZZLE_GAME_EVENT: "PUZZLE_GAME_EVENT",
	USER_EVENT:        "USER_EVENT",
	ONLINE_USERS:      "ONLINE_USERS",
	MESSAGE_EVENT:     "MESSAGE_EVENT",
	GROUP_CHAT_EVENT:  "GROUP_CHAT_EVENT",
	SHOWDOWN_EVENT:    "SHOWDOWN_EVENT",
}

func (e SubscriptionPrefix) String() string {
	return string(e)
}
