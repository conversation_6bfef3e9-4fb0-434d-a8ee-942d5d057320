package models

import (
	"encoding/json"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type TaskType int

const (
	FixtureCreation TaskType = iota
	UpdateShowdownStatus
	ScheduleShowdownRoundUpdate
	JoinGameShowdownCheck
	ScheduleShowdownEnd
	EndGame
	EndPuzzleGame
	EndGameForShowdown
	CheckShowdownRoundPlayerStatus
	SingleNotificationHandler
)

type SchedulerPayload struct {
	Type       TaskType               `json:"type"`
	Action     interface{}            `json:"action"`
	ContextMap map[string]interface{} `json:"contextMap"`
}

type SchedulerIntermediatePayload struct {
	Type       TaskType               `json:"type"`
	Action     json.RawMessage        `json:"action"`
	ContextMap map[string]interface{} `json:"contextMap"`
}

type ShowdownRoundActionPayload struct {
	ShowdownId primitive.ObjectID `json:"showdownId"`
	Round      int                `json:"round"`
}

type NotifyUsersForFixtureCreationActionPayload struct {
	ShowdownId       primitive.ObjectID `json:"showdownId"`
	CurrentRound     int                `json:"currentRound"`
	ByeParticipantId primitive.ObjectID `json:"ByeParticipantId"`
}

type UpdateShowdownStatusActionPayload struct {
	ShowdownId primitive.ObjectID    `json:"showdownId"`
	Status     ShowdownContestStatus `json:"status"`
}

type EndGameActionPayload struct {
	GameID     primitive.ObjectID `json:"gameId"`
	StartTime  time.Time          `json:"startTime"`
	GameConfig *GameConfig        `json:"gameConfig"`
}

type EndPuzzleGameActionPayload struct {
	GameID     primitive.ObjectID `json:"gameId"`
	StartTime  time.Time          `json:"startTime"`
	GameConfig *PuzzleGameConfig  `json:"gameConfig"`
}
