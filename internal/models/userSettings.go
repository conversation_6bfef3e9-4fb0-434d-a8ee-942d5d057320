package models

import (
	"bytes"
	"fmt"
	"io"
	"strconv"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type UserSettings struct {
	ID             primitive.ObjectID `json:"_id" bson:"_id"`
	UserID         primitive.ObjectID `json:"userId" bson:"userId"`
	PlaySound      bool               `json:"playSound" bson:"playSound"`
	HapticFeedback bool               `json:"hapticFeedback" bson:"hapticFeedback"`
	KeyboardType   KeyboardType       `json:"keyboardType" bson:"keyboardType"`
}

type UpdateSettingsInput struct {
	PlaySound      *bool         `json:"playSound" bson:"playSound"`
	HapticFeedback *bool         `json:"hapticFeedback" bson:"hapticFeedback"`
	KeyboardType   *KeyboardType `json:"keyboardType" bson:"keyboardType"`
}

type CreateSettingsInput struct {
	UserID         primitive.ObjectID `json:"userId" bson:"userId"`
	PlaySound      bool               `json:"playSound" bson:"playSound"`
	HapticFeedback bool               `json:"hapticFeedback" bson:"hapticFeedback"`
	KeyboardType   KeyboardType       `json:"keyboardType" bson:"keyboardType"`
}

type KeyboardType string

const (
	TelephoneKeyboard  KeyboardType = "TELEPHONE"
	CalculatorKeyboard KeyboardType = "CALCULATOR"
)

var AllKeyBoardTypes = []KeyboardType{TelephoneKeyboard, CalculatorKeyboard}

func (e KeyboardType) IsValid() bool {
	switch e {
	case TelephoneKeyboard, CalculatorKeyboard:
		return true
	}
	return false
}

func (e KeyboardType) String() string {
	return string(e)
}

func (e *KeyboardType) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = KeyboardType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid KeyboardType", str)
	}
	return nil
}

func (e KeyboardType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *KeyboardType) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e KeyboardType) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}
