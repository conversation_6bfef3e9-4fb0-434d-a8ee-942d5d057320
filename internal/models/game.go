package models

import (
	"fmt"
	"io"
	"strconv"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Game struct {
	ID *ObjectID `json:"_id" bson:"_id"`

	CreatedBy ObjectID `json:"createdBy" bson:"createdBy"`

	GameCategory       GameCategory `json:"gameCategory" bson:"gameCategory"`
	GameType           GameType     `json:"gameType" bson:"gameType"`
	GameMode           GameMode     `json:"gameMode" bson:"gameMode"`
	GameStatus         GameStatus   `json:"gameStatus" bson:"gameStatus"`
	Config             *GameConfig  `json:"config,omitempty" bson:"config"`
	Players            []*Player    `json:"players" bson:"players"`
	RematchRequestedBy *ObjectID    `json:"rematchRequestedBy,omitempty" bson:"rematchRequestedBy"`

	LeaderBoard        []*LeaderBoardEntry `json:"leaderBoard,omitempty" bson:"leaderBoard"`
	Questions          []*GameQuestion     `json:"questions,omitempty" bson:"questions"`
	EncryptedQuestions []*string           `json:"encryptedQuestions,omitempty" bson:"-"`
	MinifiedQuestions  []*string           `json:"minifiedQuestions" bson:"minifiedQuestions"`

	StartTime *time.Time `json:"startTime,omitempty" bson:"startTime"`
	EndTime   *time.Time `json:"endTime,omitempty" bson:"endTime"`

	SeriesID   *ObjectID `json:"seriesId,omitempty" bson:"seriesId"`
	ShowdownId *ObjectID `json:"showdownId,omitempty" bson:"showdownId,omitempty"`

	CreatedAt *time.Time `json:"createdAt,omitempty" bson:"createdAt"`
	UpdatedAt *time.Time `json:"updatedAt,omitempty" bson:"updatedAt"`

	ShowdownGameConfig *ShowdownGameConfig `json:"showdownGameConfig" bson:"showdownGameConfig"` // Deprecated
}

type GameCategorySpecificConfig struct {
	Category  GameCategory       `json:"category" bson:"category"`
	Blitz     *DefaultGameConfig `json:"blitz,omitempty" bson:"blitz"`
	Classical *DefaultGameConfig `json:"classical,omitempty" bson:"classical"`
	Memory    *DefaultGameConfig `json:"memory,omitempty" bson:"memory"`
	Puzzle    *PuzzleGameConfig  `json:"puzzle,omitempty" bson:"puzzle"`
}

type DefaultGameConfig struct {
	TimeLimit *int `json:"timeLimit,omitempty" bson:"timeLimit"`
}

type DefaultGameModeConfig struct {
	NumPlayers *int `json:"numPlayers,omitempty" bson:"numPlayers"`
}

type GroupPlayGameConfig struct {
	MaxTimePerQuestion *int     `json:"maxTimePerQuestion,omitempty" bson:"maxTimePerQuestion"`
	DifficultyLevel    []int    `json:"difficultyLevel,omitempty" bson:"difficultyLevel"`
	MaxGapBwGame       *int     `json:"maxGapBwGame,omitempty" bson:"maxGapBwGame"`
	MaxPlayers         *int     `json:"maxPlayers,omitempty" bson:"maxPlayers"`
	MinPlayers         *int     `json:"minPlayers,omitempty" bson:"minPlayers"`
	QuestionTags       []string `json:"questionTags,omitempty" bson:"questionTags"`
}

type GameModeSpecificConfig struct {
	Mode             GameMode               `json:"mode" bson:"mode"`
	SumdayShowdown   *ShowdownGameConfig    `json:"sumdayShowdown" bson:"sumdayShowdown"`
	OnlineSearch     *DefaultGameModeConfig `json:"onlineSearch" bson:"onlineSearch"`
	OnlineChallenge  *DefaultGameModeConfig `json:"onlineChallenge" bson:"onlineChallenge"`
	Practice         *DefaultGameModeConfig `json:"practice" bson:"practice"`
	RushWithTime     *DefaultGameModeConfig `json:"rushWithTime" bson:"rushWithTime"`
	RushWithoutTime  *DefaultGameModeConfig `json:"rushWithoutTime" bson:"rushWithoutTime"`
	GroupPlay        *GroupPlayGameConfig   `json:"groupPlay" bson:"groupPlay"`
	PlayViaLink      *DefaultGameModeConfig `json:"playViaLink" bson:"playViaLink"`
	SurvivalSaturday *DefaultGameModeConfig `json:"survivalSaturday" bson:"survivalSaturday"`
}

type GameTypeSpecificConfig struct {
	Type GameType `json:"type" bson:"type"`
}

type MinifiedGame struct {
	ID          ObjectID            `json:"_id" bson:"_id"`
	Players     []*Player           `json:"players" bson:"players"`
	Config      *GameConfig         `json:"config,omitempty" bson:"config,omitempty"`
	LeaderBoard []*LeaderBoardEntry `json:"leaderBoard,omitempty" bson:"leaderBoard,omitempty"`
	StartTime   *time.Time          `json:"startTime,omitempty" bson:"startTime,omitempty"`
	EndTime     *time.Time          `json:"endTime,omitempty" bson:"endTime,omitempty"`
}

type ShowdownGameConfig struct {
	IsRoundEnded        bool                  `json:"isRoundEnded" bson:"isRoundEnded"`
	NextGameID          *ObjectID             `json:"nextGameId" bson:"nextGameId"`
	NextGameStartsAt    *time.Time            `json:"nextGameStartsAt" bson:"nextGameStartsAt"`
	TotalGamesPlayed    int                   `json:"totalGamesPlayed" bson:"totalGamesPlayed"`
	NumOfGames          int                   `json:"numOfGames" bson:"numOfGames"`
	ShowdownGamePlayer  []*ShowdownGamePlayer `json:"showdownGamePlayer" bson:"showdownGamePlayer"`
	HasOpponentNotShown bool                  `json:"hasOpponentNotShown" bson:"hasOpponentNotShown"`
	Round               int                   `json:"round" bson:"round"`
}

type ShowdownGamePlayer struct {
	IsTie         bool     `json:"isTie" bson:"isTie"`
	IsWinner      bool     `json:"isWinner" bson:"isWinner"`
	UserID        ObjectID `json:"userId" bson:"userId"`
	Wins          float64  `json:"wins" bson:"wins"`
	Score         float64  `json:"score" bson:"score"`
	ParticipantID ObjectID `json:"participantId" bson:"participantId"`
}

type Player struct {
	UserID      ObjectID     `json:"userId" bson:"userId"`
	Rating      *int         `json:"rating,omitempty" bson:"rating,omitempty"`
	StatikCoins int          `json:"statikCoins,omitempty" bson:"statikCoins,omitempty"`
	Status      PlayerStatus `json:"status,omitempty" bson:"status,omitempty"`
	TimeLeft    *int         `json:"timeLeft" bson:"timeLeft"`
}

type GameQuestion struct {
	Question    *Question         `json:"question,omitempty" bson:"question,omitempty"`
	Submissions []*Submission     `json:"submissions,omitempty" bson:"submissions,omitempty"`
	Stats       GameQuestionStats `json:"stats,omitempty" bson:"stats,omitempty"`
}

type GameQuestionStats struct {
	FastestTime *int        `json:"fastestTime,omitempty" bson:"fastestTime,omitempty"`
	UserIds     []*ObjectID `json:"userIds,omitempty" bson:"userIds,omitempty"`
}

type Submission struct {
	UserID            *ObjectID `json:"userId,omitempty" bson:"userId,omitempty"`
	TimeTaken         *int      `json:"timeTaken,omitempty" bson:"timeTaken,omitempty"`
	Points            *int      `json:"points,omitempty" bson:"points,omitempty"`
	SubmissionTime    *Date     `json:"submissionTime,omitempty" bson:"submissionTime,omitempty"`
	IsCorrect         *bool     `json:"isCorrect,omitempty" bson:"isCorrect,omitempty"`
	InCorrectAttempts *int      `json:"inCorrectAttempts,omitempty" bson:"inCorrectAttempts,omitempty"`
	SubmittedValues   []*string `json:"submittedValues,omitempty" bson:"submittedValues,omitempty"`
}

type LeaderBoardEntry struct {
	UserID            *ObjectID `json:"userId,omitempty" bson:"userId,omitempty"`
	Correct           *int      `json:"correct,omitempty" bson:"correct,omitempty"`
	Incorrect         *int      `json:"incorrect,omitempty" bson:"incorrect,omitempty"`
	TotalPoints       *float64  `json:"totalPoints,omitempty" bson:"totalPoints,omitempty"`
	RatingChange      *int      `json:"ratingChange,omitempty" bson:"ratingChange,omitempty"`
	StatikCoinsEarned *int      `json:"statikCoinsEarned,omitempty" bson:"statikCoinsEarned,omitempty"`
	Rank              *int      `json:"rank,omitempty" bson:"rank,omitempty"`
}

type GameConfig struct {
	// timeLimit in seconds
	TimeLimit          *int     `json:"timeLimit,omitempty" bson:"timeLimit"`                   // Deprecated
	NumPlayers         *int     `json:"numPlayers,omitempty" bson:"numPlayers"`                 // Deprecated
	QuestionTags       []string `json:"questionTags,omitempty" bson:"questionTags"`             // Deprecated
	GameType           GameType `json:"gameType,omitempty" bson:"gameType"`                     // Deprecated
	DifficultyLevel    []int    `json:"difficultyLevel,omitempty" bson:"difficultyLevel"`       // Deprecated
	MaxTimePerQuestion *int     `json:"maxTimePerQuestion,omitempty" bson:"maxTimePerQuestion"` // Deprecated

	CategorySpecificConfig *GameCategorySpecificConfig `json:"categorySpecificConfig" bson:"categorySpecificConfig"`
	GameTypeSpecificConfig *GameTypeSpecificConfig     `json:"gameTypeSpecificConfig" bson:"gameTypeSpecificConfig"`
	ModeSpecificConfig     *GameModeSpecificConfig     `json:"modeSpecificConfig" bson:"modeSpecificConfig"`
}

type SearchEntry struct {
	UserID     ObjectID    `json:"userId" bson:"userId"`
	GameConfig *GameConfig `json:"gameConfig,omitempty" bson:"gameConfig,omitempty"`
	CreatedAt  time.Time   `json:"createdAt" bson:"createdAt"`
}

type GameConfigInput struct {
	TimeLimit          int      `json:"timeLimit,omitempty" bson:"timeLimit"`                   // Deprecated
	NumPlayers         int      `json:"numPlayers,omitempty" bson:"numPlayers"`                 // Deprecated
	GameType           GameType `json:"gameType,omitempty" bson:"gameType"`                     // Deprecated
	QuestionTags       []string `json:"questionTags,omitempty" bson:"questionTags"`             // Deprecated
	DifficultyLevel    []int    `json:"difficultyLevel,omitempty" bson:"difficultyLevel"`       // Deprecated
	MaxTimePerQuestion *int     `json:"maxTimePerQuestion,omitempty" bson:"maxTimePerQuestion"` // Deprecated

	CategorySpecificConfig *GameCategorySpecificConfigInput `json:"categorySpecificConfig,omitempty" bson:"categorySpecificConfig"`
	GameTypeSpecificConfig *GameTypeSpecificConfigInput     `json:"gameTypeSpecificConfig,omitempty" bson:"gameTypeSpecificConfig"`
	ModeSpecificConfig     *GameModeSpecificConfigInput     `json:"modeSpecificConfig,omitempty" bson:"modeSpecificConfig"`
}

type DefaultGameConfigInput struct {
	TimeLimit *int `json:"timeLimit,omitempty" bson:"timeLimit"`
}

type DefaultGameModeConfigInput struct {
	NumPlayers *int `json:"numPlayers,omitempty" bson:"numPlayers"`
}

type GroupPlayGameConfigInput struct {
	MaxTimePerQuestion *int     `json:"maxTimePerQuestion,omitempty" bson:"maxTimePerQuestion"`
	DifficultyLevel    []int    `json:"difficultyLevel,omitempty" bson:"difficultyLevel"`
	QuestionTags       []string `json:"questionTags,omitempty" bson:"questionTags"`
	MaxGapBwGame       *int     `json:"maxGapBwGame,omitempty" bson:"maxGapBwGame"`
	MaxPlayers         *int     `json:"maxPlayers,omitempty" bson:"maxPlayers"`
	MinPlayers         *int     `json:"minPlayers,omitempty" bson:"minPlayers"`
}

type GameModeSpecificConfigInput struct {
	Mode             GameMode                    `json:"mode,omitempty" bson:"mode"`
	SumdayShowdown   *ShowdownGameConfigInput    `json:"sumdayShowdown,omitempty" bson:"sumdayShowdown"`
	OnlineSearch     *DefaultGameModeConfigInput `json:"onlineSearch,omitempty" bson:"onlineSearch"`
	OnlineChallenge  *DefaultGameModeConfigInput `json:"onlineChallenge,omitempty" bson:"onlineChallenge"`
	Practice         *DefaultGameModeConfigInput `json:"practice,omitempty" bson:"practice"`
	RushWithTime     *DefaultGameModeConfigInput `json:"rushWithTime,omitempty" bson:"rushWithTime"`
	RushWithoutTime  *DefaultGameModeConfigInput `json:"rushWithoutTime,omitempty" bson:"rushWithoutTime"`
	GroupPlay        *GroupPlayGameConfigInput   `json:"groupPlay,omitempty" bson:"groupPlay"`
	PlayViaLink      *DefaultGameModeConfigInput `json:"playViaLink,omitempty" bson:"playViaLink"`
	SurvivalSaturday *DefaultGameModeConfigInput `json:"survivalSaturday,omitempty" bson:"survivalSaturday"`
}

type ShowdownGameConfigInput struct {
	NumberOfGames *int `json:"numberOfGames,omitempty" bson:"numberOfGames"`
}

type GameCategorySpecificConfigInput struct {
	Category  GameCategory            `json:"category,omitempty" bson:"category"`
	Blitz     *DefaultGameConfigInput `json:"blitz,omitempty" bson:"blitz"`
	Classical *DefaultGameConfigInput `json:"classical,omitempty" bson:"classical"`
	Memory    *DefaultGameConfigInput `json:"memory,omitempty" bson:"memory"`
	Puzzle    *PuzzleGameConfigInput  `json:"puzzle,omitempty" bson:"puzzle"`
}

type GameTypeSpecificConfigInput struct {
	Type GameType `json:"type,omitempty" bson:"type"`
}

type Players []*ObjectID

type AnswerResponseModel struct {
	ID                          *ObjectID                      `json:"_id" bson:"_id"`
	Config                      *GameConfig                    `json:"config,omitempty" bson:"config,omitempty"`
	GameStatus                  GameStatus                     `json:"gameStatus" bson:"gameStatus"`
	LeaderBoard                 []*LeaderBoardEntry            `json:"leaderBoard,omitempty" bson:"leaderBoard,omitempty"`
	EndTime                     *time.Time                     `json:"endTime,omitempty" bson:"endTime,omitempty"`
	Players                     []*Player                      `json:"players" bson:"players"`
	UserSubmissionsWithQuestion []*UserSubmissionsWithQuestion `json:"userSubmissionsWithQuestion" bson:"userSubmissionsWithQuestion"`
}

type UserSubmissionsWithQuestion struct {
	QuestionId  *string            `json:"questionId,omitempty" bson:"questionId,omitempty"`
	Submissions []*UserSubmissions `json:"submissions" bson:"submissions"`
}

type UserSubmissions struct {
	UserID *ObjectID `json:"userId,omitempty" bson:"userId,omitempty"`
	Points *int      `json:"points,omitempty" bson:"points,omitempty"`
}

type SubmitAnswerEvent struct {
	Game  *AnswerResponseModel `json:"game,omitempty"`
	Event *string              `json:"event,omitempty"`
}

type JoinGameInput struct {
	GameID ObjectID `json:"gameId,omitempty" bson:"gameId,omitempty"`
}

type SubmitAnswerInput struct {
	GameID            ObjectID `json:"gameId,omitempty" bson:"gameId,omitempty"`
	QuestionID        string   `json:"questionId,omitempty" bson:"questionId,omitempty"`
	SubmittedValue    string   `json:"submittedValue,omitempty" bson:"submittedValue,omitempty"`
	IsCorrect         bool     `json:"isCorrect,omitempty" bson:"isCorrect,omitempty"`
	IncorrectAttempts int      `json:"incorrectAttempts,omitempty" bson:"incorrectAttempts,omitempty"`
	TimeOfSubmission  Date     `json:"timeOfSubmission,omitempty" bson:"timeOfSubmission,omitempty"`

	GameTypeSpecificAnswer *GameTypeSpecificAnswerInput `json:"gameTypeSpecificAnswer,omitempty" bson:"gameTypeSpecificAnswer"`
}

type FlashAnzanAnswerInput struct {
	MaxScore *int `json:"maxScore,omitempty" bson:"maxScore"`
}

type GameTypeSpecificAnswerInput struct {
	Type       GameType               `json:"type" bson:"type"`
	FlashAnzan *FlashAnzanAnswerInput `json:"flashAnzan,omitempty" bson:"flashAnzan"`
}

type StartGameInput struct {
	GameID *ObjectID `json:"gameId,omitempty" bson:"gameId,omitempty"`
}

type GetGamesOutput struct {
	Games []*MinifiedGame      `json:"games,omitempty" bson:"games,omitempty"`
	Users []*UserPublicDetails `json:"users,omitempty" bson:"users,omitempty"`
}

type TimeRangeInput struct {
	StartTime time.Time `json:"startTime,omitempty" bson:"startTime"`
	EndTime   time.Time `json:"endTime,omitempty" bson:"endTime"`
}

type PageInfoInput struct {
	PageNumber int `json:"pageNumber,omitempty" bson:"pageNumber,omitempty"`
	Rows       int `json:"rows,omitempty" bson:"rows,omitempty"`
}

type GetGamesInput struct {
	UserID    ObjectID       `json:"userId,omitempty" bson:"userId,omitempty"`
	TimeRange TimeRangeInput `json:"timeRange,omitempty" bson:"timeRange,omitempty"`
	PageInfo  PageInfoInput  `json:"pageInfo,omitempty" bson:"pageInfo,omitempty"`
}

type SubscriptionOutput struct {
	Game     *Game     `json:"game,omitempty" bson:"game,omitempty"`
	Event    *string   `json:"event,omitempty" bson:"event,omitempty"`
	Question *Question `json:"question,omitempty" bson:"question,omitempty"`
}

type SearchSubscriptionOutput struct {
	Game     *Game   `json:"game,omitempty" bson:"game,omitempty"`
	Event    *string `json:"event,omitempty" bson:"event,omitempty"`
	Opponent *User   `json:"opponent,omitempty" bson:"opponent,omitempty"`
}

type SubmitFlashAnzanAnswerInput struct {
	GameID             *primitive.ObjectID `json:"gameId,omitempty" bson:"gameId"`
	QuestionIdentifier *string             `json:"questionIdentifier,omitempty" bson:"questionIdentifier"`
	SubmittedValue     *string             `json:"submittedValue,omitempty" bson:"submittedValue"`
	IsCorrect          *bool               `json:"isCorrect,omitempty" bson:"isCorrect"`
	TimeOfSubmission   *Date               `json:"timeOfSubmission,omitempty" bson:"timeOfSubmission"`
	MaxScore           *int                `json:"maxScore,omitempty" bson:"maxScore"`
}

type GameDetailedAnalysis struct {
	Game      *Game                   `json:"game" bson:"game"`
	Questions []*GameQuestionAnalysis `json:"questions" bson:"questions"`
}

type GameQuestionAnalysis struct {
	Question       *Question      `json:"question" bson:"question"`
	AvgTimes       []*UserAvgTime `json:"avgTimes" bson:"avgTimes"`
	GlobalAvgTime  float64        `json:"globalAvgTime" bson:"globalAvgTime"`
	GlobalBestTime float64        `json:"globalBestTime" bson:"globalBestTime"`
}

type UserAvgTime struct {
	UserID          string  `json:"userId" bson:"userId"`
	QuestionAvgTime float64 `json:"questionAvgTime" bson:"questionAvgTime"`
	PresetAvgTime   float64 `json:"presetAvgTime" bson:"presetAvgTime"`
	PresetBestTime  float64 `json:"presetBestTime" bson:"presetBestTime"`
}

type ChallengeUserInput struct {
	UserID     *primitive.ObjectID `json:"userId" bson:"userId"`
	GameConfig *GameConfigInput    `json:"gameConfig,omitempty" bson:"gameConfig"`
}

type ChallengeOutput struct {
	GameID       primitive.ObjectID `json:"gameId" bson:"gameId"`
	ChallengedBy primitive.ObjectID `json:"challengedBy" bson:"challengedBy"`
	GameConfig   *GameConfig        `json:"gameConfig" bson:"gameConfig"`
	CreatedAt    time.Time          `json:"createdAt" bson:"createdAt"`
	Status       *ChallengeStatus   `json:"status,omitempty" bson:"status"`
	Opponent     *User              `json:"opponent,omitempty" bson:"opponent,omitempty"`
}

type ChallengeStatus string

const (
	ChallengeStatusChallengeSent     ChallengeStatus = "CHALLENGE_SENT"
	ChallengeStatusChallengeAccepted ChallengeStatus = "CHALLENGE_ACCEPTED"
	ChallengeStatusChallengeExpired  ChallengeStatus = "CHALLENGE_EXPIRED"
	ChallengeStatusChallengeRejected ChallengeStatus = "CHALLENGE_REJECTED"
	ChallengeStatuCancelled          ChallengeStatus = "GAME_CANCELLED"
)

var AllChallengeStatus = []ChallengeStatus{
	ChallengeStatusChallengeSent,
	ChallengeStatusChallengeAccepted,
	ChallengeStatusChallengeExpired,
	ChallengeStatusChallengeRejected,
	ChallengeStatuCancelled,
}

func (e ChallengeStatus) IsValid() bool {
	switch e {
	case ChallengeStatusChallengeSent, ChallengeStatuCancelled, ChallengeStatusChallengeAccepted, ChallengeStatusChallengeExpired, ChallengeStatusChallengeRejected:
		return true
	}
	return false
}

func (e ChallengeStatus) String() string {
	return string(e)
}

func (e *ChallengeStatus) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = ChallengeStatus(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid ChallengeStatus", str)
	}
	return nil
}

func (e ChallengeStatus) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type GameStatus string

const (
	GameStatusCreated   GameStatus = "CREATED"
	GameStatusReady     GameStatus = "READY"
	GameStatusStarted   GameStatus = "STARTED"
	GameStatusPaused    GameStatus = "PAUSED"
	GameStatusEnded     GameStatus = "ENDED"
	GameStatusCancelled GameStatus = "CANCELLED"
)

var AllGameStatus = []GameStatus{
	GameStatusCreated,
	GameStatusReady,
	GameStatusStarted,
	GameStatusPaused,
	GameStatusEnded,
	GameStatusCancelled,
}

func (e GameStatus) IsValid() bool {
	switch e {
	case GameStatusCreated, GameStatusReady, GameStatusStarted, GameStatusPaused, GameStatusEnded, GameStatusCancelled:
		return true
	}
	return false
}

func (e GameStatus) String() string {
	return string(e)
}

func (e *GameStatus) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = GameStatus(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid GAME_STATUS", str)
	}
	return nil
}

func (e GameStatus) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type GameType string

const (
	GameTypePlayOnline      GameType = "PLAY_ONLINE"      // Deprecated
	GameTypePlayWithFriend  GameType = "PLAY_WITH_FRIEND" // Deprecated
	GameTypeOnlineChallenge GameType = "ONLINE_CHALLENGE" // Deprecated
	GameTypePractice        GameType = "PRACTICE"         // Deprecated
	GameTypeSumdayShowdown  GameType = "SUMDAY_SHOWDOWN"  // Deprecated
	GameTypeGroupPlay       GameType = "GROUP_PLAY"       // Deprecated
	GameTypeAbilityDuels    GameType = "ABILITY_DUELS"    // Deprecated

	GameTypeFlashAnzan    GameType = "FLASH_ANZAN"
	GameTypeDMAS          GameType = "DMAS"
	GameTypeDMASAbility   GameType = "DMAS_ABILITY"
	GameTypeDMASTimeBank  GameType = "DMAS_TIME_BANK"
	GameTypeCrossMath     GameType = "CROSS_MATH_PUZZLE"
	GameTypeKenKen        GameType = "KEN_KEN_PUZZLE"
	GameTypeFastestFinger GameType = "FASTEST_FINGER"
)

var AllGameType = []GameType{
	GameTypePlayOnline,
	GameTypePlayWithFriend,
	GameTypeOnlineChallenge,
	GameTypePractice,
	GameTypeSumdayShowdown,
	GameTypeAbilityDuels,
	GameTypeFlashAnzan,
	GameTypeFastestFinger,
	GameTypeDMAS,
	GameTypeDMASAbility,
	GameTypeDMASTimeBank,
	GameTypeCrossMath,
	GameTypeKenKen,
}

func (e GameType) IsValid() bool {
	switch e {
	case GameTypePlayOnline, GameTypePlayWithFriend, GameTypeOnlineChallenge, GameTypePractice, GameTypeAbilityDuels, GameTypeSumdayShowdown, GameTypeGroupPlay, GameTypeFlashAnzan, GameTypeFastestFinger, GameTypeDMAS, GameTypeDMASAbility, GameTypeDMASTimeBank, GameTypeCrossMath, GameTypeKenKen:
		return true
	}
	return false
}

func (e GameType) String() string {
	return string(e)
}

func (e *GameType) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = GameType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid GAME_TYPE", str)
	}
	return nil
}

func (e GameType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type GameCategory string

const (
	GameCategoryBlitz     GameCategory = "BLITZ"
	GameCategoryClassical GameCategory = "CLASSICAL"
	GameCategoryMemory    GameCategory = "MEMORY"
	GameCategoryPuzzle    GameCategory = "PUZZLE"
)

var AllGameCategory = []GameCategory{
	GameCategoryBlitz,
	GameCategoryClassical,
	GameCategoryMemory,
	GameCategoryPuzzle,
}

func (e GameCategory) IsValid() bool {
	switch e {
	case GameCategoryBlitz, GameCategoryClassical, GameCategoryMemory, GameCategoryPuzzle:
		return true
	}
	return false
}

func (e GameCategory) String() string {
	return string(e)
}

func (e *GameCategory) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = GameCategory(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid GAME_CATEGORY", str)
	}
	return nil
}

func (e GameCategory) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type GameMode string

const (
	GameModeOnlineSearch     GameMode = "ONLINE_SEARCH"
	GameModeOnlineChallenge  GameMode = "ONLINE_CHALLENGE"
	GameModePractice         GameMode = "PRACTICE"
	GameModeRushWithTime     GameMode = "RUSH_WITH_TIME"
	GameModeRushWithoutTime  GameMode = "RUSH_WITHOUT_TIME"
	GameModeGroupPlay        GameMode = "GROUP_PLAY"
	GameModePlayViaLink      GameMode = "PLAY_VIA_LINK"
	GameModeSumdayShowdown   GameMode = "SUMDAY_SHOWDOWN"
	GameModeSurvivalSaturday GameMode = "SURVIVAL_SATURDAY"
)

var AllGameMode = []GameMode{
	GameModeOnlineSearch,
	GameModeOnlineChallenge,
	GameModePractice,
	GameModeRushWithTime,
	GameModeRushWithoutTime,
	GameModeGroupPlay,
	GameModePlayViaLink,
	GameModeSumdayShowdown,
	GameModeSurvivalSaturday,
}

func (e GameMode) IsValid() bool {
	switch e {
	case GameModeOnlineSearch, GameModeOnlineChallenge, GameModePractice, GameModeRushWithTime, GameModeRushWithoutTime, GameModeGroupPlay, GameModePlayViaLink, GameModeSumdayShowdown, GameModeSurvivalSaturday:
		return true
	}
	return false
}

func (e GameMode) String() string {
	return string(e)
}

func (e *GameMode) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = GameMode(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid GAME_MODE", str)
	}
	return nil
}

func (e GameMode) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type PlayerStatus string

const (
	PlayerStatusInvited  PlayerStatus = "INVITED"
	PlayerStatusAccepted PlayerStatus = "ACCEPTED"
	PlayerStatusRejected PlayerStatus = "REJECTED"
)

var AllPlayerStatus = []PlayerStatus{
	PlayerStatusInvited,
	PlayerStatusAccepted,
	PlayerStatusRejected,
}

func (e PlayerStatus) IsValid() bool {
	switch e {
	case PlayerStatusInvited, PlayerStatusAccepted, PlayerStatusRejected:
		return true
	}
	return false
}

func (e PlayerStatus) String() string {
	return string(e)
}

func (e *PlayerStatus) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = PlayerStatus(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid PLAYER_STATUS", str)
	}
	return nil
}

func (e PlayerStatus) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type GroupPlayChatMessage struct {
	Message   string             `json:"message" bson:"message"`
	GameID    string             `json:"gameId" bson:"gameId"`
	UserName  string             `json:"userName" bson:"userName"`
	UserID    string             `json:"userId" bson:"userId"`
	SentAt    time.Time          `json:"sentAt" bson:"sentAt"`
	MessageID primitive.ObjectID `json:"messageId" bson:"messageId"`
}

type QuestionTypeWeightage struct {
	Type      int
	Weightage int
}

type GameConfigInterface interface {
	GetTimeLimit() *int
	GetNumPlayers() *int
	GetGameType() interface{}
	GetDifficultyLevel() []int
}

func (g GameConfig) GetTimeLimit() *int {
	return g.TimeLimit
}

func (g GameConfig) GetNumPlayers() *int {
	return g.NumPlayers
}

func (g GameConfig) GetGameType() interface{} {
	return g.GameType
}

func (g GameConfig) GetDifficultyLevel() []int {
	return g.DifficultyLevel
}

func (p PuzzleGameConfig) GetTimeLimit() *int {
	return p.TimeLimit
}

func (p PuzzleGameConfig) GetNumPlayers() *int {
	return p.NumPlayers
}

func (p PuzzleGameConfig) GetGameType() interface{} {
	return p.GameType
}

func (p PuzzleGameConfig) GetDifficultyLevel() []int {
	return p.DifficultyLevel
}

type GetGamesByRatingInput struct {
	UserID     *primitive.ObjectID `json:"userId,omitempty" bson:"userId"`
	PageInfo   *PageInfoInput      `json:"pageInfo,omitempty" bson:"pageInfo"`
	RatingType *string             `json:"ratingType,omitempty" bson:"ratingType"`
}

type GetGamesByRatingOutput struct {
	Games       []*MinifiedGame       `json:"games"`
	PuzzleGames []*MinifiedPuzzleGame `json:"puzzleGames"`
	Users       []*UserPublicDetails  `json:"users"`
	TotalCount  int                   `json:"totalCount"`
}
