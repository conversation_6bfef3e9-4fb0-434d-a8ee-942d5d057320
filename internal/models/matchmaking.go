package models

import "go.mongodb.org/mongo-driver/bson/primitive"

type MatchmakingPayload struct {
	Entities []*MatchmakingEntity `json:"entities"`
}

type MatchmakingEntity struct {
	ID                      primitive.ObjectID   `json:"id"`
	MatchmakingScore        int                  `json:"matchmakingScore"`
	PreviousMatchedEntities []primitive.ObjectID `json:"previousMatchedEntities"`
	HadBye                  bool                 `json:"hadBye"`
}

type MatchmakingPair struct {
	Entity1 primitive.ObjectID `json:"entity1"`
	Entity2 primitive.ObjectID `json:"entity2"`
}

type MatchmakingResponse struct {
	Pairs     []*MatchmakingPair  `json:"pairs"`
	ByeEntity *primitive.ObjectID `json:"byeEntity"`
}
