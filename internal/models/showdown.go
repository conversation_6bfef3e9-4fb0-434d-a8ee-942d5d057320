package models

import (
	"fmt"
	"io"
	"strconv"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Order int

const (
	OrderAscending  Order = 1
	OrderDescending Order = -1
)

type Showdown struct {
	ID                       *primitive.ObjectID        `json:"_id,omitempty" bson:"_id,omitempty"`
	ClubID                   *primitive.ObjectID        `json:"clubId" bson:"clubId"`
	Name                     string                     `json:"name" bson:"name"`
	Description              string                     `json:"description" bson:"description"`
	Instructions             []*string                  `json:"Instructions,omitempty" bson:"instructions,omitempty"`
	HostedBy                 *HostInfo                  `json:"hostedBy,omitempty" bson:"hostedBy,omitempty"`
	Rounds                   int                        `json:"rounds,omitempty" bson:"rounds,omitempty"`
	StartTime                time.Time                  `json:"startTime,omitempty" bson:"startTime,omitempty"`
	EndTime                  time.Time                  `json:"endTime,omitempty" bson:"endTime,omitempty"`
	RegistrationCount        *int                       `json:"registrationCount,omitempty" bson:"registrationCount,omitempty"`
	RegistrationStartTime    *time.Time                 `json:"registrationStartTime,omitempty" bson:"registrationStartTime,omitempty"`
	RegistrationEndTime      *time.Time                 `json:"registrationEndTime,omitempty" bson:"registrationEndTime,omitempty"`
	RegistrationForm         *RegistrationForm          `json:"registrationForm,omitempty" bson:"registrationForm,omitempty"`
	Duration                 int                        `json:"duration" bson:"duration"`
	GapBwRounds              int                        `json:"gapBwRounds" bson:"gapBwRounds"`
	RoundTime                int                        `json:"roundTime" bson:"roundTime"`
	Status                   ShowdownContestStatus      `json:"status,omitempty" bson:"status,omitempty"`
	RoundConfig              RoundConfig                `json:"roundConfig,omitempty" bson:"roundConfig,omitempty"`
	Stats                    *ShowdownStats             `json:"stats,omitempty" bson:"stats,omitempty"`
	Details                  ShowdownDetails            `json:"details,omitempty" bson:"details,omitempty"`
	CurrentUserParticipation *CurrentShowdonParticipant `json:"currentUserParticipation" bson:"currentUserParticipation,omitempty"`
	IsRatedEvent             *bool                      `json:"isRatedEvent" bson:"isRatedEvent"`
	CreatedAt                time.Time                  `json:"createdAt" bson:"createdAt"`
	UpdatedAt                time.Time                  `json:"updatedAt" bson:"updatedAt"`
	RecentParticipants       []*UserPublicDetails       `json:"recentParticipants,omitempty" bson:"recentParticipants,omitempty"`
	CurrentRound             int                        `json:"currentRound" bson:"currentRound"`
}

type ShowdownUserDetails struct {
	ID              ObjectID `json:"_id" bson:"_id"`
	Name            *string  `json:"name,omitempty" bson:"name"`
	Username        string   `json:"username" bson:"username"`
	ProfileImageURL *string  `json:"profileImageUrl,omitempty" bson:"profileImageUrl"`
	Rating          *int     `json:"rating,omitempty" bson:"rating"`
}

type ShowdownConfig struct {
	ShowdownId             primitive.ObjectID    `json:"showdownId"`
	TotalGames             int                   `json:"totalGames"` // Just for reference
	TotalGamesPlayed       int                   `json:"totalGamesPlayed"`
	Round                  int                   `json:"round"`
	IsPlayerAlreeadyJoined bool                  `json:"isPlayerAlreadyJoined"`
	Players                Players               `json:"players"`
	GameConfig             GameConfig            `json:"gameConfig"`
	ShowdownGamePlayer     []*ShowdownGamePlayer `json:"showdownGamePlayer"`
}

type ShowdownMinifiedNotificationPayload struct {
	ID                *primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	Name              string              `json:"name" bson:"name"`
	Description       string              `json:"description" bson:"description"`
	CurrentRound      int                 `json:"currentRound" bson:"currentRound"`
	StartTime         time.Time           `json:"startTime,omitempty" bson:"startTime,omitempty"`
	RegistrationCount *int                `json:"registrationCount,omitempty" bson:"registrationCount,omitempty"`
}

type ShowdownDetails struct {
	Format *string `json:"format,omitempty" bson:"format,omitempty"`
	Rules  *string `json:"rules,omitempty" bson:"rules,omitempty"`
}

type ShowdownEventPayload struct {
	Type    ShowdownEventType `json:"type" bson:"type"`
	Payload interface{}       `json:"payload" bson:"payload"`
}

type ShowdownParticipant struct {
	ID               *primitive.ObjectID       `json:"_id,omitempty" bson:"_id,omitempty"`
	UserID           primitive.ObjectID        `json:"userId" bson:"userId"`
	ShowdownID       primitive.ObjectID        `json:"showdownId" bson:"showdownId"`
	RegistrationData []*RegistrationFieldData  `json:"registrationData,omitempty" bson:"registrationData,omitempty"`
	Status           ShowdownParticipantStatus `json:"status" bson:"status"`
	Stats            *ShowdownParticipantStats `json:"stats" bson:"stats"`
	Rounds           []*ShowdownRound          `json:"rounds,omitempty" bson:"rounds,omitempty"`
	RecentOpponents  []primitive.ObjectID      `json:"recentOpponents" bson:"recentOpponents"`
	HadABye          bool                      `json:"hadABye" bson:"hadABye"`
	Rank             *int                      `json:"rank,omitempty" bson:"rank,omitempty"`
	RatingChange     int
	TotalScore       float64              `json:"totalScore" bson:"totalScore"`
	CreatedAt        time.Time            `json:"createdAt" bson:"createdAt"`
	UpdatedAt        time.Time            `json:"updatedAt" bson:"updatedAt"`
	UserInfo         *ShowdownUserDetails `json:"userInfo" bson:"userInfo"`
}

type ShowdownPlayerStatusPayload struct {
	UserId       primitive.ObjectID `json:"userId"`
	PlayerStatus RoundPlayerStatus  `json:"playerStatus"`
	RoundIndex   int                `json:"roundIndex"`
}

type ParticipantBasicInfo struct {
	ID         *primitive.ObjectID  `json:"_id,omitempty" bson:"_id"`
	UserID     primitive.ObjectID   `json:"userID" bson:"userID"`
	ShowdownID primitive.ObjectID   `json:"showdownId" bson:"showdownId"`
	Rounds     []*ShowdownRound     `json:"rounds" bson:"rounds"`
	UserInfo   *ShowdownUserDetails `json:"userInfo,omitempty" bson:"userInfo"`
}

type LeaderParticipantEntity struct {
	ID          primitive.ObjectID   `json:"_id" bson:"_id"`
	Participant ParticipantBasicInfo `json:"participant" bson:"participant"`
	Score       float64              `json:"score" bson:"score"`
	ShowdownId  primitive.ObjectID   `json:"showdownId" bson:"showdownId"`
	Rank        int                  `json:"rank" bson:"rank"`
	UserId      primitive.ObjectID   `json:"userId" bson:"userId"`
}

type ShowdownParticipantToUser struct {
	UserID        primitive.ObjectID `json:"userID" bson:"userID"`
	ParticipantID primitive.ObjectID `json:"participantId" bson:"participantId"`
}

type StartGameForShowdownInput struct {
	ShowdownID primitive.ObjectID `json:"showdownId" bson:"showdownId"`
	UserID     primitive.ObjectID `json:"userId" bson:"userId"`
	GameID     primitive.ObjectID `json:"gameId" bson:"gameId"`
}

type ShowdownParticipantStats struct {
	CurrentScore *int `json:"currentScore,omitempty" bson:"currentScore,omitempty"`
	Win          *int `json:"win,omitempty" bson:"win,omitempty"`
	Loss         *int `json:"loss,omitempty" bson:"loss,omitempty"`
	Draw         *int `json:"draw,omitempty" bson:"draw,omitempty"`
}

type ShowdownRound struct {
	Opponent            primitive.ObjectID   `json:"opponent" bson:"opponent"`
	Round               int                  `json:"round" bson:"round"`
	Score               float64              `json:"score" bson:"score"`
	Games               []primitive.ObjectID `json:"games" bson:"games"`
	Wins                float64              `json:"wins" bson:"wins"`
	Loose               float64              `json:"loose" bson:"loose"`
	TotalGamesPlayed    int                  `json:"totalGamesPlayed" bson:"totalGamesPlayed"`
	PlayerStatus        RoundPlayerStatus    `json:"playerStatus" bson:"playerStatus"`
	IsBye               bool                 `json:"isBye" bson:"isBye"`
	IsRoundEnded        bool                 `json:"isRoundEnded" bson:"isRoundEnded"`
	HasJoined           bool                 `json:"hasJoined" bson:"hasJoined"`
	HasOpponentNotShown bool                 `json:"hasOpponentNotShown" bson:"hasOpponentNotShown"`
	HasFailedToPlay     bool                 `json:"hasFailedToPlay" bson:"hasFailedToPlay"`
}

type ShowdownRoundDto struct {
	UserId primitive.ObjectID `json:"userId" bson:"userId"`
	Round  ShowdownRound      `json:"showdownRound" bson:"showdownRound"`
}

type ShowdownStats struct {
	TotalParticipants *int `json:"totalParticipants,omitempty" bson:"totalParticipants,omitempty"`
	TotalGamesPlayed  *int `json:"totalGamesPlayed,omitempty" bson:"totalGamesPlayed,omitempty"`
}

type RoundConfig struct {
	GameDuration int      `json:"gameDuration" bson:"gameDuration"`
	NumOfPlayer  int      `json:"numOfPlayer" bson:"numOfPlayer"`
	GameType     GameType `json:"gameType" bson:"gameType"`
	MaxGapBwGame int      `json:"maxGapBwGame" bson:"maxGapBwGame"`
	MaxWaitTime  int      `json:"maxWaitTime" bson:"maxWaitTime"`
	NumOfGames   int      `json:"numOfGames" bson:"numOfGames"`
}

type ShowdownToStartSubscription struct {
	Event    *string   `json:"event" bson:"event"`
	Showdown *Showdown `json:"showdown" bson:"showdown"`
}

type ShowdownDetailsInput struct {
	Format *string `json:"format,omitempty" bson:"format"`
	Rules  *string `json:"rules,omitempty" bson:"rules"`
}

type CreateShowdownInput struct {
	Name                  string                `json:"name" bson:"name"`
	Description           string                `json:"description" bson:"description"`
	HostedBy              *HostInfoInput        `json:"hostedBy" bson:"hostedBy"`
	IsRatedEvent          *bool                 `json:"isRatedEvent" bson:"isRatedEvent"`
	StartTime             time.Time             `json:"startTime" bson:"startTime"`
	Rounds                int                   `json:"rounds" bson:"rounds"`
	RegistrationStartTime time.Time             `json:"registrationStartTime" bson:"registrationStartTime"`
	Details               ShowdownDetailsInput  `json:"details,omitempty" bson:"details,omitempty"`
	RoundConfig           *RoundConfigInput     `json:"roundConfig" bson:"roundConfig"`
	RegistrationForm      RegistrationFormInput `json:"registrationForm" bson:"registrationForm"`
	GapBwRounds           int                   `json:"gapBwRounds" bson:"gapBwRounds"`
}

type CurrentShowdonParticipant struct {
	ShowdownID         *primitive.ObjectID      `json:"showdownId,omitempty" bson:"showdownId,omitempty"`
	UserID             *primitive.ObjectID      `json:"userId,omitempty" bson:"userId,omitempty"`
	RegistrationData   []*RegistrationFieldData `json:"registrationData,omitempty" bson:"registrationData,omitempty"`
	LastSubmissionTime *time.Time               `json:"lastSubmissionTime,omitempty" bson:"lastSubmissionTime,omitempty"`
	Stats              ShowdownParticipantStats `json:"stats,omitempty" bson:"stats,omitempty"`
	Rank               *int                     `json:"rank,omitempty" bson:"rank,omitempty"`
	Rounds             []*ShowdownRound         `json:"rounds,omitempty" bson:"rounds,omitempty"`
	HadABye            bool                     `json:"hadABye" bson:"hadABye"`
	TotalScore         float64                  `json:"totalScore" bson:"totalScore"`
	RecentOpponent     *primitive.ObjectID      `json:"recentOpponent,omitempty" bson:"recentOpponent,omitempty"`
	CurrentGame        *primitive.ObjectID      `json:"currentGame"`
	CurrentRound       *ShowdownRound           `json:"currentRound,omitempty" bson:"currentRound,omitempty"`
}

type RoundConfigInput struct {
	GameDuration int      `json:"gameDuration" bson:"gameDuration"`
	NumOfPlayer  int      `json:"numOfPlayer" bson:"numOfPlayer"`
	GameType     GameType `json:"gameType" bson:"gameType"`
	MaxGapBwGame int      `json:"maxGapBwGame" bson:"maxGapBwGame"`
	MaxWaitTime  int      `json:"maxWaitTime" bson:"maxWaitTime"`
	NumOfGames   int      `json:"numOfGames" bson:"numOfGames"`
}

type PaginatedLeaderboard struct {
	Participants []*LeaderParticipantEntity `json:"participants"`
	Count        int                        `json:"count"`
}

type PaginatedLeaderboardInput struct {
	ShowdownID primitive.ObjectID `json:"showdownId"`
	Page       int                `json:"page"`
}

type HostInfo struct {
	ID       *primitive.ObjectID `json:"id,omitempty" bson:"id,omitempty"`
	HostType *HostType           `json:"hostType,omitempty" bson:"hostType,omitempty"`
	HostLogo *string             `json:"hostLogo,omitempty" bson:"hostLogo,omitempty"`
}

type HostInfoInput struct {
	ID       *primitive.ObjectID `json:"id,omitempty" bson:"id,omitempty"`
	HostType *HostType           `json:"hostType,omitempty" bson:"hostType,omitempty"`
	HostLogo *string             `json:"hostLogo,omitempty" bson:"hostLogo,omitempty"`
}

type ShowdownRegistrationFormValuesInput struct {
	ShowdownID primitive.ObjectID `json:"showdownId" bson:"showdownId"`
	// FormData   []*RegistrationFormFieldValueInput `json:"formData" bson:"formData"`
}

type ShowdownEventType string

const (
	LEADERBOARD_UPDATE ShowdownEventType = "LEADERBOARD_UPDATE"
)

type LeaderboardUpdatePayload struct {
	Page       int                `json:"page"`
	ShowdownID primitive.ObjectID `json:"showdownId"`
}

type HostType string

const (
	HostTypeMatiks       HostType = "MATIKS"
	HostTypeOrganization HostType = "ORGANIZATION"
	HostTypeUser         HostType = "USER"
)

var AllHostType = []HostType{
	HostTypeMatiks,
	HostTypeOrganization,
	HostTypeUser,
}

func (e HostType) IsValid() bool {
	switch e {
	case HostTypeMatiks, HostTypeOrganization, HostTypeUser:
		return true
	}
	return false
}

func (e HostType) String() string {
	return string(e)
}

func (e *HostType) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = HostType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid HOST_TYPE", str)
	}
	return nil
}

func (e HostType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type Fictures struct {
	ID           primitive.ObjectID           `json:"_id" bson:"_id"`
	ShowdownID   primitive.ObjectID           `json:"showdownId" bson:"showdownId"`
	Users        []*ShowdownParticipantDetail `json:"users" bson:"users"`
	Round        int                          `json:"round" bson:"round"`
	Participants []primitive.ObjectID         `json:"participants" bson:"participants"`
}

type FicturesCollection struct {
	CurrentUserFicture *Fictures `json:"currentUserFicture" bson:"currentUserFicture"`
}

type PaginatedShowdowns struct {
	Showdowns []*Showdown `json:"showdowns" bson:"showdowns"`
	Count     int         `json:"count" bson:"count"`
}

type ShowdownParticipantDetail struct {
	ShowdownParticipant ShowdownParticipant `json:"ShowdownParticipant" bson:"ShowdownParticipant"`
	CurrentRound        ShowdownRound       `json:"currentRound" bson:"currentRound"`
}

type ShowdownParticipantStatus string

const (
	ShowdownParticipantStatusActive       ShowdownParticipantStatus = "ACTIVE"
	ShowdownParticipantStatusDisqualified ShowdownParticipantStatus = "DISQUALIFIED"
)

var AllShowdownParticipantStatus = []ShowdownParticipantStatus{
	ShowdownParticipantStatusActive,
	ShowdownParticipantStatusDisqualified,
}

func (e ShowdownParticipantStatus) IsValid() bool {
	switch e {
	case ShowdownParticipantStatusActive, ShowdownParticipantStatusDisqualified:
		return true
	}
	return false
}

func (e ShowdownParticipantStatus) String() string {
	return string(e)
}

func (e *ShowdownParticipantStatus) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = ShowdownParticipantStatus(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid SHOWDOWN_PARTICIPANT_STATUS", str)
	}
	return nil
}

func (e ShowdownParticipantStatus) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type ShowdownContestStatus string

const (
	ShowdownContestStatusUpcoming          ShowdownContestStatus = "UPCOMING"
	ShowdownContestStatusFicturesCreated   ShowdownContestStatus = "FICTURES_CREATED"
	ShowdownContestStatusRegistrationOpen  ShowdownContestStatus = "REGISTRATION_OPEN"
	ShowdownContestStatusLive              ShowdownContestStatus = "LIVE"
	ShowdownContestStatusEnded             ShowdownContestStatus = "ENDED"
	ShowdownContestStatusArchived          ShowdownContestStatus = "ARCHIVED"
	ShowdownContestStatusRegistrationEnded ShowdownContestStatus = "REGISTRATION_ENDED"
)

var AllShowdownContestStatus = []ShowdownContestStatus{
	ShowdownContestStatusUpcoming,
	ShowdownContestStatusFicturesCreated,
	ShowdownContestStatusRegistrationOpen,
	ShowdownContestStatusLive,
	ShowdownContestStatusEnded,
	ShowdownContestStatusArchived,
	ShowdownContestStatusRegistrationEnded,
}

func (e ShowdownContestStatus) IsValid() bool {
	switch e {
	case ShowdownContestStatusUpcoming, ShowdownContestStatusFicturesCreated, ShowdownContestStatusRegistrationOpen, ShowdownContestStatusLive, ShowdownContestStatusEnded, ShowdownContestStatusArchived, ShowdownContestStatusRegistrationEnded:
		return true
	}
	return false
}

func (e ShowdownContestStatus) String() string {
	return string(e)
}

func (e *ShowdownContestStatus) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = ShowdownContestStatus(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid SHOWDOWN_CONTEST_STATUS", str)
	}
	return nil
}

func (e ShowdownContestStatus) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type RoundPlayerStatus string

const (
	RoundPlayerStatusOpponentAbsent RoundPlayerStatus = "OPPONENT_ABSENT"
	RoundPlayerStatusBye            RoundPlayerStatus = "BYE"
	RoundPlayerStatusDidNotPlay     RoundPlayerStatus = "DID_NOT_PLAY"
	RoundPlayerStatusBothDidNotPlay RoundPlayerStatus = "BOTH_DID_NOT_PLAY"
	RoundPlayerStatusPendingJoin    RoundPlayerStatus = "PENDING_JOIN"
	RoundPlayerStatusRoundCompleted RoundPlayerStatus = "ROUND_COMPLETED"
)

var AllRoundPlayerStatus = []RoundPlayerStatus{
	RoundPlayerStatusOpponentAbsent,
	RoundPlayerStatusBye,
	RoundPlayerStatusDidNotPlay,
	RoundPlayerStatusBothDidNotPlay,
	RoundPlayerStatusPendingJoin,
	RoundPlayerStatusRoundCompleted,
}

func (e RoundPlayerStatus) IsValid() bool {
	switch e {
	case RoundPlayerStatusOpponentAbsent, RoundPlayerStatusBye, RoundPlayerStatusDidNotPlay, RoundPlayerStatusBothDidNotPlay, RoundPlayerStatusPendingJoin, RoundPlayerStatusRoundCompleted:
		return true
	}
	return false
}

func (e RoundPlayerStatus) String() string {
	return string(e)
}

func (e *RoundPlayerStatus) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = RoundPlayerStatus(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid RoundPlayerStatus", str)
	}
	return nil
}

func (e RoundPlayerStatus) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}
