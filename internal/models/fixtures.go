package models

import "go.mongodb.org/mongo-driver/bson/primitive"

type ShowdownFixturesInternalPayload struct {
	ParticipantID           primitive.ObjectID   `json:"participantId" bson:"participantId"`
	Rating                  int                  `json:"rating" bson:"rating"`
	PreviousMatchedEntities []primitive.ObjectID `json:"previousMatchedEntities" bson:"previousMatchedEntities"`
	HadBye                  bool                 `json:"hadBye" bson:"hadBye"`
	TotalScore              float64              `json:"totalScore" bson:"totalScore"`
	UserID                  primitive.ObjectID   `json:"userId" bson:"userId"`
	InitialGameID           *primitive.ObjectID  `json:"initialGameId" bson:"initialGameId"`
}
