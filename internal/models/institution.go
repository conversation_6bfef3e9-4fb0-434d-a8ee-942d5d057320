package models

import "go.mongodb.org/mongo-driver/bson/primitive"

type Institution struct {
	ID      primitive.ObjectID `bson:"_id" json:"id"`
	Name    string             `bson:"name" json:"name"`
	Domains []string           `bson:"domains" json:"domains"`
	Country *string            `bson:"country" json:"country"`
	State   *string            `bson:"state" json:"state"`
	City    *string            `bson:"city" json:"city"`
	Slug    string             `bson:"slug" json:"slug"`
}

type CreateInstitutionInput struct {
	Name    string   `json:"name"`
	Domains []string `json:"domains"`
	Country *string  `json:"country"`
	City    *string  `json:"city"`
	State   *string  `json:"state"`
}

type InstitutionFilter struct {
	Query  string
	Limit  int64
	Offset int64
}
