package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.73

import (
	"context"
	"fmt"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// CreateGame is the resolver for the createGame field.
func (r *mutationResolver) CreateGame(ctx context.Context, gameConfig *models.GameConfigInput) (*models.Game, error) {
	return r.GameService.CreateGame(ctx, gameConfig)
}

// SubmitAnswer is the resolver for the submitAnswer field.
func (r *mutationResolver) SubmitAnswer(ctx context.Context, answerInput *models.SubmitAnswerInput) (*models.Game, error) {
	return r.GameService.SubmitAnswer(ctx, answerInput)
}

// JoinG<PERSON> is the resolver for the joinGame field.
func (r *mutationResolver) JoinGame(ctx context.Context, joinGameInput *models.JoinGameInput) (*models.Game, error) {
	return r.GameService.JoinGame(ctx, joinGameInput)
}

// LeaveGame is the resolver for the leaveGame field.
func (r *mutationResolver) LeaveGame(ctx context.Context, gameID primitive.ObjectID) (*models.Game, error) {
	return r.GameService.LeaveGame(ctx, gameID)
}

// StartGame is the resolver for the startGame field.
func (r *mutationResolver) StartGame(ctx context.Context, startGameInput *models.StartGameInput) (*models.Game, error) {
	return r.GameService.StartGame(ctx, startGameInput)
}

// EndGame is the resolver for the endGame field.
func (r *mutationResolver) EndGame(ctx context.Context, gameID *primitive.ObjectID) (*models.Game, error) {
	return r.GameService.EndGame(ctx, gameID)
}

// RemovePlayer is the resolver for the removePlayer field.
func (r *mutationResolver) RemovePlayer(ctx context.Context, gameID primitive.ObjectID, playerID primitive.ObjectID) (bool, error) {
	return r.GameService.RemovePlayer(ctx, gameID, playerID)
}

// StartSearching is the resolver for the startSearching field.
func (r *mutationResolver) StartSearching(ctx context.Context, gameConfig *models.GameConfigInput) (*bool, error) {
	return r.GameService.StartSearching(ctx, gameConfig)
}

// AbortSearching is the resolver for the abortSearching field.
func (r *mutationResolver) AbortSearching(ctx context.Context) (*bool, error) {
	return r.GameService.AbortSearching(ctx)
}

// CancelGame is the resolver for the cancelGame field.
func (r *mutationResolver) CancelGame(ctx context.Context, gameID primitive.ObjectID) (*bool, error) {
	return r.GameService.CancelGame(ctx, gameID)
}

// RequestRematch is the resolver for the requestRematch field.
func (r *mutationResolver) RequestRematch(ctx context.Context, gameID primitive.ObjectID) (bool, error) {
	return r.GameService.RequestRematch(ctx, gameID)
}

// AcceptRematch is the resolver for the acceptRematch field.
func (r *mutationResolver) AcceptRematch(ctx context.Context, gameID primitive.ObjectID) (*models.Game, error) {
	return r.GameService.AcceptRematch(ctx, gameID)
}

// RejectRematch is the resolver for the rejectRematch field.
func (r *mutationResolver) RejectRematch(ctx context.Context, gameID primitive.ObjectID) (bool, error) {
	return r.GameService.RejectRematch(ctx, gameID)
}

// CancelRematchRequest is the resolver for the cancelRematchRequest field.
func (r *mutationResolver) CancelRematchRequest(ctx context.Context, gameID primitive.ObjectID) (bool, error) {
	return r.GameService.CancelRematchRequest(ctx, gameID)
}

// ChallengeUser is the resolver for the challengeUser field.
func (r *mutationResolver) ChallengeUser(ctx context.Context, challengeUserInput *models.ChallengeUserInput) (*models.Game, error) {
	return r.GameService.ChallengeUser(ctx, challengeUserInput)
}

// AcceptChallenge is the resolver for the acceptChallenge field.
func (r *mutationResolver) AcceptChallenge(ctx context.Context, gameID primitive.ObjectID) (*models.Game, error) {
	return r.GameService.AcceptChallenge(ctx, gameID)
}

// RejectChallenge is the resolver for the rejectChallenge field.
func (r *mutationResolver) RejectChallenge(ctx context.Context, gameID primitive.ObjectID) (*bool, error) {
	return r.GameService.RejectChallenge(ctx, gameID)
}

// SubmitFlashAnzanAnswer is the resolver for the submitFlashAnzanAnswer field.
func (r *mutationResolver) SubmitFlashAnzanAnswer(ctx context.Context, answerInput *models.SubmitFlashAnzanAnswerInput) (*models.Game, error) {
	return r.GameService.SubmitFlashAnzanAnswer(ctx, answerInput)
}

// EndAbilityDuelsGame is the resolver for the endAbilityDuelsGame field.
func (r *mutationResolver) EndAbilityDuelsGame(ctx context.Context, gameID *primitive.ObjectID) (*models.Game, error) {
	return r.GameService.EndAbilityDuelsGame(ctx, gameID)
}

// EndGameForShowdown is the resolver for the endGameForShowdown field.
func (r *mutationResolver) EndGameForShowdown(ctx context.Context, gameID *primitive.ObjectID) (*models.Game, error) {
	return r.GameService.EndGameForShowdown(ctx, gameID)
}

// GetGameByID is the resolver for the getGameById field.
func (r *queryResolver) GetGameByID(ctx context.Context, gameID *primitive.ObjectID) (*models.Game, error) {
	return r.GameService.GetGameByID(ctx, gameID)
}

// GetGamesByUser is the resolver for the getGamesByUser field.
func (r *queryResolver) GetGamesByUser(ctx context.Context, payload *models.GetGamesInput) (*models.GetGamesOutput, error) {
	return r.GameService.GetGamesByUser(ctx, payload)
}

// GetUserGamesByRatingType is the resolver for the getUserGamesByRatingType field.
func (r *queryResolver) GetUserGamesByRatingType(ctx context.Context, payload *models.GetGamesByRatingInput) (*models.GetGamesByRatingOutput, error) {
	return r.GameService.GetGamesByRatingType(ctx, payload)
}

// GetGameDetailedAnalysis is the resolver for the getGameDetailedAnalysis field.
func (r *queryResolver) GetGameDetailedAnalysis(ctx context.Context, gameID *primitive.ObjectID) (*models.GameDetailedAnalysis, error) {
	return r.GameService.GetGameDetailedAnalysis(ctx, gameID)
}

// SearchPlayer is the resolver for the searchPlayer field.
func (r *subscriptionResolver) SearchPlayer(ctx context.Context, userID *primitive.ObjectID) (<-chan *models.SearchSubscriptionOutput, error) {
	panic(fmt.Errorf("not implemented: SearchPlayer - searchPlayer"))
}

// GameEvent is the resolver for the gameEvent field.
func (r *subscriptionResolver) GameEvent(ctx context.Context, gameID *primitive.ObjectID) (<-chan *models.SubscriptionOutput, error) {
	panic(fmt.Errorf("not implemented: GameEvent - gameEvent"))
}

// RematchRequest is the resolver for the rematchRequest field.
func (r *subscriptionResolver) RematchRequest(ctx context.Context, gameID primitive.ObjectID) (<-chan *models.RematchRequestOutput, error) {
	panic(fmt.Errorf("not implemented: RematchRequest - rematchRequest"))
}
