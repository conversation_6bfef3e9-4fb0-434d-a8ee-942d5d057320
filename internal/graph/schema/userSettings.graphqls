type UserSettings {
    _id: ID!
    userId: ID!
    playSound: Boolean!
    hapticFeedback: Boolean!
    keyboardType: KeyboardType!
}

enum KeyboardType {
    TELEPHONE
    CALCULATOR
}

input UpdateSettingsInput {
    playSound: Boolean
    hapticFeedback: Boolean
    keyboardType: KeyboardType
}

extend type Query {
    getUserSettings: UserSettings @auth
}

extend type Mutation {
    updateUserSettings(settings: UpdateSettingsInput): UserSettings @auth
}
