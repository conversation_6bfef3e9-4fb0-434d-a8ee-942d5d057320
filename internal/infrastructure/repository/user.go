package repository

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/infrastructure/database"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/fx"
)

type UserRepository interface {
	Create(ctx context.Context, user *models.User) error
	GetByID(ctx context.Context, id primitive.ObjectID) (*models.User, error)
	GetByUsername(ctx context.Context, username string) (*models.User, error)
	GetByEmail(ctx context.Context, email string) (*models.User, error)
	IncrementUserFollowersCount(ctx context.Context, userId primitive.ObjectID) error
	IncrementUserFollowingsCount(ctx context.Context, userId primitive.ObjectID) error
	IncrementUserFriendsCount(ctx context.Context, userId primitive.ObjectID) error
	DecrementUserFollowersCount(ctx context.Context, userId primitive.ObjectID) error
	DecrementUserFollowingsCount(ctx context.Context, userId primitive.ObjectID) error
	DecrementUserFriendsCount(ctx context.Context, userId primitive.ObjectID) error
	UpdateUserStatikCoins(ctx context.Context, userID primitive.ObjectID, coins int) error
	FindOne(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions) (*models.User, error)
	UpdateOne(ctx context.Context, filter, update bson.M, opts ...*options.UpdateOptions) error
	Delete(ctx context.Context, id primitive.ObjectID) error
	GetUsersRatingCount(ctx context.Context, minRating, maxRating int) ([]*models.UserRatingCount, error)
	SearchByName(ctx context.Context, name string, limit int64) ([]*models.User, error)
	GetTopUsersByRating(ctx context.Context, limit int64) ([]*models.User, error)
	GetPaginatedUsersByRatingAndCreationTime(ctx context.Context, page, pageSize int64) ([]*models.User, error)
	GetTotalUsersCount(ctx context.Context, isGuest bool, ratingType *string) (int64, error)
	GetTopUsersByCountry(ctx context.Context, countryCode string, limit int64) ([]*models.User, error)
	Aggregate(ctx context.Context, pipeline mongo.Pipeline, opts ...*options.AggregateOptions) ([]*models.User, error)
	AggregateProjected(ctx context.Context, pipeline mongo.Pipeline, opts ...*options.AggregateOptions) (*mongo.Cursor, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
	CountDocuments(ctx context.Context, filter bson.M) (int64, error)
	EstimatedDocumentCount(ctx context.Context, opts ...*options.EstimatedDocumentCountOptions) (int64, error)
	Find(ctx context.Context, filter bson.M, opts ...*options.FindOptions) ([]*models.User, error)
	BulkWrite(ctx context.Context, models []mongo.WriteModel, opts ...*options.BulkWriteOptions) (*mongo.BulkWriteResult, error)
	GetPaginatedUsersByGlobalRank(ctx context.Context, page, pageSize int64) ([]*models.User, error)
	GetPaginatedUsersByRatingType(ctx context.Context, page, pageSize int64, ratingType string) ([]*models.User, error)
	GetGlobalTopPlayers(ctx context.Context, limit int) (*models.TopPlayersLeaderboard, error)
	GetFriendsLeaderboardByRatingType(ctx context.Context, userID primitive.ObjectID, page, pageSize int64, ratingType string) (*models.UserLeaderboardPage, error)
	GetFriendsTopPlayers(ctx context.Context, userID primitive.ObjectID, limit int) (*models.TopPlayersLeaderboard, error)
	GetUsersByIDs(ctx context.Context, ids []primitive.ObjectID) ([]*models.User, error)
	UpdateHasUnlockedAllGames(ctx context.Context, userID primitive.ObjectID, unlocked bool) error
	DeleteUser(userId primitive.ObjectID) error
}

type mongoUserRepository struct {
	collection *mongo.Collection
}

func NewUserRepository(lc fx.Lifecycle, db database.Database) UserRepository {
	collection := db.Collection("users")
	userRepo := mongoUserRepository{collection: collection}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "User repository initialized and ready.")
			go func() {
				err = userRepo.syncIndexes(ctx)
				if err != nil {
					zlog.Error(ctx, "Error syncing indexes for user repository: %v", err)
				}
			}()
			return db.Ping(ctx)
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down user repository.")
			return nil
		},
	})

	return &userRepo
}

func (r *mongoUserRepository) Collection() *mongo.Collection {
	return r.collection
}

func (r *mongoUserRepository) syncIndexes(ctx context.Context) error {
	return EnsureIndexes(ctx, r.collection, []mongo.IndexModel{
		{Keys: bson.D{{Key: "rating", Value: -1}}},
		{Keys: bson.D{{Key: "ratingV2.flashAnzanRating", Value: -1}}},
		{Keys: bson.D{{Key: "ratingV2.abilityDuelsRating", Value: -1}}},
		{Keys: bson.D{{Key: "referralCode", Value: 1}}, Options: options.Index().SetUnique(true)},
	})
}

func (r *mongoUserRepository) Create(ctx context.Context, user *models.User) error {
	timeNow := time.Now().UTC()
	user.CreatedAt = &timeNow
	user.UpdatedAt = &timeNow
	if user.Rating == nil || *user.Rating == 0 {
		*user.Rating = constants.DefaultRating
	}

	result, err := r.collection.InsertOne(ctx, user)
	if err != nil {
		return err
	}

	user.ID = result.InsertedID.(primitive.ObjectID)
	return nil
}

func (r *mongoUserRepository) GetByID(ctx context.Context, id primitive.ObjectID) (*models.User, error) {
	var user models.User

	err := r.collection.FindOne(ctx, bson.M{"_id": id}).Decode(&user)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

func (r *mongoUserRepository) GetPaginatedUsersByGlobalRank(ctx context.Context, page, pageSize int64) ([]*models.User, error) {
	skip := (page - 1) * pageSize
	opts := options.Find().SetSort(bson.D{
		{Key: "globalRank", Value: 1},
		// {Key: "createdAt", Value: 1},
	}).SetSkip(skip).SetLimit(pageSize)

	filter := bson.M{
		"isGuest": false,
		"$or": []bson.M{
			{"isShadowBanned": bson.M{"$exists": false}},
			{"isShadowBanned": false},
		},
	}
	cursor, err := r.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var users []*models.User
	if err = cursor.All(ctx, &users); err != nil {
		return nil, err
	}
	return users, nil
}

func (r *mongoUserRepository) DeleteUser(userId primitive.ObjectID) error {
	_, err := r.collection.DeleteOne(context.Background(), bson.M{"_id": userId})
	return err
}

func (r *mongoUserRepository) GetPaginatedUsersByRatingType(ctx context.Context, page, pageSize int64, ratingType string) ([]*models.User, error) {
	skip := (page - 1) * pageSize
	var sortField string
	switch ratingType {
	case constants.GlobalRating:
		sortField = constants.GlobalRatingField
	case constants.MemoryRating:
		sortField = constants.MemoryRatingField
	case constants.AbilityRating:
		sortField = constants.AbilityRatingField
	default:
		sortField = constants.GlobalRatingField
	}

	query := bson.M{
		"isGuest": false,
		sortField: bson.M{
			"$exists": true,
			"$ne":     nil,
		},
		"$or": []bson.M{
			{"isShadowBanned": bson.M{"$exists": false}},
			{"isShadowBanned": false},
		},
	}

	findOptions := options.Find().
		SetSort(bson.D{{Key: sortField, Value: -1}}).
		SetSkip(skip).
		SetLimit(pageSize)

	cursor, err := r.collection.Find(ctx, query, findOptions)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var users []*models.User
	if err := cursor.All(ctx, &users); err != nil {
		return nil, err
	}

	return users, nil
}

func (r *mongoUserRepository) GetGlobalTopPlayers(ctx context.Context, limit int) (*models.TopPlayersLeaderboard, error) {
	getTopPlayers := func(ratingField string) ([]*models.TopPlayerEntry, error) {
		query := bson.M{
			"isGuest":   false,
			ratingField: bson.M{"$exists": true},
			"$or": []bson.M{
				{"isShadowBanned": bson.M{"$exists": false}},
				{"isShadowBanned": false},
			},
		}

		findOptions := options.Find().
			SetSort(bson.D{{Key: ratingField, Value: -1}}).
			SetLimit(int64(limit))

		cursor, err := r.collection.Find(ctx, query, findOptions)
		if err != nil {
			return nil, err
		}
		defer cursor.Close(ctx)

		var users []*models.User
		if err := cursor.All(ctx, &users); err != nil {
			return nil, err
		}

		entries := make([]*models.TopPlayerEntry, len(users))
		for i, user := range users {
			var rating int
			switch ratingField {
			case constants.GlobalRatingField:
				rating = *user.Rating
			case constants.MemoryRatingField:
				rating = *user.RatingV2.FlashAnzanRating
			case constants.AbilityRatingField:
				rating = *user.RatingV2.AbilityDuelsRating
			}

			entries[i] = &models.TopPlayerEntry{
				User:   utils.GetUserPublicDetails(user),
				Rating: rating,
				Rank:   i + 1,
			}
		}

		return entries, nil
	}

	var globalPlayers, flashAnzanPlayers, abilityDuelsPlayers []*models.TopPlayerEntry
	var globalErr, flashErr, abilityErr error

	var wg sync.WaitGroup
	wg.Add(3)

	go func() {
		defer wg.Done()
		globalPlayers, globalErr = getTopPlayers(constants.GlobalRatingField)
	}()

	go func() {
		defer wg.Done()
		flashAnzanPlayers, flashErr = getTopPlayers(constants.MemoryRatingField)
	}()

	go func() {
		defer wg.Done()
		abilityDuelsPlayers, abilityErr = getTopPlayers(constants.AbilityRatingField)
	}()

	wg.Wait()
	if globalErr != nil {
		return nil, globalErr
	}
	if flashErr != nil {
		return nil, flashErr
	}
	if abilityErr != nil {
		return nil, abilityErr
	}

	return &models.TopPlayersLeaderboard{
		GlobalRating:  globalPlayers,
		MemoryRating:  flashAnzanPlayers,
		AbilityRating: abilityDuelsPlayers,
	}, nil
}

func (r *mongoUserRepository) GetFriendsLeaderboardByRatingType(ctx context.Context, userID primitive.ObjectID, page, pageSize int64, ratingType string) (*models.UserLeaderboardPage, error) {
	skip := (page - 1) * pageSize

	var ratingField string
	switch ratingType {
	case constants.GlobalRating:
		ratingField = constants.GlobalRatingField
	case constants.MemoryRating:
		ratingField = constants.MemoryRatingField
	case constants.AbilityRating:
		ratingField = constants.AbilityRatingField
	default:
		ratingField = constants.GlobalRatingField
	}

	friendsPipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{
			"$or": []bson.M{
				{"senderId": userID},
				{"receiverId": userID},
			},
			"acceptedAt": bson.M{"$exists": true},
		}}},
		{{Key: "$project", Value: bson.M{
			"friendId": bson.M{
				"$cond": bson.M{
					"if":   bson.M{"$eq": []interface{}{"$senderId", userID}},
					"then": "$receiverId",
					"else": "$senderId",
				},
			},
		}}},
	}

	cursor, err := r.collection.Database().Collection("friends").Aggregate(ctx, friendsPipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var friendsData []struct {
		FriendID primitive.ObjectID `bson:"friendId"`
	}
	if err := cursor.All(ctx, &friendsData); err != nil {
		return nil, err
	}

	friendIDs := make([]primitive.ObjectID, len(friendsData))
	for i, friend := range friendsData {
		friendIDs[i] = friend.FriendID
	}

	allIDs := append(friendIDs, userID)

	usersPipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{
			"_id":       bson.M{"$in": allIDs},
			"isGuest":   false,
			ratingField: bson.M{"$exists": true},
			"$or": []bson.M{
				{"isShadowBanned": bson.M{"$exists": false}},
				{"isShadowBanned": false},
			},
		}}},
		{{Key: "$sort", Value: bson.M{
			ratingField: -1,
		}}},
		{{Key: "$skip", Value: skip}},
		{{Key: "$limit", Value: pageSize}},
	}

	usersCursor, err := r.collection.Aggregate(ctx, usersPipeline)
	if err != nil {
		return nil, err
	}
	defer usersCursor.Close(ctx)

	var users []*models.User
	if err := usersCursor.All(ctx, &users); err != nil {
		return nil, err
	}

	edges := make([]*models.LeaderboardEdge, len(users))
	for i, user := range users {
		edges[i] = &models.LeaderboardEdge{
			Node:   utils.GetUserPublicDetails(user),
			Cursor: user.ID.Hex(),
		}
	}

	countPipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{
			"_id":       bson.M{"$in": allIDs},
			"isGuest":   false,
			ratingField: bson.M{"$exists": true},
		}}},
		{{Key: "$count", Value: "count"}},
	}

	countCursor, err := r.collection.Aggregate(ctx, countPipeline)
	if err != nil {
		return nil, err
	}
	defer countCursor.Close(ctx)

	var countResult []struct {
		Count int `bson:"count"`
	}

	if err := countCursor.All(ctx, &countResult); err != nil {
		return nil, err
	}

	totalCount := 0
	if len(countResult) > 0 {
		totalCount = countResult[0].Count
	}

	return &models.UserLeaderboardPage{
		Edges:      edges,
		TotalCount: totalCount,
	}, nil
}

func (r *mongoUserRepository) GetFriendsTopPlayers(ctx context.Context, userID primitive.ObjectID, limit int) (*models.TopPlayersLeaderboard, error) {
	getTopPlayersForRating := func(ratingField string) ([]*models.TopPlayerEntry, error) {
		var matchField, sortField string
		switch ratingField {
		case constants.GlobalRatingField:
			matchField = constants.GlobalRatingField
			sortField = constants.GlobalRatingField
		case constants.MemoryRatingField:
			matchField = constants.MemoryRatingField
			sortField = constants.MemoryRatingField
		case constants.AbilityRatingField:
			matchField = constants.AbilityRatingField
			sortField = constants.AbilityRatingField
		}

		friendsPipeline := []bson.M{
			{"$match": bson.M{
				"$or": []bson.M{
					{"senderId": userID},
					{"receiverId": userID},
				},
				"acceptedAt": bson.M{"$exists": true},
			}},
			{"$project": bson.M{
				"friendId": bson.M{
					"$cond": bson.M{
						"if":   bson.M{"$eq": []interface{}{"$senderId", userID}},
						"then": "$receiverId",
						"else": "$senderId",
					},
				},
			}},
		}

		cursor, err := r.collection.Database().Collection("friends").Aggregate(ctx, friendsPipeline)
		if err != nil {
			return nil, err
		}
		defer cursor.Close(ctx)

		var friendsData []struct {
			FriendID primitive.ObjectID `bson:"friendId"`
		}
		if err := cursor.All(ctx, &friendsData); err != nil {
			return nil, err
		}

		friendIDs := make([]primitive.ObjectID, len(friendsData))
		for i, friend := range friendsData {
			friendIDs[i] = friend.FriendID
		}

		allIDs := append(friendIDs, userID)

		pipeline := []bson.M{
			{"$match": bson.M{
				"_id":      bson.M{"$in": allIDs},
				"isGuest":  false,
				matchField: bson.M{"$exists": true},
				"$or": []bson.M{
					{"isShadowBanned": bson.M{"$exists": false}},
					{"isShadowBanned": false},
				},
			}},
			{"$sort": bson.M{sortField: -1}},
			{"$limit": limit},
		}

		usersCursor, err := r.collection.Aggregate(ctx, pipeline)
		if err != nil {
			return nil, err
		}
		defer usersCursor.Close(ctx)

		var users []*models.User
		if err := usersCursor.All(ctx, &users); err != nil {
			return nil, err
		}

		entries := make([]*models.TopPlayerEntry, len(users))
		for i, user := range users {
			var rating int
			switch ratingField {
			case constants.GlobalRatingField:
				if user.Rating != nil {
					rating = *user.Rating
				}
			case constants.MemoryRatingField:
				if user.RatingV2 != nil && user.RatingV2.FlashAnzanRating != nil {
					rating = *user.RatingV2.FlashAnzanRating
				}
			case constants.AbilityRatingField:
				if user.RatingV2 != nil && user.RatingV2.AbilityDuelsRating != nil {
					rating = *user.RatingV2.AbilityDuelsRating
				}
			}
			entries[i] = &models.TopPlayerEntry{
				User:   utils.GetUserPublicDetails(user),
				Rank:   i + 1,
				Rating: rating,
			}
		}

		return entries, nil
	}

	globalTopPlayers, err := getTopPlayersForRating(constants.GlobalRatingField)
	if err != nil {
		return nil, err
	}

	memoryTopPlayers, err := getTopPlayersForRating(constants.MemoryRatingField)
	if err != nil {
		return nil, err
	}

	abilityTopPlayers, err := getTopPlayersForRating(constants.AbilityRatingField)
	if err != nil {
		return nil, err
	}

	return &models.TopPlayersLeaderboard{
		GlobalRating:  globalTopPlayers,
		MemoryRating:  memoryTopPlayers,
		AbilityRating: abilityTopPlayers,
	}, nil
}

func (r *mongoUserRepository) GetPaginatedUsersByRatingAndCreationTime(ctx context.Context, page, pageSize int64) ([]*models.User, error) {
	skip := (page - 1) * pageSize
	opts := options.Find().SetSort(bson.D{
		{Key: "rating", Value: -1},
		// {Key: "createdAt", Value: 1},
	}).SetSkip(skip).SetLimit(pageSize)

	filter := bson.M{
		"isGuest": false,
		"$or": []bson.M{
			{"isShadowBanned": bson.M{"$exists": false}},
			{"isShadowBanned": false},
		},
	}
	cursor, err := r.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var users []*models.User
	if err = cursor.All(ctx, &users); err != nil {
		return nil, err
	}
	return users, nil
}

func (r *mongoUserRepository) GetTotalUsersCount(ctx context.Context, isGuest bool, ratingType *string) (int64, error) {
	filter := bson.M{
		"isGuest": false,
		"$or": []bson.M{
			{"isShadowBanned": bson.M{"$exists": false}},
			{"isShadowBanned": false},
		},
	}

	if ratingType != nil {
		var ratingField string
		if *ratingType != constants.GlobalRating {
			ratingField = "ratingV2." + *ratingType
		} else {
			ratingField = constants.GlobalRatingField
		}

		filter[ratingField] = bson.M{
			"$exists": true,
			"$ne":     nil,
		}
	}

	count, err := r.collection.CountDocuments(ctx, filter)
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (r *mongoUserRepository) GetByEmail(ctx context.Context, email string) (*models.User, error) {
	var user models.User
	err := r.collection.FindOne(ctx, bson.M{"email": email}).Decode(&user)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

func (r *mongoUserRepository) UpdateOne(ctx context.Context, filter, update bson.M, opts ...*options.UpdateOptions) error {
	opts = append(opts, options.Update().SetUpsert(true))
	_, err := r.collection.UpdateOne(ctx, filter, update, opts...)
	return err
}

func (r *mongoUserRepository) FindOne(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions) (*models.User, error) {
	var user models.User
	err := r.collection.FindOne(ctx, filter, opts...).Decode(&user)
	if err != nil {
		return nil, err
	}
	return &user, nil
}

func (r *mongoUserRepository) BulkWrite(ctx context.Context, models []mongo.WriteModel, opts ...*options.BulkWriteOptions) (*mongo.BulkWriteResult, error) {
	return r.collection.BulkWrite(ctx, models, opts...)
}

func (r *mongoUserRepository) Delete(ctx context.Context, id primitive.ObjectID) error {
	_, err := r.collection.DeleteOne(ctx, bson.M{"_id": id})
	return err
}

func (r *mongoUserRepository) GetUsersRatingCount(ctx context.Context, minRating, maxRating int) ([]*models.UserRatingCount, error) {
	pipeline := []bson.M{
		{
			"$match": bson.M{
				"rating": bson.M{
					"$gte": minRating,
					"$lte": maxRating,
				},
				"isGuest": false,
				"$or": []bson.M{
					{"isShadowBanned": bson.M{"$exists": false}},
					{"isShadowBanned": false},
				},
			},
		},
		{
			"$group": bson.M{
				"_id":   "$rating",
				"count": bson.M{"$sum": 1},
			},
		},
	}
	cursor, err := r.collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, fmt.Errorf("aggregation failed: %w", err)
	}
	defer cursor.Close(ctx)

	var result []*models.UserRatingCount

	if err := cursor.All(ctx, &result); err != nil {
		return nil, fmt.Errorf("failed to decode aggregation result: %w", err)
	}
	return result, nil
}

func (r *mongoUserRepository) SearchByName(ctx context.Context, name string, limit int64) ([]*models.User, error) {
	opts := options.Find().SetLimit(limit)
	cursor, err := r.collection.Find(ctx, bson.M{"$text": bson.M{"$search": name}}, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var users []*models.User
	if err = cursor.All(ctx, &users); err != nil {
		return nil, err
	}
	return users, nil
}

func (r *mongoUserRepository) GetTopUsersByRating(ctx context.Context, limit int64) ([]*models.User, error) {
	opts := options.Find().SetSort(bson.D{{Key: "rating", Value: -1}}).SetLimit(limit)
	filter := bson.M{
		"$or": []bson.M{
			{"isShadowBanned": bson.M{"$exists": false}},
			{"isShadowBanned": false},
		},
	}
	cursor, err := r.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var users []*models.User
	if err = cursor.All(ctx, &users); err != nil {
		return nil, err
	}
	return users, nil
}

func (r *mongoUserRepository) GetTopUsersByCountry(ctx context.Context, countryCode string, limit int64) ([]*models.User, error) {
	opts := options.Find().SetSort(bson.D{{Key: "rating", Value: -1}}).SetLimit(limit)
	filter := bson.M{
		"countryCode": countryCode,
		"$or": []bson.M{
			{"isShadowBanned": bson.M{"$exists": false}},
			{"isShadowBanned": false},
		},
	}
	cursor, err := r.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var users []*models.User
	if err = cursor.All(ctx, &users); err != nil {
		return nil, err
	}
	return users, nil
}

func (r *mongoUserRepository) Aggregate(ctx context.Context, pipeline mongo.Pipeline, opts ...*options.AggregateOptions) ([]*models.User, error) {
	cursor, err := r.collection.Aggregate(ctx, pipeline, opts...)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)
	var users []*models.User
	if err = cursor.All(ctx, &users); err != nil {
		return nil, err
	}
	return users, nil
}

func (r *mongoUserRepository) AggregateProjected(ctx context.Context, pipeline mongo.Pipeline, opts ...*options.AggregateOptions) (*mongo.Cursor, error) {
	cursor, err := r.collection.Aggregate(ctx, pipeline, opts...)
	if err != nil {
		return nil, err
	}
	return cursor, nil
}

func (r *mongoUserRepository) Count(ctx context.Context, filter bson.M) (int64, error) {
	return r.collection.CountDocuments(ctx, filter)
}

func (r *mongoUserRepository) CountDocuments(ctx context.Context, filter bson.M) (int64, error) {
	return r.collection.CountDocuments(ctx, filter)
}

func (r *mongoUserRepository) EstimatedDocumentCount(ctx context.Context, opts ...*options.EstimatedDocumentCountOptions) (int64, error) {
	return r.collection.EstimatedDocumentCount(ctx, opts...)
}

func (r *mongoUserRepository) GetByUsername(ctx context.Context, username string) (*models.User, error) {
	var user models.User

	filter := bson.M{"username": username}

	err := r.collection.FindOne(ctx, filter).Decode(&user)
	if err != nil {
		return nil, err
	}

	return &user, nil
}

func (r *mongoUserRepository) IncrementUserFollowersCount(ctx context.Context, userId primitive.ObjectID) error {
	filter := bson.M{"_id": userId}
	pipeline := mongo.Pipeline{
		bson.D{
			{Key: "$set", Value: bson.D{
				{Key: "stats.followersCount", Value: bson.D{
					{Key: "$ifNull", Value: bson.A{"$stats.followersCount", 0}},
				}},
			}},
		},
		bson.D{
			{Key: "$set", Value: bson.D{
				{Key: "stats.followersCount", Value: bson.D{
					{Key: "$add", Value: bson.A{"$stats.followersCount", 1}},
				}},
			}},
		},
	}

	_, err := r.collection.UpdateOne(ctx, filter, pipeline)
	if err != nil {
		return fmt.Errorf("failed to increment followers count: %w", err)
	}
	return nil
}

func (r *mongoUserRepository) IncrementUserFollowingsCount(ctx context.Context, userId primitive.ObjectID) error {
	filter := bson.M{"_id": userId}
	pipeline := mongo.Pipeline{
		bson.D{
			{Key: "$set", Value: bson.D{
				{Key: "stats.followingsCount", Value: bson.D{
					{Key: "$ifNull", Value: bson.A{"$stats.followingsCount", 0}},
				}},
			}},
		},
		bson.D{
			{Key: "$set", Value: bson.D{
				{Key: "stats.followingsCount", Value: bson.D{
					{Key: "$add", Value: bson.A{"$stats.followingsCount", 1}},
				}},
			}},
		},
	}

	_, err := r.collection.UpdateOne(ctx, filter, pipeline)
	if err != nil {
		return fmt.Errorf("failed to increment followings count: %w", err)
	}
	return nil
}

func (r *mongoUserRepository) IncrementUserFriendsCount(ctx context.Context, userId primitive.ObjectID) error {
	filter := bson.M{"_id": userId}
	pipeline := mongo.Pipeline{
		bson.D{
			{Key: "$set", Value: bson.D{
				{Key: "stats.friendsCount", Value: bson.D{
					{Key: "$ifNull", Value: bson.A{"$stats.friendsCount", 0}},
				}},
			}},
		},
		bson.D{
			{Key: "$set", Value: bson.D{
				{Key: "stats.friendsCount", Value: bson.D{
					{Key: "$add", Value: bson.A{"$stats.friendsCount", 1}},
				}},
			}},
		},
	}

	_, err := r.collection.UpdateOne(ctx, filter, pipeline)
	if err != nil {
		return fmt.Errorf("failed to increment friends count: %w", err)
	}
	return nil
}

func (r *mongoUserRepository) DecrementUserFollowersCount(ctx context.Context, userId primitive.ObjectID) error {
	filter := bson.M{"_id": userId}
	pipeline := mongo.Pipeline{
		bson.D{
			{Key: "$set", Value: bson.D{
				{Key: "stats.followersCount", Value: bson.D{
					{Key: "$ifNull", Value: bson.A{"$stats.followersCount", 0}},
				}},
			}},
		},
		bson.D{
			{Key: "$set", Value: bson.D{
				{Key: "stats.followersCount", Value: bson.D{
					{Key: "$max", Value: bson.A{0, bson.D{{Key: "$subtract", Value: bson.A{"$stats.followersCount", 1}}}}},
				}},
			}},
		},
	}

	_, err := r.collection.UpdateOne(ctx, filter, pipeline)
	if err != nil {
		return fmt.Errorf("failed to decrement followers count: %w", err)
	}
	return nil
}

func (r *mongoUserRepository) DecrementUserFollowingsCount(ctx context.Context, userId primitive.ObjectID) error {
	filter := bson.M{"_id": userId}
	pipeline := mongo.Pipeline{
		bson.D{
			{Key: "$set", Value: bson.D{
				{Key: "stats.followingsCount", Value: bson.D{
					{Key: "$ifNull", Value: bson.A{"$stats.followingsCount", 0}},
				}},
			}},
		},
		bson.D{
			{Key: "$set", Value: bson.D{
				{Key: "stats.followingsCount", Value: bson.D{
					{Key: "$max", Value: bson.A{0, bson.D{{Key: "$subtract", Value: bson.A{"$stats.followingsCount", 1}}}}},
				}},
			}},
		},
	}

	_, err := r.collection.UpdateOne(ctx, filter, pipeline)
	if err != nil {
		return fmt.Errorf("failed to decrement followings count: %w", err)
	}
	return nil
}

func (r *mongoUserRepository) DecrementUserFriendsCount(ctx context.Context, userId primitive.ObjectID) error {
	filter := bson.M{"_id": userId}
	pipeline := mongo.Pipeline{
		bson.D{
			{Key: "$set", Value: bson.D{
				{Key: "stats.friendsCount", Value: bson.D{
					{Key: "$ifNull", Value: bson.A{"$stats.friendsCount", 0}},
				}},
			}},
		},
		bson.D{
			{Key: "$set", Value: bson.D{
				{Key: "stats.friendsCount", Value: bson.D{
					{Key: "$max", Value: bson.A{0, bson.D{{Key: "$subtract", Value: bson.A{"$stats.friendsCount", 1}}}}},
				}},
			}},
		},
	}

	_, err := r.collection.UpdateOne(ctx, filter, pipeline)
	if err != nil {
		return fmt.Errorf("failed to decrement friends count: %w", err)
	}
	return nil
}

func (r *mongoUserRepository) Find(ctx context.Context, filter bson.M, opts ...*options.FindOptions) ([]*models.User, error) {
	cursor, err := r.collection.Find(ctx, filter, opts...)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)
	var users []*models.User
	if err = cursor.All(ctx, &users); err != nil {
		return nil, err
	}
	return users, nil
}

func (r *mongoUserRepository) UpdateUserStatikCoins(ctx context.Context, userID primitive.ObjectID, coins int) error {
	filter := bson.M{
		"$and": bson.A{
			bson.M{"_id": userID},
			bson.M{"$or": bson.A{
				bson.M{"isBot": false},
				bson.M{"isBot": bson.M{"$exists": false}},
				bson.M{"isBot": nil},
			}},
		},
	}
	update := bson.M{
		"$inc": bson.M{
			"statikCoins": coins,
		},
	}
	_, err := r.collection.UpdateOne(ctx, filter, update)

	return err
}

func (r *mongoUserRepository) GetUsersByIDs(ctx context.Context, ids []primitive.ObjectID) ([]*models.User, error) {
	filter := bson.M{"_id": bson.M{"$in": ids}}
	cursor, err := r.collection.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)
	var users []*models.User
	if err = cursor.All(ctx, &users); err != nil {
		return nil, err
	}
	return users, nil
}

func (r *mongoUserRepository) UpdateHasUnlockedAllGames(ctx context.Context, userID primitive.ObjectID, unlocked bool) error {
	filter := bson.M{"_id": userID}
	update := bson.M{
		"$set": bson.M{
			"additional.hasUnlockedAllGames": unlocked,
		},
	}

	_, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil
		}
		return fmt.Errorf("failed to update user additional data: %w", err)
	}

	return nil
}
