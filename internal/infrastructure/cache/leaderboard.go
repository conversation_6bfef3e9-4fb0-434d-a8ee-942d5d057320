package cache

import (
	"context"
	"errors"
	"fmt"

	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"github.com/redis/go-redis/v9"
	"go.uber.org/fx"
)

type LeaderboardCache interface {
	AddOrUpdateScore(ctx context.Context, leaderboardKey, userID string, score float64) error
	GetRank(ctx context.Context, leaderboardKey, userID string) (int64, error)
	GetScore(ctx context.Context, leaderboardKey, userID string) (float64, error)
	GetTopUsers(ctx context.Context, leaderboardKey string, count int64) ([]redis.Z, error)
	GetUsersByPreviousID(ctx context.Context, leaderboardKey, previousID string, pageSize int64) ([]redis.Z, error)
	GetPaginatedLeaderboard(ctx context.Context, leaderboardKey string, start, limit int64) ([]*LeaderboardEntity, error)
	GetPaginatedLeaderboardRev(ctx context.Context, leaderboardKey string, start, limit int64) ([]*LeaderboardEntity, error)
	GetAllLeaderboard(ctx context.Context, leaderboardKey string) ([]*LeaderboardEntity, error)
	DeleteLeaderboard(ctx context.Context, leaderboardKey string) error
	GetTotalEntityCount(ctx context.Context, leaderboardKey string) (int64, error)
}

type LeaderboardEntity struct {
	Rank  int64   `json:"rank"`
	ID    string  `json:"id"`
	Score float64 `json:"score"`
}

func NewRedisLeaderboardCache(lc fx.Lifecycle, client *redis.Client) LeaderboardCache {
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) (err error) {
			zlog.Debug(ctx, "Leaderboard cache initialized and ready.")
			return nil
		},
		OnStop: func(ctx context.Context) error {
			zlog.Debug(ctx, "Shutting down leaderboard cache.")
			return nil
		},
	})
	return &RedisCache{client: client}
}

func (c *RedisCache) GetAllLeaderboard(ctx context.Context, leaderboardKey string) ([]*LeaderboardEntity, error) {
	results, err := c.client.ZRevRangeWithScores(ctx, leaderboardKey, 0, -1).Result()
	if err != nil {
		return nil, err
	}
	leaderboard := make([]*LeaderboardEntity, 0, len(results))
	for i, result := range results {
		leaderboard = append(leaderboard, &LeaderboardEntity{
			Rank:  int64(i) + 1,
			ID:    result.Member.(string),
			Score: result.Score,
		})
	}
	return leaderboard, nil
}

func (c *RedisCache) AddOrUpdateScore(ctx context.Context, leaderboardKey, userID string, score float64) error {
	return c.client.ZAdd(ctx, leaderboardKey, redis.Z{
		Score:  score,
		Member: userID,
	}).Err()
}

func (c *RedisCache) GetRank(ctx context.Context, leaderboardKey, userID string) (int64, error) {
	rank, err := c.client.ZRevRank(ctx, leaderboardKey, userID).Result()
	if errors.Is(err, redis.Nil) {
		return 0, fmt.Errorf("user %s not found", userID)
	} else if err != nil {
		return 0, err
	}
	return rank + 1, nil
}

func (c *RedisCache) GetScore(ctx context.Context, leaderboardKey, userID string) (float64, error) {
	score, err := c.client.ZScore(ctx, leaderboardKey, userID).Result()
	if errors.Is(err, redis.Nil) {
		return 0, fmt.Errorf("user %s not found", userID)
	} else if err != nil {
		return 0, err
	}
	return score, nil
}

func (c *RedisCache) GetTopUsers(ctx context.Context, leaderboardKey string, count int64) ([]redis.Z, error) {
	return c.client.ZRevRangeWithScores(ctx, leaderboardKey, 0, count-1).Result()
}

func (c *RedisCache) GetUsersByPreviousID(ctx context.Context, leaderboardKey, previousID string, pageSize int64) ([]redis.Z, error) {
	var startScore float64
	var err error

	if previousID == "" {
		startScore = 1e10
	} else {
		startScore, err = c.client.ZScore(ctx, leaderboardKey, previousID).Result()
		if errors.Is(err, redis.Nil) {
			return nil, fmt.Errorf("user %s not found", previousID)
		} else if err != nil {
			return nil, err
		}
	}

	return c.client.ZRevRangeByScoreWithScores(ctx, leaderboardKey, &redis.ZRangeBy{
		Min:   "-inf",
		Max:   fmt.Sprintf("%f", startScore-1),
		Count: pageSize,
	}).Result()
}

func (c *RedisCache) GetTotalEntityCount(ctx context.Context, leaderboardKey string) (int64, error) {
	count, err := c.client.ZCard(ctx, leaderboardKey).Result()
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (c *RedisCache) GetPaginatedLeaderboard(ctx context.Context, leaderboardKey string, start, limit int64) ([]*LeaderboardEntity, error) {
	end := start + limit - 1
	results, err := c.client.ZRangeWithScores(ctx, leaderboardKey, start, end).Result()
	if err != nil {
		return nil, err
	}

	leaderboard := make([]*LeaderboardEntity, 0, len(results))
	for i, result := range results {
		leaderboard = append(leaderboard, &LeaderboardEntity{
			Rank:  start + int64(i) + 1,
			ID:    result.Member.(string),
			Score: result.Score,
		})
	}

	return leaderboard, nil
}

func (c *RedisCache) GetPaginatedLeaderboardRev(ctx context.Context, leaderboardKey string, start, limit int64) ([]*LeaderboardEntity, error) {
	end := start + limit - 1
	results, err := c.client.ZRevRangeWithScores(ctx, leaderboardKey, start, end).Result()
	if err != nil {
		return nil, err
	}

	leaderboard := make([]*LeaderboardEntity, 0, len(results))
	for i, result := range results {
		leaderboard = append(leaderboard, &LeaderboardEntity{
			Rank:  start + int64(i) + 1,
			ID:    result.Member.(string),
			Score: result.Score,
		})
	}

	return leaderboard, nil
}

func (c *RedisCache) DeleteLeaderboard(ctx context.Context, leaderboardKey string) error {
	return c.client.Del(ctx, leaderboardKey).Err()
}
