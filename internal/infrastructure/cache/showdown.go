package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

const (
	showdownKeyPrefix            string = "showdown:"
	fixturesKeyPrefix            string = "fixtures:"
	showdownParticipantKeyPrefix string = "showdownParticipant:"
	showdownLeaderboardKeyPrefix string = "showdownLeaderboard:"
)

var showdownKey = func(showdownId primitive.ObjectID) string {
	return fmt.Sprintf("%s_%s", showdownKeyPrefix, showdownId.Hex())
}

var fixturesKey = func(showdownId primitive.ObjectID) string {
	return fmt.Sprintf("%s_%s", fixturesKeyPrefix, showdownId.Hex())
}

var showdownParticipantIdUserIdKey = func(showdownId, userId primitive.ObjectID) string {
	return fmt.Sprintf("%s_%s_%s", showdownParticipantKeyPrefix, showdownId.Hex(), userId.Hex())
}

var showdownLeaderboardKey = func(showdownId primitive.ObjectID) string {
	return fmt.Sprintf("%s_%s", showdownLeaderboardKeyPrefix, showdownId.Hex())
}

var showdownParticipantKey = func(showdownId, participantId primitive.ObjectID) string {
	return fmt.Sprintf("%s_%s_%s", showdownParticipantKeyPrefix, showdownId.Hex(), participantId.Hex())
}

type ShowdownCache interface {
	GetShowdown(ctx context.Context, showdownId primitive.ObjectID) (*models.Showdown, error)
	SetShowdown(ctx context.Context, showdown *models.Showdown) error
	DeleteShowdown(ctx context.Context, showdownId primitive.ObjectID) error
	GetShowdownParticipant(ctx context.Context, showdownId, participantId primitive.ObjectID) (*models.ShowdownParticipant, error)
	SetShowdownParticipant(ctx context.Context, showdownParticipant *models.ShowdownParticipant) (int64, error)
	DeleteShowdownParticipant(ctx context.Context, showdownId, participantId primitive.ObjectID) error
	DeleteAllShowdowns(ctx context.Context) error
	GetLeaderboardEntitiesCount(ctx context.Context, showdownId primitive.ObjectID) (int64, error)
	GetPaginatedLeaderboard(ctx context.Context, showdownId primitive.ObjectID, page, pageSize int) ([]*LeaderboardEntity, error)
	GetPaginatedLeaderboardRev(ctx context.Context, showdownId primitive.ObjectID, page, pageSize int) ([]*LeaderboardEntity, error)
	SetFixtures(ctx context.Context, showdownId primitive.ObjectID, fictures *models.FicturesCollection) error
	GetFixtures(ctx context.Context, showdownId primitive.ObjectID) (*models.FicturesCollection, error)
	GetAllLeaderboardEntities(ctx context.Context, showdownId primitive.ObjectID) ([]*LeaderboardEntity, error)
	SetShowdownParticipantIdOnUserId(ctx context.Context, showdownId, participantId, userID primitive.ObjectID) error
	GetShowdownParticipantIdFromUserId(ctx context.Context, showdownId, userID primitive.ObjectID) (primitive.ObjectID, error)
}

type ShowdownCacheWrapper struct {
	cache            Cache
	leaderboardCache LeaderboardCache
}

func NewShowdownCacheWrapper(cache Cache, leaderboardCache LeaderboardCache) ShowdownCache {
	return &ShowdownCacheWrapper{cache: cache, leaderboardCache: leaderboardCache}
}

func (c *ShowdownCacheWrapper) GetShowdown(ctx context.Context, showdownId primitive.ObjectID) (*models.Showdown, error) {
	data, err := c.cache.Get(ctx, showdownKey(showdownId))
	if err != nil {
		return nil, err
	}
	showdown := &models.Showdown{}
	if err := json.Unmarshal(data, showdown); err != nil {
		return nil, err
	}
	return showdown, nil
}

func (c *ShowdownCacheWrapper) SetShowdown(ctx context.Context, showdown *models.Showdown) error {
	data, err := json.Marshal(showdown)
	if err != nil {
		return err
	}
	return c.cache.Set(ctx, showdownKey(*showdown.ID), data, 24*time.Hour)
}

func (c *ShowdownCacheWrapper) DeleteShowdown(ctx context.Context, showdownId primitive.ObjectID) error {
	return c.cache.Delete(ctx, showdownKey(showdownId))
}

func (c *ShowdownCacheWrapper) GetShowdownParticipant(ctx context.Context, showdownId, participantId primitive.ObjectID) (*models.ShowdownParticipant, error) {
	data, err := c.cache.Get(ctx, showdownParticipantKey(showdownId, participantId))
	if err != nil {
		return nil, err
	}
	showdownParticipant := &models.ShowdownParticipant{}
	if err := json.Unmarshal(data, showdownParticipant); err != nil {
		return nil, err
	}
	return showdownParticipant, nil
}

func (c *ShowdownCacheWrapper) SetShowdownParticipantIdOnUserId(ctx context.Context, showdownId, participantId, userID primitive.ObjectID) error {
	return c.cache.Set(ctx, showdownParticipantIdUserIdKey(showdownId, userID), []byte(participantId.Hex()), 24*time.Hour)
}

func (c *ShowdownCacheWrapper) GetShowdownParticipantIdFromUserId(ctx context.Context, showdownId, userID primitive.ObjectID) (primitive.ObjectID, error) {
	data, err := c.cache.Get(ctx, showdownParticipantIdUserIdKey(showdownId, userID))
	if err != nil {
		return primitive.ObjectID{}, err
	}
	return primitive.ObjectIDFromHex(string(data))
}

func (c *ShowdownCacheWrapper) SetShowdownParticipant(ctx context.Context, showdownParticipant *models.ShowdownParticipant) (int64, error) {
	if showdownParticipant == nil {
		return 0, fmt.Errorf("participant is nil")
	}
	data, err := json.Marshal(showdownParticipant)
	if err != nil {
		return 0, err
	}
	err = c.leaderboardCache.AddOrUpdateScore(ctx, showdownLeaderboardKey(showdownParticipant.ShowdownID), showdownParticipant.ID.Hex(), float64(showdownParticipant.TotalScore))
	if err != nil {
		return 0, err
	}
	err = c.cache.Set(ctx, showdownParticipantKey(showdownParticipant.ShowdownID, *showdownParticipant.ID), data, 24*time.Hour)
	if err != nil {
		return 0, err
	}
	err = c.SetShowdownParticipantIdOnUserId(ctx, showdownParticipant.ShowdownID, *showdownParticipant.ID, showdownParticipant.UserID)
	if err != nil {
		return 0, err
	}
	revRank, err := c.leaderboardCache.GetRank(ctx, showdownLeaderboardKey(showdownParticipant.ShowdownID), showdownParticipant.ID.Hex())
	if err != nil {
		return 0, nil
	}
	return revRank, nil
}

func (c *ShowdownCacheWrapper) GetLeaderboardEntitiesCount(ctx context.Context, showdownId primitive.ObjectID) (int64, error) {
	totalCount, err := c.leaderboardCache.GetTotalEntityCount(ctx, showdownLeaderboardKey(showdownId))
	if err != nil {
		return 0, err
	}
	return totalCount, nil
}

// Get from bottom scorers to top

func (c *ShowdownCacheWrapper) GetPaginatedLeaderboardRev(ctx context.Context, showdownId primitive.ObjectID, page, pageSize int) ([]*LeaderboardEntity, error) {
	if page < 1 {
		page = 1
	}
	start := (page - 1) * pageSize
	leaderBoardEntities, err := c.leaderboardCache.GetPaginatedLeaderboard(ctx, showdownLeaderboardKey(showdownId), int64(start), int64(pageSize))
	if err != nil {
		return nil, err
	}
	return leaderBoardEntities, nil
}

// Get from top scorers to bottom
func (c *ShowdownCacheWrapper) GetPaginatedLeaderboard(ctx context.Context, showdownId primitive.ObjectID, page, pageSize int) ([]*LeaderboardEntity, error) {
	if page < 1 {
		page = 1
	}
	start := (page - 1) * pageSize
	leaderBoardEntities, err := c.leaderboardCache.GetPaginatedLeaderboardRev(ctx, showdownLeaderboardKey(showdownId), int64(start), int64(pageSize))
	if err != nil {
		return nil, err
	}
	return leaderBoardEntities, nil
}

// get all from top scorers to bottom
func (c *ShowdownCacheWrapper) GetAllLeaderboardEntities(ctx context.Context, showdownId primitive.ObjectID) ([]*LeaderboardEntity, error) {
	leaderBoardEntities, err := c.leaderboardCache.GetAllLeaderboard(ctx, showdownLeaderboardKey(showdownId))
	if err != nil {
		return nil, err
	}
	return leaderBoardEntities, nil
}

func (c *ShowdownCacheWrapper) DeleteShowdownParticipant(ctx context.Context, showdownId, userId primitive.ObjectID) error {
	return c.cache.Delete(ctx, showdownParticipantIdUserIdKey(showdownId, userId))
}

func (c *ShowdownCacheWrapper) DeleteAllShowdowns(ctx context.Context) error {
	return c.cache.DeleteAll(ctx, showdownKeyPrefix+"*")
}

func (c *ShowdownCacheWrapper) GetFixtures(ctx context.Context, showdownId primitive.ObjectID) (*models.FicturesCollection, error) {
	data, err := c.cache.Get(ctx, fixturesKey(showdownId))
	if err != nil {
		return nil, err
	}
	showdown := &models.FicturesCollection{}
	if err := json.Unmarshal(data, showdown); err != nil {
		return nil, err
	}
	return showdown, nil
}

func (c *ShowdownCacheWrapper) SetFixtures(ctx context.Context, showdownId primitive.ObjectID, fictures *models.FicturesCollection) error {
	data, err := json.Marshal(fictures)
	if err != nil {
		return err
	}
	return c.cache.Set(ctx, fixturesKey(showdownId), data, 24*time.Hour)
}
