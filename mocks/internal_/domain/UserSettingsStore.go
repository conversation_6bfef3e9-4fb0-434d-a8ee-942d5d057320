// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package domain

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
)

// NewMockUserSettingsStore creates a new instance of MockUserSettingsStore. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockUserSettingsStore(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockUserSettingsStore {
	mock := &MockUserSettingsStore{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockUserSettingsStore is an autogenerated mock type for the UserSettingsStore type
type MockUserSettingsStore struct {
	mock.Mock
}

type MockUserSettingsStore_Expecter struct {
	mock *mock.Mock
}

func (_m *MockUserSettingsStore) EXPECT() *MockUserSettingsStore_Expecter {
	return &MockUserSettingsStore_Expecter{mock: &_m.Mock}
}

// GetUserSettings provides a mock function for the type MockUserSettingsStore
func (_mock *MockUserSettingsStore) GetUserSettings(ctx context.Context) (*models.UserSettings, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetUserSettings")
	}

	var r0 *models.UserSettings
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*models.UserSettings, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *models.UserSettings); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserSettings)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserSettingsStore_GetUserSettings_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserSettings'
type MockUserSettingsStore_GetUserSettings_Call struct {
	*mock.Call
}

// GetUserSettings is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockUserSettingsStore_Expecter) GetUserSettings(ctx interface{}) *MockUserSettingsStore_GetUserSettings_Call {
	return &MockUserSettingsStore_GetUserSettings_Call{Call: _e.mock.On("GetUserSettings", ctx)}
}

func (_c *MockUserSettingsStore_GetUserSettings_Call) Run(run func(ctx context.Context)) *MockUserSettingsStore_GetUserSettings_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockUserSettingsStore_GetUserSettings_Call) Return(userSettings *models.UserSettings, err error) *MockUserSettingsStore_GetUserSettings_Call {
	_c.Call.Return(userSettings, err)
	return _c
}

func (_c *MockUserSettingsStore_GetUserSettings_Call) RunAndReturn(run func(ctx context.Context) (*models.UserSettings, error)) *MockUserSettingsStore_GetUserSettings_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateUserSettings provides a mock function for the type MockUserSettingsStore
func (_mock *MockUserSettingsStore) UpdateUserSettings(ctx context.Context, settings *models.UpdateSettingsInput) (*models.UserSettings, error) {
	ret := _mock.Called(ctx, settings)

	if len(ret) == 0 {
		panic("no return value specified for UpdateUserSettings")
	}

	var r0 *models.UserSettings
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UpdateSettingsInput) (*models.UserSettings, error)); ok {
		return returnFunc(ctx, settings)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UpdateSettingsInput) *models.UserSettings); ok {
		r0 = returnFunc(ctx, settings)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserSettings)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.UpdateSettingsInput) error); ok {
		r1 = returnFunc(ctx, settings)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserSettingsStore_UpdateUserSettings_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateUserSettings'
type MockUserSettingsStore_UpdateUserSettings_Call struct {
	*mock.Call
}

// UpdateUserSettings is a helper method to define mock.On call
//   - ctx context.Context
//   - settings *models.UpdateSettingsInput
func (_e *MockUserSettingsStore_Expecter) UpdateUserSettings(ctx interface{}, settings interface{}) *MockUserSettingsStore_UpdateUserSettings_Call {
	return &MockUserSettingsStore_UpdateUserSettings_Call{Call: _e.mock.On("UpdateUserSettings", ctx, settings)}
}

func (_c *MockUserSettingsStore_UpdateUserSettings_Call) Run(run func(ctx context.Context, settings *models.UpdateSettingsInput)) *MockUserSettingsStore_UpdateUserSettings_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.UpdateSettingsInput
		if args[1] != nil {
			arg1 = args[1].(*models.UpdateSettingsInput)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockUserSettingsStore_UpdateUserSettings_Call) Return(userSettings *models.UserSettings, err error) *MockUserSettingsStore_UpdateUserSettings_Call {
	_c.Call.Return(userSettings, err)
	return _c
}

func (_c *MockUserSettingsStore_UpdateUserSettings_Call) RunAndReturn(run func(ctx context.Context, settings *models.UpdateSettingsInput) (*models.UserSettings, error)) *MockUserSettingsStore_UpdateUserSettings_Call {
	_c.Call.Return(run)
	return _c
}
