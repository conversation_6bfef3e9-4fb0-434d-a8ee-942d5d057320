// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package domain

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockDailyChallengeStore creates a new instance of MockDailyChallengeStore. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockDailyChallengeStore(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockDailyChallengeStore {
	mock := &MockDailyChallengeStore{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockDailyChallengeStore is an autogenerated mock type for the DailyChallengeStore type
type MockDailyChallengeStore struct {
	mock.Mock
}

type MockDailyChallengeStore_Expecter struct {
	mock *mock.Mock
}

func (_m *MockDailyChallengeStore) EXPECT() *MockDailyChallengeStore_Expecter {
	return &MockDailyChallengeStore_Expecter{mock: &_m.Mock}
}

// AttemptDailyChallenge provides a mock function for the type MockDailyChallengeStore
func (_mock *MockDailyChallengeStore) AttemptDailyChallenge(ctx context.Context, challengeID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, challengeID)

	if len(ret) == 0 {
		panic("no return value specified for AttemptDailyChallenge")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, challengeID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, challengeID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, challengeID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockDailyChallengeStore_AttemptDailyChallenge_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AttemptDailyChallenge'
type MockDailyChallengeStore_AttemptDailyChallenge_Call struct {
	*mock.Call
}

// AttemptDailyChallenge is a helper method to define mock.On call
//   - ctx context.Context
//   - challengeID primitive.ObjectID
func (_e *MockDailyChallengeStore_Expecter) AttemptDailyChallenge(ctx interface{}, challengeID interface{}) *MockDailyChallengeStore_AttemptDailyChallenge_Call {
	return &MockDailyChallengeStore_AttemptDailyChallenge_Call{Call: _e.mock.On("AttemptDailyChallenge", ctx, challengeID)}
}

func (_c *MockDailyChallengeStore_AttemptDailyChallenge_Call) Run(run func(ctx context.Context, challengeID primitive.ObjectID)) *MockDailyChallengeStore_AttemptDailyChallenge_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockDailyChallengeStore_AttemptDailyChallenge_Call) Return(b bool, err error) *MockDailyChallengeStore_AttemptDailyChallenge_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockDailyChallengeStore_AttemptDailyChallenge_Call) RunAndReturn(run func(ctx context.Context, challengeID primitive.ObjectID) (bool, error)) *MockDailyChallengeStore_AttemptDailyChallenge_Call {
	_c.Call.Return(run)
	return _c
}

// CheckBotBehavior provides a mock function for the type MockDailyChallengeStore
func (_mock *MockDailyChallengeStore) CheckBotBehavior(ctx context.Context, challengeID primitive.ObjectID, userID primitive.ObjectID) (*models.BotDetectionResult, error) {
	ret := _mock.Called(ctx, challengeID, userID)

	if len(ret) == 0 {
		panic("no return value specified for CheckBotBehavior")
	}

	var r0 *models.BotDetectionResult
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) (*models.BotDetectionResult, error)); ok {
		return returnFunc(ctx, challengeID, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) *models.BotDetectionResult); ok {
		r0 = returnFunc(ctx, challengeID, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.BotDetectionResult)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, challengeID, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockDailyChallengeStore_CheckBotBehavior_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CheckBotBehavior'
type MockDailyChallengeStore_CheckBotBehavior_Call struct {
	*mock.Call
}

// CheckBotBehavior is a helper method to define mock.On call
//   - ctx context.Context
//   - challengeID primitive.ObjectID
//   - userID primitive.ObjectID
func (_e *MockDailyChallengeStore_Expecter) CheckBotBehavior(ctx interface{}, challengeID interface{}, userID interface{}) *MockDailyChallengeStore_CheckBotBehavior_Call {
	return &MockDailyChallengeStore_CheckBotBehavior_Call{Call: _e.mock.On("CheckBotBehavior", ctx, challengeID, userID)}
}

func (_c *MockDailyChallengeStore_CheckBotBehavior_Call) Run(run func(ctx context.Context, challengeID primitive.ObjectID, userID primitive.ObjectID)) *MockDailyChallengeStore_CheckBotBehavior_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockDailyChallengeStore_CheckBotBehavior_Call) Return(botDetectionResult *models.BotDetectionResult, err error) *MockDailyChallengeStore_CheckBotBehavior_Call {
	_c.Call.Return(botDetectionResult, err)
	return _c
}

func (_c *MockDailyChallengeStore_CheckBotBehavior_Call) RunAndReturn(run func(ctx context.Context, challengeID primitive.ObjectID, userID primitive.ObjectID) (*models.BotDetectionResult, error)) *MockDailyChallengeStore_CheckBotBehavior_Call {
	_c.Call.Return(run)
	return _c
}

// GenerateNewChallenge provides a mock function for the type MockDailyChallengeStore
func (_mock *MockDailyChallengeStore) GenerateNewChallenge(ctx context.Context, division models.ChallengeDivision) (*models.DailyChallenge, error) {
	ret := _mock.Called(ctx, division)

	if len(ret) == 0 {
		panic("no return value specified for GenerateNewChallenge")
	}

	var r0 *models.DailyChallenge
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.ChallengeDivision) (*models.DailyChallenge, error)); ok {
		return returnFunc(ctx, division)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.ChallengeDivision) *models.DailyChallenge); ok {
		r0 = returnFunc(ctx, division)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.DailyChallenge)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.ChallengeDivision) error); ok {
		r1 = returnFunc(ctx, division)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockDailyChallengeStore_GenerateNewChallenge_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GenerateNewChallenge'
type MockDailyChallengeStore_GenerateNewChallenge_Call struct {
	*mock.Call
}

// GenerateNewChallenge is a helper method to define mock.On call
//   - ctx context.Context
//   - division models.ChallengeDivision
func (_e *MockDailyChallengeStore_Expecter) GenerateNewChallenge(ctx interface{}, division interface{}) *MockDailyChallengeStore_GenerateNewChallenge_Call {
	return &MockDailyChallengeStore_GenerateNewChallenge_Call{Call: _e.mock.On("GenerateNewChallenge", ctx, division)}
}

func (_c *MockDailyChallengeStore_GenerateNewChallenge_Call) Run(run func(ctx context.Context, division models.ChallengeDivision)) *MockDailyChallengeStore_GenerateNewChallenge_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 models.ChallengeDivision
		if args[1] != nil {
			arg1 = args[1].(models.ChallengeDivision)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockDailyChallengeStore_GenerateNewChallenge_Call) Return(dailyChallenge *models.DailyChallenge, err error) *MockDailyChallengeStore_GenerateNewChallenge_Call {
	_c.Call.Return(dailyChallenge, err)
	return _c
}

func (_c *MockDailyChallengeStore_GenerateNewChallenge_Call) RunAndReturn(run func(ctx context.Context, division models.ChallengeDivision) (*models.DailyChallenge, error)) *MockDailyChallengeStore_GenerateNewChallenge_Call {
	_c.Call.Return(run)
	return _c
}

// GetDailyChallenge provides a mock function for the type MockDailyChallengeStore
func (_mock *MockDailyChallengeStore) GetDailyChallenge(ctx context.Context) (*models.DailyChallenge, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetDailyChallenge")
	}

	var r0 *models.DailyChallenge
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*models.DailyChallenge, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *models.DailyChallenge); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.DailyChallenge)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockDailyChallengeStore_GetDailyChallenge_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDailyChallenge'
type MockDailyChallengeStore_GetDailyChallenge_Call struct {
	*mock.Call
}

// GetDailyChallenge is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockDailyChallengeStore_Expecter) GetDailyChallenge(ctx interface{}) *MockDailyChallengeStore_GetDailyChallenge_Call {
	return &MockDailyChallengeStore_GetDailyChallenge_Call{Call: _e.mock.On("GetDailyChallenge", ctx)}
}

func (_c *MockDailyChallengeStore_GetDailyChallenge_Call) Run(run func(ctx context.Context)) *MockDailyChallengeStore_GetDailyChallenge_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockDailyChallengeStore_GetDailyChallenge_Call) Return(dailyChallenge *models.DailyChallenge, err error) *MockDailyChallengeStore_GetDailyChallenge_Call {
	_c.Call.Return(dailyChallenge, err)
	return _c
}

func (_c *MockDailyChallengeStore_GetDailyChallenge_Call) RunAndReturn(run func(ctx context.Context) (*models.DailyChallenge, error)) *MockDailyChallengeStore_GetDailyChallenge_Call {
	_c.Call.Return(run)
	return _c
}

// GetDailyChallengeByID provides a mock function for the type MockDailyChallengeStore
func (_mock *MockDailyChallengeStore) GetDailyChallengeByID(ctx context.Context, id primitive.ObjectID) (*models.DailyChallenge, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetDailyChallengeByID")
	}

	var r0 *models.DailyChallenge
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.DailyChallenge, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.DailyChallenge); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.DailyChallenge)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockDailyChallengeStore_GetDailyChallengeByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDailyChallengeByID'
type MockDailyChallengeStore_GetDailyChallengeByID_Call struct {
	*mock.Call
}

// GetDailyChallengeByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id primitive.ObjectID
func (_e *MockDailyChallengeStore_Expecter) GetDailyChallengeByID(ctx interface{}, id interface{}) *MockDailyChallengeStore_GetDailyChallengeByID_Call {
	return &MockDailyChallengeStore_GetDailyChallengeByID_Call{Call: _e.mock.On("GetDailyChallengeByID", ctx, id)}
}

func (_c *MockDailyChallengeStore_GetDailyChallengeByID_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockDailyChallengeStore_GetDailyChallengeByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockDailyChallengeStore_GetDailyChallengeByID_Call) Return(dailyChallenge *models.DailyChallenge, err error) *MockDailyChallengeStore_GetDailyChallengeByID_Call {
	_c.Call.Return(dailyChallenge, err)
	return _c
}

func (_c *MockDailyChallengeStore_GetDailyChallengeByID_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (*models.DailyChallenge, error)) *MockDailyChallengeStore_GetDailyChallengeByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetDailyChallengeLeaderboard provides a mock function for the type MockDailyChallengeStore
func (_mock *MockDailyChallengeStore) GetDailyChallengeLeaderboard(ctx context.Context, challengeNumber *int, pageNumber *int, pageSize *int) (*models.LeaderboardPage, error) {
	ret := _mock.Called(ctx, challengeNumber, pageNumber, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetDailyChallengeLeaderboard")
	}

	var r0 *models.LeaderboardPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int, *int) (*models.LeaderboardPage, error)); ok {
		return returnFunc(ctx, challengeNumber, pageNumber, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int, *int) *models.LeaderboardPage); ok {
		r0 = returnFunc(ctx, challengeNumber, pageNumber, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.LeaderboardPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *int, *int, *int) error); ok {
		r1 = returnFunc(ctx, challengeNumber, pageNumber, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockDailyChallengeStore_GetDailyChallengeLeaderboard_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDailyChallengeLeaderboard'
type MockDailyChallengeStore_GetDailyChallengeLeaderboard_Call struct {
	*mock.Call
}

// GetDailyChallengeLeaderboard is a helper method to define mock.On call
//   - ctx context.Context
//   - challengeNumber *int
//   - pageNumber *int
//   - pageSize *int
func (_e *MockDailyChallengeStore_Expecter) GetDailyChallengeLeaderboard(ctx interface{}, challengeNumber interface{}, pageNumber interface{}, pageSize interface{}) *MockDailyChallengeStore_GetDailyChallengeLeaderboard_Call {
	return &MockDailyChallengeStore_GetDailyChallengeLeaderboard_Call{Call: _e.mock.On("GetDailyChallengeLeaderboard", ctx, challengeNumber, pageNumber, pageSize)}
}

func (_c *MockDailyChallengeStore_GetDailyChallengeLeaderboard_Call) Run(run func(ctx context.Context, challengeNumber *int, pageNumber *int, pageSize *int)) *MockDailyChallengeStore_GetDailyChallengeLeaderboard_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *int
		if args[1] != nil {
			arg1 = args[1].(*int)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		var arg3 *int
		if args[3] != nil {
			arg3 = args[3].(*int)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockDailyChallengeStore_GetDailyChallengeLeaderboard_Call) Return(leaderboardPage *models.LeaderboardPage, err error) *MockDailyChallengeStore_GetDailyChallengeLeaderboard_Call {
	_c.Call.Return(leaderboardPage, err)
	return _c
}

func (_c *MockDailyChallengeStore_GetDailyChallengeLeaderboard_Call) RunAndReturn(run func(ctx context.Context, challengeNumber *int, pageNumber *int, pageSize *int) (*models.LeaderboardPage, error)) *MockDailyChallengeStore_GetDailyChallengeLeaderboard_Call {
	_c.Call.Return(run)
	return _c
}

// GetDailyChallengeLeaderboardByDivision provides a mock function for the type MockDailyChallengeStore
func (_mock *MockDailyChallengeStore) GetDailyChallengeLeaderboardByDivision(ctx context.Context, dateStr *string, pageNumber *int, pageSize *int, division *models.ChallengeDivision) (*models.LeaderboardPage, error) {
	ret := _mock.Called(ctx, dateStr, pageNumber, pageSize, division)

	if len(ret) == 0 {
		panic("no return value specified for GetDailyChallengeLeaderboardByDivision")
	}

	var r0 *models.LeaderboardPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *int, *int, *models.ChallengeDivision) (*models.LeaderboardPage, error)); ok {
		return returnFunc(ctx, dateStr, pageNumber, pageSize, division)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *int, *int, *models.ChallengeDivision) *models.LeaderboardPage); ok {
		r0 = returnFunc(ctx, dateStr, pageNumber, pageSize, division)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.LeaderboardPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string, *int, *int, *models.ChallengeDivision) error); ok {
		r1 = returnFunc(ctx, dateStr, pageNumber, pageSize, division)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockDailyChallengeStore_GetDailyChallengeLeaderboardByDivision_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDailyChallengeLeaderboardByDivision'
type MockDailyChallengeStore_GetDailyChallengeLeaderboardByDivision_Call struct {
	*mock.Call
}

// GetDailyChallengeLeaderboardByDivision is a helper method to define mock.On call
//   - ctx context.Context
//   - dateStr *string
//   - pageNumber *int
//   - pageSize *int
//   - division *models.ChallengeDivision
func (_e *MockDailyChallengeStore_Expecter) GetDailyChallengeLeaderboardByDivision(ctx interface{}, dateStr interface{}, pageNumber interface{}, pageSize interface{}, division interface{}) *MockDailyChallengeStore_GetDailyChallengeLeaderboardByDivision_Call {
	return &MockDailyChallengeStore_GetDailyChallengeLeaderboardByDivision_Call{Call: _e.mock.On("GetDailyChallengeLeaderboardByDivision", ctx, dateStr, pageNumber, pageSize, division)}
}

func (_c *MockDailyChallengeStore_GetDailyChallengeLeaderboardByDivision_Call) Run(run func(ctx context.Context, dateStr *string, pageNumber *int, pageSize *int, division *models.ChallengeDivision)) *MockDailyChallengeStore_GetDailyChallengeLeaderboardByDivision_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *string
		if args[1] != nil {
			arg1 = args[1].(*string)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		var arg3 *int
		if args[3] != nil {
			arg3 = args[3].(*int)
		}
		var arg4 *models.ChallengeDivision
		if args[4] != nil {
			arg4 = args[4].(*models.ChallengeDivision)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
			arg4,
		)
	})
	return _c
}

func (_c *MockDailyChallengeStore_GetDailyChallengeLeaderboardByDivision_Call) Return(leaderboardPage *models.LeaderboardPage, err error) *MockDailyChallengeStore_GetDailyChallengeLeaderboardByDivision_Call {
	_c.Call.Return(leaderboardPage, err)
	return _c
}

func (_c *MockDailyChallengeStore_GetDailyChallengeLeaderboardByDivision_Call) RunAndReturn(run func(ctx context.Context, dateStr *string, pageNumber *int, pageSize *int, division *models.ChallengeDivision) (*models.LeaderboardPage, error)) *MockDailyChallengeStore_GetDailyChallengeLeaderboardByDivision_Call {
	_c.Call.Return(run)
	return _c
}

// GetDailyChallenges provides a mock function for the type MockDailyChallengeStore
func (_mock *MockDailyChallengeStore) GetDailyChallenges(ctx context.Context) ([]*models.DailyChallenge, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetDailyChallenges")
	}

	var r0 []*models.DailyChallenge
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) ([]*models.DailyChallenge, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) []*models.DailyChallenge); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.DailyChallenge)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockDailyChallengeStore_GetDailyChallenges_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDailyChallenges'
type MockDailyChallengeStore_GetDailyChallenges_Call struct {
	*mock.Call
}

// GetDailyChallenges is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockDailyChallengeStore_Expecter) GetDailyChallenges(ctx interface{}) *MockDailyChallengeStore_GetDailyChallenges_Call {
	return &MockDailyChallengeStore_GetDailyChallenges_Call{Call: _e.mock.On("GetDailyChallenges", ctx)}
}

func (_c *MockDailyChallengeStore_GetDailyChallenges_Call) Run(run func(ctx context.Context)) *MockDailyChallengeStore_GetDailyChallenges_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockDailyChallengeStore_GetDailyChallenges_Call) Return(dailyChallenges []*models.DailyChallenge, err error) *MockDailyChallengeStore_GetDailyChallenges_Call {
	_c.Call.Return(dailyChallenges, err)
	return _c
}

func (_c *MockDailyChallengeStore_GetDailyChallenges_Call) RunAndReturn(run func(ctx context.Context) ([]*models.DailyChallenge, error)) *MockDailyChallengeStore_GetDailyChallenges_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserChallengeResult provides a mock function for the type MockDailyChallengeStore
func (_mock *MockDailyChallengeStore) GetUserChallengeResult(ctx context.Context, challengeNumber *int) (*models.UserResult, error) {
	ret := _mock.Called(ctx, challengeNumber)

	if len(ret) == 0 {
		panic("no return value specified for GetUserChallengeResult")
	}

	var r0 *models.UserResult
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int) (*models.UserResult, error)); ok {
		return returnFunc(ctx, challengeNumber)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int) *models.UserResult); ok {
		r0 = returnFunc(ctx, challengeNumber)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserResult)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *int) error); ok {
		r1 = returnFunc(ctx, challengeNumber)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockDailyChallengeStore_GetUserChallengeResult_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserChallengeResult'
type MockDailyChallengeStore_GetUserChallengeResult_Call struct {
	*mock.Call
}

// GetUserChallengeResult is a helper method to define mock.On call
//   - ctx context.Context
//   - challengeNumber *int
func (_e *MockDailyChallengeStore_Expecter) GetUserChallengeResult(ctx interface{}, challengeNumber interface{}) *MockDailyChallengeStore_GetUserChallengeResult_Call {
	return &MockDailyChallengeStore_GetUserChallengeResult_Call{Call: _e.mock.On("GetUserChallengeResult", ctx, challengeNumber)}
}

func (_c *MockDailyChallengeStore_GetUserChallengeResult_Call) Run(run func(ctx context.Context, challengeNumber *int)) *MockDailyChallengeStore_GetUserChallengeResult_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *int
		if args[1] != nil {
			arg1 = args[1].(*int)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockDailyChallengeStore_GetUserChallengeResult_Call) Return(userResult *models.UserResult, err error) *MockDailyChallengeStore_GetUserChallengeResult_Call {
	_c.Call.Return(userResult, err)
	return _c
}

func (_c *MockDailyChallengeStore_GetUserChallengeResult_Call) RunAndReturn(run func(ctx context.Context, challengeNumber *int) (*models.UserResult, error)) *MockDailyChallengeStore_GetUserChallengeResult_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserChallengeResultByDailyChallengeID provides a mock function for the type MockDailyChallengeStore
func (_mock *MockDailyChallengeStore) GetUserChallengeResultByDailyChallengeID(ctx context.Context, challengeID primitive.ObjectID) (*models.UserDailyChallengeResultWithStats, error) {
	ret := _mock.Called(ctx, challengeID)

	if len(ret) == 0 {
		panic("no return value specified for GetUserChallengeResultByDailyChallengeID")
	}

	var r0 *models.UserDailyChallengeResultWithStats
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.UserDailyChallengeResultWithStats, error)); ok {
		return returnFunc(ctx, challengeID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.UserDailyChallengeResultWithStats); ok {
		r0 = returnFunc(ctx, challengeID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserDailyChallengeResultWithStats)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, challengeID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockDailyChallengeStore_GetUserChallengeResultByDailyChallengeID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserChallengeResultByDailyChallengeID'
type MockDailyChallengeStore_GetUserChallengeResultByDailyChallengeID_Call struct {
	*mock.Call
}

// GetUserChallengeResultByDailyChallengeID is a helper method to define mock.On call
//   - ctx context.Context
//   - challengeID primitive.ObjectID
func (_e *MockDailyChallengeStore_Expecter) GetUserChallengeResultByDailyChallengeID(ctx interface{}, challengeID interface{}) *MockDailyChallengeStore_GetUserChallengeResultByDailyChallengeID_Call {
	return &MockDailyChallengeStore_GetUserChallengeResultByDailyChallengeID_Call{Call: _e.mock.On("GetUserChallengeResultByDailyChallengeID", ctx, challengeID)}
}

func (_c *MockDailyChallengeStore_GetUserChallengeResultByDailyChallengeID_Call) Run(run func(ctx context.Context, challengeID primitive.ObjectID)) *MockDailyChallengeStore_GetUserChallengeResultByDailyChallengeID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockDailyChallengeStore_GetUserChallengeResultByDailyChallengeID_Call) Return(userDailyChallengeResultWithStats *models.UserDailyChallengeResultWithStats, err error) *MockDailyChallengeStore_GetUserChallengeResultByDailyChallengeID_Call {
	_c.Call.Return(userDailyChallengeResultWithStats, err)
	return _c
}

func (_c *MockDailyChallengeStore_GetUserChallengeResultByDailyChallengeID_Call) RunAndReturn(run func(ctx context.Context, challengeID primitive.ObjectID) (*models.UserDailyChallengeResultWithStats, error)) *MockDailyChallengeStore_GetUserChallengeResultByDailyChallengeID_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserChallengeResultByDivision provides a mock function for the type MockDailyChallengeStore
func (_mock *MockDailyChallengeStore) GetUserChallengeResultByDivision(ctx context.Context, dateStr *string, division *models.ChallengeDivision) (*models.UserResult, error) {
	ret := _mock.Called(ctx, dateStr, division)

	if len(ret) == 0 {
		panic("no return value specified for GetUserChallengeResultByDivision")
	}

	var r0 *models.UserResult
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *models.ChallengeDivision) (*models.UserResult, error)); ok {
		return returnFunc(ctx, dateStr, division)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *models.ChallengeDivision) *models.UserResult); ok {
		r0 = returnFunc(ctx, dateStr, division)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserResult)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string, *models.ChallengeDivision) error); ok {
		r1 = returnFunc(ctx, dateStr, division)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockDailyChallengeStore_GetUserChallengeResultByDivision_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserChallengeResultByDivision'
type MockDailyChallengeStore_GetUserChallengeResultByDivision_Call struct {
	*mock.Call
}

// GetUserChallengeResultByDivision is a helper method to define mock.On call
//   - ctx context.Context
//   - dateStr *string
//   - division *models.ChallengeDivision
func (_e *MockDailyChallengeStore_Expecter) GetUserChallengeResultByDivision(ctx interface{}, dateStr interface{}, division interface{}) *MockDailyChallengeStore_GetUserChallengeResultByDivision_Call {
	return &MockDailyChallengeStore_GetUserChallengeResultByDivision_Call{Call: _e.mock.On("GetUserChallengeResultByDivision", ctx, dateStr, division)}
}

func (_c *MockDailyChallengeStore_GetUserChallengeResultByDivision_Call) Run(run func(ctx context.Context, dateStr *string, division *models.ChallengeDivision)) *MockDailyChallengeStore_GetUserChallengeResultByDivision_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *string
		if args[1] != nil {
			arg1 = args[1].(*string)
		}
		var arg2 *models.ChallengeDivision
		if args[2] != nil {
			arg2 = args[2].(*models.ChallengeDivision)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockDailyChallengeStore_GetUserChallengeResultByDivision_Call) Return(userResult *models.UserResult, err error) *MockDailyChallengeStore_GetUserChallengeResultByDivision_Call {
	_c.Call.Return(userResult, err)
	return _c
}

func (_c *MockDailyChallengeStore_GetUserChallengeResultByDivision_Call) RunAndReturn(run func(ctx context.Context, dateStr *string, division *models.ChallengeDivision) (*models.UserResult, error)) *MockDailyChallengeStore_GetUserChallengeResultByDivision_Call {
	_c.Call.Return(run)
	return _c
}

// SubmitDailyChallenge provides a mock function for the type MockDailyChallengeStore
func (_mock *MockDailyChallengeStore) SubmitDailyChallenge(ctx context.Context, input models.SubmitSolutionInput) (*models.SubmitChallengeResult, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for SubmitDailyChallenge")
	}

	var r0 *models.SubmitChallengeResult
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.SubmitSolutionInput) (*models.SubmitChallengeResult, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.SubmitSolutionInput) *models.SubmitChallengeResult); ok {
		r0 = returnFunc(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.SubmitChallengeResult)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.SubmitSolutionInput) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockDailyChallengeStore_SubmitDailyChallenge_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SubmitDailyChallenge'
type MockDailyChallengeStore_SubmitDailyChallenge_Call struct {
	*mock.Call
}

// SubmitDailyChallenge is a helper method to define mock.On call
//   - ctx context.Context
//   - input models.SubmitSolutionInput
func (_e *MockDailyChallengeStore_Expecter) SubmitDailyChallenge(ctx interface{}, input interface{}) *MockDailyChallengeStore_SubmitDailyChallenge_Call {
	return &MockDailyChallengeStore_SubmitDailyChallenge_Call{Call: _e.mock.On("SubmitDailyChallenge", ctx, input)}
}

func (_c *MockDailyChallengeStore_SubmitDailyChallenge_Call) Run(run func(ctx context.Context, input models.SubmitSolutionInput)) *MockDailyChallengeStore_SubmitDailyChallenge_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 models.SubmitSolutionInput
		if args[1] != nil {
			arg1 = args[1].(models.SubmitSolutionInput)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockDailyChallengeStore_SubmitDailyChallenge_Call) Return(submitChallengeResult *models.SubmitChallengeResult, err error) *MockDailyChallengeStore_SubmitDailyChallenge_Call {
	_c.Call.Return(submitChallengeResult, err)
	return _c
}

func (_c *MockDailyChallengeStore_SubmitDailyChallenge_Call) RunAndReturn(run func(ctx context.Context, input models.SubmitSolutionInput) (*models.SubmitChallengeResult, error)) *MockDailyChallengeStore_SubmitDailyChallenge_Call {
	_c.Call.Return(run)
	return _c
}
