// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package domain

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
)

// NewMockUserResolutionStore creates a new instance of MockUserResolutionStore. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockUserResolutionStore(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockUserResolutionStore {
	mock := &MockUserResolutionStore{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockUserResolutionStore is an autogenerated mock type for the UserResolutionStore type
type MockUserResolutionStore struct {
	mock.Mock
}

type MockUserResolutionStore_Expecter struct {
	mock *mock.Mock
}

func (_m *MockUserResolutionStore) EXPECT() *MockUserResolutionStore_Expecter {
	return &MockUserResolutionStore_Expecter{mock: &_m.Mock}
}

// CheckIfPledgeTaken provides a mock function for the type MockUserResolutionStore
func (_mock *MockUserResolutionStore) CheckIfPledgeTaken(ctx context.Context) (*bool, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for CheckIfPledgeTaken")
	}

	var r0 *bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*bool, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *bool); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bool)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserResolutionStore_CheckIfPledgeTaken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CheckIfPledgeTaken'
type MockUserResolutionStore_CheckIfPledgeTaken_Call struct {
	*mock.Call
}

// CheckIfPledgeTaken is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockUserResolutionStore_Expecter) CheckIfPledgeTaken(ctx interface{}) *MockUserResolutionStore_CheckIfPledgeTaken_Call {
	return &MockUserResolutionStore_CheckIfPledgeTaken_Call{Call: _e.mock.On("CheckIfPledgeTaken", ctx)}
}

func (_c *MockUserResolutionStore_CheckIfPledgeTaken_Call) Run(run func(ctx context.Context)) *MockUserResolutionStore_CheckIfPledgeTaken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockUserResolutionStore_CheckIfPledgeTaken_Call) Return(b *bool, err error) *MockUserResolutionStore_CheckIfPledgeTaken_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockUserResolutionStore_CheckIfPledgeTaken_Call) RunAndReturn(run func(ctx context.Context) (*bool, error)) *MockUserResolutionStore_CheckIfPledgeTaken_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserResolution provides a mock function for the type MockUserResolutionStore
func (_mock *MockUserResolutionStore) GetUserResolution(ctx context.Context) (*models.UserResolution, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetUserResolution")
	}

	var r0 *models.UserResolution
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*models.UserResolution, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *models.UserResolution); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserResolution)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserResolutionStore_GetUserResolution_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserResolution'
type MockUserResolutionStore_GetUserResolution_Call struct {
	*mock.Call
}

// GetUserResolution is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockUserResolutionStore_Expecter) GetUserResolution(ctx interface{}) *MockUserResolutionStore_GetUserResolution_Call {
	return &MockUserResolutionStore_GetUserResolution_Call{Call: _e.mock.On("GetUserResolution", ctx)}
}

func (_c *MockUserResolutionStore_GetUserResolution_Call) Run(run func(ctx context.Context)) *MockUserResolutionStore_GetUserResolution_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockUserResolutionStore_GetUserResolution_Call) Return(userResolution *models.UserResolution, err error) *MockUserResolutionStore_GetUserResolution_Call {
	_c.Call.Return(userResolution, err)
	return _c
}

func (_c *MockUserResolutionStore_GetUserResolution_Call) RunAndReturn(run func(ctx context.Context) (*models.UserResolution, error)) *MockUserResolutionStore_GetUserResolution_Call {
	_c.Call.Return(run)
	return _c
}

// TakePledge provides a mock function for the type MockUserResolutionStore
func (_mock *MockUserResolutionStore) TakePledge(ctx context.Context, duration *int) (*bool, error) {
	ret := _mock.Called(ctx, duration)

	if len(ret) == 0 {
		panic("no return value specified for TakePledge")
	}

	var r0 *bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int) (*bool, error)); ok {
		return returnFunc(ctx, duration)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int) *bool); ok {
		r0 = returnFunc(ctx, duration)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bool)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *int) error); ok {
		r1 = returnFunc(ctx, duration)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserResolutionStore_TakePledge_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'TakePledge'
type MockUserResolutionStore_TakePledge_Call struct {
	*mock.Call
}

// TakePledge is a helper method to define mock.On call
//   - ctx context.Context
//   - duration *int
func (_e *MockUserResolutionStore_Expecter) TakePledge(ctx interface{}, duration interface{}) *MockUserResolutionStore_TakePledge_Call {
	return &MockUserResolutionStore_TakePledge_Call{Call: _e.mock.On("TakePledge", ctx, duration)}
}

func (_c *MockUserResolutionStore_TakePledge_Call) Run(run func(ctx context.Context, duration *int)) *MockUserResolutionStore_TakePledge_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *int
		if args[1] != nil {
			arg1 = args[1].(*int)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockUserResolutionStore_TakePledge_Call) Return(b *bool, err error) *MockUserResolutionStore_TakePledge_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockUserResolutionStore_TakePledge_Call) RunAndReturn(run func(ctx context.Context, duration *int) (*bool, error)) *MockUserResolutionStore_TakePledge_Call {
	_c.Call.Return(run)
	return _c
}
