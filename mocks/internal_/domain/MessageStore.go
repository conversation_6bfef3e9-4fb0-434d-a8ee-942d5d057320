// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package domain

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockMessageStore creates a new instance of MockMessageStore. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockMessageStore(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockMessageStore {
	mock := &MockMessageStore{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockMessageStore is an autogenerated mock type for the MessageStore type
type MockMessageStore struct {
	mock.Mock
}

type MockMessageStore_Expecter struct {
	mock *mock.Mock
}

func (_m *MockMessageStore) EXPECT() *MockMessageStore_Expecter {
	return &MockMessageStore_Expecter{mock: &_m.Mock}
}

// CreateMessage provides a mock function for the type MockMessageStore
func (_mock *MockMessageStore) CreateMessage(ctx context.Context, messageInput models.CreateMessageInput) (*models.Message, error) {
	ret := _mock.Called(ctx, messageInput)

	if len(ret) == 0 {
		panic("no return value specified for CreateMessage")
	}

	var r0 *models.Message
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateMessageInput) (*models.Message, error)); ok {
		return returnFunc(ctx, messageInput)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateMessageInput) *models.Message); ok {
		r0 = returnFunc(ctx, messageInput)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Message)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.CreateMessageInput) error); ok {
		r1 = returnFunc(ctx, messageInput)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMessageStore_CreateMessage_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateMessage'
type MockMessageStore_CreateMessage_Call struct {
	*mock.Call
}

// CreateMessage is a helper method to define mock.On call
//   - ctx context.Context
//   - messageInput models.CreateMessageInput
func (_e *MockMessageStore_Expecter) CreateMessage(ctx interface{}, messageInput interface{}) *MockMessageStore_CreateMessage_Call {
	return &MockMessageStore_CreateMessage_Call{Call: _e.mock.On("CreateMessage", ctx, messageInput)}
}

func (_c *MockMessageStore_CreateMessage_Call) Run(run func(ctx context.Context, messageInput models.CreateMessageInput)) *MockMessageStore_CreateMessage_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 models.CreateMessageInput
		if args[1] != nil {
			arg1 = args[1].(models.CreateMessageInput)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockMessageStore_CreateMessage_Call) Return(message *models.Message, err error) *MockMessageStore_CreateMessage_Call {
	_c.Call.Return(message, err)
	return _c
}

func (_c *MockMessageStore_CreateMessage_Call) RunAndReturn(run func(ctx context.Context, messageInput models.CreateMessageInput) (*models.Message, error)) *MockMessageStore_CreateMessage_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllMessageGroup provides a mock function for the type MockMessageStore
func (_mock *MockMessageStore) GetAllMessageGroup(ctx context.Context, input *models.GetAllMessageGroupsInput) (*models.PaginatedMessageGroups, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for GetAllMessageGroup")
	}

	var r0 *models.PaginatedMessageGroups
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.GetAllMessageGroupsInput) (*models.PaginatedMessageGroups, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.GetAllMessageGroupsInput) *models.PaginatedMessageGroups); ok {
		r0 = returnFunc(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PaginatedMessageGroups)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.GetAllMessageGroupsInput) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMessageStore_GetAllMessageGroup_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllMessageGroup'
type MockMessageStore_GetAllMessageGroup_Call struct {
	*mock.Call
}

// GetAllMessageGroup is a helper method to define mock.On call
//   - ctx context.Context
//   - input *models.GetAllMessageGroupsInput
func (_e *MockMessageStore_Expecter) GetAllMessageGroup(ctx interface{}, input interface{}) *MockMessageStore_GetAllMessageGroup_Call {
	return &MockMessageStore_GetAllMessageGroup_Call{Call: _e.mock.On("GetAllMessageGroup", ctx, input)}
}

func (_c *MockMessageStore_GetAllMessageGroup_Call) Run(run func(ctx context.Context, input *models.GetAllMessageGroupsInput)) *MockMessageStore_GetAllMessageGroup_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.GetAllMessageGroupsInput
		if args[1] != nil {
			arg1 = args[1].(*models.GetAllMessageGroupsInput)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockMessageStore_GetAllMessageGroup_Call) Return(paginatedMessageGroups *models.PaginatedMessageGroups, err error) *MockMessageStore_GetAllMessageGroup_Call {
	_c.Call.Return(paginatedMessageGroups, err)
	return _c
}

func (_c *MockMessageStore_GetAllMessageGroup_Call) RunAndReturn(run func(ctx context.Context, input *models.GetAllMessageGroupsInput) (*models.PaginatedMessageGroups, error)) *MockMessageStore_GetAllMessageGroup_Call {
	_c.Call.Return(run)
	return _c
}

// GetMessageGroupDetailsByID provides a mock function for the type MockMessageStore
func (_mock *MockMessageStore) GetMessageGroupDetailsByID(ctx context.Context, groupID primitive.ObjectID) (*models.MessageGroup, error) {
	ret := _mock.Called(ctx, groupID)

	if len(ret) == 0 {
		panic("no return value specified for GetMessageGroupDetailsByID")
	}

	var r0 *models.MessageGroup
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.MessageGroup, error)); ok {
		return returnFunc(ctx, groupID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.MessageGroup); ok {
		r0 = returnFunc(ctx, groupID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.MessageGroup)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, groupID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMessageStore_GetMessageGroupDetailsByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMessageGroupDetailsByID'
type MockMessageStore_GetMessageGroupDetailsByID_Call struct {
	*mock.Call
}

// GetMessageGroupDetailsByID is a helper method to define mock.On call
//   - ctx context.Context
//   - groupID primitive.ObjectID
func (_e *MockMessageStore_Expecter) GetMessageGroupDetailsByID(ctx interface{}, groupID interface{}) *MockMessageStore_GetMessageGroupDetailsByID_Call {
	return &MockMessageStore_GetMessageGroupDetailsByID_Call{Call: _e.mock.On("GetMessageGroupDetailsByID", ctx, groupID)}
}

func (_c *MockMessageStore_GetMessageGroupDetailsByID_Call) Run(run func(ctx context.Context, groupID primitive.ObjectID)) *MockMessageStore_GetMessageGroupDetailsByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockMessageStore_GetMessageGroupDetailsByID_Call) Return(messageGroup *models.MessageGroup, err error) *MockMessageStore_GetMessageGroupDetailsByID_Call {
	_c.Call.Return(messageGroup, err)
	return _c
}

func (_c *MockMessageStore_GetMessageGroupDetailsByID_Call) RunAndReturn(run func(ctx context.Context, groupID primitive.ObjectID) (*models.MessageGroup, error)) *MockMessageStore_GetMessageGroupDetailsByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetMessageGroupIdForFriends provides a mock function for the type MockMessageStore
func (_mock *MockMessageStore) GetMessageGroupIdForFriends(ctx context.Context, friendID primitive.ObjectID) (primitive.ObjectID, error) {
	ret := _mock.Called(ctx, friendID)

	if len(ret) == 0 {
		panic("no return value specified for GetMessageGroupIdForFriends")
	}

	var r0 primitive.ObjectID
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (primitive.ObjectID, error)); ok {
		return returnFunc(ctx, friendID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) primitive.ObjectID); ok {
		r0 = returnFunc(ctx, friendID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(primitive.ObjectID)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, friendID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMessageStore_GetMessageGroupIdForFriends_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMessageGroupIdForFriends'
type MockMessageStore_GetMessageGroupIdForFriends_Call struct {
	*mock.Call
}

// GetMessageGroupIdForFriends is a helper method to define mock.On call
//   - ctx context.Context
//   - friendID primitive.ObjectID
func (_e *MockMessageStore_Expecter) GetMessageGroupIdForFriends(ctx interface{}, friendID interface{}) *MockMessageStore_GetMessageGroupIdForFriends_Call {
	return &MockMessageStore_GetMessageGroupIdForFriends_Call{Call: _e.mock.On("GetMessageGroupIdForFriends", ctx, friendID)}
}

func (_c *MockMessageStore_GetMessageGroupIdForFriends_Call) Run(run func(ctx context.Context, friendID primitive.ObjectID)) *MockMessageStore_GetMessageGroupIdForFriends_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockMessageStore_GetMessageGroupIdForFriends_Call) Return(objectID primitive.ObjectID, err error) *MockMessageStore_GetMessageGroupIdForFriends_Call {
	_c.Call.Return(objectID, err)
	return _c
}

func (_c *MockMessageStore_GetMessageGroupIdForFriends_Call) RunAndReturn(run func(ctx context.Context, friendID primitive.ObjectID) (primitive.ObjectID, error)) *MockMessageStore_GetMessageGroupIdForFriends_Call {
	_c.Call.Return(run)
	return _c
}

// GetMessagesByGroupID provides a mock function for the type MockMessageStore
func (_mock *MockMessageStore) GetMessagesByGroupID(ctx context.Context, groupID primitive.ObjectID, lastMessageId *primitive.ObjectID, pageSize *int, sortDirection *models.SortDirection) (*models.PaginatedMessage, error) {
	ret := _mock.Called(ctx, groupID, lastMessageId, pageSize, sortDirection)

	if len(ret) == 0 {
		panic("no return value specified for GetMessagesByGroupID")
	}

	var r0 *models.PaginatedMessage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *primitive.ObjectID, *int, *models.SortDirection) (*models.PaginatedMessage, error)); ok {
		return returnFunc(ctx, groupID, lastMessageId, pageSize, sortDirection)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *primitive.ObjectID, *int, *models.SortDirection) *models.PaginatedMessage); ok {
		r0 = returnFunc(ctx, groupID, lastMessageId, pageSize, sortDirection)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PaginatedMessage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, *primitive.ObjectID, *int, *models.SortDirection) error); ok {
		r1 = returnFunc(ctx, groupID, lastMessageId, pageSize, sortDirection)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMessageStore_GetMessagesByGroupID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMessagesByGroupID'
type MockMessageStore_GetMessagesByGroupID_Call struct {
	*mock.Call
}

// GetMessagesByGroupID is a helper method to define mock.On call
//   - ctx context.Context
//   - groupID primitive.ObjectID
//   - lastMessageId *primitive.ObjectID
//   - pageSize *int
//   - sortDirection *models.SortDirection
func (_e *MockMessageStore_Expecter) GetMessagesByGroupID(ctx interface{}, groupID interface{}, lastMessageId interface{}, pageSize interface{}, sortDirection interface{}) *MockMessageStore_GetMessagesByGroupID_Call {
	return &MockMessageStore_GetMessagesByGroupID_Call{Call: _e.mock.On("GetMessagesByGroupID", ctx, groupID, lastMessageId, pageSize, sortDirection)}
}

func (_c *MockMessageStore_GetMessagesByGroupID_Call) Run(run func(ctx context.Context, groupID primitive.ObjectID, lastMessageId *primitive.ObjectID, pageSize *int, sortDirection *models.SortDirection)) *MockMessageStore_GetMessagesByGroupID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 *primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(*primitive.ObjectID)
		}
		var arg3 *int
		if args[3] != nil {
			arg3 = args[3].(*int)
		}
		var arg4 *models.SortDirection
		if args[4] != nil {
			arg4 = args[4].(*models.SortDirection)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
			arg4,
		)
	})
	return _c
}

func (_c *MockMessageStore_GetMessagesByGroupID_Call) Return(paginatedMessages *models.PaginatedMessage, err error) *MockMessageStore_GetMessagesByGroupID_Call {
	_c.Call.Return(paginatedMessages, err)
	return _c
}

func (_c *MockMessageStore_GetMessagesByGroupID_Call) RunAndReturn(run func(ctx context.Context, groupID primitive.ObjectID, lastMessageId *primitive.ObjectID, pageSize *int, sortDirection *models.SortDirection) (*models.PaginatedMessage, error)) *MockMessageStore_GetMessagesByGroupID_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateLastMessageRead provides a mock function for the type MockMessageStore
func (_mock *MockMessageStore) UpdateLastMessageRead(ctx context.Context, groupID primitive.ObjectID, lastMessageRead primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, groupID, lastMessageRead)

	if len(ret) == 0 {
		panic("no return value specified for UpdateLastMessageRead")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, groupID, lastMessageRead)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, groupID, lastMessageRead)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, groupID, lastMessageRead)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMessageStore_UpdateLastMessageRead_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateLastMessageRead'
type MockMessageStore_UpdateLastMessageRead_Call struct {
	*mock.Call
}

// UpdateLastMessageRead is a helper method to define mock.On call
//   - ctx context.Context
//   - groupID primitive.ObjectID
//   - lastMessageRead primitive.ObjectID
func (_e *MockMessageStore_Expecter) UpdateLastMessageRead(ctx interface{}, groupID interface{}, lastMessageRead interface{}) *MockMessageStore_UpdateLastMessageRead_Call {
	return &MockMessageStore_UpdateLastMessageRead_Call{Call: _e.mock.On("UpdateLastMessageRead", ctx, groupID, lastMessageRead)}
}

func (_c *MockMessageStore_UpdateLastMessageRead_Call) Run(run func(ctx context.Context, groupID primitive.ObjectID, lastMessageRead primitive.ObjectID)) *MockMessageStore_UpdateLastMessageRead_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockMessageStore_UpdateLastMessageRead_Call) Return(b bool, err error) *MockMessageStore_UpdateLastMessageRead_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockMessageStore_UpdateLastMessageRead_Call) RunAndReturn(run func(ctx context.Context, groupID primitive.ObjectID, lastMessageRead primitive.ObjectID) (bool, error)) *MockMessageStore_UpdateLastMessageRead_Call {
	_c.Call.Return(run)
	return _c
}
