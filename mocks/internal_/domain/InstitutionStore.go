// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package domain

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
)

// NewMockInstitutionStore creates a new instance of MockInstitutionStore. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockInstitutionStore(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockInstitutionStore {
	mock := &MockInstitutionStore{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockInstitutionStore is an autogenerated mock type for the InstitutionStore type
type MockInstitutionStore struct {
	mock.Mock
}

type MockInstitutionStore_Expecter struct {
	mock *mock.Mock
}

func (_m *MockInstitutionStore) EXPECT() *MockInstitutionStore_Expecter {
	return &MockInstitutionStore_Expecter{mock: &_m.Mock}
}

// AddNewInstitute provides a mock function for the type MockInstitutionStore
func (_mock *MockInstitutionStore) AddNewInstitute(ctx context.Context, input models.CreateInstitutionInput) (*models.Institution, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for AddNewInstitute")
	}

	var r0 *models.Institution
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateInstitutionInput) (*models.Institution, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateInstitutionInput) *models.Institution); ok {
		r0 = returnFunc(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Institution)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.CreateInstitutionInput) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockInstitutionStore_AddNewInstitute_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AddNewInstitute'
type MockInstitutionStore_AddNewInstitute_Call struct {
	*mock.Call
}

// AddNewInstitute is a helper method to define mock.On call
//   - ctx context.Context
//   - input models.CreateInstitutionInput
func (_e *MockInstitutionStore_Expecter) AddNewInstitute(ctx interface{}, input interface{}) *MockInstitutionStore_AddNewInstitute_Call {
	return &MockInstitutionStore_AddNewInstitute_Call{Call: _e.mock.On("AddNewInstitute", ctx, input)}
}

func (_c *MockInstitutionStore_AddNewInstitute_Call) Run(run func(ctx context.Context, input models.CreateInstitutionInput)) *MockInstitutionStore_AddNewInstitute_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 models.CreateInstitutionInput
		if args[1] != nil {
			arg1 = args[1].(models.CreateInstitutionInput)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockInstitutionStore_AddNewInstitute_Call) Return(institution *models.Institution, err error) *MockInstitutionStore_AddNewInstitute_Call {
	_c.Call.Return(institution, err)
	return _c
}

func (_c *MockInstitutionStore_AddNewInstitute_Call) RunAndReturn(run func(ctx context.Context, input models.CreateInstitutionInput) (*models.Institution, error)) *MockInstitutionStore_AddNewInstitute_Call {
	_c.Call.Return(run)
	return _c
}

// SearchInstitute provides a mock function for the type MockInstitutionStore
func (_mock *MockInstitutionStore) SearchInstitute(ctx context.Context, query string, limit *int) ([]*models.Institution, error) {
	ret := _mock.Called(ctx, query, limit)

	if len(ret) == 0 {
		panic("no return value specified for SearchInstitute")
	}

	var r0 []*models.Institution
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, *int) ([]*models.Institution, error)); ok {
		return returnFunc(ctx, query, limit)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, *int) []*models.Institution); ok {
		r0 = returnFunc(ctx, query, limit)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.Institution)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, *int) error); ok {
		r1 = returnFunc(ctx, query, limit)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockInstitutionStore_SearchInstitute_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SearchInstitute'
type MockInstitutionStore_SearchInstitute_Call struct {
	*mock.Call
}

// SearchInstitute is a helper method to define mock.On call
//   - ctx context.Context
//   - query string
//   - limit *int
func (_e *MockInstitutionStore_Expecter) SearchInstitute(ctx interface{}, query interface{}, limit interface{}) *MockInstitutionStore_SearchInstitute_Call {
	return &MockInstitutionStore_SearchInstitute_Call{Call: _e.mock.On("SearchInstitute", ctx, query, limit)}
}

func (_c *MockInstitutionStore_SearchInstitute_Call) Run(run func(ctx context.Context, query string, limit *int)) *MockInstitutionStore_SearchInstitute_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockInstitutionStore_SearchInstitute_Call) Return(institutions []*models.Institution, err error) *MockInstitutionStore_SearchInstitute_Call {
	_c.Call.Return(institutions, err)
	return _c
}

func (_c *MockInstitutionStore_SearchInstitute_Call) RunAndReturn(run func(ctx context.Context, query string, limit *int) ([]*models.Institution, error)) *MockInstitutionStore_SearchInstitute_Call {
	_c.Call.Return(run)
	return _c
}
