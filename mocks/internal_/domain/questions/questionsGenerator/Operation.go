// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package questionsGenerator

import (
	mock "github.com/stretchr/testify/mock"
)

// NewMockOperation creates a new instance of MockOperation. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockOperation(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockOperation {
	mock := &MockOperation{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockOperation is an autogenerated mock type for the Operation type
type MockOperation struct {
	mock.Mock
}

type MockOperation_Expecter struct {
	mock *mock.Mock
}

func (_m *MockOperation) EXPECT() *MockOperation_Expecter {
	return &MockOperation_Expecter{mock: &_m.Mock}
}

// MaxCells provides a mock function for the type MockOperation
func (_mock *MockOperation) MaxCells() int {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for MaxCells")
	}

	var r0 int
	if returnFunc, ok := ret.Get(0).(func() int); ok {
		r0 = returnFunc()
	} else {
		r0 = ret.Get(0).(int)
	}
	return r0
}

// MockOperation_MaxCells_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'MaxCells'
type MockOperation_MaxCells_Call struct {
	*mock.Call
}

// MaxCells is a helper method to define mock.On call
func (_e *MockOperation_Expecter) MaxCells() *MockOperation_MaxCells_Call {
	return &MockOperation_MaxCells_Call{Call: _e.mock.On("MaxCells")}
}

func (_c *MockOperation_MaxCells_Call) Run(run func()) *MockOperation_MaxCells_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockOperation_MaxCells_Call) Return(n int) *MockOperation_MaxCells_Call {
	_c.Call.Return(n)
	return _c
}

func (_c *MockOperation_MaxCells_Call) RunAndReturn(run func() int) *MockOperation_MaxCells_Call {
	_c.Call.Return(run)
	return _c
}

// MinCells provides a mock function for the type MockOperation
func (_mock *MockOperation) MinCells() int {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for MinCells")
	}

	var r0 int
	if returnFunc, ok := ret.Get(0).(func() int); ok {
		r0 = returnFunc()
	} else {
		r0 = ret.Get(0).(int)
	}
	return r0
}

// MockOperation_MinCells_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'MinCells'
type MockOperation_MinCells_Call struct {
	*mock.Call
}

// MinCells is a helper method to define mock.On call
func (_e *MockOperation_Expecter) MinCells() *MockOperation_MinCells_Call {
	return &MockOperation_MinCells_Call{Call: _e.mock.On("MinCells")}
}

func (_c *MockOperation_MinCells_Call) Run(run func()) *MockOperation_MinCells_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockOperation_MinCells_Call) Return(n int) *MockOperation_MinCells_Call {
	_c.Call.Return(n)
	return _c
}

func (_c *MockOperation_MinCells_Call) RunAndReturn(run func() int) *MockOperation_MinCells_Call {
	_c.Call.Return(run)
	return _c
}

// Operation provides a mock function for the type MockOperation
func (_mock *MockOperation) Operation(arrayOfNumbers []int) (int, bool) {
	ret := _mock.Called(arrayOfNumbers)

	if len(ret) == 0 {
		panic("no return value specified for Operation")
	}

	var r0 int
	var r1 bool
	if returnFunc, ok := ret.Get(0).(func([]int) (int, bool)); ok {
		return returnFunc(arrayOfNumbers)
	}
	if returnFunc, ok := ret.Get(0).(func([]int) int); ok {
		r0 = returnFunc(arrayOfNumbers)
	} else {
		r0 = ret.Get(0).(int)
	}
	if returnFunc, ok := ret.Get(1).(func([]int) bool); ok {
		r1 = returnFunc(arrayOfNumbers)
	} else {
		r1 = ret.Get(1).(bool)
	}
	return r0, r1
}

// MockOperation_Operation_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Operation'
type MockOperation_Operation_Call struct {
	*mock.Call
}

// Operation is a helper method to define mock.On call
//   - arrayOfNumbers []int
func (_e *MockOperation_Expecter) Operation(arrayOfNumbers interface{}) *MockOperation_Operation_Call {
	return &MockOperation_Operation_Call{Call: _e.mock.On("Operation", arrayOfNumbers)}
}

func (_c *MockOperation_Operation_Call) Run(run func(arrayOfNumbers []int)) *MockOperation_Operation_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 []int
		if args[0] != nil {
			arg0 = args[0].([]int)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockOperation_Operation_Call) Return(n int, b bool) *MockOperation_Operation_Call {
	_c.Call.Return(n, b)
	return _c
}

func (_c *MockOperation_Operation_Call) RunAndReturn(run func(arrayOfNumbers []int) (int, bool)) *MockOperation_Operation_Call {
	_c.Call.Return(run)
	return _c
}

// Symbol provides a mock function for the type MockOperation
func (_mock *MockOperation) Symbol() string {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for Symbol")
	}

	var r0 string
	if returnFunc, ok := ret.Get(0).(func() string); ok {
		r0 = returnFunc()
	} else {
		r0 = ret.Get(0).(string)
	}
	return r0
}

// MockOperation_Symbol_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Symbol'
type MockOperation_Symbol_Call struct {
	*mock.Call
}

// Symbol is a helper method to define mock.On call
func (_e *MockOperation_Expecter) Symbol() *MockOperation_Symbol_Call {
	return &MockOperation_Symbol_Call{Call: _e.mock.On("Symbol")}
}

func (_c *MockOperation_Symbol_Call) Run(run func()) *MockOperation_Symbol_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockOperation_Symbol_Call) Return(s string) *MockOperation_Symbol_Call {
	_c.Call.Return(s)
	return _c
}

func (_c *MockOperation_Symbol_Call) RunAndReturn(run func() string) *MockOperation_Symbol_Call {
	_c.Call.Return(run)
	return _c
}
