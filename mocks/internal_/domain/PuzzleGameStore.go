// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package domain

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockPuzzleGameStore creates a new instance of MockPuzzleGameStore. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockPuzzleGameStore(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockPuzzleGameStore {
	mock := &MockPuzzleGameStore{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockPuzzleGameStore is an autogenerated mock type for the PuzzleGameStore type
type MockPuzzleGameStore struct {
	mock.Mock
}

type MockPuzzleGameStore_Expecter struct {
	mock *mock.Mock
}

func (_m *MockPuzzleGameStore) EXPECT() *MockPuzzleGameStore_Expecter {
	return &MockPuzzleGameStore_Expecter{mock: &_m.Mock}
}

// AbortSearchingForPuzzleGame provides a mock function for the type MockPuzzleGameStore
func (_mock *MockPuzzleGameStore) AbortSearchingForPuzzleGame(ctx context.Context) (*bool, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for AbortSearchingForPuzzleGame")
	}

	var r0 *bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*bool, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *bool); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bool)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleGameStore_AbortSearchingForPuzzleGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AbortSearchingForPuzzleGame'
type MockPuzzleGameStore_AbortSearchingForPuzzleGame_Call struct {
	*mock.Call
}

// AbortSearchingForPuzzleGame is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockPuzzleGameStore_Expecter) AbortSearchingForPuzzleGame(ctx interface{}) *MockPuzzleGameStore_AbortSearchingForPuzzleGame_Call {
	return &MockPuzzleGameStore_AbortSearchingForPuzzleGame_Call{Call: _e.mock.On("AbortSearchingForPuzzleGame", ctx)}
}

func (_c *MockPuzzleGameStore_AbortSearchingForPuzzleGame_Call) Run(run func(ctx context.Context)) *MockPuzzleGameStore_AbortSearchingForPuzzleGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockPuzzleGameStore_AbortSearchingForPuzzleGame_Call) Return(b *bool, err error) *MockPuzzleGameStore_AbortSearchingForPuzzleGame_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockPuzzleGameStore_AbortSearchingForPuzzleGame_Call) RunAndReturn(run func(ctx context.Context) (*bool, error)) *MockPuzzleGameStore_AbortSearchingForPuzzleGame_Call {
	_c.Call.Return(run)
	return _c
}

// AcceptChallengeOfPuzzleGame provides a mock function for the type MockPuzzleGameStore
func (_mock *MockPuzzleGameStore) AcceptChallengeOfPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for AcceptChallengeOfPuzzleGame")
	}

	var r0 *models.PuzzleGame
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.PuzzleGame, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.PuzzleGame); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PuzzleGame)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleGameStore_AcceptChallengeOfPuzzleGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AcceptChallengeOfPuzzleGame'
type MockPuzzleGameStore_AcceptChallengeOfPuzzleGame_Call struct {
	*mock.Call
}

// AcceptChallengeOfPuzzleGame is a helper method to define mock.On call
//   - ctx context.Context
//   - gameID primitive.ObjectID
func (_e *MockPuzzleGameStore_Expecter) AcceptChallengeOfPuzzleGame(ctx interface{}, gameID interface{}) *MockPuzzleGameStore_AcceptChallengeOfPuzzleGame_Call {
	return &MockPuzzleGameStore_AcceptChallengeOfPuzzleGame_Call{Call: _e.mock.On("AcceptChallengeOfPuzzleGame", ctx, gameID)}
}

func (_c *MockPuzzleGameStore_AcceptChallengeOfPuzzleGame_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID)) *MockPuzzleGameStore_AcceptChallengeOfPuzzleGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockPuzzleGameStore_AcceptChallengeOfPuzzleGame_Call) Return(puzzleGame *models.PuzzleGame, err error) *MockPuzzleGameStore_AcceptChallengeOfPuzzleGame_Call {
	_c.Call.Return(puzzleGame, err)
	return _c
}

func (_c *MockPuzzleGameStore_AcceptChallengeOfPuzzleGame_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error)) *MockPuzzleGameStore_AcceptChallengeOfPuzzleGame_Call {
	_c.Call.Return(run)
	return _c
}

// AcceptRematchOfPuzzleGame provides a mock function for the type MockPuzzleGameStore
func (_mock *MockPuzzleGameStore) AcceptRematchOfPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for AcceptRematchOfPuzzleGame")
	}

	var r0 *models.PuzzleGame
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.PuzzleGame, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.PuzzleGame); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PuzzleGame)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleGameStore_AcceptRematchOfPuzzleGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AcceptRematchOfPuzzleGame'
type MockPuzzleGameStore_AcceptRematchOfPuzzleGame_Call struct {
	*mock.Call
}

// AcceptRematchOfPuzzleGame is a helper method to define mock.On call
//   - ctx context.Context
//   - gameID primitive.ObjectID
func (_e *MockPuzzleGameStore_Expecter) AcceptRematchOfPuzzleGame(ctx interface{}, gameID interface{}) *MockPuzzleGameStore_AcceptRematchOfPuzzleGame_Call {
	return &MockPuzzleGameStore_AcceptRematchOfPuzzleGame_Call{Call: _e.mock.On("AcceptRematchOfPuzzleGame", ctx, gameID)}
}

func (_c *MockPuzzleGameStore_AcceptRematchOfPuzzleGame_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID)) *MockPuzzleGameStore_AcceptRematchOfPuzzleGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockPuzzleGameStore_AcceptRematchOfPuzzleGame_Call) Return(puzzleGame *models.PuzzleGame, err error) *MockPuzzleGameStore_AcceptRematchOfPuzzleGame_Call {
	_c.Call.Return(puzzleGame, err)
	return _c
}

func (_c *MockPuzzleGameStore_AcceptRematchOfPuzzleGame_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error)) *MockPuzzleGameStore_AcceptRematchOfPuzzleGame_Call {
	_c.Call.Return(run)
	return _c
}

// CancelPuzzleGame provides a mock function for the type MockPuzzleGameStore
func (_mock *MockPuzzleGameStore) CancelPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (*bool, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for CancelPuzzleGame")
	}

	var r0 *bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*bool, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *bool); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bool)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleGameStore_CancelPuzzleGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CancelPuzzleGame'
type MockPuzzleGameStore_CancelPuzzleGame_Call struct {
	*mock.Call
}

// CancelPuzzleGame is a helper method to define mock.On call
//   - ctx context.Context
//   - gameID primitive.ObjectID
func (_e *MockPuzzleGameStore_Expecter) CancelPuzzleGame(ctx interface{}, gameID interface{}) *MockPuzzleGameStore_CancelPuzzleGame_Call {
	return &MockPuzzleGameStore_CancelPuzzleGame_Call{Call: _e.mock.On("CancelPuzzleGame", ctx, gameID)}
}

func (_c *MockPuzzleGameStore_CancelPuzzleGame_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID)) *MockPuzzleGameStore_CancelPuzzleGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockPuzzleGameStore_CancelPuzzleGame_Call) Return(b *bool, err error) *MockPuzzleGameStore_CancelPuzzleGame_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockPuzzleGameStore_CancelPuzzleGame_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID) (*bool, error)) *MockPuzzleGameStore_CancelPuzzleGame_Call {
	_c.Call.Return(run)
	return _c
}

// ChallengeUserForPuzzleGame provides a mock function for the type MockPuzzleGameStore
func (_mock *MockPuzzleGameStore) ChallengeUserForPuzzleGame(ctx context.Context, challengeUserInput *models.ChallengeUserForPuzzleGameInput) (*models.PuzzleGame, error) {
	ret := _mock.Called(ctx, challengeUserInput)

	if len(ret) == 0 {
		panic("no return value specified for ChallengeUserForPuzzleGame")
	}

	var r0 *models.PuzzleGame
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.ChallengeUserForPuzzleGameInput) (*models.PuzzleGame, error)); ok {
		return returnFunc(ctx, challengeUserInput)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.ChallengeUserForPuzzleGameInput) *models.PuzzleGame); ok {
		r0 = returnFunc(ctx, challengeUserInput)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PuzzleGame)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.ChallengeUserForPuzzleGameInput) error); ok {
		r1 = returnFunc(ctx, challengeUserInput)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleGameStore_ChallengeUserForPuzzleGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ChallengeUserForPuzzleGame'
type MockPuzzleGameStore_ChallengeUserForPuzzleGame_Call struct {
	*mock.Call
}

// ChallengeUserForPuzzleGame is a helper method to define mock.On call
//   - ctx context.Context
//   - challengeUserInput *models.ChallengeUserForPuzzleGameInput
func (_e *MockPuzzleGameStore_Expecter) ChallengeUserForPuzzleGame(ctx interface{}, challengeUserInput interface{}) *MockPuzzleGameStore_ChallengeUserForPuzzleGame_Call {
	return &MockPuzzleGameStore_ChallengeUserForPuzzleGame_Call{Call: _e.mock.On("ChallengeUserForPuzzleGame", ctx, challengeUserInput)}
}

func (_c *MockPuzzleGameStore_ChallengeUserForPuzzleGame_Call) Run(run func(ctx context.Context, challengeUserInput *models.ChallengeUserForPuzzleGameInput)) *MockPuzzleGameStore_ChallengeUserForPuzzleGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.ChallengeUserForPuzzleGameInput
		if args[1] != nil {
			arg1 = args[1].(*models.ChallengeUserForPuzzleGameInput)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockPuzzleGameStore_ChallengeUserForPuzzleGame_Call) Return(puzzleGame *models.PuzzleGame, err error) *MockPuzzleGameStore_ChallengeUserForPuzzleGame_Call {
	_c.Call.Return(puzzleGame, err)
	return _c
}

func (_c *MockPuzzleGameStore_ChallengeUserForPuzzleGame_Call) RunAndReturn(run func(ctx context.Context, challengeUserInput *models.ChallengeUserForPuzzleGameInput) (*models.PuzzleGame, error)) *MockPuzzleGameStore_ChallengeUserForPuzzleGame_Call {
	_c.Call.Return(run)
	return _c
}

// CreatePuzzleGame provides a mock function for the type MockPuzzleGameStore
func (_mock *MockPuzzleGameStore) CreatePuzzleGame(ctx context.Context, gameConfig *models.PuzzleGameConfigInput) (*models.PuzzleGame, error) {
	ret := _mock.Called(ctx, gameConfig)

	if len(ret) == 0 {
		panic("no return value specified for CreatePuzzleGame")
	}

	var r0 *models.PuzzleGame
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.PuzzleGameConfigInput) (*models.PuzzleGame, error)); ok {
		return returnFunc(ctx, gameConfig)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.PuzzleGameConfigInput) *models.PuzzleGame); ok {
		r0 = returnFunc(ctx, gameConfig)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PuzzleGame)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.PuzzleGameConfigInput) error); ok {
		r1 = returnFunc(ctx, gameConfig)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleGameStore_CreatePuzzleGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreatePuzzleGame'
type MockPuzzleGameStore_CreatePuzzleGame_Call struct {
	*mock.Call
}

// CreatePuzzleGame is a helper method to define mock.On call
//   - ctx context.Context
//   - gameConfig *models.PuzzleGameConfigInput
func (_e *MockPuzzleGameStore_Expecter) CreatePuzzleGame(ctx interface{}, gameConfig interface{}) *MockPuzzleGameStore_CreatePuzzleGame_Call {
	return &MockPuzzleGameStore_CreatePuzzleGame_Call{Call: _e.mock.On("CreatePuzzleGame", ctx, gameConfig)}
}

func (_c *MockPuzzleGameStore_CreatePuzzleGame_Call) Run(run func(ctx context.Context, gameConfig *models.PuzzleGameConfigInput)) *MockPuzzleGameStore_CreatePuzzleGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.PuzzleGameConfigInput
		if args[1] != nil {
			arg1 = args[1].(*models.PuzzleGameConfigInput)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockPuzzleGameStore_CreatePuzzleGame_Call) Return(puzzleGame *models.PuzzleGame, err error) *MockPuzzleGameStore_CreatePuzzleGame_Call {
	_c.Call.Return(puzzleGame, err)
	return _c
}

func (_c *MockPuzzleGameStore_CreatePuzzleGame_Call) RunAndReturn(run func(ctx context.Context, gameConfig *models.PuzzleGameConfigInput) (*models.PuzzleGame, error)) *MockPuzzleGameStore_CreatePuzzleGame_Call {
	_c.Call.Return(run)
	return _c
}

// EndPuzzleGame provides a mock function for the type MockPuzzleGameStore
func (_mock *MockPuzzleGameStore) EndPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for EndPuzzleGame")
	}

	var r0 *models.PuzzleGame
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.PuzzleGame, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.PuzzleGame); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PuzzleGame)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleGameStore_EndPuzzleGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'EndPuzzleGame'
type MockPuzzleGameStore_EndPuzzleGame_Call struct {
	*mock.Call
}

// EndPuzzleGame is a helper method to define mock.On call
//   - ctx context.Context
//   - gameID primitive.ObjectID
func (_e *MockPuzzleGameStore_Expecter) EndPuzzleGame(ctx interface{}, gameID interface{}) *MockPuzzleGameStore_EndPuzzleGame_Call {
	return &MockPuzzleGameStore_EndPuzzleGame_Call{Call: _e.mock.On("EndPuzzleGame", ctx, gameID)}
}

func (_c *MockPuzzleGameStore_EndPuzzleGame_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID)) *MockPuzzleGameStore_EndPuzzleGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockPuzzleGameStore_EndPuzzleGame_Call) Return(puzzleGame *models.PuzzleGame, err error) *MockPuzzleGameStore_EndPuzzleGame_Call {
	_c.Call.Return(puzzleGame, err)
	return _c
}

func (_c *MockPuzzleGameStore_EndPuzzleGame_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error)) *MockPuzzleGameStore_EndPuzzleGame_Call {
	_c.Call.Return(run)
	return _c
}

// GetFriendsTop5CrossMathPuzzleRushStats provides a mock function for the type MockPuzzleGameStore
func (_mock *MockPuzzleGameStore) GetFriendsTop5CrossMathPuzzleRushStats(ctx context.Context) ([]*models.CrossMathPuzzleRushPlayerInfo, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetFriendsTop5CrossMathPuzzleRushStats")
	}

	var r0 []*models.CrossMathPuzzleRushPlayerInfo
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) ([]*models.CrossMathPuzzleRushPlayerInfo, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) []*models.CrossMathPuzzleRushPlayerInfo); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.CrossMathPuzzleRushPlayerInfo)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleGameStore_GetFriendsTop5CrossMathPuzzleRushStats_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFriendsTop5CrossMathPuzzleRushStats'
type MockPuzzleGameStore_GetFriendsTop5CrossMathPuzzleRushStats_Call struct {
	*mock.Call
}

// GetFriendsTop5CrossMathPuzzleRushStats is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockPuzzleGameStore_Expecter) GetFriendsTop5CrossMathPuzzleRushStats(ctx interface{}) *MockPuzzleGameStore_GetFriendsTop5CrossMathPuzzleRushStats_Call {
	return &MockPuzzleGameStore_GetFriendsTop5CrossMathPuzzleRushStats_Call{Call: _e.mock.On("GetFriendsTop5CrossMathPuzzleRushStats", ctx)}
}

func (_c *MockPuzzleGameStore_GetFriendsTop5CrossMathPuzzleRushStats_Call) Run(run func(ctx context.Context)) *MockPuzzleGameStore_GetFriendsTop5CrossMathPuzzleRushStats_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockPuzzleGameStore_GetFriendsTop5CrossMathPuzzleRushStats_Call) Return(crossMathPuzzleRushPlayerInfos []*models.CrossMathPuzzleRushPlayerInfo, err error) *MockPuzzleGameStore_GetFriendsTop5CrossMathPuzzleRushStats_Call {
	_c.Call.Return(crossMathPuzzleRushPlayerInfos, err)
	return _c
}

func (_c *MockPuzzleGameStore_GetFriendsTop5CrossMathPuzzleRushStats_Call) RunAndReturn(run func(ctx context.Context) ([]*models.CrossMathPuzzleRushPlayerInfo, error)) *MockPuzzleGameStore_GetFriendsTop5CrossMathPuzzleRushStats_Call {
	_c.Call.Return(run)
	return _c
}

// GetGlobalTop5CrossMathPuzzleRushStats provides a mock function for the type MockPuzzleGameStore
func (_mock *MockPuzzleGameStore) GetGlobalTop5CrossMathPuzzleRushStats(ctx context.Context) ([]*models.CrossMathPuzzleRushPlayerInfo, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetGlobalTop5CrossMathPuzzleRushStats")
	}

	var r0 []*models.CrossMathPuzzleRushPlayerInfo
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) ([]*models.CrossMathPuzzleRushPlayerInfo, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) []*models.CrossMathPuzzleRushPlayerInfo); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.CrossMathPuzzleRushPlayerInfo)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleGameStore_GetGlobalTop5CrossMathPuzzleRushStats_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetGlobalTop5CrossMathPuzzleRushStats'
type MockPuzzleGameStore_GetGlobalTop5CrossMathPuzzleRushStats_Call struct {
	*mock.Call
}

// GetGlobalTop5CrossMathPuzzleRushStats is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockPuzzleGameStore_Expecter) GetGlobalTop5CrossMathPuzzleRushStats(ctx interface{}) *MockPuzzleGameStore_GetGlobalTop5CrossMathPuzzleRushStats_Call {
	return &MockPuzzleGameStore_GetGlobalTop5CrossMathPuzzleRushStats_Call{Call: _e.mock.On("GetGlobalTop5CrossMathPuzzleRushStats", ctx)}
}

func (_c *MockPuzzleGameStore_GetGlobalTop5CrossMathPuzzleRushStats_Call) Run(run func(ctx context.Context)) *MockPuzzleGameStore_GetGlobalTop5CrossMathPuzzleRushStats_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockPuzzleGameStore_GetGlobalTop5CrossMathPuzzleRushStats_Call) Return(crossMathPuzzleRushPlayerInfos []*models.CrossMathPuzzleRushPlayerInfo, err error) *MockPuzzleGameStore_GetGlobalTop5CrossMathPuzzleRushStats_Call {
	_c.Call.Return(crossMathPuzzleRushPlayerInfos, err)
	return _c
}

func (_c *MockPuzzleGameStore_GetGlobalTop5CrossMathPuzzleRushStats_Call) RunAndReturn(run func(ctx context.Context) ([]*models.CrossMathPuzzleRushPlayerInfo, error)) *MockPuzzleGameStore_GetGlobalTop5CrossMathPuzzleRushStats_Call {
	_c.Call.Return(run)
	return _c
}

// GetMyCrossMathPuzzleRushStats provides a mock function for the type MockPuzzleGameStore
func (_mock *MockPuzzleGameStore) GetMyCrossMathPuzzleRushStats(ctx context.Context) (*models.CrossMathPuzzleRushStats, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetMyCrossMathPuzzleRushStats")
	}

	var r0 *models.CrossMathPuzzleRushStats
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*models.CrossMathPuzzleRushStats, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *models.CrossMathPuzzleRushStats); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.CrossMathPuzzleRushStats)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleGameStore_GetMyCrossMathPuzzleRushStats_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMyCrossMathPuzzleRushStats'
type MockPuzzleGameStore_GetMyCrossMathPuzzleRushStats_Call struct {
	*mock.Call
}

// GetMyCrossMathPuzzleRushStats is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockPuzzleGameStore_Expecter) GetMyCrossMathPuzzleRushStats(ctx interface{}) *MockPuzzleGameStore_GetMyCrossMathPuzzleRushStats_Call {
	return &MockPuzzleGameStore_GetMyCrossMathPuzzleRushStats_Call{Call: _e.mock.On("GetMyCrossMathPuzzleRushStats", ctx)}
}

func (_c *MockPuzzleGameStore_GetMyCrossMathPuzzleRushStats_Call) Run(run func(ctx context.Context)) *MockPuzzleGameStore_GetMyCrossMathPuzzleRushStats_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockPuzzleGameStore_GetMyCrossMathPuzzleRushStats_Call) Return(crossMathPuzzleRushStats *models.CrossMathPuzzleRushStats, err error) *MockPuzzleGameStore_GetMyCrossMathPuzzleRushStats_Call {
	_c.Call.Return(crossMathPuzzleRushStats, err)
	return _c
}

func (_c *MockPuzzleGameStore_GetMyCrossMathPuzzleRushStats_Call) RunAndReturn(run func(ctx context.Context) (*models.CrossMathPuzzleRushStats, error)) *MockPuzzleGameStore_GetMyCrossMathPuzzleRushStats_Call {
	_c.Call.Return(run)
	return _c
}

// GetPuzzleGameByID provides a mock function for the type MockPuzzleGameStore
func (_mock *MockPuzzleGameStore) GetPuzzleGameByID(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for GetPuzzleGameByID")
	}

	var r0 *models.PuzzleGame
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.PuzzleGame, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.PuzzleGame); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PuzzleGame)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleGameStore_GetPuzzleGameByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPuzzleGameByID'
type MockPuzzleGameStore_GetPuzzleGameByID_Call struct {
	*mock.Call
}

// GetPuzzleGameByID is a helper method to define mock.On call
//   - ctx context.Context
//   - gameID primitive.ObjectID
func (_e *MockPuzzleGameStore_Expecter) GetPuzzleGameByID(ctx interface{}, gameID interface{}) *MockPuzzleGameStore_GetPuzzleGameByID_Call {
	return &MockPuzzleGameStore_GetPuzzleGameByID_Call{Call: _e.mock.On("GetPuzzleGameByID", ctx, gameID)}
}

func (_c *MockPuzzleGameStore_GetPuzzleGameByID_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID)) *MockPuzzleGameStore_GetPuzzleGameByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockPuzzleGameStore_GetPuzzleGameByID_Call) Return(puzzleGame *models.PuzzleGame, err error) *MockPuzzleGameStore_GetPuzzleGameByID_Call {
	_c.Call.Return(puzzleGame, err)
	return _c
}

func (_c *MockPuzzleGameStore_GetPuzzleGameByID_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error)) *MockPuzzleGameStore_GetPuzzleGameByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetPuzzleGamesByUser provides a mock function for the type MockPuzzleGameStore
func (_mock *MockPuzzleGameStore) GetPuzzleGamesByUser(ctx context.Context, payload *models.GetPuzzleGamesInput) (*models.GetPuzzleGamesOutput, error) {
	ret := _mock.Called(ctx, payload)

	if len(ret) == 0 {
		panic("no return value specified for GetPuzzleGamesByUser")
	}

	var r0 *models.GetPuzzleGamesOutput
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.GetPuzzleGamesInput) (*models.GetPuzzleGamesOutput, error)); ok {
		return returnFunc(ctx, payload)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.GetPuzzleGamesInput) *models.GetPuzzleGamesOutput); ok {
		r0 = returnFunc(ctx, payload)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.GetPuzzleGamesOutput)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.GetPuzzleGamesInput) error); ok {
		r1 = returnFunc(ctx, payload)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleGameStore_GetPuzzleGamesByUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPuzzleGamesByUser'
type MockPuzzleGameStore_GetPuzzleGamesByUser_Call struct {
	*mock.Call
}

// GetPuzzleGamesByUser is a helper method to define mock.On call
//   - ctx context.Context
//   - payload *models.GetPuzzleGamesInput
func (_e *MockPuzzleGameStore_Expecter) GetPuzzleGamesByUser(ctx interface{}, payload interface{}) *MockPuzzleGameStore_GetPuzzleGamesByUser_Call {
	return &MockPuzzleGameStore_GetPuzzleGamesByUser_Call{Call: _e.mock.On("GetPuzzleGamesByUser", ctx, payload)}
}

func (_c *MockPuzzleGameStore_GetPuzzleGamesByUser_Call) Run(run func(ctx context.Context, payload *models.GetPuzzleGamesInput)) *MockPuzzleGameStore_GetPuzzleGamesByUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.GetPuzzleGamesInput
		if args[1] != nil {
			arg1 = args[1].(*models.GetPuzzleGamesInput)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockPuzzleGameStore_GetPuzzleGamesByUser_Call) Return(getPuzzleGamesOutput *models.GetPuzzleGamesOutput, err error) *MockPuzzleGameStore_GetPuzzleGamesByUser_Call {
	_c.Call.Return(getPuzzleGamesOutput, err)
	return _c
}

func (_c *MockPuzzleGameStore_GetPuzzleGamesByUser_Call) RunAndReturn(run func(ctx context.Context, payload *models.GetPuzzleGamesInput) (*models.GetPuzzleGamesOutput, error)) *MockPuzzleGameStore_GetPuzzleGamesByUser_Call {
	_c.Call.Return(run)
	return _c
}

// JoinPuzzleGame provides a mock function for the type MockPuzzleGameStore
func (_mock *MockPuzzleGameStore) JoinPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for JoinPuzzleGame")
	}

	var r0 *models.PuzzleGame
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.PuzzleGame, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.PuzzleGame); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PuzzleGame)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleGameStore_JoinPuzzleGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'JoinPuzzleGame'
type MockPuzzleGameStore_JoinPuzzleGame_Call struct {
	*mock.Call
}

// JoinPuzzleGame is a helper method to define mock.On call
//   - ctx context.Context
//   - gameID primitive.ObjectID
func (_e *MockPuzzleGameStore_Expecter) JoinPuzzleGame(ctx interface{}, gameID interface{}) *MockPuzzleGameStore_JoinPuzzleGame_Call {
	return &MockPuzzleGameStore_JoinPuzzleGame_Call{Call: _e.mock.On("JoinPuzzleGame", ctx, gameID)}
}

func (_c *MockPuzzleGameStore_JoinPuzzleGame_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID)) *MockPuzzleGameStore_JoinPuzzleGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockPuzzleGameStore_JoinPuzzleGame_Call) Return(puzzleGame *models.PuzzleGame, err error) *MockPuzzleGameStore_JoinPuzzleGame_Call {
	_c.Call.Return(puzzleGame, err)
	return _c
}

func (_c *MockPuzzleGameStore_JoinPuzzleGame_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error)) *MockPuzzleGameStore_JoinPuzzleGame_Call {
	_c.Call.Return(run)
	return _c
}

// LeavePuzzleGame provides a mock function for the type MockPuzzleGameStore
func (_mock *MockPuzzleGameStore) LeavePuzzleGame(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for LeavePuzzleGame")
	}

	var r0 *models.PuzzleGame
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.PuzzleGame, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.PuzzleGame); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PuzzleGame)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleGameStore_LeavePuzzleGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LeavePuzzleGame'
type MockPuzzleGameStore_LeavePuzzleGame_Call struct {
	*mock.Call
}

// LeavePuzzleGame is a helper method to define mock.On call
//   - ctx context.Context
//   - gameID primitive.ObjectID
func (_e *MockPuzzleGameStore_Expecter) LeavePuzzleGame(ctx interface{}, gameID interface{}) *MockPuzzleGameStore_LeavePuzzleGame_Call {
	return &MockPuzzleGameStore_LeavePuzzleGame_Call{Call: _e.mock.On("LeavePuzzleGame", ctx, gameID)}
}

func (_c *MockPuzzleGameStore_LeavePuzzleGame_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID)) *MockPuzzleGameStore_LeavePuzzleGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockPuzzleGameStore_LeavePuzzleGame_Call) Return(puzzleGame *models.PuzzleGame, err error) *MockPuzzleGameStore_LeavePuzzleGame_Call {
	_c.Call.Return(puzzleGame, err)
	return _c
}

func (_c *MockPuzzleGameStore_LeavePuzzleGame_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error)) *MockPuzzleGameStore_LeavePuzzleGame_Call {
	_c.Call.Return(run)
	return _c
}

// RejectChallengeOfPuzzleGame provides a mock function for the type MockPuzzleGameStore
func (_mock *MockPuzzleGameStore) RejectChallengeOfPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (*bool, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for RejectChallengeOfPuzzleGame")
	}

	var r0 *bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*bool, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *bool); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bool)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleGameStore_RejectChallengeOfPuzzleGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RejectChallengeOfPuzzleGame'
type MockPuzzleGameStore_RejectChallengeOfPuzzleGame_Call struct {
	*mock.Call
}

// RejectChallengeOfPuzzleGame is a helper method to define mock.On call
//   - ctx context.Context
//   - gameID primitive.ObjectID
func (_e *MockPuzzleGameStore_Expecter) RejectChallengeOfPuzzleGame(ctx interface{}, gameID interface{}) *MockPuzzleGameStore_RejectChallengeOfPuzzleGame_Call {
	return &MockPuzzleGameStore_RejectChallengeOfPuzzleGame_Call{Call: _e.mock.On("RejectChallengeOfPuzzleGame", ctx, gameID)}
}

func (_c *MockPuzzleGameStore_RejectChallengeOfPuzzleGame_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID)) *MockPuzzleGameStore_RejectChallengeOfPuzzleGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockPuzzleGameStore_RejectChallengeOfPuzzleGame_Call) Return(b *bool, err error) *MockPuzzleGameStore_RejectChallengeOfPuzzleGame_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockPuzzleGameStore_RejectChallengeOfPuzzleGame_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID) (*bool, error)) *MockPuzzleGameStore_RejectChallengeOfPuzzleGame_Call {
	_c.Call.Return(run)
	return _c
}

// RejectRematchOfPuzzleGame provides a mock function for the type MockPuzzleGameStore
func (_mock *MockPuzzleGameStore) RejectRematchOfPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for RejectRematchOfPuzzleGame")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleGameStore_RejectRematchOfPuzzleGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RejectRematchOfPuzzleGame'
type MockPuzzleGameStore_RejectRematchOfPuzzleGame_Call struct {
	*mock.Call
}

// RejectRematchOfPuzzleGame is a helper method to define mock.On call
//   - ctx context.Context
//   - gameID primitive.ObjectID
func (_e *MockPuzzleGameStore_Expecter) RejectRematchOfPuzzleGame(ctx interface{}, gameID interface{}) *MockPuzzleGameStore_RejectRematchOfPuzzleGame_Call {
	return &MockPuzzleGameStore_RejectRematchOfPuzzleGame_Call{Call: _e.mock.On("RejectRematchOfPuzzleGame", ctx, gameID)}
}

func (_c *MockPuzzleGameStore_RejectRematchOfPuzzleGame_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID)) *MockPuzzleGameStore_RejectRematchOfPuzzleGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockPuzzleGameStore_RejectRematchOfPuzzleGame_Call) Return(b bool, err error) *MockPuzzleGameStore_RejectRematchOfPuzzleGame_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockPuzzleGameStore_RejectRematchOfPuzzleGame_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID) (bool, error)) *MockPuzzleGameStore_RejectRematchOfPuzzleGame_Call {
	_c.Call.Return(run)
	return _c
}

// RemovePlayerFromPuzzleGame provides a mock function for the type MockPuzzleGameStore
func (_mock *MockPuzzleGameStore) RemovePlayerFromPuzzleGame(ctx context.Context, gameID primitive.ObjectID, playerID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, gameID, playerID)

	if len(ret) == 0 {
		panic("no return value specified for RemovePlayerFromPuzzleGame")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, gameID, playerID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, gameID, playerID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID, playerID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleGameStore_RemovePlayerFromPuzzleGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RemovePlayerFromPuzzleGame'
type MockPuzzleGameStore_RemovePlayerFromPuzzleGame_Call struct {
	*mock.Call
}

// RemovePlayerFromPuzzleGame is a helper method to define mock.On call
//   - ctx context.Context
//   - gameID primitive.ObjectID
//   - playerID primitive.ObjectID
func (_e *MockPuzzleGameStore_Expecter) RemovePlayerFromPuzzleGame(ctx interface{}, gameID interface{}, playerID interface{}) *MockPuzzleGameStore_RemovePlayerFromPuzzleGame_Call {
	return &MockPuzzleGameStore_RemovePlayerFromPuzzleGame_Call{Call: _e.mock.On("RemovePlayerFromPuzzleGame", ctx, gameID, playerID)}
}

func (_c *MockPuzzleGameStore_RemovePlayerFromPuzzleGame_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID, playerID primitive.ObjectID)) *MockPuzzleGameStore_RemovePlayerFromPuzzleGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockPuzzleGameStore_RemovePlayerFromPuzzleGame_Call) Return(b bool, err error) *MockPuzzleGameStore_RemovePlayerFromPuzzleGame_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockPuzzleGameStore_RemovePlayerFromPuzzleGame_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID, playerID primitive.ObjectID) (bool, error)) *MockPuzzleGameStore_RemovePlayerFromPuzzleGame_Call {
	_c.Call.Return(run)
	return _c
}

// RequestRematchForPuzzleGame provides a mock function for the type MockPuzzleGameStore
func (_mock *MockPuzzleGameStore) RequestRematchForPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for RequestRematchForPuzzleGame")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleGameStore_RequestRematchForPuzzleGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RequestRematchForPuzzleGame'
type MockPuzzleGameStore_RequestRematchForPuzzleGame_Call struct {
	*mock.Call
}

// RequestRematchForPuzzleGame is a helper method to define mock.On call
//   - ctx context.Context
//   - gameID primitive.ObjectID
func (_e *MockPuzzleGameStore_Expecter) RequestRematchForPuzzleGame(ctx interface{}, gameID interface{}) *MockPuzzleGameStore_RequestRematchForPuzzleGame_Call {
	return &MockPuzzleGameStore_RequestRematchForPuzzleGame_Call{Call: _e.mock.On("RequestRematchForPuzzleGame", ctx, gameID)}
}

func (_c *MockPuzzleGameStore_RequestRematchForPuzzleGame_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID)) *MockPuzzleGameStore_RequestRematchForPuzzleGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockPuzzleGameStore_RequestRematchForPuzzleGame_Call) Return(b bool, err error) *MockPuzzleGameStore_RequestRematchForPuzzleGame_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockPuzzleGameStore_RequestRematchForPuzzleGame_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID) (bool, error)) *MockPuzzleGameStore_RequestRematchForPuzzleGame_Call {
	_c.Call.Return(run)
	return _c
}

// StartPuzzleGame provides a mock function for the type MockPuzzleGameStore
func (_mock *MockPuzzleGameStore) StartPuzzleGame(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for StartPuzzleGame")
	}

	var r0 *models.PuzzleGame
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.PuzzleGame, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.PuzzleGame); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PuzzleGame)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleGameStore_StartPuzzleGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'StartPuzzleGame'
type MockPuzzleGameStore_StartPuzzleGame_Call struct {
	*mock.Call
}

// StartPuzzleGame is a helper method to define mock.On call
//   - ctx context.Context
//   - gameID primitive.ObjectID
func (_e *MockPuzzleGameStore_Expecter) StartPuzzleGame(ctx interface{}, gameID interface{}) *MockPuzzleGameStore_StartPuzzleGame_Call {
	return &MockPuzzleGameStore_StartPuzzleGame_Call{Call: _e.mock.On("StartPuzzleGame", ctx, gameID)}
}

func (_c *MockPuzzleGameStore_StartPuzzleGame_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID)) *MockPuzzleGameStore_StartPuzzleGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockPuzzleGameStore_StartPuzzleGame_Call) Return(puzzleGame *models.PuzzleGame, err error) *MockPuzzleGameStore_StartPuzzleGame_Call {
	_c.Call.Return(puzzleGame, err)
	return _c
}

func (_c *MockPuzzleGameStore_StartPuzzleGame_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error)) *MockPuzzleGameStore_StartPuzzleGame_Call {
	_c.Call.Return(run)
	return _c
}

// StartSearchingForPuzzleGame provides a mock function for the type MockPuzzleGameStore
func (_mock *MockPuzzleGameStore) StartSearchingForPuzzleGame(ctx context.Context, gameConfig *models.PuzzleGameConfigInput) (*bool, error) {
	ret := _mock.Called(ctx, gameConfig)

	if len(ret) == 0 {
		panic("no return value specified for StartSearchingForPuzzleGame")
	}

	var r0 *bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.PuzzleGameConfigInput) (*bool, error)); ok {
		return returnFunc(ctx, gameConfig)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.PuzzleGameConfigInput) *bool); ok {
		r0 = returnFunc(ctx, gameConfig)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bool)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.PuzzleGameConfigInput) error); ok {
		r1 = returnFunc(ctx, gameConfig)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleGameStore_StartSearchingForPuzzleGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'StartSearchingForPuzzleGame'
type MockPuzzleGameStore_StartSearchingForPuzzleGame_Call struct {
	*mock.Call
}

// StartSearchingForPuzzleGame is a helper method to define mock.On call
//   - ctx context.Context
//   - gameConfig *models.PuzzleGameConfigInput
func (_e *MockPuzzleGameStore_Expecter) StartSearchingForPuzzleGame(ctx interface{}, gameConfig interface{}) *MockPuzzleGameStore_StartSearchingForPuzzleGame_Call {
	return &MockPuzzleGameStore_StartSearchingForPuzzleGame_Call{Call: _e.mock.On("StartSearchingForPuzzleGame", ctx, gameConfig)}
}

func (_c *MockPuzzleGameStore_StartSearchingForPuzzleGame_Call) Run(run func(ctx context.Context, gameConfig *models.PuzzleGameConfigInput)) *MockPuzzleGameStore_StartSearchingForPuzzleGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.PuzzleGameConfigInput
		if args[1] != nil {
			arg1 = args[1].(*models.PuzzleGameConfigInput)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockPuzzleGameStore_StartSearchingForPuzzleGame_Call) Return(b *bool, err error) *MockPuzzleGameStore_StartSearchingForPuzzleGame_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockPuzzleGameStore_StartSearchingForPuzzleGame_Call) RunAndReturn(run func(ctx context.Context, gameConfig *models.PuzzleGameConfigInput) (*bool, error)) *MockPuzzleGameStore_StartSearchingForPuzzleGame_Call {
	_c.Call.Return(run)
	return _c
}

// SubmitPuzzleGameAnswer provides a mock function for the type MockPuzzleGameStore
func (_mock *MockPuzzleGameStore) SubmitPuzzleGameAnswer(ctx context.Context, answerInput *models.SubmitPuzzleGameAnswerInput) (*models.PuzzleGame, error) {
	ret := _mock.Called(ctx, answerInput)

	if len(ret) == 0 {
		panic("no return value specified for SubmitPuzzleGameAnswer")
	}

	var r0 *models.PuzzleGame
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.SubmitPuzzleGameAnswerInput) (*models.PuzzleGame, error)); ok {
		return returnFunc(ctx, answerInput)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.SubmitPuzzleGameAnswerInput) *models.PuzzleGame); ok {
		r0 = returnFunc(ctx, answerInput)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PuzzleGame)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.SubmitPuzzleGameAnswerInput) error); ok {
		r1 = returnFunc(ctx, answerInput)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleGameStore_SubmitPuzzleGameAnswer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SubmitPuzzleGameAnswer'
type MockPuzzleGameStore_SubmitPuzzleGameAnswer_Call struct {
	*mock.Call
}

// SubmitPuzzleGameAnswer is a helper method to define mock.On call
//   - ctx context.Context
//   - answerInput *models.SubmitPuzzleGameAnswerInput
func (_e *MockPuzzleGameStore_Expecter) SubmitPuzzleGameAnswer(ctx interface{}, answerInput interface{}) *MockPuzzleGameStore_SubmitPuzzleGameAnswer_Call {
	return &MockPuzzleGameStore_SubmitPuzzleGameAnswer_Call{Call: _e.mock.On("SubmitPuzzleGameAnswer", ctx, answerInput)}
}

func (_c *MockPuzzleGameStore_SubmitPuzzleGameAnswer_Call) Run(run func(ctx context.Context, answerInput *models.SubmitPuzzleGameAnswerInput)) *MockPuzzleGameStore_SubmitPuzzleGameAnswer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.SubmitPuzzleGameAnswerInput
		if args[1] != nil {
			arg1 = args[1].(*models.SubmitPuzzleGameAnswerInput)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockPuzzleGameStore_SubmitPuzzleGameAnswer_Call) Return(puzzleGame *models.PuzzleGame, err error) *MockPuzzleGameStore_SubmitPuzzleGameAnswer_Call {
	_c.Call.Return(puzzleGame, err)
	return _c
}

func (_c *MockPuzzleGameStore_SubmitPuzzleGameAnswer_Call) RunAndReturn(run func(ctx context.Context, answerInput *models.SubmitPuzzleGameAnswerInput) (*models.PuzzleGame, error)) *MockPuzzleGameStore_SubmitPuzzleGameAnswer_Call {
	_c.Call.Return(run)
	return _c
}

// SubmitPuzzleGameRush provides a mock function for the type MockPuzzleGameStore
func (_mock *MockPuzzleGameStore) SubmitPuzzleGameRush(ctx context.Context, input models.SubmitPuzzleRushGame) (*models.CrossMathPuzzleRush, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for SubmitPuzzleGameRush")
	}

	var r0 *models.CrossMathPuzzleRush
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.SubmitPuzzleRushGame) (*models.CrossMathPuzzleRush, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.SubmitPuzzleRushGame) *models.CrossMathPuzzleRush); ok {
		r0 = returnFunc(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.CrossMathPuzzleRush)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.SubmitPuzzleRushGame) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleGameStore_SubmitPuzzleGameRush_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SubmitPuzzleGameRush'
type MockPuzzleGameStore_SubmitPuzzleGameRush_Call struct {
	*mock.Call
}

// SubmitPuzzleGameRush is a helper method to define mock.On call
//   - ctx context.Context
//   - input models.SubmitPuzzleRushGame
func (_e *MockPuzzleGameStore_Expecter) SubmitPuzzleGameRush(ctx interface{}, input interface{}) *MockPuzzleGameStore_SubmitPuzzleGameRush_Call {
	return &MockPuzzleGameStore_SubmitPuzzleGameRush_Call{Call: _e.mock.On("SubmitPuzzleGameRush", ctx, input)}
}

func (_c *MockPuzzleGameStore_SubmitPuzzleGameRush_Call) Run(run func(ctx context.Context, input models.SubmitPuzzleRushGame)) *MockPuzzleGameStore_SubmitPuzzleGameRush_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 models.SubmitPuzzleRushGame
		if args[1] != nil {
			arg1 = args[1].(models.SubmitPuzzleRushGame)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockPuzzleGameStore_SubmitPuzzleGameRush_Call) Return(crossMathPuzzleRush *models.CrossMathPuzzleRush, err error) *MockPuzzleGameStore_SubmitPuzzleGameRush_Call {
	_c.Call.Return(crossMathPuzzleRush, err)
	return _c
}

func (_c *MockPuzzleGameStore_SubmitPuzzleGameRush_Call) RunAndReturn(run func(ctx context.Context, input models.SubmitPuzzleRushGame) (*models.CrossMathPuzzleRush, error)) *MockPuzzleGameStore_SubmitPuzzleGameRush_Call {
	_c.Call.Return(run)
	return _c
}
