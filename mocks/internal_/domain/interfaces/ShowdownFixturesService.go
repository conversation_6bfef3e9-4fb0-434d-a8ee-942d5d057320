// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package interfaces

import (
	"context"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockShowdownFixturesService creates a new instance of MockShowdownFixturesService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockShowdownFixturesService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockShowdownFixturesService {
	mock := &MockShowdownFixturesService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockShowdownFixturesService is an autogenerated mock type for the ShowdownFixturesService type
type MockShowdownFixturesService struct {
	mock.Mock
}

type MockShowdownFixturesService_Expecter struct {
	mock *mock.Mock
}

func (_m *MockShowdownFixturesService) EXPECT() *MockShowdownFixturesService_Expecter {
	return &MockShowdownFixturesService_Expecter{mock: &_m.Mock}
}

// CreateFixture provides a mock function for the type MockShowdownFixturesService
func (_mock *MockShowdownFixturesService) CreateFixture(ctx context.Context, showdownId primitive.ObjectID, round int) error {
	ret := _mock.Called(ctx, showdownId, round)

	if len(ret) == 0 {
		panic("no return value specified for CreateFixture")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int) error); ok {
		r0 = returnFunc(ctx, showdownId, round)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockShowdownFixturesService_CreateFixture_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateFixture'
type MockShowdownFixturesService_CreateFixture_Call struct {
	*mock.Call
}

// CreateFixture is a helper method to define mock.On call
//   - ctx context.Context
//   - showdownId primitive.ObjectID
//   - round int
func (_e *MockShowdownFixturesService_Expecter) CreateFixture(ctx interface{}, showdownId interface{}, round interface{}) *MockShowdownFixturesService_CreateFixture_Call {
	return &MockShowdownFixturesService_CreateFixture_Call{Call: _e.mock.On("CreateFixture", ctx, showdownId, round)}
}

func (_c *MockShowdownFixturesService_CreateFixture_Call) Run(run func(ctx context.Context, showdownId primitive.ObjectID, round int)) *MockShowdownFixturesService_CreateFixture_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 int
		if args[2] != nil {
			arg2 = args[2].(int)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockShowdownFixturesService_CreateFixture_Call) Return(err error) *MockShowdownFixturesService_CreateFixture_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockShowdownFixturesService_CreateFixture_Call) RunAndReturn(run func(ctx context.Context, showdownId primitive.ObjectID, round int) error) *MockShowdownFixturesService_CreateFixture_Call {
	_c.Call.Return(run)
	return _c
}
