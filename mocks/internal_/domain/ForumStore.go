// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package domain

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockForumStore creates a new instance of MockForumStore. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockForumStore(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockForumStore {
	mock := &MockForumStore{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockForumStore is an autogenerated mock type for the ForumStore type
type MockForumStore struct {
	mock.Mock
}

type MockForumStore_Expecter struct {
	mock *mock.Mock
}

func (_m *MockForumStore) EXPECT() *MockForumStore_Expecter {
	return &MockForumStore_Expecter{mock: &_m.<PERSON>}
}

// CreateForum provides a mock function for the type MockForumStore
func (_mock *MockForumStore) CreateForum(ctx context.Context, input models.CreateForumInput) (*models.Forum, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CreateForum")
	}

	var r0 *models.Forum
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateForumInput) (*models.Forum, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateForumInput) *models.Forum); ok {
		r0 = returnFunc(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Forum)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.CreateForumInput) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockForumStore_CreateForum_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateForum'
type MockForumStore_CreateForum_Call struct {
	*mock.Call
}

// CreateForum is a helper method to define mock.On call
//   - ctx context.Context
//   - input models.CreateForumInput
func (_e *MockForumStore_Expecter) CreateForum(ctx interface{}, input interface{}) *MockForumStore_CreateForum_Call {
	return &MockForumStore_CreateForum_Call{Call: _e.mock.On("CreateForum", ctx, input)}
}

func (_c *MockForumStore_CreateForum_Call) Run(run func(ctx context.Context, input models.CreateForumInput)) *MockForumStore_CreateForum_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 models.CreateForumInput
		if args[1] != nil {
			arg1 = args[1].(models.CreateForumInput)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockForumStore_CreateForum_Call) Return(forum *models.Forum, err error) *MockForumStore_CreateForum_Call {
	_c.Call.Return(forum, err)
	return _c
}

func (_c *MockForumStore_CreateForum_Call) RunAndReturn(run func(ctx context.Context, input models.CreateForumInput) (*models.Forum, error)) *MockForumStore_CreateForum_Call {
	_c.Call.Return(run)
	return _c
}

// CreateForumReply provides a mock function for the type MockForumStore
func (_mock *MockForumStore) CreateForumReply(ctx context.Context, input models.CreateForumReplyInput) (*models.ForumReply, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CreateForumReply")
	}

	var r0 *models.ForumReply
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateForumReplyInput) (*models.ForumReply, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateForumReplyInput) *models.ForumReply); ok {
		r0 = returnFunc(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ForumReply)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.CreateForumReplyInput) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockForumStore_CreateForumReply_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateForumReply'
type MockForumStore_CreateForumReply_Call struct {
	*mock.Call
}

// CreateForumReply is a helper method to define mock.On call
//   - ctx context.Context
//   - input models.CreateForumReplyInput
func (_e *MockForumStore_Expecter) CreateForumReply(ctx interface{}, input interface{}) *MockForumStore_CreateForumReply_Call {
	return &MockForumStore_CreateForumReply_Call{Call: _e.mock.On("CreateForumReply", ctx, input)}
}

func (_c *MockForumStore_CreateForumReply_Call) Run(run func(ctx context.Context, input models.CreateForumReplyInput)) *MockForumStore_CreateForumReply_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 models.CreateForumReplyInput
		if args[1] != nil {
			arg1 = args[1].(models.CreateForumReplyInput)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockForumStore_CreateForumReply_Call) Return(forumReply *models.ForumReply, err error) *MockForumStore_CreateForumReply_Call {
	_c.Call.Return(forumReply, err)
	return _c
}

func (_c *MockForumStore_CreateForumReply_Call) RunAndReturn(run func(ctx context.Context, input models.CreateForumReplyInput) (*models.ForumReply, error)) *MockForumStore_CreateForumReply_Call {
	_c.Call.Return(run)
	return _c
}

// CreateForumThread provides a mock function for the type MockForumStore
func (_mock *MockForumStore) CreateForumThread(ctx context.Context, input models.CreateForumThreadInput) (*models.ForumThread, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CreateForumThread")
	}

	var r0 *models.ForumThread
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateForumThreadInput) (*models.ForumThread, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateForumThreadInput) *models.ForumThread); ok {
		r0 = returnFunc(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ForumThread)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.CreateForumThreadInput) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockForumStore_CreateForumThread_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateForumThread'
type MockForumStore_CreateForumThread_Call struct {
	*mock.Call
}

// CreateForumThread is a helper method to define mock.On call
//   - ctx context.Context
//   - input models.CreateForumThreadInput
func (_e *MockForumStore_Expecter) CreateForumThread(ctx interface{}, input interface{}) *MockForumStore_CreateForumThread_Call {
	return &MockForumStore_CreateForumThread_Call{Call: _e.mock.On("CreateForumThread", ctx, input)}
}

func (_c *MockForumStore_CreateForumThread_Call) Run(run func(ctx context.Context, input models.CreateForumThreadInput)) *MockForumStore_CreateForumThread_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 models.CreateForumThreadInput
		if args[1] != nil {
			arg1 = args[1].(models.CreateForumThreadInput)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockForumStore_CreateForumThread_Call) Return(forumThread *models.ForumThread, err error) *MockForumStore_CreateForumThread_Call {
	_c.Call.Return(forumThread, err)
	return _c
}

func (_c *MockForumStore_CreateForumThread_Call) RunAndReturn(run func(ctx context.Context, input models.CreateForumThreadInput) (*models.ForumThread, error)) *MockForumStore_CreateForumThread_Call {
	_c.Call.Return(run)
	return _c
}

// Forum provides a mock function for the type MockForumStore
func (_mock *MockForumStore) Forum(ctx context.Context, id primitive.ObjectID) (*models.Forum, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for Forum")
	}

	var r0 *models.Forum
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.Forum, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.Forum); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Forum)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockForumStore_Forum_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Forum'
type MockForumStore_Forum_Call struct {
	*mock.Call
}

// Forum is a helper method to define mock.On call
//   - ctx context.Context
//   - id primitive.ObjectID
func (_e *MockForumStore_Expecter) Forum(ctx interface{}, id interface{}) *MockForumStore_Forum_Call {
	return &MockForumStore_Forum_Call{Call: _e.mock.On("Forum", ctx, id)}
}

func (_c *MockForumStore_Forum_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockForumStore_Forum_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockForumStore_Forum_Call) Return(forum *models.Forum, err error) *MockForumStore_Forum_Call {
	_c.Call.Return(forum, err)
	return _c
}

func (_c *MockForumStore_Forum_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (*models.Forum, error)) *MockForumStore_Forum_Call {
	_c.Call.Return(run)
	return _c
}

// ForumReplies provides a mock function for the type MockForumStore
func (_mock *MockForumStore) ForumReplies(ctx context.Context, threadID primitive.ObjectID, page *int, pageSize *int) (*models.RepliesPage, error) {
	ret := _mock.Called(ctx, threadID, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for ForumReplies")
	}

	var r0 *models.RepliesPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *int, *int) (*models.RepliesPage, error)); ok {
		return returnFunc(ctx, threadID, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *int, *int) *models.RepliesPage); ok {
		r0 = returnFunc(ctx, threadID, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.RepliesPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, *int, *int) error); ok {
		r1 = returnFunc(ctx, threadID, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockForumStore_ForumReplies_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ForumReplies'
type MockForumStore_ForumReplies_Call struct {
	*mock.Call
}

// ForumReplies is a helper method to define mock.On call
//   - ctx context.Context
//   - threadID primitive.ObjectID
//   - page *int
//   - pageSize *int
func (_e *MockForumStore_Expecter) ForumReplies(ctx interface{}, threadID interface{}, page interface{}, pageSize interface{}) *MockForumStore_ForumReplies_Call {
	return &MockForumStore_ForumReplies_Call{Call: _e.mock.On("ForumReplies", ctx, threadID, page, pageSize)}
}

func (_c *MockForumStore_ForumReplies_Call) Run(run func(ctx context.Context, threadID primitive.ObjectID, page *int, pageSize *int)) *MockForumStore_ForumReplies_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		var arg3 *int
		if args[3] != nil {
			arg3 = args[3].(*int)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockForumStore_ForumReplies_Call) Return(repliesPage *models.RepliesPage, err error) *MockForumStore_ForumReplies_Call {
	_c.Call.Return(repliesPage, err)
	return _c
}

func (_c *MockForumStore_ForumReplies_Call) RunAndReturn(run func(ctx context.Context, threadID primitive.ObjectID, page *int, pageSize *int) (*models.RepliesPage, error)) *MockForumStore_ForumReplies_Call {
	_c.Call.Return(run)
	return _c
}

// ForumThread provides a mock function for the type MockForumStore
func (_mock *MockForumStore) ForumThread(ctx context.Context, id primitive.ObjectID) (*models.ForumThread, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for ForumThread")
	}

	var r0 *models.ForumThread
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.ForumThread, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.ForumThread); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ForumThread)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockForumStore_ForumThread_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ForumThread'
type MockForumStore_ForumThread_Call struct {
	*mock.Call
}

// ForumThread is a helper method to define mock.On call
//   - ctx context.Context
//   - id primitive.ObjectID
func (_e *MockForumStore_Expecter) ForumThread(ctx interface{}, id interface{}) *MockForumStore_ForumThread_Call {
	return &MockForumStore_ForumThread_Call{Call: _e.mock.On("ForumThread", ctx, id)}
}

func (_c *MockForumStore_ForumThread_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockForumStore_ForumThread_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockForumStore_ForumThread_Call) Return(forumThread *models.ForumThread, err error) *MockForumStore_ForumThread_Call {
	_c.Call.Return(forumThread, err)
	return _c
}

func (_c *MockForumStore_ForumThread_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (*models.ForumThread, error)) *MockForumStore_ForumThread_Call {
	_c.Call.Return(run)
	return _c
}

// ForumThreads provides a mock function for the type MockForumStore
func (_mock *MockForumStore) ForumThreads(ctx context.Context, forumID primitive.ObjectID, page *int, pageSize *int) (*models.ThreadsPage, error) {
	ret := _mock.Called(ctx, forumID, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for ForumThreads")
	}

	var r0 *models.ThreadsPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *int, *int) (*models.ThreadsPage, error)); ok {
		return returnFunc(ctx, forumID, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *int, *int) *models.ThreadsPage); ok {
		r0 = returnFunc(ctx, forumID, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ThreadsPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, *int, *int) error); ok {
		r1 = returnFunc(ctx, forumID, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockForumStore_ForumThreads_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ForumThreads'
type MockForumStore_ForumThreads_Call struct {
	*mock.Call
}

// ForumThreads is a helper method to define mock.On call
//   - ctx context.Context
//   - forumID primitive.ObjectID
//   - page *int
//   - pageSize *int
func (_e *MockForumStore_Expecter) ForumThreads(ctx interface{}, forumID interface{}, page interface{}, pageSize interface{}) *MockForumStore_ForumThreads_Call {
	return &MockForumStore_ForumThreads_Call{Call: _e.mock.On("ForumThreads", ctx, forumID, page, pageSize)}
}

func (_c *MockForumStore_ForumThreads_Call) Run(run func(ctx context.Context, forumID primitive.ObjectID, page *int, pageSize *int)) *MockForumStore_ForumThreads_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		var arg3 *int
		if args[3] != nil {
			arg3 = args[3].(*int)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockForumStore_ForumThreads_Call) Return(threadsPage *models.ThreadsPage, err error) *MockForumStore_ForumThreads_Call {
	_c.Call.Return(threadsPage, err)
	return _c
}

func (_c *MockForumStore_ForumThreads_Call) RunAndReturn(run func(ctx context.Context, forumID primitive.ObjectID, page *int, pageSize *int) (*models.ThreadsPage, error)) *MockForumStore_ForumThreads_Call {
	_c.Call.Return(run)
	return _c
}

// Forums provides a mock function for the type MockForumStore
func (_mock *MockForumStore) Forums(ctx context.Context, clubID primitive.ObjectID, page *int, pageSize *int) (*models.ForumPage, error) {
	ret := _mock.Called(ctx, clubID, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for Forums")
	}

	var r0 *models.ForumPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *int, *int) (*models.ForumPage, error)); ok {
		return returnFunc(ctx, clubID, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *int, *int) *models.ForumPage); ok {
		r0 = returnFunc(ctx, clubID, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ForumPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, *int, *int) error); ok {
		r1 = returnFunc(ctx, clubID, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockForumStore_Forums_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Forums'
type MockForumStore_Forums_Call struct {
	*mock.Call
}

// Forums is a helper method to define mock.On call
//   - ctx context.Context
//   - clubID primitive.ObjectID
//   - page *int
//   - pageSize *int
func (_e *MockForumStore_Expecter) Forums(ctx interface{}, clubID interface{}, page interface{}, pageSize interface{}) *MockForumStore_Forums_Call {
	return &MockForumStore_Forums_Call{Call: _e.mock.On("Forums", ctx, clubID, page, pageSize)}
}

func (_c *MockForumStore_Forums_Call) Run(run func(ctx context.Context, clubID primitive.ObjectID, page *int, pageSize *int)) *MockForumStore_Forums_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		var arg3 *int
		if args[3] != nil {
			arg3 = args[3].(*int)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockForumStore_Forums_Call) Return(forumPage *models.ForumPage, err error) *MockForumStore_Forums_Call {
	_c.Call.Return(forumPage, err)
	return _c
}

func (_c *MockForumStore_Forums_Call) RunAndReturn(run func(ctx context.Context, clubID primitive.ObjectID, page *int, pageSize *int) (*models.ForumPage, error)) *MockForumStore_Forums_Call {
	_c.Call.Return(run)
	return _c
}
