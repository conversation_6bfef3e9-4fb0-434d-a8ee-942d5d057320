// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package domain

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	"github.com/99designs/gqlgen/graphql"
	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockClubsStore creates a new instance of MockClubsStore. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockClubsStore(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockClubsStore {
	mock := &MockClubsStore{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockClubsStore is an autogenerated mock type for the ClubsStore type
type MockClubsStore struct {
	mock.Mock
}

type MockClubsStore_Expecter struct {
	mock *mock.Mock
}

func (_m *MockClubsStore) EXPECT() *MockClubsStore_Expecter {
	return &MockClubsStore_Expecter{mock: &_m.Mock}
}

// AddClubMember provides a mock function for the type MockClubsStore
func (_mock *MockClubsStore) AddClubMember(ctx context.Context, clubID primitive.ObjectID, userID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, clubID, userID)

	if len(ret) == 0 {
		panic("no return value specified for AddClubMember")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, clubID, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, clubID, userID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, clubID, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubsStore_AddClubMember_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AddClubMember'
type MockClubsStore_AddClubMember_Call struct {
	*mock.Call
}

// AddClubMember is a helper method to define mock.On call
//   - ctx context.Context
//   - clubID primitive.ObjectID
//   - userID primitive.ObjectID
func (_e *MockClubsStore_Expecter) AddClubMember(ctx interface{}, clubID interface{}, userID interface{}) *MockClubsStore_AddClubMember_Call {
	return &MockClubsStore_AddClubMember_Call{Call: _e.mock.On("AddClubMember", ctx, clubID, userID)}
}

func (_c *MockClubsStore_AddClubMember_Call) Run(run func(ctx context.Context, clubID primitive.ObjectID, userID primitive.ObjectID)) *MockClubsStore_AddClubMember_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockClubsStore_AddClubMember_Call) Return(b bool, err error) *MockClubsStore_AddClubMember_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockClubsStore_AddClubMember_Call) RunAndReturn(run func(ctx context.Context, clubID primitive.ObjectID, userID primitive.ObjectID) (bool, error)) *MockClubsStore_AddClubMember_Call {
	_c.Call.Return(run)
	return _c
}

// Club provides a mock function for the type MockClubsStore
func (_mock *MockClubsStore) Club(ctx context.Context, id primitive.ObjectID) (*models.Club, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for Club")
	}

	var r0 *models.Club
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.Club, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.Club); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Club)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubsStore_Club_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Club'
type MockClubsStore_Club_Call struct {
	*mock.Call
}

// Club is a helper method to define mock.On call
//   - ctx context.Context
//   - id primitive.ObjectID
func (_e *MockClubsStore_Expecter) Club(ctx interface{}, id interface{}) *MockClubsStore_Club_Call {
	return &MockClubsStore_Club_Call{Call: _e.mock.On("Club", ctx, id)}
}

func (_c *MockClubsStore_Club_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockClubsStore_Club_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockClubsStore_Club_Call) Return(club *models.Club, err error) *MockClubsStore_Club_Call {
	_c.Call.Return(club, err)
	return _c
}

func (_c *MockClubsStore_Club_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (*models.Club, error)) *MockClubsStore_Club_Call {
	_c.Call.Return(run)
	return _c
}

// Clubs provides a mock function for the type MockClubsStore
func (_mock *MockClubsStore) Clubs(ctx context.Context, page *int, pageSize *int, visibility *models.Visibility, search *string) (*models.ClubsPage, error) {
	ret := _mock.Called(ctx, page, pageSize, visibility, search)

	if len(ret) == 0 {
		panic("no return value specified for Clubs")
	}

	var r0 *models.ClubsPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int, *models.Visibility, *string) (*models.ClubsPage, error)); ok {
		return returnFunc(ctx, page, pageSize, visibility, search)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int, *models.Visibility, *string) *models.ClubsPage); ok {
		r0 = returnFunc(ctx, page, pageSize, visibility, search)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ClubsPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *int, *int, *models.Visibility, *string) error); ok {
		r1 = returnFunc(ctx, page, pageSize, visibility, search)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubsStore_Clubs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Clubs'
type MockClubsStore_Clubs_Call struct {
	*mock.Call
}

// Clubs is a helper method to define mock.On call
//   - ctx context.Context
//   - page *int
//   - pageSize *int
//   - visibility *models.Visibility
//   - search *string
func (_e *MockClubsStore_Expecter) Clubs(ctx interface{}, page interface{}, pageSize interface{}, visibility interface{}, search interface{}) *MockClubsStore_Clubs_Call {
	return &MockClubsStore_Clubs_Call{Call: _e.mock.On("Clubs", ctx, page, pageSize, visibility, search)}
}

func (_c *MockClubsStore_Clubs_Call) Run(run func(ctx context.Context, page *int, pageSize *int, visibility *models.Visibility, search *string)) *MockClubsStore_Clubs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *int
		if args[1] != nil {
			arg1 = args[1].(*int)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		var arg3 *models.Visibility
		if args[3] != nil {
			arg3 = args[3].(*models.Visibility)
		}
		var arg4 *string
		if args[4] != nil {
			arg4 = args[4].(*string)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
			arg4,
		)
	})
	return _c
}

func (_c *MockClubsStore_Clubs_Call) Return(clubsPage *models.ClubsPage, err error) *MockClubsStore_Clubs_Call {
	_c.Call.Return(clubsPage, err)
	return _c
}

func (_c *MockClubsStore_Clubs_Call) RunAndReturn(run func(ctx context.Context, page *int, pageSize *int, visibility *models.Visibility, search *string) (*models.ClubsPage, error)) *MockClubsStore_Clubs_Call {
	_c.Call.Return(run)
	return _c
}

// CreateClub provides a mock function for the type MockClubsStore
func (_mock *MockClubsStore) CreateClub(ctx context.Context, input models.CreateClubInput) (*models.Club, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CreateClub")
	}

	var r0 *models.Club
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateClubInput) (*models.Club, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateClubInput) *models.Club); ok {
		r0 = returnFunc(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Club)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.CreateClubInput) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubsStore_CreateClub_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateClub'
type MockClubsStore_CreateClub_Call struct {
	*mock.Call
}

// CreateClub is a helper method to define mock.On call
//   - ctx context.Context
//   - input models.CreateClubInput
func (_e *MockClubsStore_Expecter) CreateClub(ctx interface{}, input interface{}) *MockClubsStore_CreateClub_Call {
	return &MockClubsStore_CreateClub_Call{Call: _e.mock.On("CreateClub", ctx, input)}
}

func (_c *MockClubsStore_CreateClub_Call) Run(run func(ctx context.Context, input models.CreateClubInput)) *MockClubsStore_CreateClub_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 models.CreateClubInput
		if args[1] != nil {
			arg1 = args[1].(models.CreateClubInput)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockClubsStore_CreateClub_Call) Return(club *models.Club, err error) *MockClubsStore_CreateClub_Call {
	_c.Call.Return(club, err)
	return _c
}

func (_c *MockClubsStore_CreateClub_Call) RunAndReturn(run func(ctx context.Context, input models.CreateClubInput) (*models.Club, error)) *MockClubsStore_CreateClub_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteClub provides a mock function for the type MockClubsStore
func (_mock *MockClubsStore) DeleteClub(ctx context.Context, id primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteClub")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, id)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubsStore_DeleteClub_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteClub'
type MockClubsStore_DeleteClub_Call struct {
	*mock.Call
}

// DeleteClub is a helper method to define mock.On call
//   - ctx context.Context
//   - id primitive.ObjectID
func (_e *MockClubsStore_Expecter) DeleteClub(ctx interface{}, id interface{}) *MockClubsStore_DeleteClub_Call {
	return &MockClubsStore_DeleteClub_Call{Call: _e.mock.On("DeleteClub", ctx, id)}
}

func (_c *MockClubsStore_DeleteClub_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockClubsStore_DeleteClub_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockClubsStore_DeleteClub_Call) Return(b bool, err error) *MockClubsStore_DeleteClub_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockClubsStore_DeleteClub_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (bool, error)) *MockClubsStore_DeleteClub_Call {
	_c.Call.Return(run)
	return _c
}

// GetClubLeaderboard provides a mock function for the type MockClubsStore
func (_mock *MockClubsStore) GetClubLeaderboard(ctx context.Context, clubID primitive.ObjectID, page *int, pageSize *int) (*models.ClubLeaderboard, error) {
	ret := _mock.Called(ctx, clubID, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetClubLeaderboard")
	}

	var r0 *models.ClubLeaderboard
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *int, *int) (*models.ClubLeaderboard, error)); ok {
		return returnFunc(ctx, clubID, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *int, *int) *models.ClubLeaderboard); ok {
		r0 = returnFunc(ctx, clubID, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ClubLeaderboard)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, *int, *int) error); ok {
		r1 = returnFunc(ctx, clubID, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubsStore_GetClubLeaderboard_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetClubLeaderboard'
type MockClubsStore_GetClubLeaderboard_Call struct {
	*mock.Call
}

// GetClubLeaderboard is a helper method to define mock.On call
//   - ctx context.Context
//   - clubID primitive.ObjectID
//   - page *int
//   - pageSize *int
func (_e *MockClubsStore_Expecter) GetClubLeaderboard(ctx interface{}, clubID interface{}, page interface{}, pageSize interface{}) *MockClubsStore_GetClubLeaderboard_Call {
	return &MockClubsStore_GetClubLeaderboard_Call{Call: _e.mock.On("GetClubLeaderboard", ctx, clubID, page, pageSize)}
}

func (_c *MockClubsStore_GetClubLeaderboard_Call) Run(run func(ctx context.Context, clubID primitive.ObjectID, page *int, pageSize *int)) *MockClubsStore_GetClubLeaderboard_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		var arg3 *int
		if args[3] != nil {
			arg3 = args[3].(*int)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockClubsStore_GetClubLeaderboard_Call) Return(clubLeaderboard *models.ClubLeaderboard, err error) *MockClubsStore_GetClubLeaderboard_Call {
	_c.Call.Return(clubLeaderboard, err)
	return _c
}

func (_c *MockClubsStore_GetClubLeaderboard_Call) RunAndReturn(run func(ctx context.Context, clubID primitive.ObjectID, page *int, pageSize *int) (*models.ClubLeaderboard, error)) *MockClubsStore_GetClubLeaderboard_Call {
	_c.Call.Return(run)
	return _c
}

// GetClubMemberInfo provides a mock function for the type MockClubsStore
func (_mock *MockClubsStore) GetClubMemberInfo(ctx context.Context, clubID primitive.ObjectID) (*models.ClubMember, error) {
	ret := _mock.Called(ctx, clubID)

	if len(ret) == 0 {
		panic("no return value specified for GetClubMemberInfo")
	}

	var r0 *models.ClubMember
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.ClubMember, error)); ok {
		return returnFunc(ctx, clubID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.ClubMember); ok {
		r0 = returnFunc(ctx, clubID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ClubMember)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, clubID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubsStore_GetClubMemberInfo_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetClubMemberInfo'
type MockClubsStore_GetClubMemberInfo_Call struct {
	*mock.Call
}

// GetClubMemberInfo is a helper method to define mock.On call
//   - ctx context.Context
//   - clubID primitive.ObjectID
func (_e *MockClubsStore_Expecter) GetClubMemberInfo(ctx interface{}, clubID interface{}) *MockClubsStore_GetClubMemberInfo_Call {
	return &MockClubsStore_GetClubMemberInfo_Call{Call: _e.mock.On("GetClubMemberInfo", ctx, clubID)}
}

func (_c *MockClubsStore_GetClubMemberInfo_Call) Run(run func(ctx context.Context, clubID primitive.ObjectID)) *MockClubsStore_GetClubMemberInfo_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockClubsStore_GetClubMemberInfo_Call) Return(clubMember *models.ClubMember, err error) *MockClubsStore_GetClubMemberInfo_Call {
	_c.Call.Return(clubMember, err)
	return _c
}

func (_c *MockClubsStore_GetClubMemberInfo_Call) RunAndReturn(run func(ctx context.Context, clubID primitive.ObjectID) (*models.ClubMember, error)) *MockClubsStore_GetClubMemberInfo_Call {
	_c.Call.Return(run)
	return _c
}

// JoinClub provides a mock function for the type MockClubsStore
func (_mock *MockClubsStore) JoinClub(ctx context.Context, clubID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, clubID)

	if len(ret) == 0 {
		panic("no return value specified for JoinClub")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, clubID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, clubID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, clubID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubsStore_JoinClub_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'JoinClub'
type MockClubsStore_JoinClub_Call struct {
	*mock.Call
}

// JoinClub is a helper method to define mock.On call
//   - ctx context.Context
//   - clubID primitive.ObjectID
func (_e *MockClubsStore_Expecter) JoinClub(ctx interface{}, clubID interface{}) *MockClubsStore_JoinClub_Call {
	return &MockClubsStore_JoinClub_Call{Call: _e.mock.On("JoinClub", ctx, clubID)}
}

func (_c *MockClubsStore_JoinClub_Call) Run(run func(ctx context.Context, clubID primitive.ObjectID)) *MockClubsStore_JoinClub_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockClubsStore_JoinClub_Call) Return(b bool, err error) *MockClubsStore_JoinClub_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockClubsStore_JoinClub_Call) RunAndReturn(run func(ctx context.Context, clubID primitive.ObjectID) (bool, error)) *MockClubsStore_JoinClub_Call {
	_c.Call.Return(run)
	return _c
}

// LeaveClub provides a mock function for the type MockClubsStore
func (_mock *MockClubsStore) LeaveClub(ctx context.Context, clubID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, clubID)

	if len(ret) == 0 {
		panic("no return value specified for LeaveClub")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, clubID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, clubID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, clubID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubsStore_LeaveClub_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LeaveClub'
type MockClubsStore_LeaveClub_Call struct {
	*mock.Call
}

// LeaveClub is a helper method to define mock.On call
//   - ctx context.Context
//   - clubID primitive.ObjectID
func (_e *MockClubsStore_Expecter) LeaveClub(ctx interface{}, clubID interface{}) *MockClubsStore_LeaveClub_Call {
	return &MockClubsStore_LeaveClub_Call{Call: _e.mock.On("LeaveClub", ctx, clubID)}
}

func (_c *MockClubsStore_LeaveClub_Call) Run(run func(ctx context.Context, clubID primitive.ObjectID)) *MockClubsStore_LeaveClub_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockClubsStore_LeaveClub_Call) Return(b bool, err error) *MockClubsStore_LeaveClub_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockClubsStore_LeaveClub_Call) RunAndReturn(run func(ctx context.Context, clubID primitive.ObjectID) (bool, error)) *MockClubsStore_LeaveClub_Call {
	_c.Call.Return(run)
	return _c
}

// Members provides a mock function for the type MockClubsStore
func (_mock *MockClubsStore) Members(ctx context.Context, clubID primitive.ObjectID, membershipStatus models.ClubMembershipStatus, page *int, pageSize *int) (*models.ClubMembersPage, error) {
	ret := _mock.Called(ctx, clubID, membershipStatus, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for Members")
	}

	var r0 *models.ClubMembersPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, models.ClubMembershipStatus, *int, *int) (*models.ClubMembersPage, error)); ok {
		return returnFunc(ctx, clubID, membershipStatus, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, models.ClubMembershipStatus, *int, *int) *models.ClubMembersPage); ok {
		r0 = returnFunc(ctx, clubID, membershipStatus, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ClubMembersPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, models.ClubMembershipStatus, *int, *int) error); ok {
		r1 = returnFunc(ctx, clubID, membershipStatus, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubsStore_Members_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Members'
type MockClubsStore_Members_Call struct {
	*mock.Call
}

// Members is a helper method to define mock.On call
//   - ctx context.Context
//   - clubID primitive.ObjectID
//   - membershipStatus models.ClubMembershipStatus
//   - page *int
//   - pageSize *int
func (_e *MockClubsStore_Expecter) Members(ctx interface{}, clubID interface{}, membershipStatus interface{}, page interface{}, pageSize interface{}) *MockClubsStore_Members_Call {
	return &MockClubsStore_Members_Call{Call: _e.mock.On("Members", ctx, clubID, membershipStatus, page, pageSize)}
}

func (_c *MockClubsStore_Members_Call) Run(run func(ctx context.Context, clubID primitive.ObjectID, membershipStatus models.ClubMembershipStatus, page *int, pageSize *int)) *MockClubsStore_Members_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 models.ClubMembershipStatus
		if args[2] != nil {
			arg2 = args[2].(models.ClubMembershipStatus)
		}
		var arg3 *int
		if args[3] != nil {
			arg3 = args[3].(*int)
		}
		var arg4 *int
		if args[4] != nil {
			arg4 = args[4].(*int)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
			arg4,
		)
	})
	return _c
}

func (_c *MockClubsStore_Members_Call) Return(clubMembersPage *models.ClubMembersPage, err error) *MockClubsStore_Members_Call {
	_c.Call.Return(clubMembersPage, err)
	return _c
}

func (_c *MockClubsStore_Members_Call) RunAndReturn(run func(ctx context.Context, clubID primitive.ObjectID, membershipStatus models.ClubMembershipStatus, page *int, pageSize *int) (*models.ClubMembersPage, error)) *MockClubsStore_Members_Call {
	_c.Call.Return(run)
	return _c
}

// RemoveClubMember provides a mock function for the type MockClubsStore
func (_mock *MockClubsStore) RemoveClubMember(ctx context.Context, clubID primitive.ObjectID, userID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, clubID, userID)

	if len(ret) == 0 {
		panic("no return value specified for RemoveClubMember")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, clubID, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, clubID, userID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, clubID, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubsStore_RemoveClubMember_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RemoveClubMember'
type MockClubsStore_RemoveClubMember_Call struct {
	*mock.Call
}

// RemoveClubMember is a helper method to define mock.On call
//   - ctx context.Context
//   - clubID primitive.ObjectID
//   - userID primitive.ObjectID
func (_e *MockClubsStore_Expecter) RemoveClubMember(ctx interface{}, clubID interface{}, userID interface{}) *MockClubsStore_RemoveClubMember_Call {
	return &MockClubsStore_RemoveClubMember_Call{Call: _e.mock.On("RemoveClubMember", ctx, clubID, userID)}
}

func (_c *MockClubsStore_RemoveClubMember_Call) Run(run func(ctx context.Context, clubID primitive.ObjectID, userID primitive.ObjectID)) *MockClubsStore_RemoveClubMember_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockClubsStore_RemoveClubMember_Call) Return(b bool, err error) *MockClubsStore_RemoveClubMember_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockClubsStore_RemoveClubMember_Call) RunAndReturn(run func(ctx context.Context, clubID primitive.ObjectID, userID primitive.ObjectID) (bool, error)) *MockClubsStore_RemoveClubMember_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateClub provides a mock function for the type MockClubsStore
func (_mock *MockClubsStore) UpdateClub(ctx context.Context, input models.UpdateClubInput) (bool, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for UpdateClub")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.UpdateClubInput) (bool, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.UpdateClubInput) bool); ok {
		r0 = returnFunc(ctx, input)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.UpdateClubInput) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubsStore_UpdateClub_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateClub'
type MockClubsStore_UpdateClub_Call struct {
	*mock.Call
}

// UpdateClub is a helper method to define mock.On call
//   - ctx context.Context
//   - input models.UpdateClubInput
func (_e *MockClubsStore_Expecter) UpdateClub(ctx interface{}, input interface{}) *MockClubsStore_UpdateClub_Call {
	return &MockClubsStore_UpdateClub_Call{Call: _e.mock.On("UpdateClub", ctx, input)}
}

func (_c *MockClubsStore_UpdateClub_Call) Run(run func(ctx context.Context, input models.UpdateClubInput)) *MockClubsStore_UpdateClub_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 models.UpdateClubInput
		if args[1] != nil {
			arg1 = args[1].(models.UpdateClubInput)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockClubsStore_UpdateClub_Call) Return(b bool, err error) *MockClubsStore_UpdateClub_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockClubsStore_UpdateClub_Call) RunAndReturn(run func(ctx context.Context, input models.UpdateClubInput) (bool, error)) *MockClubsStore_UpdateClub_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateMemberRole provides a mock function for the type MockClubsStore
func (_mock *MockClubsStore) UpdateMemberRole(ctx context.Context, clubID primitive.ObjectID, userID primitive.ObjectID, role models.ClubMemberRole) (*models.ClubMember, error) {
	ret := _mock.Called(ctx, clubID, userID, role)

	if len(ret) == 0 {
		panic("no return value specified for UpdateMemberRole")
	}

	var r0 *models.ClubMember
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID, models.ClubMemberRole) (*models.ClubMember, error)); ok {
		return returnFunc(ctx, clubID, userID, role)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID, models.ClubMemberRole) *models.ClubMember); ok {
		r0 = returnFunc(ctx, clubID, userID, role)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ClubMember)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, primitive.ObjectID, models.ClubMemberRole) error); ok {
		r1 = returnFunc(ctx, clubID, userID, role)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubsStore_UpdateMemberRole_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateMemberRole'
type MockClubsStore_UpdateMemberRole_Call struct {
	*mock.Call
}

// UpdateMemberRole is a helper method to define mock.On call
//   - ctx context.Context
//   - clubID primitive.ObjectID
//   - userID primitive.ObjectID
//   - role models.ClubMemberRole
func (_e *MockClubsStore_Expecter) UpdateMemberRole(ctx interface{}, clubID interface{}, userID interface{}, role interface{}) *MockClubsStore_UpdateMemberRole_Call {
	return &MockClubsStore_UpdateMemberRole_Call{Call: _e.mock.On("UpdateMemberRole", ctx, clubID, userID, role)}
}

func (_c *MockClubsStore_UpdateMemberRole_Call) Run(run func(ctx context.Context, clubID primitive.ObjectID, userID primitive.ObjectID, role models.ClubMemberRole)) *MockClubsStore_UpdateMemberRole_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(primitive.ObjectID)
		}
		var arg3 models.ClubMemberRole
		if args[3] != nil {
			arg3 = args[3].(models.ClubMemberRole)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockClubsStore_UpdateMemberRole_Call) Return(clubMember *models.ClubMember, err error) *MockClubsStore_UpdateMemberRole_Call {
	_c.Call.Return(clubMember, err)
	return _c
}

func (_c *MockClubsStore_UpdateMemberRole_Call) RunAndReturn(run func(ctx context.Context, clubID primitive.ObjectID, userID primitive.ObjectID, role models.ClubMemberRole) (*models.ClubMember, error)) *MockClubsStore_UpdateMemberRole_Call {
	_c.Call.Return(run)
	return _c
}

// UploadClubBannerImage provides a mock function for the type MockClubsStore
func (_mock *MockClubsStore) UploadClubBannerImage(ctx context.Context, file graphql.Upload, clubID primitive.ObjectID) (*models.File, error) {
	ret := _mock.Called(ctx, file, clubID)

	if len(ret) == 0 {
		panic("no return value specified for UploadClubBannerImage")
	}

	var r0 *models.File
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, graphql.Upload, primitive.ObjectID) (*models.File, error)); ok {
		return returnFunc(ctx, file, clubID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, graphql.Upload, primitive.ObjectID) *models.File); ok {
		r0 = returnFunc(ctx, file, clubID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.File)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, graphql.Upload, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, file, clubID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubsStore_UploadClubBannerImage_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UploadClubBannerImage'
type MockClubsStore_UploadClubBannerImage_Call struct {
	*mock.Call
}

// UploadClubBannerImage is a helper method to define mock.On call
//   - ctx context.Context
//   - file graphql.Upload
//   - clubID primitive.ObjectID
func (_e *MockClubsStore_Expecter) UploadClubBannerImage(ctx interface{}, file interface{}, clubID interface{}) *MockClubsStore_UploadClubBannerImage_Call {
	return &MockClubsStore_UploadClubBannerImage_Call{Call: _e.mock.On("UploadClubBannerImage", ctx, file, clubID)}
}

func (_c *MockClubsStore_UploadClubBannerImage_Call) Run(run func(ctx context.Context, file graphql.Upload, clubID primitive.ObjectID)) *MockClubsStore_UploadClubBannerImage_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 graphql.Upload
		if args[1] != nil {
			arg1 = args[1].(graphql.Upload)
		}
		var arg2 primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockClubsStore_UploadClubBannerImage_Call) Return(file1 *models.File, err error) *MockClubsStore_UploadClubBannerImage_Call {
	_c.Call.Return(file1, err)
	return _c
}

func (_c *MockClubsStore_UploadClubBannerImage_Call) RunAndReturn(run func(ctx context.Context, file graphql.Upload, clubID primitive.ObjectID) (*models.File, error)) *MockClubsStore_UploadClubBannerImage_Call {
	_c.Call.Return(run)
	return _c
}

// UploadClubLogoImage provides a mock function for the type MockClubsStore
func (_mock *MockClubsStore) UploadClubLogoImage(ctx context.Context, file graphql.Upload, clubID primitive.ObjectID) (*models.File, error) {
	ret := _mock.Called(ctx, file, clubID)

	if len(ret) == 0 {
		panic("no return value specified for UploadClubLogoImage")
	}

	var r0 *models.File
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, graphql.Upload, primitive.ObjectID) (*models.File, error)); ok {
		return returnFunc(ctx, file, clubID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, graphql.Upload, primitive.ObjectID) *models.File); ok {
		r0 = returnFunc(ctx, file, clubID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.File)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, graphql.Upload, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, file, clubID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubsStore_UploadClubLogoImage_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UploadClubLogoImage'
type MockClubsStore_UploadClubLogoImage_Call struct {
	*mock.Call
}

// UploadClubLogoImage is a helper method to define mock.On call
//   - ctx context.Context
//   - file graphql.Upload
//   - clubID primitive.ObjectID
func (_e *MockClubsStore_Expecter) UploadClubLogoImage(ctx interface{}, file interface{}, clubID interface{}) *MockClubsStore_UploadClubLogoImage_Call {
	return &MockClubsStore_UploadClubLogoImage_Call{Call: _e.mock.On("UploadClubLogoImage", ctx, file, clubID)}
}

func (_c *MockClubsStore_UploadClubLogoImage_Call) Run(run func(ctx context.Context, file graphql.Upload, clubID primitive.ObjectID)) *MockClubsStore_UploadClubLogoImage_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 graphql.Upload
		if args[1] != nil {
			arg1 = args[1].(graphql.Upload)
		}
		var arg2 primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockClubsStore_UploadClubLogoImage_Call) Return(file1 *models.File, err error) *MockClubsStore_UploadClubLogoImage_Call {
	_c.Call.Return(file1, err)
	return _c
}

func (_c *MockClubsStore_UploadClubLogoImage_Call) RunAndReturn(run func(ctx context.Context, file graphql.Upload, clubID primitive.ObjectID) (*models.File, error)) *MockClubsStore_UploadClubLogoImage_Call {
	_c.Call.Return(run)
	return _c
}
