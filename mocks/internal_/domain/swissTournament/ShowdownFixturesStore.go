// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package swissTournament

import (
	"context"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockShowdownFixturesStore creates a new instance of MockShowdownFixturesStore. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockShowdownFixturesStore(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockShowdownFixturesStore {
	mock := &MockShowdownFixturesStore{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockShowdownFixturesStore is an autogenerated mock type for the ShowdownFixturesStore type
type MockShowdownFixturesStore struct {
	mock.Mock
}

type MockShowdownFixturesStore_Expecter struct {
	mock *mock.Mock
}

func (_m *MockShowdownFixturesStore) EXPECT() *MockShowdownFixturesStore_Expecter {
	return &MockShowdownFixturesStore_Expecter{mock: &_m.Mock}
}

// CreateFixture provides a mock function for the type MockShowdownFixturesStore
func (_mock *MockShowdownFixturesStore) CreateFixture(ctx context.Context, showdownId primitive.ObjectID, round int) error {
	ret := _mock.Called(ctx, showdownId, round)

	if len(ret) == 0 {
		panic("no return value specified for CreateFixture")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int) error); ok {
		r0 = returnFunc(ctx, showdownId, round)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockShowdownFixturesStore_CreateFixture_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateFixture'
type MockShowdownFixturesStore_CreateFixture_Call struct {
	*mock.Call
}

// CreateFixture is a helper method to define mock.On call
//   - ctx context.Context
//   - showdownId primitive.ObjectID
//   - round int
func (_e *MockShowdownFixturesStore_Expecter) CreateFixture(ctx interface{}, showdownId interface{}, round interface{}) *MockShowdownFixturesStore_CreateFixture_Call {
	return &MockShowdownFixturesStore_CreateFixture_Call{Call: _e.mock.On("CreateFixture", ctx, showdownId, round)}
}

func (_c *MockShowdownFixturesStore_CreateFixture_Call) Run(run func(ctx context.Context, showdownId primitive.ObjectID, round int)) *MockShowdownFixturesStore_CreateFixture_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 int
		if args[2] != nil {
			arg2 = args[2].(int)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockShowdownFixturesStore_CreateFixture_Call) Return(err error) *MockShowdownFixturesStore_CreateFixture_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockShowdownFixturesStore_CreateFixture_Call) RunAndReturn(run func(ctx context.Context, showdownId primitive.ObjectID, round int) error) *MockShowdownFixturesStore_CreateFixture_Call {
	_c.Call.Return(run)
	return _c
}
