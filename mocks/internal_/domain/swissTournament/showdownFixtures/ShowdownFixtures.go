// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package showdownfixtures

import (
	"context"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockShowdownFixtures creates a new instance of MockShowdownFixtures. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockShowdownFixtures(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockShowdownFixtures {
	mock := &MockShowdownFixtures{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockShowdownFixtures is an autogenerated mock type for the ShowdownFixtures type
type MockShowdownFixtures struct {
	mock.Mock
}

type MockShowdownFixtures_Expecter struct {
	mock *mock.Mock
}

func (_m *MockShowdownFixtures) EXPECT() *MockShowdownFixtures_Expecter {
	return &MockShowdownFixtures_Expecter{mock: &_m.Mock}
}

// CreateFixture provides a mock function for the type MockShowdownFixtures
func (_mock *MockShowdownFixtures) CreateFixture(ctx context.Context, showdownId primitive.ObjectID, round int) error {
	ret := _mock.Called(ctx, showdownId, round)

	if len(ret) == 0 {
		panic("no return value specified for CreateFixture")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int) error); ok {
		r0 = returnFunc(ctx, showdownId, round)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockShowdownFixtures_CreateFixture_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateFixture'
type MockShowdownFixtures_CreateFixture_Call struct {
	*mock.Call
}

// CreateFixture is a helper method to define mock.On call
//   - ctx context.Context
//   - showdownId primitive.ObjectID
//   - round int
func (_e *MockShowdownFixtures_Expecter) CreateFixture(ctx interface{}, showdownId interface{}, round interface{}) *MockShowdownFixtures_CreateFixture_Call {
	return &MockShowdownFixtures_CreateFixture_Call{Call: _e.mock.On("CreateFixture", ctx, showdownId, round)}
}

func (_c *MockShowdownFixtures_CreateFixture_Call) Run(run func(ctx context.Context, showdownId primitive.ObjectID, round int)) *MockShowdownFixtures_CreateFixture_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 int
		if args[2] != nil {
			arg2 = args[2].(int)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockShowdownFixtures_CreateFixture_Call) Return(err error) *MockShowdownFixtures_CreateFixture_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockShowdownFixtures_CreateFixture_Call) RunAndReturn(run func(ctx context.Context, showdownId primitive.ObjectID, round int) error) *MockShowdownFixtures_CreateFixture_Call {
	_c.Call.Return(run)
	return _c
}
