// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package domain

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"
	"time"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockClubEventStore creates a new instance of MockClubEventStore. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockClubEventStore(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockClubEventStore {
	mock := &MockClubEventStore{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockClubEventStore is an autogenerated mock type for the ClubEventStore type
type MockClubEventStore struct {
	mock.Mock
}

type MockClubEventStore_Expecter struct {
	mock *mock.Mock
}

func (_m *MockClubEventStore) EXPECT() *MockClubEventStore_Expecter {
	return &MockClubEventStore_Expecter{mock: &_m.Mock}
}

// ClubEvent provides a mock function for the type MockClubEventStore
func (_mock *MockClubEventStore) ClubEvent(ctx context.Context, id primitive.ObjectID) (*models.ClubEvent, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for ClubEvent")
	}

	var r0 *models.ClubEvent
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.ClubEvent, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.ClubEvent); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ClubEvent)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubEventStore_ClubEvent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ClubEvent'
type MockClubEventStore_ClubEvent_Call struct {
	*mock.Call
}

// ClubEvent is a helper method to define mock.On call
//   - ctx context.Context
//   - id primitive.ObjectID
func (_e *MockClubEventStore_Expecter) ClubEvent(ctx interface{}, id interface{}) *MockClubEventStore_ClubEvent_Call {
	return &MockClubEventStore_ClubEvent_Call{Call: _e.mock.On("ClubEvent", ctx, id)}
}

func (_c *MockClubEventStore_ClubEvent_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockClubEventStore_ClubEvent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockClubEventStore_ClubEvent_Call) Return(clubEvent *models.ClubEvent, err error) *MockClubEventStore_ClubEvent_Call {
	_c.Call.Return(clubEvent, err)
	return _c
}

func (_c *MockClubEventStore_ClubEvent_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (*models.ClubEvent, error)) *MockClubEventStore_ClubEvent_Call {
	_c.Call.Return(run)
	return _c
}

// ClubEvents provides a mock function for the type MockClubEventStore
func (_mock *MockClubEventStore) ClubEvents(ctx context.Context, page *int, pageSize *int, clubID *primitive.ObjectID, eventType *models.ClubEventType, from *time.Time, to *time.Time) (*models.ClubEventsPage, error) {
	ret := _mock.Called(ctx, page, pageSize, clubID, eventType, from, to)

	if len(ret) == 0 {
		panic("no return value specified for ClubEvents")
	}

	var r0 *models.ClubEventsPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int, *primitive.ObjectID, *models.ClubEventType, *time.Time, *time.Time) (*models.ClubEventsPage, error)); ok {
		return returnFunc(ctx, page, pageSize, clubID, eventType, from, to)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int, *primitive.ObjectID, *models.ClubEventType, *time.Time, *time.Time) *models.ClubEventsPage); ok {
		r0 = returnFunc(ctx, page, pageSize, clubID, eventType, from, to)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ClubEventsPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *int, *int, *primitive.ObjectID, *models.ClubEventType, *time.Time, *time.Time) error); ok {
		r1 = returnFunc(ctx, page, pageSize, clubID, eventType, from, to)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubEventStore_ClubEvents_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ClubEvents'
type MockClubEventStore_ClubEvents_Call struct {
	*mock.Call
}

// ClubEvents is a helper method to define mock.On call
//   - ctx context.Context
//   - page *int
//   - pageSize *int
//   - clubID *primitive.ObjectID
//   - eventType *models.ClubEventType
//   - from *time.Time
//   - to *time.Time
func (_e *MockClubEventStore_Expecter) ClubEvents(ctx interface{}, page interface{}, pageSize interface{}, clubID interface{}, eventType interface{}, from interface{}, to interface{}) *MockClubEventStore_ClubEvents_Call {
	return &MockClubEventStore_ClubEvents_Call{Call: _e.mock.On("ClubEvents", ctx, page, pageSize, clubID, eventType, from, to)}
}

func (_c *MockClubEventStore_ClubEvents_Call) Run(run func(ctx context.Context, page *int, pageSize *int, clubID *primitive.ObjectID, eventType *models.ClubEventType, from *time.Time, to *time.Time)) *MockClubEventStore_ClubEvents_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *int
		if args[1] != nil {
			arg1 = args[1].(*int)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		var arg3 *primitive.ObjectID
		if args[3] != nil {
			arg3 = args[3].(*primitive.ObjectID)
		}
		var arg4 *models.ClubEventType
		if args[4] != nil {
			arg4 = args[4].(*models.ClubEventType)
		}
		var arg5 *time.Time
		if args[5] != nil {
			arg5 = args[5].(*time.Time)
		}
		var arg6 *time.Time
		if args[6] != nil {
			arg6 = args[6].(*time.Time)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
			arg4,
			arg5,
			arg6,
		)
	})
	return _c
}

func (_c *MockClubEventStore_ClubEvents_Call) Return(clubEventsPage *models.ClubEventsPage, err error) *MockClubEventStore_ClubEvents_Call {
	_c.Call.Return(clubEventsPage, err)
	return _c
}

func (_c *MockClubEventStore_ClubEvents_Call) RunAndReturn(run func(ctx context.Context, page *int, pageSize *int, clubID *primitive.ObjectID, eventType *models.ClubEventType, from *time.Time, to *time.Time) (*models.ClubEventsPage, error)) *MockClubEventStore_ClubEvents_Call {
	_c.Call.Return(run)
	return _c
}

// CreateClubEvent provides a mock function for the type MockClubEventStore
func (_mock *MockClubEventStore) CreateClubEvent(ctx context.Context, input models.CreateClubEventInput) (*models.ClubEvent, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CreateClubEvent")
	}

	var r0 *models.ClubEvent
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateClubEventInput) (*models.ClubEvent, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateClubEventInput) *models.ClubEvent); ok {
		r0 = returnFunc(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ClubEvent)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.CreateClubEventInput) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubEventStore_CreateClubEvent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateClubEvent'
type MockClubEventStore_CreateClubEvent_Call struct {
	*mock.Call
}

// CreateClubEvent is a helper method to define mock.On call
//   - ctx context.Context
//   - input models.CreateClubEventInput
func (_e *MockClubEventStore_Expecter) CreateClubEvent(ctx interface{}, input interface{}) *MockClubEventStore_CreateClubEvent_Call {
	return &MockClubEventStore_CreateClubEvent_Call{Call: _e.mock.On("CreateClubEvent", ctx, input)}
}

func (_c *MockClubEventStore_CreateClubEvent_Call) Run(run func(ctx context.Context, input models.CreateClubEventInput)) *MockClubEventStore_CreateClubEvent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 models.CreateClubEventInput
		if args[1] != nil {
			arg1 = args[1].(models.CreateClubEventInput)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockClubEventStore_CreateClubEvent_Call) Return(clubEvent *models.ClubEvent, err error) *MockClubEventStore_CreateClubEvent_Call {
	_c.Call.Return(clubEvent, err)
	return _c
}

func (_c *MockClubEventStore_CreateClubEvent_Call) RunAndReturn(run func(ctx context.Context, input models.CreateClubEventInput) (*models.ClubEvent, error)) *MockClubEventStore_CreateClubEvent_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteClubEvent provides a mock function for the type MockClubEventStore
func (_mock *MockClubEventStore) DeleteClubEvent(ctx context.Context, id primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteClubEvent")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, id)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubEventStore_DeleteClubEvent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteClubEvent'
type MockClubEventStore_DeleteClubEvent_Call struct {
	*mock.Call
}

// DeleteClubEvent is a helper method to define mock.On call
//   - ctx context.Context
//   - id primitive.ObjectID
func (_e *MockClubEventStore_Expecter) DeleteClubEvent(ctx interface{}, id interface{}) *MockClubEventStore_DeleteClubEvent_Call {
	return &MockClubEventStore_DeleteClubEvent_Call{Call: _e.mock.On("DeleteClubEvent", ctx, id)}
}

func (_c *MockClubEventStore_DeleteClubEvent_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockClubEventStore_DeleteClubEvent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockClubEventStore_DeleteClubEvent_Call) Return(b bool, err error) *MockClubEventStore_DeleteClubEvent_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockClubEventStore_DeleteClubEvent_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (bool, error)) *MockClubEventStore_DeleteClubEvent_Call {
	_c.Call.Return(run)
	return _c
}

// JoinClubEvent provides a mock function for the type MockClubEventStore
func (_mock *MockClubEventStore) JoinClubEvent(ctx context.Context, eventID primitive.ObjectID) (*models.ClubEventParticipant, error) {
	ret := _mock.Called(ctx, eventID)

	if len(ret) == 0 {
		panic("no return value specified for JoinClubEvent")
	}

	var r0 *models.ClubEventParticipant
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.ClubEventParticipant, error)); ok {
		return returnFunc(ctx, eventID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.ClubEventParticipant); ok {
		r0 = returnFunc(ctx, eventID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ClubEventParticipant)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, eventID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubEventStore_JoinClubEvent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'JoinClubEvent'
type MockClubEventStore_JoinClubEvent_Call struct {
	*mock.Call
}

// JoinClubEvent is a helper method to define mock.On call
//   - ctx context.Context
//   - eventID primitive.ObjectID
func (_e *MockClubEventStore_Expecter) JoinClubEvent(ctx interface{}, eventID interface{}) *MockClubEventStore_JoinClubEvent_Call {
	return &MockClubEventStore_JoinClubEvent_Call{Call: _e.mock.On("JoinClubEvent", ctx, eventID)}
}

func (_c *MockClubEventStore_JoinClubEvent_Call) Run(run func(ctx context.Context, eventID primitive.ObjectID)) *MockClubEventStore_JoinClubEvent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockClubEventStore_JoinClubEvent_Call) Return(clubEventParticipant *models.ClubEventParticipant, err error) *MockClubEventStore_JoinClubEvent_Call {
	_c.Call.Return(clubEventParticipant, err)
	return _c
}

func (_c *MockClubEventStore_JoinClubEvent_Call) RunAndReturn(run func(ctx context.Context, eventID primitive.ObjectID) (*models.ClubEventParticipant, error)) *MockClubEventStore_JoinClubEvent_Call {
	_c.Call.Return(run)
	return _c
}

// LeaveClubEvent provides a mock function for the type MockClubEventStore
func (_mock *MockClubEventStore) LeaveClubEvent(ctx context.Context, eventID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, eventID)

	if len(ret) == 0 {
		panic("no return value specified for LeaveClubEvent")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, eventID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, eventID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, eventID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubEventStore_LeaveClubEvent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LeaveClubEvent'
type MockClubEventStore_LeaveClubEvent_Call struct {
	*mock.Call
}

// LeaveClubEvent is a helper method to define mock.On call
//   - ctx context.Context
//   - eventID primitive.ObjectID
func (_e *MockClubEventStore_Expecter) LeaveClubEvent(ctx interface{}, eventID interface{}) *MockClubEventStore_LeaveClubEvent_Call {
	return &MockClubEventStore_LeaveClubEvent_Call{Call: _e.mock.On("LeaveClubEvent", ctx, eventID)}
}

func (_c *MockClubEventStore_LeaveClubEvent_Call) Run(run func(ctx context.Context, eventID primitive.ObjectID)) *MockClubEventStore_LeaveClubEvent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockClubEventStore_LeaveClubEvent_Call) Return(b bool, err error) *MockClubEventStore_LeaveClubEvent_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockClubEventStore_LeaveClubEvent_Call) RunAndReturn(run func(ctx context.Context, eventID primitive.ObjectID) (bool, error)) *MockClubEventStore_LeaveClubEvent_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateClubEvent provides a mock function for the type MockClubEventStore
func (_mock *MockClubEventStore) UpdateClubEvent(ctx context.Context, input models.UpdateClubEventInput) (*models.ClubEvent, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for UpdateClubEvent")
	}

	var r0 *models.ClubEvent
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.UpdateClubEventInput) (*models.ClubEvent, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.UpdateClubEventInput) *models.ClubEvent); ok {
		r0 = returnFunc(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ClubEvent)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.UpdateClubEventInput) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubEventStore_UpdateClubEvent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateClubEvent'
type MockClubEventStore_UpdateClubEvent_Call struct {
	*mock.Call
}

// UpdateClubEvent is a helper method to define mock.On call
//   - ctx context.Context
//   - input models.UpdateClubEventInput
func (_e *MockClubEventStore_Expecter) UpdateClubEvent(ctx interface{}, input interface{}) *MockClubEventStore_UpdateClubEvent_Call {
	return &MockClubEventStore_UpdateClubEvent_Call{Call: _e.mock.On("UpdateClubEvent", ctx, input)}
}

func (_c *MockClubEventStore_UpdateClubEvent_Call) Run(run func(ctx context.Context, input models.UpdateClubEventInput)) *MockClubEventStore_UpdateClubEvent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 models.UpdateClubEventInput
		if args[1] != nil {
			arg1 = args[1].(models.UpdateClubEventInput)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockClubEventStore_UpdateClubEvent_Call) Return(clubEvent *models.ClubEvent, err error) *MockClubEventStore_UpdateClubEvent_Call {
	_c.Call.Return(clubEvent, err)
	return _c
}

func (_c *MockClubEventStore_UpdateClubEvent_Call) RunAndReturn(run func(ctx context.Context, input models.UpdateClubEventInput) (*models.ClubEvent, error)) *MockClubEventStore_UpdateClubEvent_Call {
	_c.Call.Return(run)
	return _c
}
