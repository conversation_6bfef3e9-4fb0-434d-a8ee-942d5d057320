// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package domain

import (
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewMockWeeklyLeagueStore creates a new instance of MockWeeklyLeagueStore. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockWeeklyLeagueStore(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockWeeklyLeagueStore {
	mock := &MockWeeklyLeagueStore{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockWeeklyLeagueStore is an autogenerated mock type for the WeeklyLeagueStore type
type MockWeeklyLeagueStore struct {
	mock.Mock
}

type MockWeeklyLeagueStore_Expecter struct {
	mock *mock.Mock
}

func (_m *MockWeeklyLeagueStore) EXPECT() *MockWeeklyLeagueStore_Expecter {
	return &MockWeeklyLeagueStore_Expecter{mock: &_m.<PERSON>ck}
}

// ProcessWeeklyLeagueAssignments provides a mock function for the type MockWeeklyLeagueStore
func (_mock *MockWeeklyLeagueStore) ProcessWeeklyLeagueAssignments(ctx context.Context) error {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for ProcessWeeklyLeagueAssignments")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = returnFunc(ctx)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockWeeklyLeagueStore_ProcessWeeklyLeagueAssignments_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ProcessWeeklyLeagueAssignments'
type MockWeeklyLeagueStore_ProcessWeeklyLeagueAssignments_Call struct {
	*mock.Call
}

// ProcessWeeklyLeagueAssignments is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockWeeklyLeagueStore_Expecter) ProcessWeeklyLeagueAssignments(ctx interface{}) *MockWeeklyLeagueStore_ProcessWeeklyLeagueAssignments_Call {
	return &MockWeeklyLeagueStore_ProcessWeeklyLeagueAssignments_Call{Call: _e.mock.On("ProcessWeeklyLeagueAssignments", ctx)}
}

func (_c *MockWeeklyLeagueStore_ProcessWeeklyLeagueAssignments_Call) Run(run func(ctx context.Context)) *MockWeeklyLeagueStore_ProcessWeeklyLeagueAssignments_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockWeeklyLeagueStore_ProcessWeeklyLeagueAssignments_Call) Return(err error) *MockWeeklyLeagueStore_ProcessWeeklyLeagueAssignments_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockWeeklyLeagueStore_ProcessWeeklyLeagueAssignments_Call) RunAndReturn(run func(ctx context.Context) error) *MockWeeklyLeagueStore_ProcessWeeklyLeagueAssignments_Call {
	_c.Call.Return(run)
	return _c
}

// RunWeeklyLeagueProcess provides a mock function for the type MockWeeklyLeagueStore
func (_mock *MockWeeklyLeagueStore) RunWeeklyLeagueProcess(ctx context.Context) error {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for RunWeeklyLeagueProcess")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = returnFunc(ctx)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockWeeklyLeagueStore_RunWeeklyLeagueProcess_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RunWeeklyLeagueProcess'
type MockWeeklyLeagueStore_RunWeeklyLeagueProcess_Call struct {
	*mock.Call
}

// RunWeeklyLeagueProcess is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockWeeklyLeagueStore_Expecter) RunWeeklyLeagueProcess(ctx interface{}) *MockWeeklyLeagueStore_RunWeeklyLeagueProcess_Call {
	return &MockWeeklyLeagueStore_RunWeeklyLeagueProcess_Call{Call: _e.mock.On("RunWeeklyLeagueProcess", ctx)}
}

func (_c *MockWeeklyLeagueStore_RunWeeklyLeagueProcess_Call) Run(run func(ctx context.Context)) *MockWeeklyLeagueStore_RunWeeklyLeagueProcess_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockWeeklyLeagueStore_RunWeeklyLeagueProcess_Call) Return(err error) *MockWeeklyLeagueStore_RunWeeklyLeagueProcess_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockWeeklyLeagueStore_RunWeeklyLeagueProcess_Call) RunAndReturn(run func(ctx context.Context) error) *MockWeeklyLeagueStore_RunWeeklyLeagueProcess_Call {
	_c.Call.Return(run)
	return _c
}
