// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package domain

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockAnnouncementStore creates a new instance of MockAnnouncementStore. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockAnnouncementStore(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockAnnouncementStore {
	mock := &MockAnnouncementStore{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockAnnouncementStore is an autogenerated mock type for the AnnouncementStore type
type MockAnnouncementStore struct {
	mock.Mock
}

type MockAnnouncementStore_Expecter struct {
	mock *mock.Mock
}

func (_m *MockAnnouncementStore) EXPECT() *MockAnnouncementStore_Expecter {
	return &MockAnnouncementStore_Expecter{mock: &_m.Mock}
}

// CreateAnnouncement provides a mock function for the type MockAnnouncementStore
func (_mock *MockAnnouncementStore) CreateAnnouncement(ctx context.Context, input models.CreateAnnouncementInput) (*models.Announcement, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CreateAnnouncement")
	}

	var r0 *models.Announcement
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateAnnouncementInput) (*models.Announcement, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateAnnouncementInput) *models.Announcement); ok {
		r0 = returnFunc(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Announcement)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.CreateAnnouncementInput) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockAnnouncementStore_CreateAnnouncement_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateAnnouncement'
type MockAnnouncementStore_CreateAnnouncement_Call struct {
	*mock.Call
}

// CreateAnnouncement is a helper method to define mock.On call
//   - ctx context.Context
//   - input models.CreateAnnouncementInput
func (_e *MockAnnouncementStore_Expecter) CreateAnnouncement(ctx interface{}, input interface{}) *MockAnnouncementStore_CreateAnnouncement_Call {
	return &MockAnnouncementStore_CreateAnnouncement_Call{Call: _e.mock.On("CreateAnnouncement", ctx, input)}
}

func (_c *MockAnnouncementStore_CreateAnnouncement_Call) Run(run func(ctx context.Context, input models.CreateAnnouncementInput)) *MockAnnouncementStore_CreateAnnouncement_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 models.CreateAnnouncementInput
		if args[1] != nil {
			arg1 = args[1].(models.CreateAnnouncementInput)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockAnnouncementStore_CreateAnnouncement_Call) Return(announcement *models.Announcement, err error) *MockAnnouncementStore_CreateAnnouncement_Call {
	_c.Call.Return(announcement, err)
	return _c
}

func (_c *MockAnnouncementStore_CreateAnnouncement_Call) RunAndReturn(run func(ctx context.Context, input models.CreateAnnouncementInput) (*models.Announcement, error)) *MockAnnouncementStore_CreateAnnouncement_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteAnnouncement provides a mock function for the type MockAnnouncementStore
func (_mock *MockAnnouncementStore) DeleteAnnouncement(ctx context.Context, id primitive.ObjectID) (*models.AnnouncementMutationResponse, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteAnnouncement")
	}

	var r0 *models.AnnouncementMutationResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.AnnouncementMutationResponse, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.AnnouncementMutationResponse); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.AnnouncementMutationResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockAnnouncementStore_DeleteAnnouncement_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteAnnouncement'
type MockAnnouncementStore_DeleteAnnouncement_Call struct {
	*mock.Call
}

// DeleteAnnouncement is a helper method to define mock.On call
//   - ctx context.Context
//   - id primitive.ObjectID
func (_e *MockAnnouncementStore_Expecter) DeleteAnnouncement(ctx interface{}, id interface{}) *MockAnnouncementStore_DeleteAnnouncement_Call {
	return &MockAnnouncementStore_DeleteAnnouncement_Call{Call: _e.mock.On("DeleteAnnouncement", ctx, id)}
}

func (_c *MockAnnouncementStore_DeleteAnnouncement_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockAnnouncementStore_DeleteAnnouncement_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockAnnouncementStore_DeleteAnnouncement_Call) Return(announcementMutationResponse *models.AnnouncementMutationResponse, err error) *MockAnnouncementStore_DeleteAnnouncement_Call {
	_c.Call.Return(announcementMutationResponse, err)
	return _c
}

func (_c *MockAnnouncementStore_DeleteAnnouncement_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (*models.AnnouncementMutationResponse, error)) *MockAnnouncementStore_DeleteAnnouncement_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllAnnouncements provides a mock function for the type MockAnnouncementStore
func (_mock *MockAnnouncementStore) GetAllAnnouncements(ctx context.Context, limit *int, offset *int, announcementType *models.AnnouncementType) ([]*models.Announcement, error) {
	ret := _mock.Called(ctx, limit, offset, announcementType)

	if len(ret) == 0 {
		panic("no return value specified for GetAllAnnouncements")
	}

	var r0 []*models.Announcement
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int, *models.AnnouncementType) ([]*models.Announcement, error)); ok {
		return returnFunc(ctx, limit, offset, announcementType)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int, *models.AnnouncementType) []*models.Announcement); ok {
		r0 = returnFunc(ctx, limit, offset, announcementType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.Announcement)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *int, *int, *models.AnnouncementType) error); ok {
		r1 = returnFunc(ctx, limit, offset, announcementType)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockAnnouncementStore_GetAllAnnouncements_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllAnnouncements'
type MockAnnouncementStore_GetAllAnnouncements_Call struct {
	*mock.Call
}

// GetAllAnnouncements is a helper method to define mock.On call
//   - ctx context.Context
//   - limit *int
//   - offset *int
//   - announcementType *models.AnnouncementType
func (_e *MockAnnouncementStore_Expecter) GetAllAnnouncements(ctx interface{}, limit interface{}, offset interface{}, announcementType interface{}) *MockAnnouncementStore_GetAllAnnouncements_Call {
	return &MockAnnouncementStore_GetAllAnnouncements_Call{Call: _e.mock.On("GetAllAnnouncements", ctx, limit, offset, announcementType)}
}

func (_c *MockAnnouncementStore_GetAllAnnouncements_Call) Run(run func(ctx context.Context, limit *int, offset *int, announcementType *models.AnnouncementType)) *MockAnnouncementStore_GetAllAnnouncements_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *int
		if args[1] != nil {
			arg1 = args[1].(*int)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		var arg3 *models.AnnouncementType
		if args[3] != nil {
			arg3 = args[3].(*models.AnnouncementType)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockAnnouncementStore_GetAllAnnouncements_Call) Return(announcements []*models.Announcement, err error) *MockAnnouncementStore_GetAllAnnouncements_Call {
	_c.Call.Return(announcements, err)
	return _c
}

func (_c *MockAnnouncementStore_GetAllAnnouncements_Call) RunAndReturn(run func(ctx context.Context, limit *int, offset *int, announcementType *models.AnnouncementType) ([]*models.Announcement, error)) *MockAnnouncementStore_GetAllAnnouncements_Call {
	_c.Call.Return(run)
	return _c
}

// GetAnnouncementByID provides a mock function for the type MockAnnouncementStore
func (_mock *MockAnnouncementStore) GetAnnouncementByID(ctx context.Context, id primitive.ObjectID) (*models.Announcement, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetAnnouncementByID")
	}

	var r0 *models.Announcement
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.Announcement, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.Announcement); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Announcement)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockAnnouncementStore_GetAnnouncementByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAnnouncementByID'
type MockAnnouncementStore_GetAnnouncementByID_Call struct {
	*mock.Call
}

// GetAnnouncementByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id primitive.ObjectID
func (_e *MockAnnouncementStore_Expecter) GetAnnouncementByID(ctx interface{}, id interface{}) *MockAnnouncementStore_GetAnnouncementByID_Call {
	return &MockAnnouncementStore_GetAnnouncementByID_Call{Call: _e.mock.On("GetAnnouncementByID", ctx, id)}
}

func (_c *MockAnnouncementStore_GetAnnouncementByID_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockAnnouncementStore_GetAnnouncementByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockAnnouncementStore_GetAnnouncementByID_Call) Return(announcement *models.Announcement, err error) *MockAnnouncementStore_GetAnnouncementByID_Call {
	_c.Call.Return(announcement, err)
	return _c
}

func (_c *MockAnnouncementStore_GetAnnouncementByID_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (*models.Announcement, error)) *MockAnnouncementStore_GetAnnouncementByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetUnreadAnnouncements provides a mock function for the type MockAnnouncementStore
func (_mock *MockAnnouncementStore) GetUnreadAnnouncements(ctx context.Context, limit *int, offset *int) ([]*models.Announcement, error) {
	ret := _mock.Called(ctx, limit, offset)

	if len(ret) == 0 {
		panic("no return value specified for GetUnreadAnnouncements")
	}

	var r0 []*models.Announcement
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int) ([]*models.Announcement, error)); ok {
		return returnFunc(ctx, limit, offset)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int) []*models.Announcement); ok {
		r0 = returnFunc(ctx, limit, offset)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.Announcement)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *int, *int) error); ok {
		r1 = returnFunc(ctx, limit, offset)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockAnnouncementStore_GetUnreadAnnouncements_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUnreadAnnouncements'
type MockAnnouncementStore_GetUnreadAnnouncements_Call struct {
	*mock.Call
}

// GetUnreadAnnouncements is a helper method to define mock.On call
//   - ctx context.Context
//   - limit *int
//   - offset *int
func (_e *MockAnnouncementStore_Expecter) GetUnreadAnnouncements(ctx interface{}, limit interface{}, offset interface{}) *MockAnnouncementStore_GetUnreadAnnouncements_Call {
	return &MockAnnouncementStore_GetUnreadAnnouncements_Call{Call: _e.mock.On("GetUnreadAnnouncements", ctx, limit, offset)}
}

func (_c *MockAnnouncementStore_GetUnreadAnnouncements_Call) Run(run func(ctx context.Context, limit *int, offset *int)) *MockAnnouncementStore_GetUnreadAnnouncements_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *int
		if args[1] != nil {
			arg1 = args[1].(*int)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockAnnouncementStore_GetUnreadAnnouncements_Call) Return(announcements []*models.Announcement, err error) *MockAnnouncementStore_GetUnreadAnnouncements_Call {
	_c.Call.Return(announcements, err)
	return _c
}

func (_c *MockAnnouncementStore_GetUnreadAnnouncements_Call) RunAndReturn(run func(ctx context.Context, limit *int, offset *int) ([]*models.Announcement, error)) *MockAnnouncementStore_GetUnreadAnnouncements_Call {
	_c.Call.Return(run)
	return _c
}

// MarkAllAnnouncementsAsRead provides a mock function for the type MockAnnouncementStore
func (_mock *MockAnnouncementStore) MarkAllAnnouncementsAsRead(ctx context.Context) (*models.AnnouncementMutationResponse, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for MarkAllAnnouncementsAsRead")
	}

	var r0 *models.AnnouncementMutationResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*models.AnnouncementMutationResponse, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *models.AnnouncementMutationResponse); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.AnnouncementMutationResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockAnnouncementStore_MarkAllAnnouncementsAsRead_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'MarkAllAnnouncementsAsRead'
type MockAnnouncementStore_MarkAllAnnouncementsAsRead_Call struct {
	*mock.Call
}

// MarkAllAnnouncementsAsRead is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockAnnouncementStore_Expecter) MarkAllAnnouncementsAsRead(ctx interface{}) *MockAnnouncementStore_MarkAllAnnouncementsAsRead_Call {
	return &MockAnnouncementStore_MarkAllAnnouncementsAsRead_Call{Call: _e.mock.On("MarkAllAnnouncementsAsRead", ctx)}
}

func (_c *MockAnnouncementStore_MarkAllAnnouncementsAsRead_Call) Run(run func(ctx context.Context)) *MockAnnouncementStore_MarkAllAnnouncementsAsRead_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockAnnouncementStore_MarkAllAnnouncementsAsRead_Call) Return(announcementMutationResponse *models.AnnouncementMutationResponse, err error) *MockAnnouncementStore_MarkAllAnnouncementsAsRead_Call {
	_c.Call.Return(announcementMutationResponse, err)
	return _c
}

func (_c *MockAnnouncementStore_MarkAllAnnouncementsAsRead_Call) RunAndReturn(run func(ctx context.Context) (*models.AnnouncementMutationResponse, error)) *MockAnnouncementStore_MarkAllAnnouncementsAsRead_Call {
	_c.Call.Return(run)
	return _c
}

// MarkAnnouncementAsRead provides a mock function for the type MockAnnouncementStore
func (_mock *MockAnnouncementStore) MarkAnnouncementAsRead(ctx context.Context, announcementID primitive.ObjectID) (*models.AnnouncementMutationResponse, error) {
	ret := _mock.Called(ctx, announcementID)

	if len(ret) == 0 {
		panic("no return value specified for MarkAnnouncementAsRead")
	}

	var r0 *models.AnnouncementMutationResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.AnnouncementMutationResponse, error)); ok {
		return returnFunc(ctx, announcementID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.AnnouncementMutationResponse); ok {
		r0 = returnFunc(ctx, announcementID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.AnnouncementMutationResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, announcementID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockAnnouncementStore_MarkAnnouncementAsRead_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'MarkAnnouncementAsRead'
type MockAnnouncementStore_MarkAnnouncementAsRead_Call struct {
	*mock.Call
}

// MarkAnnouncementAsRead is a helper method to define mock.On call
//   - ctx context.Context
//   - announcementID primitive.ObjectID
func (_e *MockAnnouncementStore_Expecter) MarkAnnouncementAsRead(ctx interface{}, announcementID interface{}) *MockAnnouncementStore_MarkAnnouncementAsRead_Call {
	return &MockAnnouncementStore_MarkAnnouncementAsRead_Call{Call: _e.mock.On("MarkAnnouncementAsRead", ctx, announcementID)}
}

func (_c *MockAnnouncementStore_MarkAnnouncementAsRead_Call) Run(run func(ctx context.Context, announcementID primitive.ObjectID)) *MockAnnouncementStore_MarkAnnouncementAsRead_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockAnnouncementStore_MarkAnnouncementAsRead_Call) Return(announcementMutationResponse *models.AnnouncementMutationResponse, err error) *MockAnnouncementStore_MarkAnnouncementAsRead_Call {
	_c.Call.Return(announcementMutationResponse, err)
	return _c
}

func (_c *MockAnnouncementStore_MarkAnnouncementAsRead_Call) RunAndReturn(run func(ctx context.Context, announcementID primitive.ObjectID) (*models.AnnouncementMutationResponse, error)) *MockAnnouncementStore_MarkAnnouncementAsRead_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateAnnouncement provides a mock function for the type MockAnnouncementStore
func (_mock *MockAnnouncementStore) UpdateAnnouncement(ctx context.Context, id primitive.ObjectID, input models.UpdateAnnouncementInput) (*models.Announcement, error) {
	ret := _mock.Called(ctx, id, input)

	if len(ret) == 0 {
		panic("no return value specified for UpdateAnnouncement")
	}

	var r0 *models.Announcement
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, models.UpdateAnnouncementInput) (*models.Announcement, error)); ok {
		return returnFunc(ctx, id, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, models.UpdateAnnouncementInput) *models.Announcement); ok {
		r0 = returnFunc(ctx, id, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Announcement)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, models.UpdateAnnouncementInput) error); ok {
		r1 = returnFunc(ctx, id, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockAnnouncementStore_UpdateAnnouncement_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateAnnouncement'
type MockAnnouncementStore_UpdateAnnouncement_Call struct {
	*mock.Call
}

// UpdateAnnouncement is a helper method to define mock.On call
//   - ctx context.Context
//   - id primitive.ObjectID
//   - input models.UpdateAnnouncementInput
func (_e *MockAnnouncementStore_Expecter) UpdateAnnouncement(ctx interface{}, id interface{}, input interface{}) *MockAnnouncementStore_UpdateAnnouncement_Call {
	return &MockAnnouncementStore_UpdateAnnouncement_Call{Call: _e.mock.On("UpdateAnnouncement", ctx, id, input)}
}

func (_c *MockAnnouncementStore_UpdateAnnouncement_Call) Run(run func(ctx context.Context, id primitive.ObjectID, input models.UpdateAnnouncementInput)) *MockAnnouncementStore_UpdateAnnouncement_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 models.UpdateAnnouncementInput
		if args[2] != nil {
			arg2 = args[2].(models.UpdateAnnouncementInput)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockAnnouncementStore_UpdateAnnouncement_Call) Return(announcement *models.Announcement, err error) *MockAnnouncementStore_UpdateAnnouncement_Call {
	_c.Call.Return(announcement, err)
	return _c
}

func (_c *MockAnnouncementStore_UpdateAnnouncement_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID, input models.UpdateAnnouncementInput) (*models.Announcement, error)) *MockAnnouncementStore_UpdateAnnouncement_Call {
	_c.Call.Return(run)
	return _c
}
