// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package domain

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"
	"time"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockBotDetectionStore creates a new instance of MockBotDetectionStore. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockBotDetectionStore(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockBotDetectionStore {
	mock := &MockBotDetectionStore{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockBotDetectionStore is an autogenerated mock type for the BotDetectionStore type
type MockBotDetectionStore struct {
	mock.Mock
}

type MockBotDetectionStore_Expecter struct {
	mock *mock.Mock
}

func (_m *MockBotDetectionStore) EXPECT() *MockBotDetectionStore_Expecter {
	return &MockBotDetectionStore_Expecter{mock: &_m.Mock}
}

// DetectBotBehavior provides a mock function for the type MockBotDetectionStore
func (_mock *MockBotDetectionStore) DetectBotBehavior(ctx context.Context, userID primitive.ObjectID, gameID *primitive.ObjectID, submissionTimes []int) (*models.BotDetection, error) {
	ret := _mock.Called(ctx, userID, gameID, submissionTimes)

	if len(ret) == 0 {
		panic("no return value specified for DetectBotBehavior")
	}

	var r0 *models.BotDetection
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *primitive.ObjectID, []int) (*models.BotDetection, error)); ok {
		return returnFunc(ctx, userID, gameID, submissionTimes)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *primitive.ObjectID, []int) *models.BotDetection); ok {
		r0 = returnFunc(ctx, userID, gameID, submissionTimes)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.BotDetection)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, *primitive.ObjectID, []int) error); ok {
		r1 = returnFunc(ctx, userID, gameID, submissionTimes)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockBotDetectionStore_DetectBotBehavior_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DetectBotBehavior'
type MockBotDetectionStore_DetectBotBehavior_Call struct {
	*mock.Call
}

// DetectBotBehavior is a helper method to define mock.On call
//   - ctx context.Context
//   - userID primitive.ObjectID
//   - gameID *primitive.ObjectID
//   - submissionTimes []int
func (_e *MockBotDetectionStore_Expecter) DetectBotBehavior(ctx interface{}, userID interface{}, gameID interface{}, submissionTimes interface{}) *MockBotDetectionStore_DetectBotBehavior_Call {
	return &MockBotDetectionStore_DetectBotBehavior_Call{Call: _e.mock.On("DetectBotBehavior", ctx, userID, gameID, submissionTimes)}
}

func (_c *MockBotDetectionStore_DetectBotBehavior_Call) Run(run func(ctx context.Context, userID primitive.ObjectID, gameID *primitive.ObjectID, submissionTimes []int)) *MockBotDetectionStore_DetectBotBehavior_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 *primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(*primitive.ObjectID)
		}
		var arg3 []int
		if args[3] != nil {
			arg3 = args[3].([]int)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockBotDetectionStore_DetectBotBehavior_Call) Return(botDetection *models.BotDetection, err error) *MockBotDetectionStore_DetectBotBehavior_Call {
	_c.Call.Return(botDetection, err)
	return _c
}

func (_c *MockBotDetectionStore_DetectBotBehavior_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID, gameID *primitive.ObjectID, submissionTimes []int) (*models.BotDetection, error)) *MockBotDetectionStore_DetectBotBehavior_Call {
	_c.Call.Return(run)
	return _c
}

// GetRecentUserBotDetections provides a mock function for the type MockBotDetectionStore
func (_mock *MockBotDetectionStore) GetRecentUserBotDetections(ctx context.Context, userID primitive.ObjectID, since time.Time) ([]*models.BotDetection, error) {
	ret := _mock.Called(ctx, userID, since)

	if len(ret) == 0 {
		panic("no return value specified for GetRecentUserBotDetections")
	}

	var r0 []*models.BotDetection
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, time.Time) ([]*models.BotDetection, error)); ok {
		return returnFunc(ctx, userID, since)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, time.Time) []*models.BotDetection); ok {
		r0 = returnFunc(ctx, userID, since)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.BotDetection)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, time.Time) error); ok {
		r1 = returnFunc(ctx, userID, since)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockBotDetectionStore_GetRecentUserBotDetections_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRecentUserBotDetections'
type MockBotDetectionStore_GetRecentUserBotDetections_Call struct {
	*mock.Call
}

// GetRecentUserBotDetections is a helper method to define mock.On call
//   - ctx context.Context
//   - userID primitive.ObjectID
//   - since time.Time
func (_e *MockBotDetectionStore_Expecter) GetRecentUserBotDetections(ctx interface{}, userID interface{}, since interface{}) *MockBotDetectionStore_GetRecentUserBotDetections_Call {
	return &MockBotDetectionStore_GetRecentUserBotDetections_Call{Call: _e.mock.On("GetRecentUserBotDetections", ctx, userID, since)}
}

func (_c *MockBotDetectionStore_GetRecentUserBotDetections_Call) Run(run func(ctx context.Context, userID primitive.ObjectID, since time.Time)) *MockBotDetectionStore_GetRecentUserBotDetections_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 time.Time
		if args[2] != nil {
			arg2 = args[2].(time.Time)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockBotDetectionStore_GetRecentUserBotDetections_Call) Return(botDetections []*models.BotDetection, err error) *MockBotDetectionStore_GetRecentUserBotDetections_Call {
	_c.Call.Return(botDetections, err)
	return _c
}

func (_c *MockBotDetectionStore_GetRecentUserBotDetections_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID, since time.Time) ([]*models.BotDetection, error)) *MockBotDetectionStore_GetRecentUserBotDetections_Call {
	_c.Call.Return(run)
	return _c
}

// GetShadowBannedUsers provides a mock function for the type MockBotDetectionStore
func (_mock *MockBotDetectionStore) GetShadowBannedUsers(ctx context.Context) ([]primitive.ObjectID, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetShadowBannedUsers")
	}

	var r0 []primitive.ObjectID
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) ([]primitive.ObjectID, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) []primitive.ObjectID); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]primitive.ObjectID)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockBotDetectionStore_GetShadowBannedUsers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetShadowBannedUsers'
type MockBotDetectionStore_GetShadowBannedUsers_Call struct {
	*mock.Call
}

// GetShadowBannedUsers is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockBotDetectionStore_Expecter) GetShadowBannedUsers(ctx interface{}) *MockBotDetectionStore_GetShadowBannedUsers_Call {
	return &MockBotDetectionStore_GetShadowBannedUsers_Call{Call: _e.mock.On("GetShadowBannedUsers", ctx)}
}

func (_c *MockBotDetectionStore_GetShadowBannedUsers_Call) Run(run func(ctx context.Context)) *MockBotDetectionStore_GetShadowBannedUsers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockBotDetectionStore_GetShadowBannedUsers_Call) Return(objectIDs []primitive.ObjectID, err error) *MockBotDetectionStore_GetShadowBannedUsers_Call {
	_c.Call.Return(objectIDs, err)
	return _c
}

func (_c *MockBotDetectionStore_GetShadowBannedUsers_Call) RunAndReturn(run func(ctx context.Context) ([]primitive.ObjectID, error)) *MockBotDetectionStore_GetShadowBannedUsers_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserBotDetections provides a mock function for the type MockBotDetectionStore
func (_mock *MockBotDetectionStore) GetUserBotDetections(ctx context.Context, userID primitive.ObjectID) ([]*models.BotDetection, error) {
	ret := _mock.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetUserBotDetections")
	}

	var r0 []*models.BotDetection
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) ([]*models.BotDetection, error)); ok {
		return returnFunc(ctx, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) []*models.BotDetection); ok {
		r0 = returnFunc(ctx, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.BotDetection)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockBotDetectionStore_GetUserBotDetections_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserBotDetections'
type MockBotDetectionStore_GetUserBotDetections_Call struct {
	*mock.Call
}

// GetUserBotDetections is a helper method to define mock.On call
//   - ctx context.Context
//   - userID primitive.ObjectID
func (_e *MockBotDetectionStore_Expecter) GetUserBotDetections(ctx interface{}, userID interface{}) *MockBotDetectionStore_GetUserBotDetections_Call {
	return &MockBotDetectionStore_GetUserBotDetections_Call{Call: _e.mock.On("GetUserBotDetections", ctx, userID)}
}

func (_c *MockBotDetectionStore_GetUserBotDetections_Call) Run(run func(ctx context.Context, userID primitive.ObjectID)) *MockBotDetectionStore_GetUserBotDetections_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockBotDetectionStore_GetUserBotDetections_Call) Return(botDetections []*models.BotDetection, err error) *MockBotDetectionStore_GetUserBotDetections_Call {
	_c.Call.Return(botDetections, err)
	return _c
}

func (_c *MockBotDetectionStore_GetUserBotDetections_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID) ([]*models.BotDetection, error)) *MockBotDetectionStore_GetUserBotDetections_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserShadowBanStatus provides a mock function for the type MockBotDetectionStore
func (_mock *MockBotDetectionStore) GetUserShadowBanStatus(ctx context.Context, userID primitive.ObjectID) (*models.UserShadowBan, error) {
	ret := _mock.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetUserShadowBanStatus")
	}

	var r0 *models.UserShadowBan
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.UserShadowBan, error)); ok {
		return returnFunc(ctx, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.UserShadowBan); ok {
		r0 = returnFunc(ctx, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserShadowBan)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockBotDetectionStore_GetUserShadowBanStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserShadowBanStatus'
type MockBotDetectionStore_GetUserShadowBanStatus_Call struct {
	*mock.Call
}

// GetUserShadowBanStatus is a helper method to define mock.On call
//   - ctx context.Context
//   - userID primitive.ObjectID
func (_e *MockBotDetectionStore_Expecter) GetUserShadowBanStatus(ctx interface{}, userID interface{}) *MockBotDetectionStore_GetUserShadowBanStatus_Call {
	return &MockBotDetectionStore_GetUserShadowBanStatus_Call{Call: _e.mock.On("GetUserShadowBanStatus", ctx, userID)}
}

func (_c *MockBotDetectionStore_GetUserShadowBanStatus_Call) Run(run func(ctx context.Context, userID primitive.ObjectID)) *MockBotDetectionStore_GetUserShadowBanStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockBotDetectionStore_GetUserShadowBanStatus_Call) Return(userShadowBan *models.UserShadowBan, err error) *MockBotDetectionStore_GetUserShadowBanStatus_Call {
	_c.Call.Return(userShadowBan, err)
	return _c
}

func (_c *MockBotDetectionStore_GetUserShadowBanStatus_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID) (*models.UserShadowBan, error)) *MockBotDetectionStore_GetUserShadowBanStatus_Call {
	_c.Call.Return(run)
	return _c
}

// HandleEndGameBotDetection provides a mock function for the type MockBotDetectionStore
func (_mock *MockBotDetectionStore) HandleEndGameBotDetection(ctx context.Context, game *models.Game, winnerID primitive.ObjectID, loserID primitive.ObjectID) map[primitive.ObjectID]bool {
	ret := _mock.Called(ctx, game, winnerID, loserID)

	if len(ret) == 0 {
		panic("no return value specified for HandleEndGameBotDetection")
	}

	var r0 map[primitive.ObjectID]bool
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.Game, primitive.ObjectID, primitive.ObjectID) map[primitive.ObjectID]bool); ok {
		r0 = returnFunc(ctx, game, winnerID, loserID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[primitive.ObjectID]bool)
		}
	}
	return r0
}

// MockBotDetectionStore_HandleEndGameBotDetection_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'HandleEndGameBotDetection'
type MockBotDetectionStore_HandleEndGameBotDetection_Call struct {
	*mock.Call
}

// HandleEndGameBotDetection is a helper method to define mock.On call
//   - ctx context.Context
//   - game *models.Game
//   - winnerID primitive.ObjectID
//   - loserID primitive.ObjectID
func (_e *MockBotDetectionStore_Expecter) HandleEndGameBotDetection(ctx interface{}, game interface{}, winnerID interface{}, loserID interface{}) *MockBotDetectionStore_HandleEndGameBotDetection_Call {
	return &MockBotDetectionStore_HandleEndGameBotDetection_Call{Call: _e.mock.On("HandleEndGameBotDetection", ctx, game, winnerID, loserID)}
}

func (_c *MockBotDetectionStore_HandleEndGameBotDetection_Call) Run(run func(ctx context.Context, game *models.Game, winnerID primitive.ObjectID, loserID primitive.ObjectID)) *MockBotDetectionStore_HandleEndGameBotDetection_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.Game
		if args[1] != nil {
			arg1 = args[1].(*models.Game)
		}
		var arg2 primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(primitive.ObjectID)
		}
		var arg3 primitive.ObjectID
		if args[3] != nil {
			arg3 = args[3].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockBotDetectionStore_HandleEndGameBotDetection_Call) Return(objectIDToBool map[primitive.ObjectID]bool) *MockBotDetectionStore_HandleEndGameBotDetection_Call {
	_c.Call.Return(objectIDToBool)
	return _c
}

func (_c *MockBotDetectionStore_HandleEndGameBotDetection_Call) RunAndReturn(run func(ctx context.Context, game *models.Game, winnerID primitive.ObjectID, loserID primitive.ObjectID) map[primitive.ObjectID]bool) *MockBotDetectionStore_HandleEndGameBotDetection_Call {
	_c.Call.Return(run)
	return _c
}

// HandleSubmissionBotDetection provides a mock function for the type MockBotDetectionStore
func (_mock *MockBotDetectionStore) HandleSubmissionBotDetection(ctx context.Context, userID primitive.ObjectID, gameID *primitive.ObjectID, submissionTimes []int) (bool, *models.BotDetection, error) {
	ret := _mock.Called(ctx, userID, gameID, submissionTimes)

	if len(ret) == 0 {
		panic("no return value specified for HandleSubmissionBotDetection")
	}

	var r0 bool
	var r1 *models.BotDetection
	var r2 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *primitive.ObjectID, []int) (bool, *models.BotDetection, error)); ok {
		return returnFunc(ctx, userID, gameID, submissionTimes)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *primitive.ObjectID, []int) bool); ok {
		r0 = returnFunc(ctx, userID, gameID, submissionTimes)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, *primitive.ObjectID, []int) *models.BotDetection); ok {
		r1 = returnFunc(ctx, userID, gameID, submissionTimes)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).(*models.BotDetection)
		}
	}
	if returnFunc, ok := ret.Get(2).(func(context.Context, primitive.ObjectID, *primitive.ObjectID, []int) error); ok {
		r2 = returnFunc(ctx, userID, gameID, submissionTimes)
	} else {
		r2 = ret.Error(2)
	}
	return r0, r1, r2
}

// MockBotDetectionStore_HandleSubmissionBotDetection_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'HandleSubmissionBotDetection'
type MockBotDetectionStore_HandleSubmissionBotDetection_Call struct {
	*mock.Call
}

// HandleSubmissionBotDetection is a helper method to define mock.On call
//   - ctx context.Context
//   - userID primitive.ObjectID
//   - gameID *primitive.ObjectID
//   - submissionTimes []int
func (_e *MockBotDetectionStore_Expecter) HandleSubmissionBotDetection(ctx interface{}, userID interface{}, gameID interface{}, submissionTimes interface{}) *MockBotDetectionStore_HandleSubmissionBotDetection_Call {
	return &MockBotDetectionStore_HandleSubmissionBotDetection_Call{Call: _e.mock.On("HandleSubmissionBotDetection", ctx, userID, gameID, submissionTimes)}
}

func (_c *MockBotDetectionStore_HandleSubmissionBotDetection_Call) Run(run func(ctx context.Context, userID primitive.ObjectID, gameID *primitive.ObjectID, submissionTimes []int)) *MockBotDetectionStore_HandleSubmissionBotDetection_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 *primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(*primitive.ObjectID)
		}
		var arg3 []int
		if args[3] != nil {
			arg3 = args[3].([]int)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockBotDetectionStore_HandleSubmissionBotDetection_Call) Return(b bool, botDetection *models.BotDetection, err error) *MockBotDetectionStore_HandleSubmissionBotDetection_Call {
	_c.Call.Return(b, botDetection, err)
	return _c
}

func (_c *MockBotDetectionStore_HandleSubmissionBotDetection_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID, gameID *primitive.ObjectID, submissionTimes []int) (bool, *models.BotDetection, error)) *MockBotDetectionStore_HandleSubmissionBotDetection_Call {
	_c.Call.Return(run)
	return _c
}

// IsUserShadowBanned provides a mock function for the type MockBotDetectionStore
func (_mock *MockBotDetectionStore) IsUserShadowBanned(ctx context.Context, userID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for IsUserShadowBanned")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, userID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockBotDetectionStore_IsUserShadowBanned_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsUserShadowBanned'
type MockBotDetectionStore_IsUserShadowBanned_Call struct {
	*mock.Call
}

// IsUserShadowBanned is a helper method to define mock.On call
//   - ctx context.Context
//   - userID primitive.ObjectID
func (_e *MockBotDetectionStore_Expecter) IsUserShadowBanned(ctx interface{}, userID interface{}) *MockBotDetectionStore_IsUserShadowBanned_Call {
	return &MockBotDetectionStore_IsUserShadowBanned_Call{Call: _e.mock.On("IsUserShadowBanned", ctx, userID)}
}

func (_c *MockBotDetectionStore_IsUserShadowBanned_Call) Run(run func(ctx context.Context, userID primitive.ObjectID)) *MockBotDetectionStore_IsUserShadowBanned_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockBotDetectionStore_IsUserShadowBanned_Call) Return(b bool, err error) *MockBotDetectionStore_IsUserShadowBanned_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockBotDetectionStore_IsUserShadowBanned_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID) (bool, error)) *MockBotDetectionStore_IsUserShadowBanned_Call {
	_c.Call.Return(run)
	return _c
}

// ShadowBanUser provides a mock function for the type MockBotDetectionStore
func (_mock *MockBotDetectionStore) ShadowBanUser(ctx context.Context, userID primitive.ObjectID, status models.UserShadowBanStatus, reason string, detectionIDs []primitive.ObjectID) error {
	ret := _mock.Called(ctx, userID, status, reason, detectionIDs)

	if len(ret) == 0 {
		panic("no return value specified for ShadowBanUser")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, models.UserShadowBanStatus, string, []primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, userID, status, reason, detectionIDs)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockBotDetectionStore_ShadowBanUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ShadowBanUser'
type MockBotDetectionStore_ShadowBanUser_Call struct {
	*mock.Call
}

// ShadowBanUser is a helper method to define mock.On call
//   - ctx context.Context
//   - userID primitive.ObjectID
//   - status models.UserShadowBanStatus
//   - reason string
//   - detectionIDs []primitive.ObjectID
func (_e *MockBotDetectionStore_Expecter) ShadowBanUser(ctx interface{}, userID interface{}, status interface{}, reason interface{}, detectionIDs interface{}) *MockBotDetectionStore_ShadowBanUser_Call {
	return &MockBotDetectionStore_ShadowBanUser_Call{Call: _e.mock.On("ShadowBanUser", ctx, userID, status, reason, detectionIDs)}
}

func (_c *MockBotDetectionStore_ShadowBanUser_Call) Run(run func(ctx context.Context, userID primitive.ObjectID, status models.UserShadowBanStatus, reason string, detectionIDs []primitive.ObjectID)) *MockBotDetectionStore_ShadowBanUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 models.UserShadowBanStatus
		if args[2] != nil {
			arg2 = args[2].(models.UserShadowBanStatus)
		}
		var arg3 string
		if args[3] != nil {
			arg3 = args[3].(string)
		}
		var arg4 []primitive.ObjectID
		if args[4] != nil {
			arg4 = args[4].([]primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
			arg4,
		)
	})
	return _c
}

func (_c *MockBotDetectionStore_ShadowBanUser_Call) Return(err error) *MockBotDetectionStore_ShadowBanUser_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockBotDetectionStore_ShadowBanUser_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID, status models.UserShadowBanStatus, reason string, detectionIDs []primitive.ObjectID) error) *MockBotDetectionStore_ShadowBanUser_Call {
	_c.Call.Return(run)
	return _c
}

// ShouldPublishEventToUser provides a mock function for the type MockBotDetectionStore
func (_mock *MockBotDetectionStore) ShouldPublishEventToUser(ctx context.Context, userID primitive.ObjectID, eventSourceUserID primitive.ObjectID) bool {
	ret := _mock.Called(ctx, userID, eventSourceUserID)

	if len(ret) == 0 {
		panic("no return value specified for ShouldPublishEventToUser")
	}

	var r0 bool
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, userID, eventSourceUserID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	return r0
}

// MockBotDetectionStore_ShouldPublishEventToUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ShouldPublishEventToUser'
type MockBotDetectionStore_ShouldPublishEventToUser_Call struct {
	*mock.Call
}

// ShouldPublishEventToUser is a helper method to define mock.On call
//   - ctx context.Context
//   - userID primitive.ObjectID
//   - eventSourceUserID primitive.ObjectID
func (_e *MockBotDetectionStore_Expecter) ShouldPublishEventToUser(ctx interface{}, userID interface{}, eventSourceUserID interface{}) *MockBotDetectionStore_ShouldPublishEventToUser_Call {
	return &MockBotDetectionStore_ShouldPublishEventToUser_Call{Call: _e.mock.On("ShouldPublishEventToUser", ctx, userID, eventSourceUserID)}
}

func (_c *MockBotDetectionStore_ShouldPublishEventToUser_Call) Run(run func(ctx context.Context, userID primitive.ObjectID, eventSourceUserID primitive.ObjectID)) *MockBotDetectionStore_ShouldPublishEventToUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockBotDetectionStore_ShouldPublishEventToUser_Call) Return(b bool) *MockBotDetectionStore_ShouldPublishEventToUser_Call {
	_c.Call.Return(b)
	return _c
}

func (_c *MockBotDetectionStore_ShouldPublishEventToUser_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID, eventSourceUserID primitive.ObjectID) bool) *MockBotDetectionStore_ShouldPublishEventToUser_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateUserShadowBanStatus provides a mock function for the type MockBotDetectionStore
func (_mock *MockBotDetectionStore) UpdateUserShadowBanStatus(ctx context.Context, userID primitive.ObjectID, status models.UserShadowBanStatus) error {
	ret := _mock.Called(ctx, userID, status)

	if len(ret) == 0 {
		panic("no return value specified for UpdateUserShadowBanStatus")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, models.UserShadowBanStatus) error); ok {
		r0 = returnFunc(ctx, userID, status)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockBotDetectionStore_UpdateUserShadowBanStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateUserShadowBanStatus'
type MockBotDetectionStore_UpdateUserShadowBanStatus_Call struct {
	*mock.Call
}

// UpdateUserShadowBanStatus is a helper method to define mock.On call
//   - ctx context.Context
//   - userID primitive.ObjectID
//   - status models.UserShadowBanStatus
func (_e *MockBotDetectionStore_Expecter) UpdateUserShadowBanStatus(ctx interface{}, userID interface{}, status interface{}) *MockBotDetectionStore_UpdateUserShadowBanStatus_Call {
	return &MockBotDetectionStore_UpdateUserShadowBanStatus_Call{Call: _e.mock.On("UpdateUserShadowBanStatus", ctx, userID, status)}
}

func (_c *MockBotDetectionStore_UpdateUserShadowBanStatus_Call) Run(run func(ctx context.Context, userID primitive.ObjectID, status models.UserShadowBanStatus)) *MockBotDetectionStore_UpdateUserShadowBanStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 models.UserShadowBanStatus
		if args[2] != nil {
			arg2 = args[2].(models.UserShadowBanStatus)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockBotDetectionStore_UpdateUserShadowBanStatus_Call) Return(err error) *MockBotDetectionStore_UpdateUserShadowBanStatus_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockBotDetectionStore_UpdateUserShadowBanStatus_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID, status models.UserShadowBanStatus) error) *MockBotDetectionStore_UpdateUserShadowBanStatus_Call {
	_c.Call.Return(run)
	return _c
}
