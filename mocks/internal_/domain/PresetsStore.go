// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package domain

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockPresetsStore creates a new instance of MockPresetsStore. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockPresetsStore(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockPresetsStore {
	mock := &MockPresetsStore{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockPresetsStore is an autogenerated mock type for the PresetsStore type
type MockPresetsStore struct {
	mock.Mock
}

type MockPresetsStore_Expecter struct {
	mock *mock.Mock
}

func (_m *MockPresetsStore) EXPECT() *MockPresetsStore_Expecter {
	return &MockPresetsStore_Expecter{mock: &_m.Mock}
}

// DeleteUserSavedPreset provides a mock function for the type MockPresetsStore
func (_mock *MockPresetsStore) DeleteUserSavedPreset(ctx context.Context, presetId primitive.ObjectID) (*bool, error) {
	ret := _mock.Called(ctx, presetId)

	if len(ret) == 0 {
		panic("no return value specified for DeleteUserSavedPreset")
	}

	var r0 *bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*bool, error)); ok {
		return returnFunc(ctx, presetId)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *bool); ok {
		r0 = returnFunc(ctx, presetId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bool)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, presetId)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPresetsStore_DeleteUserSavedPreset_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteUserSavedPreset'
type MockPresetsStore_DeleteUserSavedPreset_Call struct {
	*mock.Call
}

// DeleteUserSavedPreset is a helper method to define mock.On call
//   - ctx context.Context
//   - presetId primitive.ObjectID
func (_e *MockPresetsStore_Expecter) DeleteUserSavedPreset(ctx interface{}, presetId interface{}) *MockPresetsStore_DeleteUserSavedPreset_Call {
	return &MockPresetsStore_DeleteUserSavedPreset_Call{Call: _e.mock.On("DeleteUserSavedPreset", ctx, presetId)}
}

func (_c *MockPresetsStore_DeleteUserSavedPreset_Call) Run(run func(ctx context.Context, presetId primitive.ObjectID)) *MockPresetsStore_DeleteUserSavedPreset_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockPresetsStore_DeleteUserSavedPreset_Call) Return(b *bool, err error) *MockPresetsStore_DeleteUserSavedPreset_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockPresetsStore_DeleteUserSavedPreset_Call) RunAndReturn(run func(ctx context.Context, presetId primitive.ObjectID) (*bool, error)) *MockPresetsStore_DeleteUserSavedPreset_Call {
	_c.Call.Return(run)
	return _c
}

// GetGlobalPresets provides a mock function for the type MockPresetsStore
func (_mock *MockPresetsStore) GetGlobalPresets(ctx context.Context, page *int, pageSize *int) (*models.GlobalPresets, error) {
	ret := _mock.Called(ctx, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetGlobalPresets")
	}

	var r0 *models.GlobalPresets
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int) (*models.GlobalPresets, error)); ok {
		return returnFunc(ctx, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int) *models.GlobalPresets); ok {
		r0 = returnFunc(ctx, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.GlobalPresets)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *int, *int) error); ok {
		r1 = returnFunc(ctx, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPresetsStore_GetGlobalPresets_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetGlobalPresets'
type MockPresetsStore_GetGlobalPresets_Call struct {
	*mock.Call
}

// GetGlobalPresets is a helper method to define mock.On call
//   - ctx context.Context
//   - page *int
//   - pageSize *int
func (_e *MockPresetsStore_Expecter) GetGlobalPresets(ctx interface{}, page interface{}, pageSize interface{}) *MockPresetsStore_GetGlobalPresets_Call {
	return &MockPresetsStore_GetGlobalPresets_Call{Call: _e.mock.On("GetGlobalPresets", ctx, page, pageSize)}
}

func (_c *MockPresetsStore_GetGlobalPresets_Call) Run(run func(ctx context.Context, page *int, pageSize *int)) *MockPresetsStore_GetGlobalPresets_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *int
		if args[1] != nil {
			arg1 = args[1].(*int)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockPresetsStore_GetGlobalPresets_Call) Return(globalPresets *models.GlobalPresets, err error) *MockPresetsStore_GetGlobalPresets_Call {
	_c.Call.Return(globalPresets, err)
	return _c
}

func (_c *MockPresetsStore_GetGlobalPresets_Call) RunAndReturn(run func(ctx context.Context, page *int, pageSize *int) (*models.GlobalPresets, error)) *MockPresetsStore_GetGlobalPresets_Call {
	_c.Call.Return(run)
	return _c
}

// GetGlobalPresetsByIdentifier provides a mock function for the type MockPresetsStore
func (_mock *MockPresetsStore) GetGlobalPresetsByIdentifier(ctx context.Context, identifier *string) (*models.GlobalPreset, error) {
	ret := _mock.Called(ctx, identifier)

	if len(ret) == 0 {
		panic("no return value specified for GetGlobalPresetsByIdentifier")
	}

	var r0 *models.GlobalPreset
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (*models.GlobalPreset, error)); ok {
		return returnFunc(ctx, identifier)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) *models.GlobalPreset); ok {
		r0 = returnFunc(ctx, identifier)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.GlobalPreset)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, identifier)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPresetsStore_GetGlobalPresetsByIdentifier_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetGlobalPresetsByIdentifier'
type MockPresetsStore_GetGlobalPresetsByIdentifier_Call struct {
	*mock.Call
}

// GetGlobalPresetsByIdentifier is a helper method to define mock.On call
//   - ctx context.Context
//   - identifier *string
func (_e *MockPresetsStore_Expecter) GetGlobalPresetsByIdentifier(ctx interface{}, identifier interface{}) *MockPresetsStore_GetGlobalPresetsByIdentifier_Call {
	return &MockPresetsStore_GetGlobalPresetsByIdentifier_Call{Call: _e.mock.On("GetGlobalPresetsByIdentifier", ctx, identifier)}
}

func (_c *MockPresetsStore_GetGlobalPresetsByIdentifier_Call) Run(run func(ctx context.Context, identifier *string)) *MockPresetsStore_GetGlobalPresetsByIdentifier_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *string
		if args[1] != nil {
			arg1 = args[1].(*string)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockPresetsStore_GetGlobalPresetsByIdentifier_Call) Return(globalPreset *models.GlobalPreset, err error) *MockPresetsStore_GetGlobalPresetsByIdentifier_Call {
	_c.Call.Return(globalPreset, err)
	return _c
}

func (_c *MockPresetsStore_GetGlobalPresetsByIdentifier_Call) RunAndReturn(run func(ctx context.Context, identifier *string) (*models.GlobalPreset, error)) *MockPresetsStore_GetGlobalPresetsByIdentifier_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserPresetByIdentifier provides a mock function for the type MockPresetsStore
func (_mock *MockPresetsStore) GetUserPresetByIdentifier(ctx context.Context, identifier *string, userId primitive.ObjectID) (*models.UserPreset, error) {
	ret := _mock.Called(ctx, identifier, userId)

	if len(ret) == 0 {
		panic("no return value specified for GetUserPresetByIdentifier")
	}

	var r0 *models.UserPreset
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, primitive.ObjectID) (*models.UserPreset, error)); ok {
		return returnFunc(ctx, identifier, userId)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, primitive.ObjectID) *models.UserPreset); ok {
		r0 = returnFunc(ctx, identifier, userId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserPreset)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, identifier, userId)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPresetsStore_GetUserPresetByIdentifier_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserPresetByIdentifier'
type MockPresetsStore_GetUserPresetByIdentifier_Call struct {
	*mock.Call
}

// GetUserPresetByIdentifier is a helper method to define mock.On call
//   - ctx context.Context
//   - identifier *string
//   - userId primitive.ObjectID
func (_e *MockPresetsStore_Expecter) GetUserPresetByIdentifier(ctx interface{}, identifier interface{}, userId interface{}) *MockPresetsStore_GetUserPresetByIdentifier_Call {
	return &MockPresetsStore_GetUserPresetByIdentifier_Call{Call: _e.mock.On("GetUserPresetByIdentifier", ctx, identifier, userId)}
}

func (_c *MockPresetsStore_GetUserPresetByIdentifier_Call) Run(run func(ctx context.Context, identifier *string, userId primitive.ObjectID)) *MockPresetsStore_GetUserPresetByIdentifier_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *string
		if args[1] != nil {
			arg1 = args[1].(*string)
		}
		var arg2 primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockPresetsStore_GetUserPresetByIdentifier_Call) Return(userPreset *models.UserPreset, err error) *MockPresetsStore_GetUserPresetByIdentifier_Call {
	_c.Call.Return(userPreset, err)
	return _c
}

func (_c *MockPresetsStore_GetUserPresetByIdentifier_Call) RunAndReturn(run func(ctx context.Context, identifier *string, userId primitive.ObjectID) (*models.UserPreset, error)) *MockPresetsStore_GetUserPresetByIdentifier_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserPresetStatsByDate provides a mock function for the type MockPresetsStore
func (_mock *MockPresetsStore) GetUserPresetStatsByDate(ctx context.Context, username *string, durationFilter *int, identifier *string) ([]*models.UserPresetDayStats, error) {
	ret := _mock.Called(ctx, username, durationFilter, identifier)

	if len(ret) == 0 {
		panic("no return value specified for GetUserPresetStatsByDate")
	}

	var r0 []*models.UserPresetDayStats
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *int, *string) ([]*models.UserPresetDayStats, error)); ok {
		return returnFunc(ctx, username, durationFilter, identifier)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *int, *string) []*models.UserPresetDayStats); ok {
		r0 = returnFunc(ctx, username, durationFilter, identifier)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.UserPresetDayStats)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string, *int, *string) error); ok {
		r1 = returnFunc(ctx, username, durationFilter, identifier)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPresetsStore_GetUserPresetStatsByDate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserPresetStatsByDate'
type MockPresetsStore_GetUserPresetStatsByDate_Call struct {
	*mock.Call
}

// GetUserPresetStatsByDate is a helper method to define mock.On call
//   - ctx context.Context
//   - username *string
//   - durationFilter *int
//   - identifier *string
func (_e *MockPresetsStore_Expecter) GetUserPresetStatsByDate(ctx interface{}, username interface{}, durationFilter interface{}, identifier interface{}) *MockPresetsStore_GetUserPresetStatsByDate_Call {
	return &MockPresetsStore_GetUserPresetStatsByDate_Call{Call: _e.mock.On("GetUserPresetStatsByDate", ctx, username, durationFilter, identifier)}
}

func (_c *MockPresetsStore_GetUserPresetStatsByDate_Call) Run(run func(ctx context.Context, username *string, durationFilter *int, identifier *string)) *MockPresetsStore_GetUserPresetStatsByDate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *string
		if args[1] != nil {
			arg1 = args[1].(*string)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		var arg3 *string
		if args[3] != nil {
			arg3 = args[3].(*string)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockPresetsStore_GetUserPresetStatsByDate_Call) Return(userPresetDayStatss []*models.UserPresetDayStats, err error) *MockPresetsStore_GetUserPresetStatsByDate_Call {
	_c.Call.Return(userPresetDayStatss, err)
	return _c
}

func (_c *MockPresetsStore_GetUserPresetStatsByDate_Call) RunAndReturn(run func(ctx context.Context, username *string, durationFilter *int, identifier *string) ([]*models.UserPresetDayStats, error)) *MockPresetsStore_GetUserPresetStatsByDate_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserPresetsByIdentifier provides a mock function for the type MockPresetsStore
func (_mock *MockPresetsStore) GetUserPresetsByIdentifier(ctx context.Context, identifier *string) (*models.UserPreset, error) {
	ret := _mock.Called(ctx, identifier)

	if len(ret) == 0 {
		panic("no return value specified for GetUserPresetsByIdentifier")
	}

	var r0 *models.UserPreset
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (*models.UserPreset, error)); ok {
		return returnFunc(ctx, identifier)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) *models.UserPreset); ok {
		r0 = returnFunc(ctx, identifier)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserPreset)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, identifier)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPresetsStore_GetUserPresetsByIdentifier_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserPresetsByIdentifier'
type MockPresetsStore_GetUserPresetsByIdentifier_Call struct {
	*mock.Call
}

// GetUserPresetsByIdentifier is a helper method to define mock.On call
//   - ctx context.Context
//   - identifier *string
func (_e *MockPresetsStore_Expecter) GetUserPresetsByIdentifier(ctx interface{}, identifier interface{}) *MockPresetsStore_GetUserPresetsByIdentifier_Call {
	return &MockPresetsStore_GetUserPresetsByIdentifier_Call{Call: _e.mock.On("GetUserPresetsByIdentifier", ctx, identifier)}
}

func (_c *MockPresetsStore_GetUserPresetsByIdentifier_Call) Run(run func(ctx context.Context, identifier *string)) *MockPresetsStore_GetUserPresetsByIdentifier_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *string
		if args[1] != nil {
			arg1 = args[1].(*string)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockPresetsStore_GetUserPresetsByIdentifier_Call) Return(userPreset *models.UserPreset, err error) *MockPresetsStore_GetUserPresetsByIdentifier_Call {
	_c.Call.Return(userPreset, err)
	return _c
}

func (_c *MockPresetsStore_GetUserPresetsByIdentifier_Call) RunAndReturn(run func(ctx context.Context, identifier *string) (*models.UserPreset, error)) *MockPresetsStore_GetUserPresetsByIdentifier_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserRecentPresets provides a mock function for the type MockPresetsStore
func (_mock *MockPresetsStore) GetUserRecentPresets(ctx context.Context, page *int, pageSize *int) (*models.UserPresets, error) {
	ret := _mock.Called(ctx, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetUserRecentPresets")
	}

	var r0 *models.UserPresets
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int) (*models.UserPresets, error)); ok {
		return returnFunc(ctx, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int) *models.UserPresets); ok {
		r0 = returnFunc(ctx, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserPresets)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *int, *int) error); ok {
		r1 = returnFunc(ctx, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPresetsStore_GetUserRecentPresets_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserRecentPresets'
type MockPresetsStore_GetUserRecentPresets_Call struct {
	*mock.Call
}

// GetUserRecentPresets is a helper method to define mock.On call
//   - ctx context.Context
//   - page *int
//   - pageSize *int
func (_e *MockPresetsStore_Expecter) GetUserRecentPresets(ctx interface{}, page interface{}, pageSize interface{}) *MockPresetsStore_GetUserRecentPresets_Call {
	return &MockPresetsStore_GetUserRecentPresets_Call{Call: _e.mock.On("GetUserRecentPresets", ctx, page, pageSize)}
}

func (_c *MockPresetsStore_GetUserRecentPresets_Call) Run(run func(ctx context.Context, page *int, pageSize *int)) *MockPresetsStore_GetUserRecentPresets_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *int
		if args[1] != nil {
			arg1 = args[1].(*int)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockPresetsStore_GetUserRecentPresets_Call) Return(userPresets *models.UserPresets, err error) *MockPresetsStore_GetUserRecentPresets_Call {
	_c.Call.Return(userPresets, err)
	return _c
}

func (_c *MockPresetsStore_GetUserRecentPresets_Call) RunAndReturn(run func(ctx context.Context, page *int, pageSize *int) (*models.UserPresets, error)) *MockPresetsStore_GetUserRecentPresets_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserSavedPresets provides a mock function for the type MockPresetsStore
func (_mock *MockPresetsStore) GetUserSavedPresets(ctx context.Context, page *int, pageSize *int) (*models.UserPresets, error) {
	ret := _mock.Called(ctx, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetUserSavedPresets")
	}

	var r0 *models.UserPresets
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int) (*models.UserPresets, error)); ok {
		return returnFunc(ctx, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int) *models.UserPresets); ok {
		r0 = returnFunc(ctx, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserPresets)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *int, *int) error); ok {
		r1 = returnFunc(ctx, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPresetsStore_GetUserSavedPresets_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserSavedPresets'
type MockPresetsStore_GetUserSavedPresets_Call struct {
	*mock.Call
}

// GetUserSavedPresets is a helper method to define mock.On call
//   - ctx context.Context
//   - page *int
//   - pageSize *int
func (_e *MockPresetsStore_Expecter) GetUserSavedPresets(ctx interface{}, page interface{}, pageSize interface{}) *MockPresetsStore_GetUserSavedPresets_Call {
	return &MockPresetsStore_GetUserSavedPresets_Call{Call: _e.mock.On("GetUserSavedPresets", ctx, page, pageSize)}
}

func (_c *MockPresetsStore_GetUserSavedPresets_Call) Run(run func(ctx context.Context, page *int, pageSize *int)) *MockPresetsStore_GetUserSavedPresets_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *int
		if args[1] != nil {
			arg1 = args[1].(*int)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockPresetsStore_GetUserSavedPresets_Call) Return(userPresets *models.UserPresets, err error) *MockPresetsStore_GetUserSavedPresets_Call {
	_c.Call.Return(userPresets, err)
	return _c
}

func (_c *MockPresetsStore_GetUserSavedPresets_Call) RunAndReturn(run func(ctx context.Context, page *int, pageSize *int) (*models.UserPresets, error)) *MockPresetsStore_GetUserSavedPresets_Call {
	_c.Call.Return(run)
	return _c
}

// GetUsersAllPlayedPresets provides a mock function for the type MockPresetsStore
func (_mock *MockPresetsStore) GetUsersAllPlayedPresets(ctx context.Context, username *string) (*models.AllPlayedPresetsOutput, error) {
	ret := _mock.Called(ctx, username)

	if len(ret) == 0 {
		panic("no return value specified for GetUsersAllPlayedPresets")
	}

	var r0 *models.AllPlayedPresetsOutput
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (*models.AllPlayedPresetsOutput, error)); ok {
		return returnFunc(ctx, username)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) *models.AllPlayedPresetsOutput); ok {
		r0 = returnFunc(ctx, username)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.AllPlayedPresetsOutput)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, username)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPresetsStore_GetUsersAllPlayedPresets_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUsersAllPlayedPresets'
type MockPresetsStore_GetUsersAllPlayedPresets_Call struct {
	*mock.Call
}

// GetUsersAllPlayedPresets is a helper method to define mock.On call
//   - ctx context.Context
//   - username *string
func (_e *MockPresetsStore_Expecter) GetUsersAllPlayedPresets(ctx interface{}, username interface{}) *MockPresetsStore_GetUsersAllPlayedPresets_Call {
	return &MockPresetsStore_GetUsersAllPlayedPresets_Call{Call: _e.mock.On("GetUsersAllPlayedPresets", ctx, username)}
}

func (_c *MockPresetsStore_GetUsersAllPlayedPresets_Call) Run(run func(ctx context.Context, username *string)) *MockPresetsStore_GetUsersAllPlayedPresets_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *string
		if args[1] != nil {
			arg1 = args[1].(*string)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockPresetsStore_GetUsersAllPlayedPresets_Call) Return(allPlayedPresetsOutput *models.AllPlayedPresetsOutput, err error) *MockPresetsStore_GetUsersAllPlayedPresets_Call {
	_c.Call.Return(allPlayedPresetsOutput, err)
	return _c
}

func (_c *MockPresetsStore_GetUsersAllPlayedPresets_Call) RunAndReturn(run func(ctx context.Context, username *string) (*models.AllPlayedPresetsOutput, error)) *MockPresetsStore_GetUsersAllPlayedPresets_Call {
	_c.Call.Return(run)
	return _c
}

// SaveUserPreset provides a mock function for the type MockPresetsStore
func (_mock *MockPresetsStore) SaveUserPreset(ctx context.Context, identifier *string, name *string) (*models.UserPreset, error) {
	ret := _mock.Called(ctx, identifier, name)

	if len(ret) == 0 {
		panic("no return value specified for SaveUserPreset")
	}

	var r0 *models.UserPreset
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *string) (*models.UserPreset, error)); ok {
		return returnFunc(ctx, identifier, name)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *string) *models.UserPreset); ok {
		r0 = returnFunc(ctx, identifier, name)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserPreset)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string, *string) error); ok {
		r1 = returnFunc(ctx, identifier, name)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPresetsStore_SaveUserPreset_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SaveUserPreset'
type MockPresetsStore_SaveUserPreset_Call struct {
	*mock.Call
}

// SaveUserPreset is a helper method to define mock.On call
//   - ctx context.Context
//   - identifier *string
//   - name *string
func (_e *MockPresetsStore_Expecter) SaveUserPreset(ctx interface{}, identifier interface{}, name interface{}) *MockPresetsStore_SaveUserPreset_Call {
	return &MockPresetsStore_SaveUserPreset_Call{Call: _e.mock.On("SaveUserPreset", ctx, identifier, name)}
}

func (_c *MockPresetsStore_SaveUserPreset_Call) Run(run func(ctx context.Context, identifier *string, name *string)) *MockPresetsStore_SaveUserPreset_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *string
		if args[1] != nil {
			arg1 = args[1].(*string)
		}
		var arg2 *string
		if args[2] != nil {
			arg2 = args[2].(*string)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockPresetsStore_SaveUserPreset_Call) Return(userPreset *models.UserPreset, err error) *MockPresetsStore_SaveUserPreset_Call {
	_c.Call.Return(userPreset, err)
	return _c
}

func (_c *MockPresetsStore_SaveUserPreset_Call) RunAndReturn(run func(ctx context.Context, identifier *string, name *string) (*models.UserPreset, error)) *MockPresetsStore_SaveUserPreset_Call {
	_c.Call.Return(run)
	return _c
}

// SubmitUserPresetResult provides a mock function for the type MockPresetsStore
func (_mock *MockPresetsStore) SubmitUserPresetResult(ctx context.Context, userPresetResultInput *models.UserPresetResultInput) (*bool, error) {
	ret := _mock.Called(ctx, userPresetResultInput)

	if len(ret) == 0 {
		panic("no return value specified for SubmitUserPresetResult")
	}

	var r0 *bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UserPresetResultInput) (*bool, error)); ok {
		return returnFunc(ctx, userPresetResultInput)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UserPresetResultInput) *bool); ok {
		r0 = returnFunc(ctx, userPresetResultInput)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bool)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.UserPresetResultInput) error); ok {
		r1 = returnFunc(ctx, userPresetResultInput)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPresetsStore_SubmitUserPresetResult_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SubmitUserPresetResult'
type MockPresetsStore_SubmitUserPresetResult_Call struct {
	*mock.Call
}

// SubmitUserPresetResult is a helper method to define mock.On call
//   - ctx context.Context
//   - userPresetResultInput *models.UserPresetResultInput
func (_e *MockPresetsStore_Expecter) SubmitUserPresetResult(ctx interface{}, userPresetResultInput interface{}) *MockPresetsStore_SubmitUserPresetResult_Call {
	return &MockPresetsStore_SubmitUserPresetResult_Call{Call: _e.mock.On("SubmitUserPresetResult", ctx, userPresetResultInput)}
}

func (_c *MockPresetsStore_SubmitUserPresetResult_Call) Run(run func(ctx context.Context, userPresetResultInput *models.UserPresetResultInput)) *MockPresetsStore_SubmitUserPresetResult_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.UserPresetResultInput
		if args[1] != nil {
			arg1 = args[1].(*models.UserPresetResultInput)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockPresetsStore_SubmitUserPresetResult_Call) Return(b *bool, err error) *MockPresetsStore_SubmitUserPresetResult_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockPresetsStore_SubmitUserPresetResult_Call) RunAndReturn(run func(ctx context.Context, userPresetResultInput *models.UserPresetResultInput) (*bool, error)) *MockPresetsStore_SubmitUserPresetResult_Call {
	_c.Call.Return(run)
	return _c
}

// SubmitUserQuestions provides a mock function for the type MockPresetsStore
func (_mock *MockPresetsStore) SubmitUserQuestions(ctx context.Context, questions any, userID primitive.ObjectID) (*bool, error) {
	ret := _mock.Called(ctx, questions, userID)

	if len(ret) == 0 {
		panic("no return value specified for SubmitUserQuestions")
	}

	var r0 *bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, any, primitive.ObjectID) (*bool, error)); ok {
		return returnFunc(ctx, questions, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, any, primitive.ObjectID) *bool); ok {
		r0 = returnFunc(ctx, questions, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bool)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, any, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, questions, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPresetsStore_SubmitUserQuestions_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SubmitUserQuestions'
type MockPresetsStore_SubmitUserQuestions_Call struct {
	*mock.Call
}

// SubmitUserQuestions is a helper method to define mock.On call
//   - ctx context.Context
//   - questions any
//   - userID primitive.ObjectID
func (_e *MockPresetsStore_Expecter) SubmitUserQuestions(ctx interface{}, questions interface{}, userID interface{}) *MockPresetsStore_SubmitUserQuestions_Call {
	return &MockPresetsStore_SubmitUserQuestions_Call{Call: _e.mock.On("SubmitUserQuestions", ctx, questions, userID)}
}

func (_c *MockPresetsStore_SubmitUserQuestions_Call) Run(run func(ctx context.Context, questions any, userID primitive.ObjectID)) *MockPresetsStore_SubmitUserQuestions_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 any
		if args[1] != nil {
			arg1 = args[1].(any)
		}
		var arg2 primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockPresetsStore_SubmitUserQuestions_Call) Return(b *bool, err error) *MockPresetsStore_SubmitUserQuestions_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockPresetsStore_SubmitUserQuestions_Call) RunAndReturn(run func(ctx context.Context, questions any, userID primitive.ObjectID) (*bool, error)) *MockPresetsStore_SubmitUserQuestions_Call {
	_c.Call.Return(run)
	return _c
}
