// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package domain

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockNotificationStore creates a new instance of MockNotificationStore. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockNotificationStore(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockNotificationStore {
	mock := &MockNotificationStore{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockNotificationStore is an autogenerated mock type for the NotificationStore type
type MockNotificationStore struct {
	mock.Mock
}

type MockNotificationStore_Expecter struct {
	mock *mock.Mock
}

func (_m *MockNotificationStore) EXPECT() *MockNotificationStore_Expecter {
	return &MockNotificationStore_Expecter{mock: &_m.Mock}
}

// RegisterDeviceToken provides a mock function for the type MockNotificationStore
func (_mock *MockNotificationStore) RegisterDeviceToken(ctx context.Context, pushNotificationToken string, deviceID *string, platform *string) (*models.DeviceTokenRegistrationResponse, error) {
	ret := _mock.Called(ctx, pushNotificationToken, deviceID, platform)

	if len(ret) == 0 {
		panic("no return value specified for RegisterDeviceToken")
	}

	var r0 *models.DeviceTokenRegistrationResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, *string, *string) (*models.DeviceTokenRegistrationResponse, error)); ok {
		return returnFunc(ctx, pushNotificationToken, deviceID, platform)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, *string, *string) *models.DeviceTokenRegistrationResponse); ok {
		r0 = returnFunc(ctx, pushNotificationToken, deviceID, platform)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.DeviceTokenRegistrationResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, *string, *string) error); ok {
		r1 = returnFunc(ctx, pushNotificationToken, deviceID, platform)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockNotificationStore_RegisterDeviceToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RegisterDeviceToken'
type MockNotificationStore_RegisterDeviceToken_Call struct {
	*mock.Call
}

// RegisterDeviceToken is a helper method to define mock.On call
//   - ctx context.Context
//   - pushNotificationToken string
//   - deviceID *string
//   - platform *string
func (_e *MockNotificationStore_Expecter) RegisterDeviceToken(ctx interface{}, pushNotificationToken interface{}, deviceID interface{}, platform interface{}) *MockNotificationStore_RegisterDeviceToken_Call {
	return &MockNotificationStore_RegisterDeviceToken_Call{Call: _e.mock.On("RegisterDeviceToken", ctx, pushNotificationToken, deviceID, platform)}
}

func (_c *MockNotificationStore_RegisterDeviceToken_Call) Run(run func(ctx context.Context, pushNotificationToken string, deviceID *string, platform *string)) *MockNotificationStore_RegisterDeviceToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		var arg2 *string
		if args[2] != nil {
			arg2 = args[2].(*string)
		}
		var arg3 *string
		if args[3] != nil {
			arg3 = args[3].(*string)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockNotificationStore_RegisterDeviceToken_Call) Return(deviceTokenRegistrationResponse *models.DeviceTokenRegistrationResponse, err error) *MockNotificationStore_RegisterDeviceToken_Call {
	_c.Call.Return(deviceTokenRegistrationResponse, err)
	return _c
}

func (_c *MockNotificationStore_RegisterDeviceToken_Call) RunAndReturn(run func(ctx context.Context, pushNotificationToken string, deviceID *string, platform *string) (*models.DeviceTokenRegistrationResponse, error)) *MockNotificationStore_RegisterDeviceToken_Call {
	_c.Call.Return(run)
	return _c
}

// SendEmailNotification provides a mock function for the type MockNotificationStore
func (_mock *MockNotificationStore) SendEmailNotification(ctx context.Context, subject string, body string, to []string) error {
	ret := _mock.Called(ctx, subject, body, to)

	if len(ret) == 0 {
		panic("no return value specified for SendEmailNotification")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string, []string) error); ok {
		r0 = returnFunc(ctx, subject, body, to)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockNotificationStore_SendEmailNotification_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendEmailNotification'
type MockNotificationStore_SendEmailNotification_Call struct {
	*mock.Call
}

// SendEmailNotification is a helper method to define mock.On call
//   - ctx context.Context
//   - subject string
//   - body string
//   - to []string
func (_e *MockNotificationStore_Expecter) SendEmailNotification(ctx interface{}, subject interface{}, body interface{}, to interface{}) *MockNotificationStore_SendEmailNotification_Call {
	return &MockNotificationStore_SendEmailNotification_Call{Call: _e.mock.On("SendEmailNotification", ctx, subject, body, to)}
}

func (_c *MockNotificationStore_SendEmailNotification_Call) Run(run func(ctx context.Context, subject string, body string, to []string)) *MockNotificationStore_SendEmailNotification_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		var arg2 string
		if args[2] != nil {
			arg2 = args[2].(string)
		}
		var arg3 []string
		if args[3] != nil {
			arg3 = args[3].([]string)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockNotificationStore_SendEmailNotification_Call) Return(err error) *MockNotificationStore_SendEmailNotification_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockNotificationStore_SendEmailNotification_Call) RunAndReturn(run func(ctx context.Context, subject string, body string, to []string) error) *MockNotificationStore_SendEmailNotification_Call {
	_c.Call.Return(run)
	return _c
}

// SendFeedback provides a mock function for the type MockNotificationStore
func (_mock *MockNotificationStore) SendFeedback(ctx context.Context, feedBack *models.Feedback) (*bool, error) {
	ret := _mock.Called(ctx, feedBack)

	if len(ret) == 0 {
		panic("no return value specified for SendFeedback")
	}

	var r0 *bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.Feedback) (*bool, error)); ok {
		return returnFunc(ctx, feedBack)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.Feedback) *bool); ok {
		r0 = returnFunc(ctx, feedBack)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bool)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.Feedback) error); ok {
		r1 = returnFunc(ctx, feedBack)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockNotificationStore_SendFeedback_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendFeedback'
type MockNotificationStore_SendFeedback_Call struct {
	*mock.Call
}

// SendFeedback is a helper method to define mock.On call
//   - ctx context.Context
//   - feedBack *models.Feedback
func (_e *MockNotificationStore_Expecter) SendFeedback(ctx interface{}, feedBack interface{}) *MockNotificationStore_SendFeedback_Call {
	return &MockNotificationStore_SendFeedback_Call{Call: _e.mock.On("SendFeedback", ctx, feedBack)}
}

func (_c *MockNotificationStore_SendFeedback_Call) Run(run func(ctx context.Context, feedBack *models.Feedback)) *MockNotificationStore_SendFeedback_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.Feedback
		if args[1] != nil {
			arg1 = args[1].(*models.Feedback)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockNotificationStore_SendFeedback_Call) Return(b *bool, err error) *MockNotificationStore_SendFeedback_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockNotificationStore_SendFeedback_Call) RunAndReturn(run func(ctx context.Context, feedBack *models.Feedback) (*bool, error)) *MockNotificationStore_SendFeedback_Call {
	_c.Call.Return(run)
	return _c
}

// SendInAppPopup provides a mock function for the type MockNotificationStore
func (_mock *MockNotificationStore) SendInAppPopup(ctx context.Context, message string, userIDs []primitive.ObjectID) error {
	ret := _mock.Called(ctx, message, userIDs)

	if len(ret) == 0 {
		panic("no return value specified for SendInAppPopup")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, []primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, message, userIDs)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockNotificationStore_SendInAppPopup_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendInAppPopup'
type MockNotificationStore_SendInAppPopup_Call struct {
	*mock.Call
}

// SendInAppPopup is a helper method to define mock.On call
//   - ctx context.Context
//   - message string
//   - userIDs []primitive.ObjectID
func (_e *MockNotificationStore_Expecter) SendInAppPopup(ctx interface{}, message interface{}, userIDs interface{}) *MockNotificationStore_SendInAppPopup_Call {
	return &MockNotificationStore_SendInAppPopup_Call{Call: _e.mock.On("SendInAppPopup", ctx, message, userIDs)}
}

func (_c *MockNotificationStore_SendInAppPopup_Call) Run(run func(ctx context.Context, message string, userIDs []primitive.ObjectID)) *MockNotificationStore_SendInAppPopup_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		var arg2 []primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].([]primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockNotificationStore_SendInAppPopup_Call) Return(err error) *MockNotificationStore_SendInAppPopup_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockNotificationStore_SendInAppPopup_Call) RunAndReturn(run func(ctx context.Context, message string, userIDs []primitive.ObjectID) error) *MockNotificationStore_SendInAppPopup_Call {
	_c.Call.Return(run)
	return _c
}

// SendNotification provides a mock function for the type MockNotificationStore
func (_mock *MockNotificationStore) SendNotification(ctx context.Context, notification *models.UserNotification) error {
	ret := _mock.Called(ctx, notification)

	if len(ret) == 0 {
		panic("no return value specified for SendNotification")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UserNotification) error); ok {
		r0 = returnFunc(ctx, notification)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockNotificationStore_SendNotification_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendNotification'
type MockNotificationStore_SendNotification_Call struct {
	*mock.Call
}

// SendNotification is a helper method to define mock.On call
//   - ctx context.Context
//   - notification *models.UserNotification
func (_e *MockNotificationStore_Expecter) SendNotification(ctx interface{}, notification interface{}) *MockNotificationStore_SendNotification_Call {
	return &MockNotificationStore_SendNotification_Call{Call: _e.mock.On("SendNotification", ctx, notification)}
}

func (_c *MockNotificationStore_SendNotification_Call) Run(run func(ctx context.Context, notification *models.UserNotification)) *MockNotificationStore_SendNotification_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.UserNotification
		if args[1] != nil {
			arg1 = args[1].(*models.UserNotification)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockNotificationStore_SendNotification_Call) Return(err error) *MockNotificationStore_SendNotification_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockNotificationStore_SendNotification_Call) RunAndReturn(run func(ctx context.Context, notification *models.UserNotification) error) *MockNotificationStore_SendNotification_Call {
	_c.Call.Return(run)
	return _c
}

// SendPushNotificationToUsers provides a mock function for the type MockNotificationStore
func (_mock *MockNotificationStore) SendPushNotificationToUsers(ctx context.Context, body string, title string, data map[string]string, userIDs []primitive.ObjectID) error {
	ret := _mock.Called(ctx, body, title, data, userIDs)

	if len(ret) == 0 {
		panic("no return value specified for SendPushNotificationToUsers")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string, map[string]string, []primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, body, title, data, userIDs)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockNotificationStore_SendPushNotificationToUsers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendPushNotificationToUsers'
type MockNotificationStore_SendPushNotificationToUsers_Call struct {
	*mock.Call
}

// SendPushNotificationToUsers is a helper method to define mock.On call
//   - ctx context.Context
//   - body string
//   - title string
//   - data map[string]string
//   - userIDs []primitive.ObjectID
func (_e *MockNotificationStore_Expecter) SendPushNotificationToUsers(ctx interface{}, body interface{}, title interface{}, data interface{}, userIDs interface{}) *MockNotificationStore_SendPushNotificationToUsers_Call {
	return &MockNotificationStore_SendPushNotificationToUsers_Call{Call: _e.mock.On("SendPushNotificationToUsers", ctx, body, title, data, userIDs)}
}

func (_c *MockNotificationStore_SendPushNotificationToUsers_Call) Run(run func(ctx context.Context, body string, title string, data map[string]string, userIDs []primitive.ObjectID)) *MockNotificationStore_SendPushNotificationToUsers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		var arg2 string
		if args[2] != nil {
			arg2 = args[2].(string)
		}
		var arg3 map[string]string
		if args[3] != nil {
			arg3 = args[3].(map[string]string)
		}
		var arg4 []primitive.ObjectID
		if args[4] != nil {
			arg4 = args[4].([]primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
			arg4,
		)
	})
	return _c
}

func (_c *MockNotificationStore_SendPushNotificationToUsers_Call) Return(err error) *MockNotificationStore_SendPushNotificationToUsers_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockNotificationStore_SendPushNotificationToUsers_Call) RunAndReturn(run func(ctx context.Context, body string, title string, data map[string]string, userIDs []primitive.ObjectID) error) *MockNotificationStore_SendPushNotificationToUsers_Call {
	_c.Call.Return(run)
	return _c
}

// UnregisterDeviceToken provides a mock function for the type MockNotificationStore
func (_mock *MockNotificationStore) UnregisterDeviceToken(ctx context.Context, pushNotificationToken *string, deviceID *string) (*models.DeviceTokenRegistrationResponse, error) {
	ret := _mock.Called(ctx, pushNotificationToken, deviceID)

	if len(ret) == 0 {
		panic("no return value specified for UnregisterDeviceToken")
	}

	var r0 *models.DeviceTokenRegistrationResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *string) (*models.DeviceTokenRegistrationResponse, error)); ok {
		return returnFunc(ctx, pushNotificationToken, deviceID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *string) *models.DeviceTokenRegistrationResponse); ok {
		r0 = returnFunc(ctx, pushNotificationToken, deviceID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.DeviceTokenRegistrationResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string, *string) error); ok {
		r1 = returnFunc(ctx, pushNotificationToken, deviceID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockNotificationStore_UnregisterDeviceToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UnregisterDeviceToken'
type MockNotificationStore_UnregisterDeviceToken_Call struct {
	*mock.Call
}

// UnregisterDeviceToken is a helper method to define mock.On call
//   - ctx context.Context
//   - pushNotificationToken *string
//   - deviceID *string
func (_e *MockNotificationStore_Expecter) UnregisterDeviceToken(ctx interface{}, pushNotificationToken interface{}, deviceID interface{}) *MockNotificationStore_UnregisterDeviceToken_Call {
	return &MockNotificationStore_UnregisterDeviceToken_Call{Call: _e.mock.On("UnregisterDeviceToken", ctx, pushNotificationToken, deviceID)}
}

func (_c *MockNotificationStore_UnregisterDeviceToken_Call) Run(run func(ctx context.Context, pushNotificationToken *string, deviceID *string)) *MockNotificationStore_UnregisterDeviceToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *string
		if args[1] != nil {
			arg1 = args[1].(*string)
		}
		var arg2 *string
		if args[2] != nil {
			arg2 = args[2].(*string)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockNotificationStore_UnregisterDeviceToken_Call) Return(deviceTokenRegistrationResponse *models.DeviceTokenRegistrationResponse, err error) *MockNotificationStore_UnregisterDeviceToken_Call {
	_c.Call.Return(deviceTokenRegistrationResponse, err)
	return _c
}

func (_c *MockNotificationStore_UnregisterDeviceToken_Call) RunAndReturn(run func(ctx context.Context, pushNotificationToken *string, deviceID *string) (*models.DeviceTokenRegistrationResponse, error)) *MockNotificationStore_UnregisterDeviceToken_Call {
	_c.Call.Return(run)
	return _c
}
