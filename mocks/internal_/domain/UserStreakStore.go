// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package domain

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
)

// NewMockUserStreakStore creates a new instance of MockUserStreakStore. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockUserStreakStore(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockUserStreakStore {
	mock := &MockUserStreakStore{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockUserStreakStore is an autogenerated mock type for the UserStreakStore type
type MockUserStreakStore struct {
	mock.Mock
}

type MockUserStreakStore_Expecter struct {
	mock *mock.Mock
}

func (_m *MockUserStreakStore) EXPECT() *MockUserStreakStore_Expecter {
	return &MockUserStreakStore_Expecter{mock: &_m.Mock}
}

// CheckUserStreakStatus provides a mock function for the type MockUserStreakStore
func (_mock *MockUserStreakStore) CheckUserStreakStatus(ctx context.Context) (*models.StreakStatusResponse, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for CheckUserStreakStatus")
	}

	var r0 *models.StreakStatusResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*models.StreakStatusResponse, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *models.StreakStatusResponse); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.StreakStatusResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStreakStore_CheckUserStreakStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CheckUserStreakStatus'
type MockUserStreakStore_CheckUserStreakStatus_Call struct {
	*mock.Call
}

// CheckUserStreakStatus is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockUserStreakStore_Expecter) CheckUserStreakStatus(ctx interface{}) *MockUserStreakStore_CheckUserStreakStatus_Call {
	return &MockUserStreakStore_CheckUserStreakStatus_Call{Call: _e.mock.On("CheckUserStreakStatus", ctx)}
}

func (_c *MockUserStreakStore_CheckUserStreakStatus_Call) Run(run func(ctx context.Context)) *MockUserStreakStore_CheckUserStreakStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockUserStreakStore_CheckUserStreakStatus_Call) Return(streakStatusResponse *models.StreakStatusResponse, err error) *MockUserStreakStore_CheckUserStreakStatus_Call {
	_c.Call.Return(streakStatusResponse, err)
	return _c
}

func (_c *MockUserStreakStore_CheckUserStreakStatus_Call) RunAndReturn(run func(ctx context.Context) (*models.StreakStatusResponse, error)) *MockUserStreakStore_CheckUserStreakStatus_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserStreakHistoryByMonth provides a mock function for the type MockUserStreakStore
func (_mock *MockUserStreakStore) GetUserStreakHistoryByMonth(ctx context.Context, yearMonths []string) ([]*models.StreakEntry, error) {
	ret := _mock.Called(ctx, yearMonths)

	if len(ret) == 0 {
		panic("no return value specified for GetUserStreakHistoryByMonth")
	}

	var r0 []*models.StreakEntry
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, []string) ([]*models.StreakEntry, error)); ok {
		return returnFunc(ctx, yearMonths)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, []string) []*models.StreakEntry); ok {
		r0 = returnFunc(ctx, yearMonths)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.StreakEntry)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, []string) error); ok {
		r1 = returnFunc(ctx, yearMonths)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStreakStore_GetUserStreakHistoryByMonth_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserStreakHistoryByMonth'
type MockUserStreakStore_GetUserStreakHistoryByMonth_Call struct {
	*mock.Call
}

// GetUserStreakHistoryByMonth is a helper method to define mock.On call
//   - ctx context.Context
//   - yearMonths []string
func (_e *MockUserStreakStore_Expecter) GetUserStreakHistoryByMonth(ctx interface{}, yearMonths interface{}) *MockUserStreakStore_GetUserStreakHistoryByMonth_Call {
	return &MockUserStreakStore_GetUserStreakHistoryByMonth_Call{Call: _e.mock.On("GetUserStreakHistoryByMonth", ctx, yearMonths)}
}

func (_c *MockUserStreakStore_GetUserStreakHistoryByMonth_Call) Run(run func(ctx context.Context, yearMonths []string)) *MockUserStreakStore_GetUserStreakHistoryByMonth_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 []string
		if args[1] != nil {
			arg1 = args[1].([]string)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockUserStreakStore_GetUserStreakHistoryByMonth_Call) Return(streakEntrys []*models.StreakEntry, err error) *MockUserStreakStore_GetUserStreakHistoryByMonth_Call {
	_c.Call.Return(streakEntrys, err)
	return _c
}

func (_c *MockUserStreakStore_GetUserStreakHistoryByMonth_Call) RunAndReturn(run func(ctx context.Context, yearMonths []string) ([]*models.StreakEntry, error)) *MockUserStreakStore_GetUserStreakHistoryByMonth_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserUpdatedStreak provides a mock function for the type MockUserStreakStore
func (_mock *MockUserStreakStore) GetUserUpdatedStreak(ctx context.Context) (*models.UserStreaks, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetUserUpdatedStreak")
	}

	var r0 *models.UserStreaks
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*models.UserStreaks, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *models.UserStreaks); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserStreaks)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStreakStore_GetUserUpdatedStreak_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserUpdatedStreak'
type MockUserStreakStore_GetUserUpdatedStreak_Call struct {
	*mock.Call
}

// GetUserUpdatedStreak is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockUserStreakStore_Expecter) GetUserUpdatedStreak(ctx interface{}) *MockUserStreakStore_GetUserUpdatedStreak_Call {
	return &MockUserStreakStore_GetUserUpdatedStreak_Call{Call: _e.mock.On("GetUserUpdatedStreak", ctx)}
}

func (_c *MockUserStreakStore_GetUserUpdatedStreak_Call) Run(run func(ctx context.Context)) *MockUserStreakStore_GetUserUpdatedStreak_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockUserStreakStore_GetUserUpdatedStreak_Call) Return(userStreaks *models.UserStreaks, err error) *MockUserStreakStore_GetUserUpdatedStreak_Call {
	_c.Call.Return(userStreaks, err)
	return _c
}

func (_c *MockUserStreakStore_GetUserUpdatedStreak_Call) RunAndReturn(run func(ctx context.Context) (*models.UserStreaks, error)) *MockUserStreakStore_GetUserUpdatedStreak_Call {
	_c.Call.Return(run)
	return _c
}

// UseStreakFreezer provides a mock function for the type MockUserStreakStore
func (_mock *MockUserStreakStore) UseStreakFreezer(ctx context.Context) (bool, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for UseStreakFreezer")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (bool, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) bool); ok {
		r0 = returnFunc(ctx)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStreakStore_UseStreakFreezer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UseStreakFreezer'
type MockUserStreakStore_UseStreakFreezer_Call struct {
	*mock.Call
}

// UseStreakFreezer is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockUserStreakStore_Expecter) UseStreakFreezer(ctx interface{}) *MockUserStreakStore_UseStreakFreezer_Call {
	return &MockUserStreakStore_UseStreakFreezer_Call{Call: _e.mock.On("UseStreakFreezer", ctx)}
}

func (_c *MockUserStreakStore_UseStreakFreezer_Call) Run(run func(ctx context.Context)) *MockUserStreakStore_UseStreakFreezer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockUserStreakStore_UseStreakFreezer_Call) Return(b bool, err error) *MockUserStreakStore_UseStreakFreezer_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockUserStreakStore_UseStreakFreezer_Call) RunAndReturn(run func(ctx context.Context) (bool, error)) *MockUserStreakStore_UseStreakFreezer_Call {
	_c.Call.Return(run)
	return _c
}
