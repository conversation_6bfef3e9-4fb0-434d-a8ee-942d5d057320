// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package generated

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"
	"time"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockQueryResolver creates a new instance of MockQueryResolver. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockQueryResolver(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockQueryResolver {
	mock := &MockQueryResolver{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockQueryResolver is an autogenerated mock type for the QueryResolver type
type MockQueryResolver struct {
	mock.Mock
}

type MockQueryResolver_Expecter struct {
	mock *mock.Mock
}

func (_m *MockQueryResolver) EXPECT() *MockQueryResolver_Expecter {
	return &MockQueryResolver_Expecter{mock: &_m.Mock}
}

// CheckBotBehavior provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) CheckBotBehavior(ctx context.Context, challengeID primitive.ObjectID, userID primitive.ObjectID) (*models.BotDetectionResult, error) {
	ret := _mock.Called(ctx, challengeID, userID)

	if len(ret) == 0 {
		panic("no return value specified for CheckBotBehavior")
	}

	var r0 *models.BotDetectionResult
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) (*models.BotDetectionResult, error)); ok {
		return returnFunc(ctx, challengeID, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) *models.BotDetectionResult); ok {
		r0 = returnFunc(ctx, challengeID, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.BotDetectionResult)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, challengeID, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_CheckBotBehavior_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CheckBotBehavior'
type MockQueryResolver_CheckBotBehavior_Call struct {
	*mock.Call
}

// CheckBotBehavior is a helper method to define mock.On call
//   - ctx context.Context
//   - challengeID primitive.ObjectID
//   - userID primitive.ObjectID
func (_e *MockQueryResolver_Expecter) CheckBotBehavior(ctx interface{}, challengeID interface{}, userID interface{}) *MockQueryResolver_CheckBotBehavior_Call {
	return &MockQueryResolver_CheckBotBehavior_Call{Call: _e.mock.On("CheckBotBehavior", ctx, challengeID, userID)}
}

func (_c *MockQueryResolver_CheckBotBehavior_Call) Run(run func(ctx context.Context, challengeID primitive.ObjectID, userID primitive.ObjectID)) *MockQueryResolver_CheckBotBehavior_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockQueryResolver_CheckBotBehavior_Call) Return(botDetectionResult *models.BotDetectionResult, err error) *MockQueryResolver_CheckBotBehavior_Call {
	_c.Call.Return(botDetectionResult, err)
	return _c
}

func (_c *MockQueryResolver_CheckBotBehavior_Call) RunAndReturn(run func(ctx context.Context, challengeID primitive.ObjectID, userID primitive.ObjectID) (*models.BotDetectionResult, error)) *MockQueryResolver_CheckBotBehavior_Call {
	_c.Call.Return(run)
	return _c
}

// CheckIfPledgeTaken provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) CheckIfPledgeTaken(ctx context.Context) (*bool, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for CheckIfPledgeTaken")
	}

	var r0 *bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*bool, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *bool); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bool)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_CheckIfPledgeTaken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CheckIfPledgeTaken'
type MockQueryResolver_CheckIfPledgeTaken_Call struct {
	*mock.Call
}

// CheckIfPledgeTaken is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockQueryResolver_Expecter) CheckIfPledgeTaken(ctx interface{}) *MockQueryResolver_CheckIfPledgeTaken_Call {
	return &MockQueryResolver_CheckIfPledgeTaken_Call{Call: _e.mock.On("CheckIfPledgeTaken", ctx)}
}

func (_c *MockQueryResolver_CheckIfPledgeTaken_Call) Run(run func(ctx context.Context)) *MockQueryResolver_CheckIfPledgeTaken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockQueryResolver_CheckIfPledgeTaken_Call) Return(b *bool, err error) *MockQueryResolver_CheckIfPledgeTaken_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockQueryResolver_CheckIfPledgeTaken_Call) RunAndReturn(run func(ctx context.Context) (*bool, error)) *MockQueryResolver_CheckIfPledgeTaken_Call {
	_c.Call.Return(run)
	return _c
}

// CheckUserStreakStatus provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) CheckUserStreakStatus(ctx context.Context) (*models.StreakStatusResponse, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for CheckUserStreakStatus")
	}

	var r0 *models.StreakStatusResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*models.StreakStatusResponse, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *models.StreakStatusResponse); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.StreakStatusResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_CheckUserStreakStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CheckUserStreakStatus'
type MockQueryResolver_CheckUserStreakStatus_Call struct {
	*mock.Call
}

// CheckUserStreakStatus is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockQueryResolver_Expecter) CheckUserStreakStatus(ctx interface{}) *MockQueryResolver_CheckUserStreakStatus_Call {
	return &MockQueryResolver_CheckUserStreakStatus_Call{Call: _e.mock.On("CheckUserStreakStatus", ctx)}
}

func (_c *MockQueryResolver_CheckUserStreakStatus_Call) Run(run func(ctx context.Context)) *MockQueryResolver_CheckUserStreakStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockQueryResolver_CheckUserStreakStatus_Call) Return(streakStatusResponse *models.StreakStatusResponse, err error) *MockQueryResolver_CheckUserStreakStatus_Call {
	_c.Call.Return(streakStatusResponse, err)
	return _c
}

func (_c *MockQueryResolver_CheckUserStreakStatus_Call) RunAndReturn(run func(ctx context.Context) (*models.StreakStatusResponse, error)) *MockQueryResolver_CheckUserStreakStatus_Call {
	_c.Call.Return(run)
	return _c
}

// Club provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) Club(ctx context.Context, id primitive.ObjectID) (*models.Club, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for Club")
	}

	var r0 *models.Club
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.Club, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.Club); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Club)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_Club_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Club'
type MockQueryResolver_Club_Call struct {
	*mock.Call
}

// Club is a helper method to define mock.On call
//   - ctx context.Context
//   - id primitive.ObjectID
func (_e *MockQueryResolver_Expecter) Club(ctx interface{}, id interface{}) *MockQueryResolver_Club_Call {
	return &MockQueryResolver_Club_Call{Call: _e.mock.On("Club", ctx, id)}
}

func (_c *MockQueryResolver_Club_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockQueryResolver_Club_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockQueryResolver_Club_Call) Return(club *models.Club, err error) *MockQueryResolver_Club_Call {
	_c.Call.Return(club, err)
	return _c
}

func (_c *MockQueryResolver_Club_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (*models.Club, error)) *MockQueryResolver_Club_Call {
	_c.Call.Return(run)
	return _c
}

// ClubAnnouncements provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) ClubAnnouncements(ctx context.Context, page *int, pageSize *int, clubID *primitive.ObjectID, from *time.Time, to *time.Time) (*models.ClubAnnouncementsPage, error) {
	ret := _mock.Called(ctx, page, pageSize, clubID, from, to)

	if len(ret) == 0 {
		panic("no return value specified for ClubAnnouncements")
	}

	var r0 *models.ClubAnnouncementsPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int, *primitive.ObjectID, *time.Time, *time.Time) (*models.ClubAnnouncementsPage, error)); ok {
		return returnFunc(ctx, page, pageSize, clubID, from, to)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int, *primitive.ObjectID, *time.Time, *time.Time) *models.ClubAnnouncementsPage); ok {
		r0 = returnFunc(ctx, page, pageSize, clubID, from, to)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ClubAnnouncementsPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *int, *int, *primitive.ObjectID, *time.Time, *time.Time) error); ok {
		r1 = returnFunc(ctx, page, pageSize, clubID, from, to)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_ClubAnnouncements_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ClubAnnouncements'
type MockQueryResolver_ClubAnnouncements_Call struct {
	*mock.Call
}

// ClubAnnouncements is a helper method to define mock.On call
//   - ctx context.Context
//   - page *int
//   - pageSize *int
//   - clubID *primitive.ObjectID
//   - from *time.Time
//   - to *time.Time
func (_e *MockQueryResolver_Expecter) ClubAnnouncements(ctx interface{}, page interface{}, pageSize interface{}, clubID interface{}, from interface{}, to interface{}) *MockQueryResolver_ClubAnnouncements_Call {
	return &MockQueryResolver_ClubAnnouncements_Call{Call: _e.mock.On("ClubAnnouncements", ctx, page, pageSize, clubID, from, to)}
}

func (_c *MockQueryResolver_ClubAnnouncements_Call) Run(run func(ctx context.Context, page *int, pageSize *int, clubID *primitive.ObjectID, from *time.Time, to *time.Time)) *MockQueryResolver_ClubAnnouncements_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *int
		if args[1] != nil {
			arg1 = args[1].(*int)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		var arg3 *primitive.ObjectID
		if args[3] != nil {
			arg3 = args[3].(*primitive.ObjectID)
		}
		var arg4 *time.Time
		if args[4] != nil {
			arg4 = args[4].(*time.Time)
		}
		var arg5 *time.Time
		if args[5] != nil {
			arg5 = args[5].(*time.Time)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
			arg4,
			arg5,
		)
	})
	return _c
}

func (_c *MockQueryResolver_ClubAnnouncements_Call) Return(clubAnnouncementsPage *models.ClubAnnouncementsPage, err error) *MockQueryResolver_ClubAnnouncements_Call {
	_c.Call.Return(clubAnnouncementsPage, err)
	return _c
}

func (_c *MockQueryResolver_ClubAnnouncements_Call) RunAndReturn(run func(ctx context.Context, page *int, pageSize *int, clubID *primitive.ObjectID, from *time.Time, to *time.Time) (*models.ClubAnnouncementsPage, error)) *MockQueryResolver_ClubAnnouncements_Call {
	_c.Call.Return(run)
	return _c
}

// ClubEvent provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) ClubEvent(ctx context.Context, id primitive.ObjectID) (*models.ClubEvent, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for ClubEvent")
	}

	var r0 *models.ClubEvent
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.ClubEvent, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.ClubEvent); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ClubEvent)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_ClubEvent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ClubEvent'
type MockQueryResolver_ClubEvent_Call struct {
	*mock.Call
}

// ClubEvent is a helper method to define mock.On call
//   - ctx context.Context
//   - id primitive.ObjectID
func (_e *MockQueryResolver_Expecter) ClubEvent(ctx interface{}, id interface{}) *MockQueryResolver_ClubEvent_Call {
	return &MockQueryResolver_ClubEvent_Call{Call: _e.mock.On("ClubEvent", ctx, id)}
}

func (_c *MockQueryResolver_ClubEvent_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockQueryResolver_ClubEvent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockQueryResolver_ClubEvent_Call) Return(clubEvent *models.ClubEvent, err error) *MockQueryResolver_ClubEvent_Call {
	_c.Call.Return(clubEvent, err)
	return _c
}

func (_c *MockQueryResolver_ClubEvent_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (*models.ClubEvent, error)) *MockQueryResolver_ClubEvent_Call {
	_c.Call.Return(run)
	return _c
}

// ClubEvents provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) ClubEvents(ctx context.Context, page *int, pageSize *int, clubID *primitive.ObjectID, clubEventType *models.ClubEventType, from *time.Time, to *time.Time) (*models.ClubEventsPage, error) {
	ret := _mock.Called(ctx, page, pageSize, clubID, clubEventType, from, to)

	if len(ret) == 0 {
		panic("no return value specified for ClubEvents")
	}

	var r0 *models.ClubEventsPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int, *primitive.ObjectID, *models.ClubEventType, *time.Time, *time.Time) (*models.ClubEventsPage, error)); ok {
		return returnFunc(ctx, page, pageSize, clubID, clubEventType, from, to)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int, *primitive.ObjectID, *models.ClubEventType, *time.Time, *time.Time) *models.ClubEventsPage); ok {
		r0 = returnFunc(ctx, page, pageSize, clubID, clubEventType, from, to)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ClubEventsPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *int, *int, *primitive.ObjectID, *models.ClubEventType, *time.Time, *time.Time) error); ok {
		r1 = returnFunc(ctx, page, pageSize, clubID, clubEventType, from, to)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_ClubEvents_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ClubEvents'
type MockQueryResolver_ClubEvents_Call struct {
	*mock.Call
}

// ClubEvents is a helper method to define mock.On call
//   - ctx context.Context
//   - page *int
//   - pageSize *int
//   - clubID *primitive.ObjectID
//   - clubEventType *models.ClubEventType
//   - from *time.Time
//   - to *time.Time
func (_e *MockQueryResolver_Expecter) ClubEvents(ctx interface{}, page interface{}, pageSize interface{}, clubID interface{}, clubEventType interface{}, from interface{}, to interface{}) *MockQueryResolver_ClubEvents_Call {
	return &MockQueryResolver_ClubEvents_Call{Call: _e.mock.On("ClubEvents", ctx, page, pageSize, clubID, clubEventType, from, to)}
}

func (_c *MockQueryResolver_ClubEvents_Call) Run(run func(ctx context.Context, page *int, pageSize *int, clubID *primitive.ObjectID, clubEventType *models.ClubEventType, from *time.Time, to *time.Time)) *MockQueryResolver_ClubEvents_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *int
		if args[1] != nil {
			arg1 = args[1].(*int)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		var arg3 *primitive.ObjectID
		if args[3] != nil {
			arg3 = args[3].(*primitive.ObjectID)
		}
		var arg4 *models.ClubEventType
		if args[4] != nil {
			arg4 = args[4].(*models.ClubEventType)
		}
		var arg5 *time.Time
		if args[5] != nil {
			arg5 = args[5].(*time.Time)
		}
		var arg6 *time.Time
		if args[6] != nil {
			arg6 = args[6].(*time.Time)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
			arg4,
			arg5,
			arg6,
		)
	})
	return _c
}

func (_c *MockQueryResolver_ClubEvents_Call) Return(clubEventsPage *models.ClubEventsPage, err error) *MockQueryResolver_ClubEvents_Call {
	_c.Call.Return(clubEventsPage, err)
	return _c
}

func (_c *MockQueryResolver_ClubEvents_Call) RunAndReturn(run func(ctx context.Context, page *int, pageSize *int, clubID *primitive.ObjectID, clubEventType *models.ClubEventType, from *time.Time, to *time.Time) (*models.ClubEventsPage, error)) *MockQueryResolver_ClubEvents_Call {
	_c.Call.Return(run)
	return _c
}

// ClubMembers provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) ClubMembers(ctx context.Context, clubID primitive.ObjectID, clubMembershipStatus models.ClubMembershipStatus, page *int, pageSize *int) (*models.ClubMembersPage, error) {
	ret := _mock.Called(ctx, clubID, clubMembershipStatus, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for ClubMembers")
	}

	var r0 *models.ClubMembersPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, models.ClubMembershipStatus, *int, *int) (*models.ClubMembersPage, error)); ok {
		return returnFunc(ctx, clubID, clubMembershipStatus, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, models.ClubMembershipStatus, *int, *int) *models.ClubMembersPage); ok {
		r0 = returnFunc(ctx, clubID, clubMembershipStatus, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ClubMembersPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, models.ClubMembershipStatus, *int, *int) error); ok {
		r1 = returnFunc(ctx, clubID, clubMembershipStatus, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_ClubMembers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ClubMembers'
type MockQueryResolver_ClubMembers_Call struct {
	*mock.Call
}

// ClubMembers is a helper method to define mock.On call
//   - ctx context.Context
//   - clubID primitive.ObjectID
//   - clubMembershipStatus models.ClubMembershipStatus
//   - page *int
//   - pageSize *int
func (_e *MockQueryResolver_Expecter) ClubMembers(ctx interface{}, clubID interface{}, clubMembershipStatus interface{}, page interface{}, pageSize interface{}) *MockQueryResolver_ClubMembers_Call {
	return &MockQueryResolver_ClubMembers_Call{Call: _e.mock.On("ClubMembers", ctx, clubID, clubMembershipStatus, page, pageSize)}
}

func (_c *MockQueryResolver_ClubMembers_Call) Run(run func(ctx context.Context, clubID primitive.ObjectID, clubMembershipStatus models.ClubMembershipStatus, page *int, pageSize *int)) *MockQueryResolver_ClubMembers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 models.ClubMembershipStatus
		if args[2] != nil {
			arg2 = args[2].(models.ClubMembershipStatus)
		}
		var arg3 *int
		if args[3] != nil {
			arg3 = args[3].(*int)
		}
		var arg4 *int
		if args[4] != nil {
			arg4 = args[4].(*int)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
			arg4,
		)
	})
	return _c
}

func (_c *MockQueryResolver_ClubMembers_Call) Return(clubMembersPage *models.ClubMembersPage, err error) *MockQueryResolver_ClubMembers_Call {
	_c.Call.Return(clubMembersPage, err)
	return _c
}

func (_c *MockQueryResolver_ClubMembers_Call) RunAndReturn(run func(ctx context.Context, clubID primitive.ObjectID, clubMembershipStatus models.ClubMembershipStatus, page *int, pageSize *int) (*models.ClubMembersPage, error)) *MockQueryResolver_ClubMembers_Call {
	_c.Call.Return(run)
	return _c
}

// Clubs provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) Clubs(ctx context.Context, page *int, pageSize *int, visibility *models.Visibility, search *string) (*models.ClubsPage, error) {
	ret := _mock.Called(ctx, page, pageSize, visibility, search)

	if len(ret) == 0 {
		panic("no return value specified for Clubs")
	}

	var r0 *models.ClubsPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int, *models.Visibility, *string) (*models.ClubsPage, error)); ok {
		return returnFunc(ctx, page, pageSize, visibility, search)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int, *models.Visibility, *string) *models.ClubsPage); ok {
		r0 = returnFunc(ctx, page, pageSize, visibility, search)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ClubsPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *int, *int, *models.Visibility, *string) error); ok {
		r1 = returnFunc(ctx, page, pageSize, visibility, search)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_Clubs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Clubs'
type MockQueryResolver_Clubs_Call struct {
	*mock.Call
}

// Clubs is a helper method to define mock.On call
//   - ctx context.Context
//   - page *int
//   - pageSize *int
//   - visibility *models.Visibility
//   - search *string
func (_e *MockQueryResolver_Expecter) Clubs(ctx interface{}, page interface{}, pageSize interface{}, visibility interface{}, search interface{}) *MockQueryResolver_Clubs_Call {
	return &MockQueryResolver_Clubs_Call{Call: _e.mock.On("Clubs", ctx, page, pageSize, visibility, search)}
}

func (_c *MockQueryResolver_Clubs_Call) Run(run func(ctx context.Context, page *int, pageSize *int, visibility *models.Visibility, search *string)) *MockQueryResolver_Clubs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *int
		if args[1] != nil {
			arg1 = args[1].(*int)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		var arg3 *models.Visibility
		if args[3] != nil {
			arg3 = args[3].(*models.Visibility)
		}
		var arg4 *string
		if args[4] != nil {
			arg4 = args[4].(*string)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
			arg4,
		)
	})
	return _c
}

func (_c *MockQueryResolver_Clubs_Call) Return(clubsPage *models.ClubsPage, err error) *MockQueryResolver_Clubs_Call {
	_c.Call.Return(clubsPage, err)
	return _c
}

func (_c *MockQueryResolver_Clubs_Call) RunAndReturn(run func(ctx context.Context, page *int, pageSize *int, visibility *models.Visibility, search *string) (*models.ClubsPage, error)) *MockQueryResolver_Clubs_Call {
	_c.Call.Return(run)
	return _c
}

// Forum provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) Forum(ctx context.Context, id primitive.ObjectID) (*models.Forum, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for Forum")
	}

	var r0 *models.Forum
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.Forum, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.Forum); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Forum)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_Forum_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Forum'
type MockQueryResolver_Forum_Call struct {
	*mock.Call
}

// Forum is a helper method to define mock.On call
//   - ctx context.Context
//   - id primitive.ObjectID
func (_e *MockQueryResolver_Expecter) Forum(ctx interface{}, id interface{}) *MockQueryResolver_Forum_Call {
	return &MockQueryResolver_Forum_Call{Call: _e.mock.On("Forum", ctx, id)}
}

func (_c *MockQueryResolver_Forum_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockQueryResolver_Forum_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockQueryResolver_Forum_Call) Return(forum *models.Forum, err error) *MockQueryResolver_Forum_Call {
	_c.Call.Return(forum, err)
	return _c
}

func (_c *MockQueryResolver_Forum_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (*models.Forum, error)) *MockQueryResolver_Forum_Call {
	_c.Call.Return(run)
	return _c
}

// ForumReplies provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) ForumReplies(ctx context.Context, threadID primitive.ObjectID, page *int, pageSize *int) (*models.RepliesPage, error) {
	ret := _mock.Called(ctx, threadID, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for ForumReplies")
	}

	var r0 *models.RepliesPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *int, *int) (*models.RepliesPage, error)); ok {
		return returnFunc(ctx, threadID, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *int, *int) *models.RepliesPage); ok {
		r0 = returnFunc(ctx, threadID, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.RepliesPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, *int, *int) error); ok {
		r1 = returnFunc(ctx, threadID, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_ForumReplies_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ForumReplies'
type MockQueryResolver_ForumReplies_Call struct {
	*mock.Call
}

// ForumReplies is a helper method to define mock.On call
//   - ctx context.Context
//   - threadID primitive.ObjectID
//   - page *int
//   - pageSize *int
func (_e *MockQueryResolver_Expecter) ForumReplies(ctx interface{}, threadID interface{}, page interface{}, pageSize interface{}) *MockQueryResolver_ForumReplies_Call {
	return &MockQueryResolver_ForumReplies_Call{Call: _e.mock.On("ForumReplies", ctx, threadID, page, pageSize)}
}

func (_c *MockQueryResolver_ForumReplies_Call) Run(run func(ctx context.Context, threadID primitive.ObjectID, page *int, pageSize *int)) *MockQueryResolver_ForumReplies_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		var arg3 *int
		if args[3] != nil {
			arg3 = args[3].(*int)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockQueryResolver_ForumReplies_Call) Return(repliesPage *models.RepliesPage, err error) *MockQueryResolver_ForumReplies_Call {
	_c.Call.Return(repliesPage, err)
	return _c
}

func (_c *MockQueryResolver_ForumReplies_Call) RunAndReturn(run func(ctx context.Context, threadID primitive.ObjectID, page *int, pageSize *int) (*models.RepliesPage, error)) *MockQueryResolver_ForumReplies_Call {
	_c.Call.Return(run)
	return _c
}

// ForumThread provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) ForumThread(ctx context.Context, id primitive.ObjectID) (*models.ForumThread, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for ForumThread")
	}

	var r0 *models.ForumThread
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.ForumThread, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.ForumThread); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ForumThread)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_ForumThread_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ForumThread'
type MockQueryResolver_ForumThread_Call struct {
	*mock.Call
}

// ForumThread is a helper method to define mock.On call
//   - ctx context.Context
//   - id primitive.ObjectID
func (_e *MockQueryResolver_Expecter) ForumThread(ctx interface{}, id interface{}) *MockQueryResolver_ForumThread_Call {
	return &MockQueryResolver_ForumThread_Call{Call: _e.mock.On("ForumThread", ctx, id)}
}

func (_c *MockQueryResolver_ForumThread_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockQueryResolver_ForumThread_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockQueryResolver_ForumThread_Call) Return(forumThread *models.ForumThread, err error) *MockQueryResolver_ForumThread_Call {
	_c.Call.Return(forumThread, err)
	return _c
}

func (_c *MockQueryResolver_ForumThread_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (*models.ForumThread, error)) *MockQueryResolver_ForumThread_Call {
	_c.Call.Return(run)
	return _c
}

// ForumThreads provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) ForumThreads(ctx context.Context, forumID primitive.ObjectID, page *int, pageSize *int) (*models.ThreadsPage, error) {
	ret := _mock.Called(ctx, forumID, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for ForumThreads")
	}

	var r0 *models.ThreadsPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *int, *int) (*models.ThreadsPage, error)); ok {
		return returnFunc(ctx, forumID, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *int, *int) *models.ThreadsPage); ok {
		r0 = returnFunc(ctx, forumID, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ThreadsPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, *int, *int) error); ok {
		r1 = returnFunc(ctx, forumID, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_ForumThreads_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ForumThreads'
type MockQueryResolver_ForumThreads_Call struct {
	*mock.Call
}

// ForumThreads is a helper method to define mock.On call
//   - ctx context.Context
//   - forumID primitive.ObjectID
//   - page *int
//   - pageSize *int
func (_e *MockQueryResolver_Expecter) ForumThreads(ctx interface{}, forumID interface{}, page interface{}, pageSize interface{}) *MockQueryResolver_ForumThreads_Call {
	return &MockQueryResolver_ForumThreads_Call{Call: _e.mock.On("ForumThreads", ctx, forumID, page, pageSize)}
}

func (_c *MockQueryResolver_ForumThreads_Call) Run(run func(ctx context.Context, forumID primitive.ObjectID, page *int, pageSize *int)) *MockQueryResolver_ForumThreads_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		var arg3 *int
		if args[3] != nil {
			arg3 = args[3].(*int)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockQueryResolver_ForumThreads_Call) Return(threadsPage *models.ThreadsPage, err error) *MockQueryResolver_ForumThreads_Call {
	_c.Call.Return(threadsPage, err)
	return _c
}

func (_c *MockQueryResolver_ForumThreads_Call) RunAndReturn(run func(ctx context.Context, forumID primitive.ObjectID, page *int, pageSize *int) (*models.ThreadsPage, error)) *MockQueryResolver_ForumThreads_Call {
	_c.Call.Return(run)
	return _c
}

// Forums provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) Forums(ctx context.Context, clubID primitive.ObjectID, page *int, pageSize *int) (*models.ForumPage, error) {
	ret := _mock.Called(ctx, clubID, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for Forums")
	}

	var r0 *models.ForumPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *int, *int) (*models.ForumPage, error)); ok {
		return returnFunc(ctx, clubID, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *int, *int) *models.ForumPage); ok {
		r0 = returnFunc(ctx, clubID, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ForumPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, *int, *int) error); ok {
		r1 = returnFunc(ctx, clubID, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_Forums_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Forums'
type MockQueryResolver_Forums_Call struct {
	*mock.Call
}

// Forums is a helper method to define mock.On call
//   - ctx context.Context
//   - clubID primitive.ObjectID
//   - page *int
//   - pageSize *int
func (_e *MockQueryResolver_Expecter) Forums(ctx interface{}, clubID interface{}, page interface{}, pageSize interface{}) *MockQueryResolver_Forums_Call {
	return &MockQueryResolver_Forums_Call{Call: _e.mock.On("Forums", ctx, clubID, page, pageSize)}
}

func (_c *MockQueryResolver_Forums_Call) Run(run func(ctx context.Context, clubID primitive.ObjectID, page *int, pageSize *int)) *MockQueryResolver_Forums_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		var arg3 *int
		if args[3] != nil {
			arg3 = args[3].(*int)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockQueryResolver_Forums_Call) Return(forumPage *models.ForumPage, err error) *MockQueryResolver_Forums_Call {
	_c.Call.Return(forumPage, err)
	return _c
}

func (_c *MockQueryResolver_Forums_Call) RunAndReturn(run func(ctx context.Context, clubID primitive.ObjectID, page *int, pageSize *int) (*models.ForumPage, error)) *MockQueryResolver_Forums_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllAnnouncements provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetAllAnnouncements(ctx context.Context, limit *int, offset *int, typeArg *models.AnnouncementType) ([]*models.Announcement, error) {
	ret := _mock.Called(ctx, limit, offset, typeArg)

	if len(ret) == 0 {
		panic("no return value specified for GetAllAnnouncements")
	}

	var r0 []*models.Announcement
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int, *models.AnnouncementType) ([]*models.Announcement, error)); ok {
		return returnFunc(ctx, limit, offset, typeArg)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int, *models.AnnouncementType) []*models.Announcement); ok {
		r0 = returnFunc(ctx, limit, offset, typeArg)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.Announcement)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *int, *int, *models.AnnouncementType) error); ok {
		r1 = returnFunc(ctx, limit, offset, typeArg)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetAllAnnouncements_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllAnnouncements'
type MockQueryResolver_GetAllAnnouncements_Call struct {
	*mock.Call
}

// GetAllAnnouncements is a helper method to define mock.On call
//   - ctx context.Context
//   - limit *int
//   - offset *int
//   - typeArg *models.AnnouncementType
func (_e *MockQueryResolver_Expecter) GetAllAnnouncements(ctx interface{}, limit interface{}, offset interface{}, typeArg interface{}) *MockQueryResolver_GetAllAnnouncements_Call {
	return &MockQueryResolver_GetAllAnnouncements_Call{Call: _e.mock.On("GetAllAnnouncements", ctx, limit, offset, typeArg)}
}

func (_c *MockQueryResolver_GetAllAnnouncements_Call) Run(run func(ctx context.Context, limit *int, offset *int, typeArg *models.AnnouncementType)) *MockQueryResolver_GetAllAnnouncements_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *int
		if args[1] != nil {
			arg1 = args[1].(*int)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		var arg3 *models.AnnouncementType
		if args[3] != nil {
			arg3 = args[3].(*models.AnnouncementType)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetAllAnnouncements_Call) Return(announcements []*models.Announcement, err error) *MockQueryResolver_GetAllAnnouncements_Call {
	_c.Call.Return(announcements, err)
	return _c
}

func (_c *MockQueryResolver_GetAllAnnouncements_Call) RunAndReturn(run func(ctx context.Context, limit *int, offset *int, typeArg *models.AnnouncementType) ([]*models.Announcement, error)) *MockQueryResolver_GetAllAnnouncements_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllMessageGroups provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetAllMessageGroups(ctx context.Context, input *models.GetAllMessageGroupsInput) (*models.PaginatedMessageGroups, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for GetAllMessageGroups")
	}

	var r0 *models.PaginatedMessageGroups
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.GetAllMessageGroupsInput) (*models.PaginatedMessageGroups, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.GetAllMessageGroupsInput) *models.PaginatedMessageGroups); ok {
		r0 = returnFunc(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PaginatedMessageGroups)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.GetAllMessageGroupsInput) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetAllMessageGroups_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllMessageGroups'
type MockQueryResolver_GetAllMessageGroups_Call struct {
	*mock.Call
}

// GetAllMessageGroups is a helper method to define mock.On call
//   - ctx context.Context
//   - input *models.GetAllMessageGroupsInput
func (_e *MockQueryResolver_Expecter) GetAllMessageGroups(ctx interface{}, input interface{}) *MockQueryResolver_GetAllMessageGroups_Call {
	return &MockQueryResolver_GetAllMessageGroups_Call{Call: _e.mock.On("GetAllMessageGroups", ctx, input)}
}

func (_c *MockQueryResolver_GetAllMessageGroups_Call) Run(run func(ctx context.Context, input *models.GetAllMessageGroupsInput)) *MockQueryResolver_GetAllMessageGroups_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.GetAllMessageGroupsInput
		if args[1] != nil {
			arg1 = args[1].(*models.GetAllMessageGroupsInput)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetAllMessageGroups_Call) Return(paginatedMessageGroups *models.PaginatedMessageGroups, err error) *MockQueryResolver_GetAllMessageGroups_Call {
	_c.Call.Return(paginatedMessageGroups, err)
	return _c
}

func (_c *MockQueryResolver_GetAllMessageGroups_Call) RunAndReturn(run func(ctx context.Context, input *models.GetAllMessageGroupsInput) (*models.PaginatedMessageGroups, error)) *MockQueryResolver_GetAllMessageGroups_Call {
	_c.Call.Return(run)
	return _c
}

// GetAnnouncement provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetAnnouncement(ctx context.Context, id primitive.ObjectID) (*models.Announcement, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetAnnouncement")
	}

	var r0 *models.Announcement
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.Announcement, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.Announcement); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Announcement)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetAnnouncement_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAnnouncement'
type MockQueryResolver_GetAnnouncement_Call struct {
	*mock.Call
}

// GetAnnouncement is a helper method to define mock.On call
//   - ctx context.Context
//   - id primitive.ObjectID
func (_e *MockQueryResolver_Expecter) GetAnnouncement(ctx interface{}, id interface{}) *MockQueryResolver_GetAnnouncement_Call {
	return &MockQueryResolver_GetAnnouncement_Call{Call: _e.mock.On("GetAnnouncement", ctx, id)}
}

func (_c *MockQueryResolver_GetAnnouncement_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockQueryResolver_GetAnnouncement_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetAnnouncement_Call) Return(announcement *models.Announcement, err error) *MockQueryResolver_GetAnnouncement_Call {
	_c.Call.Return(announcement, err)
	return _c
}

func (_c *MockQueryResolver_GetAnnouncement_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (*models.Announcement, error)) *MockQueryResolver_GetAnnouncement_Call {
	_c.Call.Return(run)
	return _c
}

// GetClubLeaderboard provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetClubLeaderboard(ctx context.Context, clubID primitive.ObjectID, page *int, pageSize *int) (*models.ClubLeaderboard, error) {
	ret := _mock.Called(ctx, clubID, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetClubLeaderboard")
	}

	var r0 *models.ClubLeaderboard
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *int, *int) (*models.ClubLeaderboard, error)); ok {
		return returnFunc(ctx, clubID, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *int, *int) *models.ClubLeaderboard); ok {
		r0 = returnFunc(ctx, clubID, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ClubLeaderboard)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, *int, *int) error); ok {
		r1 = returnFunc(ctx, clubID, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetClubLeaderboard_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetClubLeaderboard'
type MockQueryResolver_GetClubLeaderboard_Call struct {
	*mock.Call
}

// GetClubLeaderboard is a helper method to define mock.On call
//   - ctx context.Context
//   - clubID primitive.ObjectID
//   - page *int
//   - pageSize *int
func (_e *MockQueryResolver_Expecter) GetClubLeaderboard(ctx interface{}, clubID interface{}, page interface{}, pageSize interface{}) *MockQueryResolver_GetClubLeaderboard_Call {
	return &MockQueryResolver_GetClubLeaderboard_Call{Call: _e.mock.On("GetClubLeaderboard", ctx, clubID, page, pageSize)}
}

func (_c *MockQueryResolver_GetClubLeaderboard_Call) Run(run func(ctx context.Context, clubID primitive.ObjectID, page *int, pageSize *int)) *MockQueryResolver_GetClubLeaderboard_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		var arg3 *int
		if args[3] != nil {
			arg3 = args[3].(*int)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetClubLeaderboard_Call) Return(clubLeaderboard *models.ClubLeaderboard, err error) *MockQueryResolver_GetClubLeaderboard_Call {
	_c.Call.Return(clubLeaderboard, err)
	return _c
}

func (_c *MockQueryResolver_GetClubLeaderboard_Call) RunAndReturn(run func(ctx context.Context, clubID primitive.ObjectID, page *int, pageSize *int) (*models.ClubLeaderboard, error)) *MockQueryResolver_GetClubLeaderboard_Call {
	_c.Call.Return(run)
	return _c
}

// GetClubMemberInfo provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetClubMemberInfo(ctx context.Context, clubID primitive.ObjectID) (*models.ClubMember, error) {
	ret := _mock.Called(ctx, clubID)

	if len(ret) == 0 {
		panic("no return value specified for GetClubMemberInfo")
	}

	var r0 *models.ClubMember
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.ClubMember, error)); ok {
		return returnFunc(ctx, clubID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.ClubMember); ok {
		r0 = returnFunc(ctx, clubID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ClubMember)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, clubID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetClubMemberInfo_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetClubMemberInfo'
type MockQueryResolver_GetClubMemberInfo_Call struct {
	*mock.Call
}

// GetClubMemberInfo is a helper method to define mock.On call
//   - ctx context.Context
//   - clubID primitive.ObjectID
func (_e *MockQueryResolver_Expecter) GetClubMemberInfo(ctx interface{}, clubID interface{}) *MockQueryResolver_GetClubMemberInfo_Call {
	return &MockQueryResolver_GetClubMemberInfo_Call{Call: _e.mock.On("GetClubMemberInfo", ctx, clubID)}
}

func (_c *MockQueryResolver_GetClubMemberInfo_Call) Run(run func(ctx context.Context, clubID primitive.ObjectID)) *MockQueryResolver_GetClubMemberInfo_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetClubMemberInfo_Call) Return(clubMember *models.ClubMember, err error) *MockQueryResolver_GetClubMemberInfo_Call {
	_c.Call.Return(clubMember, err)
	return _c
}

func (_c *MockQueryResolver_GetClubMemberInfo_Call) RunAndReturn(run func(ctx context.Context, clubID primitive.ObjectID) (*models.ClubMember, error)) *MockQueryResolver_GetClubMemberInfo_Call {
	_c.Call.Return(run)
	return _c
}

// GetContestByID provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetContestByID(ctx context.Context, contestID primitive.ObjectID) (*models.Contest, error) {
	ret := _mock.Called(ctx, contestID)

	if len(ret) == 0 {
		panic("no return value specified for GetContestByID")
	}

	var r0 *models.Contest
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.Contest, error)); ok {
		return returnFunc(ctx, contestID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.Contest); ok {
		r0 = returnFunc(ctx, contestID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Contest)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, contestID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetContestByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetContestByID'
type MockQueryResolver_GetContestByID_Call struct {
	*mock.Call
}

// GetContestByID is a helper method to define mock.On call
//   - ctx context.Context
//   - contestID primitive.ObjectID
func (_e *MockQueryResolver_Expecter) GetContestByID(ctx interface{}, contestID interface{}) *MockQueryResolver_GetContestByID_Call {
	return &MockQueryResolver_GetContestByID_Call{Call: _e.mock.On("GetContestByID", ctx, contestID)}
}

func (_c *MockQueryResolver_GetContestByID_Call) Run(run func(ctx context.Context, contestID primitive.ObjectID)) *MockQueryResolver_GetContestByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetContestByID_Call) Return(contest *models.Contest, err error) *MockQueryResolver_GetContestByID_Call {
	_c.Call.Return(contest, err)
	return _c
}

func (_c *MockQueryResolver_GetContestByID_Call) RunAndReturn(run func(ctx context.Context, contestID primitive.ObjectID) (*models.Contest, error)) *MockQueryResolver_GetContestByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetContestLeaderboard provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetContestLeaderboard(ctx context.Context, contestID primitive.ObjectID, pageNumber *int, pageSize *int) (*models.ContestLeaderboard, error) {
	ret := _mock.Called(ctx, contestID, pageNumber, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetContestLeaderboard")
	}

	var r0 *models.ContestLeaderboard
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *int, *int) (*models.ContestLeaderboard, error)); ok {
		return returnFunc(ctx, contestID, pageNumber, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *int, *int) *models.ContestLeaderboard); ok {
		r0 = returnFunc(ctx, contestID, pageNumber, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ContestLeaderboard)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, *int, *int) error); ok {
		r1 = returnFunc(ctx, contestID, pageNumber, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetContestLeaderboard_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetContestLeaderboard'
type MockQueryResolver_GetContestLeaderboard_Call struct {
	*mock.Call
}

// GetContestLeaderboard is a helper method to define mock.On call
//   - ctx context.Context
//   - contestID primitive.ObjectID
//   - pageNumber *int
//   - pageSize *int
func (_e *MockQueryResolver_Expecter) GetContestLeaderboard(ctx interface{}, contestID interface{}, pageNumber interface{}, pageSize interface{}) *MockQueryResolver_GetContestLeaderboard_Call {
	return &MockQueryResolver_GetContestLeaderboard_Call{Call: _e.mock.On("GetContestLeaderboard", ctx, contestID, pageNumber, pageSize)}
}

func (_c *MockQueryResolver_GetContestLeaderboard_Call) Run(run func(ctx context.Context, contestID primitive.ObjectID, pageNumber *int, pageSize *int)) *MockQueryResolver_GetContestLeaderboard_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		var arg3 *int
		if args[3] != nil {
			arg3 = args[3].(*int)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetContestLeaderboard_Call) Return(contestLeaderboard *models.ContestLeaderboard, err error) *MockQueryResolver_GetContestLeaderboard_Call {
	_c.Call.Return(contestLeaderboard, err)
	return _c
}

func (_c *MockQueryResolver_GetContestLeaderboard_Call) RunAndReturn(run func(ctx context.Context, contestID primitive.ObjectID, pageNumber *int, pageSize *int) (*models.ContestLeaderboard, error)) *MockQueryResolver_GetContestLeaderboard_Call {
	_c.Call.Return(run)
	return _c
}

// GetContestsByStatus provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetContestsByStatus(ctx context.Context, statuses []models.ContestStatus, page *int, pageSize *int, sortDirection *string) (*models.PaginatedContests, error) {
	ret := _mock.Called(ctx, statuses, page, pageSize, sortDirection)

	if len(ret) == 0 {
		panic("no return value specified for GetContestsByStatus")
	}

	var r0 *models.PaginatedContests
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, []models.ContestStatus, *int, *int, *string) (*models.PaginatedContests, error)); ok {
		return returnFunc(ctx, statuses, page, pageSize, sortDirection)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, []models.ContestStatus, *int, *int, *string) *models.PaginatedContests); ok {
		r0 = returnFunc(ctx, statuses, page, pageSize, sortDirection)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PaginatedContests)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, []models.ContestStatus, *int, *int, *string) error); ok {
		r1 = returnFunc(ctx, statuses, page, pageSize, sortDirection)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetContestsByStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetContestsByStatus'
type MockQueryResolver_GetContestsByStatus_Call struct {
	*mock.Call
}

// GetContestsByStatus is a helper method to define mock.On call
//   - ctx context.Context
//   - statuses []models.ContestStatus
//   - page *int
//   - pageSize *int
//   - sortDirection *string
func (_e *MockQueryResolver_Expecter) GetContestsByStatus(ctx interface{}, statuses interface{}, page interface{}, pageSize interface{}, sortDirection interface{}) *MockQueryResolver_GetContestsByStatus_Call {
	return &MockQueryResolver_GetContestsByStatus_Call{Call: _e.mock.On("GetContestsByStatus", ctx, statuses, page, pageSize, sortDirection)}
}

func (_c *MockQueryResolver_GetContestsByStatus_Call) Run(run func(ctx context.Context, statuses []models.ContestStatus, page *int, pageSize *int, sortDirection *string)) *MockQueryResolver_GetContestsByStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 []models.ContestStatus
		if args[1] != nil {
			arg1 = args[1].([]models.ContestStatus)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		var arg3 *int
		if args[3] != nil {
			arg3 = args[3].(*int)
		}
		var arg4 *string
		if args[4] != nil {
			arg4 = args[4].(*string)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
			arg4,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetContestsByStatus_Call) Return(paginatedContests *models.PaginatedContests, err error) *MockQueryResolver_GetContestsByStatus_Call {
	_c.Call.Return(paginatedContests, err)
	return _c
}

func (_c *MockQueryResolver_GetContestsByStatus_Call) RunAndReturn(run func(ctx context.Context, statuses []models.ContestStatus, page *int, pageSize *int, sortDirection *string) (*models.PaginatedContests, error)) *MockQueryResolver_GetContestsByStatus_Call {
	_c.Call.Return(run)
	return _c
}

// GetCurrentUser provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetCurrentUser(ctx context.Context) (*models.User, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetCurrentUser")
	}

	var r0 *models.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*models.User, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *models.User); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetCurrentUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetCurrentUser'
type MockQueryResolver_GetCurrentUser_Call struct {
	*mock.Call
}

// GetCurrentUser is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockQueryResolver_Expecter) GetCurrentUser(ctx interface{}) *MockQueryResolver_GetCurrentUser_Call {
	return &MockQueryResolver_GetCurrentUser_Call{Call: _e.mock.On("GetCurrentUser", ctx)}
}

func (_c *MockQueryResolver_GetCurrentUser_Call) Run(run func(ctx context.Context)) *MockQueryResolver_GetCurrentUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetCurrentUser_Call) Return(user *models.User, err error) *MockQueryResolver_GetCurrentUser_Call {
	_c.Call.Return(user, err)
	return _c
}

func (_c *MockQueryResolver_GetCurrentUser_Call) RunAndReturn(run func(ctx context.Context) (*models.User, error)) *MockQueryResolver_GetCurrentUser_Call {
	_c.Call.Return(run)
	return _c
}

// GetDailyChallenge provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetDailyChallenge(ctx context.Context) (*models.DailyChallenge, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetDailyChallenge")
	}

	var r0 *models.DailyChallenge
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*models.DailyChallenge, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *models.DailyChallenge); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.DailyChallenge)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetDailyChallenge_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDailyChallenge'
type MockQueryResolver_GetDailyChallenge_Call struct {
	*mock.Call
}

// GetDailyChallenge is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockQueryResolver_Expecter) GetDailyChallenge(ctx interface{}) *MockQueryResolver_GetDailyChallenge_Call {
	return &MockQueryResolver_GetDailyChallenge_Call{Call: _e.mock.On("GetDailyChallenge", ctx)}
}

func (_c *MockQueryResolver_GetDailyChallenge_Call) Run(run func(ctx context.Context)) *MockQueryResolver_GetDailyChallenge_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetDailyChallenge_Call) Return(dailyChallenge *models.DailyChallenge, err error) *MockQueryResolver_GetDailyChallenge_Call {
	_c.Call.Return(dailyChallenge, err)
	return _c
}

func (_c *MockQueryResolver_GetDailyChallenge_Call) RunAndReturn(run func(ctx context.Context) (*models.DailyChallenge, error)) *MockQueryResolver_GetDailyChallenge_Call {
	_c.Call.Return(run)
	return _c
}

// GetDailyChallengeByID provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetDailyChallengeByID(ctx context.Context, id primitive.ObjectID) (*models.DailyChallenge, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetDailyChallengeByID")
	}

	var r0 *models.DailyChallenge
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.DailyChallenge, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.DailyChallenge); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.DailyChallenge)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetDailyChallengeByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDailyChallengeByID'
type MockQueryResolver_GetDailyChallengeByID_Call struct {
	*mock.Call
}

// GetDailyChallengeByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id primitive.ObjectID
func (_e *MockQueryResolver_Expecter) GetDailyChallengeByID(ctx interface{}, id interface{}) *MockQueryResolver_GetDailyChallengeByID_Call {
	return &MockQueryResolver_GetDailyChallengeByID_Call{Call: _e.mock.On("GetDailyChallengeByID", ctx, id)}
}

func (_c *MockQueryResolver_GetDailyChallengeByID_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockQueryResolver_GetDailyChallengeByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetDailyChallengeByID_Call) Return(dailyChallenge *models.DailyChallenge, err error) *MockQueryResolver_GetDailyChallengeByID_Call {
	_c.Call.Return(dailyChallenge, err)
	return _c
}

func (_c *MockQueryResolver_GetDailyChallengeByID_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (*models.DailyChallenge, error)) *MockQueryResolver_GetDailyChallengeByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetDailyChallengeLeaderboard provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetDailyChallengeLeaderboard(ctx context.Context, challengeNumber *int, pageNumber *int, pageSize *int) (*models.LeaderboardPage, error) {
	ret := _mock.Called(ctx, challengeNumber, pageNumber, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetDailyChallengeLeaderboard")
	}

	var r0 *models.LeaderboardPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int, *int) (*models.LeaderboardPage, error)); ok {
		return returnFunc(ctx, challengeNumber, pageNumber, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int, *int) *models.LeaderboardPage); ok {
		r0 = returnFunc(ctx, challengeNumber, pageNumber, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.LeaderboardPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *int, *int, *int) error); ok {
		r1 = returnFunc(ctx, challengeNumber, pageNumber, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetDailyChallengeLeaderboard_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDailyChallengeLeaderboard'
type MockQueryResolver_GetDailyChallengeLeaderboard_Call struct {
	*mock.Call
}

// GetDailyChallengeLeaderboard is a helper method to define mock.On call
//   - ctx context.Context
//   - challengeNumber *int
//   - pageNumber *int
//   - pageSize *int
func (_e *MockQueryResolver_Expecter) GetDailyChallengeLeaderboard(ctx interface{}, challengeNumber interface{}, pageNumber interface{}, pageSize interface{}) *MockQueryResolver_GetDailyChallengeLeaderboard_Call {
	return &MockQueryResolver_GetDailyChallengeLeaderboard_Call{Call: _e.mock.On("GetDailyChallengeLeaderboard", ctx, challengeNumber, pageNumber, pageSize)}
}

func (_c *MockQueryResolver_GetDailyChallengeLeaderboard_Call) Run(run func(ctx context.Context, challengeNumber *int, pageNumber *int, pageSize *int)) *MockQueryResolver_GetDailyChallengeLeaderboard_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *int
		if args[1] != nil {
			arg1 = args[1].(*int)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		var arg3 *int
		if args[3] != nil {
			arg3 = args[3].(*int)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetDailyChallengeLeaderboard_Call) Return(leaderboardPage *models.LeaderboardPage, err error) *MockQueryResolver_GetDailyChallengeLeaderboard_Call {
	_c.Call.Return(leaderboardPage, err)
	return _c
}

func (_c *MockQueryResolver_GetDailyChallengeLeaderboard_Call) RunAndReturn(run func(ctx context.Context, challengeNumber *int, pageNumber *int, pageSize *int) (*models.LeaderboardPage, error)) *MockQueryResolver_GetDailyChallengeLeaderboard_Call {
	_c.Call.Return(run)
	return _c
}

// GetDailyChallengeLeaderboardByDivision provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetDailyChallengeLeaderboardByDivision(ctx context.Context, dateStr *string, division *models.ChallengeDivision, pageNumber *int, pageSize *int) (*models.LeaderboardPage, error) {
	ret := _mock.Called(ctx, dateStr, division, pageNumber, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetDailyChallengeLeaderboardByDivision")
	}

	var r0 *models.LeaderboardPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *models.ChallengeDivision, *int, *int) (*models.LeaderboardPage, error)); ok {
		return returnFunc(ctx, dateStr, division, pageNumber, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *models.ChallengeDivision, *int, *int) *models.LeaderboardPage); ok {
		r0 = returnFunc(ctx, dateStr, division, pageNumber, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.LeaderboardPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string, *models.ChallengeDivision, *int, *int) error); ok {
		r1 = returnFunc(ctx, dateStr, division, pageNumber, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetDailyChallengeLeaderboardByDivision_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDailyChallengeLeaderboardByDivision'
type MockQueryResolver_GetDailyChallengeLeaderboardByDivision_Call struct {
	*mock.Call
}

// GetDailyChallengeLeaderboardByDivision is a helper method to define mock.On call
//   - ctx context.Context
//   - dateStr *string
//   - division *models.ChallengeDivision
//   - pageNumber *int
//   - pageSize *int
func (_e *MockQueryResolver_Expecter) GetDailyChallengeLeaderboardByDivision(ctx interface{}, dateStr interface{}, division interface{}, pageNumber interface{}, pageSize interface{}) *MockQueryResolver_GetDailyChallengeLeaderboardByDivision_Call {
	return &MockQueryResolver_GetDailyChallengeLeaderboardByDivision_Call{Call: _e.mock.On("GetDailyChallengeLeaderboardByDivision", ctx, dateStr, division, pageNumber, pageSize)}
}

func (_c *MockQueryResolver_GetDailyChallengeLeaderboardByDivision_Call) Run(run func(ctx context.Context, dateStr *string, division *models.ChallengeDivision, pageNumber *int, pageSize *int)) *MockQueryResolver_GetDailyChallengeLeaderboardByDivision_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *string
		if args[1] != nil {
			arg1 = args[1].(*string)
		}
		var arg2 *models.ChallengeDivision
		if args[2] != nil {
			arg2 = args[2].(*models.ChallengeDivision)
		}
		var arg3 *int
		if args[3] != nil {
			arg3 = args[3].(*int)
		}
		var arg4 *int
		if args[4] != nil {
			arg4 = args[4].(*int)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
			arg4,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetDailyChallengeLeaderboardByDivision_Call) Return(leaderboardPage *models.LeaderboardPage, err error) *MockQueryResolver_GetDailyChallengeLeaderboardByDivision_Call {
	_c.Call.Return(leaderboardPage, err)
	return _c
}

func (_c *MockQueryResolver_GetDailyChallengeLeaderboardByDivision_Call) RunAndReturn(run func(ctx context.Context, dateStr *string, division *models.ChallengeDivision, pageNumber *int, pageSize *int) (*models.LeaderboardPage, error)) *MockQueryResolver_GetDailyChallengeLeaderboardByDivision_Call {
	_c.Call.Return(run)
	return _c
}

// GetDailyChallengeLeaderboardByDivison provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetDailyChallengeLeaderboardByDivison(ctx context.Context, dateStr *string, division *models.ChallengeDivision, pageNumber *int, pageSize *int) (*models.LeaderboardPage, error) {
	ret := _mock.Called(ctx, dateStr, division, pageNumber, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetDailyChallengeLeaderboardByDivison")
	}

	var r0 *models.LeaderboardPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *models.ChallengeDivision, *int, *int) (*models.LeaderboardPage, error)); ok {
		return returnFunc(ctx, dateStr, division, pageNumber, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *models.ChallengeDivision, *int, *int) *models.LeaderboardPage); ok {
		r0 = returnFunc(ctx, dateStr, division, pageNumber, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.LeaderboardPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string, *models.ChallengeDivision, *int, *int) error); ok {
		r1 = returnFunc(ctx, dateStr, division, pageNumber, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetDailyChallengeLeaderboardByDivison_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDailyChallengeLeaderboardByDivison'
type MockQueryResolver_GetDailyChallengeLeaderboardByDivison_Call struct {
	*mock.Call
}

// GetDailyChallengeLeaderboardByDivison is a helper method to define mock.On call
//   - ctx context.Context
//   - dateStr *string
//   - division *models.ChallengeDivision
//   - pageNumber *int
//   - pageSize *int
func (_e *MockQueryResolver_Expecter) GetDailyChallengeLeaderboardByDivison(ctx interface{}, dateStr interface{}, division interface{}, pageNumber interface{}, pageSize interface{}) *MockQueryResolver_GetDailyChallengeLeaderboardByDivison_Call {
	return &MockQueryResolver_GetDailyChallengeLeaderboardByDivison_Call{Call: _e.mock.On("GetDailyChallengeLeaderboardByDivison", ctx, dateStr, division, pageNumber, pageSize)}
}

func (_c *MockQueryResolver_GetDailyChallengeLeaderboardByDivison_Call) Run(run func(ctx context.Context, dateStr *string, division *models.ChallengeDivision, pageNumber *int, pageSize *int)) *MockQueryResolver_GetDailyChallengeLeaderboardByDivison_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *string
		if args[1] != nil {
			arg1 = args[1].(*string)
		}
		var arg2 *models.ChallengeDivision
		if args[2] != nil {
			arg2 = args[2].(*models.ChallengeDivision)
		}
		var arg3 *int
		if args[3] != nil {
			arg3 = args[3].(*int)
		}
		var arg4 *int
		if args[4] != nil {
			arg4 = args[4].(*int)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
			arg4,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetDailyChallengeLeaderboardByDivison_Call) Return(leaderboardPage *models.LeaderboardPage, err error) *MockQueryResolver_GetDailyChallengeLeaderboardByDivison_Call {
	_c.Call.Return(leaderboardPage, err)
	return _c
}

func (_c *MockQueryResolver_GetDailyChallengeLeaderboardByDivison_Call) RunAndReturn(run func(ctx context.Context, dateStr *string, division *models.ChallengeDivision, pageNumber *int, pageSize *int) (*models.LeaderboardPage, error)) *MockQueryResolver_GetDailyChallengeLeaderboardByDivison_Call {
	_c.Call.Return(run)
	return _c
}

// GetDailyChallenges provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetDailyChallenges(ctx context.Context) ([]*models.DailyChallenge, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetDailyChallenges")
	}

	var r0 []*models.DailyChallenge
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) ([]*models.DailyChallenge, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) []*models.DailyChallenge); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.DailyChallenge)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetDailyChallenges_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDailyChallenges'
type MockQueryResolver_GetDailyChallenges_Call struct {
	*mock.Call
}

// GetDailyChallenges is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockQueryResolver_Expecter) GetDailyChallenges(ctx interface{}) *MockQueryResolver_GetDailyChallenges_Call {
	return &MockQueryResolver_GetDailyChallenges_Call{Call: _e.mock.On("GetDailyChallenges", ctx)}
}

func (_c *MockQueryResolver_GetDailyChallenges_Call) Run(run func(ctx context.Context)) *MockQueryResolver_GetDailyChallenges_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetDailyChallenges_Call) Return(dailyChallenges []*models.DailyChallenge, err error) *MockQueryResolver_GetDailyChallenges_Call {
	_c.Call.Return(dailyChallenges, err)
	return _c
}

func (_c *MockQueryResolver_GetDailyChallenges_Call) RunAndReturn(run func(ctx context.Context) ([]*models.DailyChallenge, error)) *MockQueryResolver_GetDailyChallenges_Call {
	_c.Call.Return(run)
	return _c
}

// GetDailyPuzzle provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetDailyPuzzle(ctx context.Context, date string) (*models.Puzzle, error) {
	ret := _mock.Called(ctx, date)

	if len(ret) == 0 {
		panic("no return value specified for GetDailyPuzzle")
	}

	var r0 *models.Puzzle
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (*models.Puzzle, error)); ok {
		return returnFunc(ctx, date)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) *models.Puzzle); ok {
		r0 = returnFunc(ctx, date)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Puzzle)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, date)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetDailyPuzzle_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDailyPuzzle'
type MockQueryResolver_GetDailyPuzzle_Call struct {
	*mock.Call
}

// GetDailyPuzzle is a helper method to define mock.On call
//   - ctx context.Context
//   - date string
func (_e *MockQueryResolver_Expecter) GetDailyPuzzle(ctx interface{}, date interface{}) *MockQueryResolver_GetDailyPuzzle_Call {
	return &MockQueryResolver_GetDailyPuzzle_Call{Call: _e.mock.On("GetDailyPuzzle", ctx, date)}
}

func (_c *MockQueryResolver_GetDailyPuzzle_Call) Run(run func(ctx context.Context, date string)) *MockQueryResolver_GetDailyPuzzle_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetDailyPuzzle_Call) Return(puzzle *models.Puzzle, err error) *MockQueryResolver_GetDailyPuzzle_Call {
	_c.Call.Return(puzzle, err)
	return _c
}

func (_c *MockQueryResolver_GetDailyPuzzle_Call) RunAndReturn(run func(ctx context.Context, date string) (*models.Puzzle, error)) *MockQueryResolver_GetDailyPuzzle_Call {
	_c.Call.Return(run)
	return _c
}

// GetDailyPuzzleByType provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetDailyPuzzleByType(ctx context.Context, date string, puzzleType models.PuzzleType) (*models.Puzzle, error) {
	ret := _mock.Called(ctx, date, puzzleType)

	if len(ret) == 0 {
		panic("no return value specified for GetDailyPuzzleByType")
	}

	var r0 *models.Puzzle
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, models.PuzzleType) (*models.Puzzle, error)); ok {
		return returnFunc(ctx, date, puzzleType)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, models.PuzzleType) *models.Puzzle); ok {
		r0 = returnFunc(ctx, date, puzzleType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Puzzle)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, models.PuzzleType) error); ok {
		r1 = returnFunc(ctx, date, puzzleType)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetDailyPuzzleByType_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDailyPuzzleByType'
type MockQueryResolver_GetDailyPuzzleByType_Call struct {
	*mock.Call
}

// GetDailyPuzzleByType is a helper method to define mock.On call
//   - ctx context.Context
//   - date string
//   - puzzleType models.PuzzleType
func (_e *MockQueryResolver_Expecter) GetDailyPuzzleByType(ctx interface{}, date interface{}, puzzleType interface{}) *MockQueryResolver_GetDailyPuzzleByType_Call {
	return &MockQueryResolver_GetDailyPuzzleByType_Call{Call: _e.mock.On("GetDailyPuzzleByType", ctx, date, puzzleType)}
}

func (_c *MockQueryResolver_GetDailyPuzzleByType_Call) Run(run func(ctx context.Context, date string, puzzleType models.PuzzleType)) *MockQueryResolver_GetDailyPuzzleByType_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		var arg2 models.PuzzleType
		if args[2] != nil {
			arg2 = args[2].(models.PuzzleType)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetDailyPuzzleByType_Call) Return(puzzle *models.Puzzle, err error) *MockQueryResolver_GetDailyPuzzleByType_Call {
	_c.Call.Return(puzzle, err)
	return _c
}

func (_c *MockQueryResolver_GetDailyPuzzleByType_Call) RunAndReturn(run func(ctx context.Context, date string, puzzleType models.PuzzleType) (*models.Puzzle, error)) *MockQueryResolver_GetDailyPuzzleByType_Call {
	_c.Call.Return(run)
	return _c
}

// GetFeaturedContests provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetFeaturedContests(ctx context.Context) ([]*models.Contest, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetFeaturedContests")
	}

	var r0 []*models.Contest
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) ([]*models.Contest, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) []*models.Contest); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.Contest)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetFeaturedContests_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFeaturedContests'
type MockQueryResolver_GetFeaturedContests_Call struct {
	*mock.Call
}

// GetFeaturedContests is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockQueryResolver_Expecter) GetFeaturedContests(ctx interface{}) *MockQueryResolver_GetFeaturedContests_Call {
	return &MockQueryResolver_GetFeaturedContests_Call{Call: _e.mock.On("GetFeaturedContests", ctx)}
}

func (_c *MockQueryResolver_GetFeaturedContests_Call) Run(run func(ctx context.Context)) *MockQueryResolver_GetFeaturedContests_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetFeaturedContests_Call) Return(contests []*models.Contest, err error) *MockQueryResolver_GetFeaturedContests_Call {
	_c.Call.Return(contests, err)
	return _c
}

func (_c *MockQueryResolver_GetFeaturedContests_Call) RunAndReturn(run func(ctx context.Context) ([]*models.Contest, error)) *MockQueryResolver_GetFeaturedContests_Call {
	_c.Call.Return(run)
	return _c
}

// GetFeaturedShowdown provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetFeaturedShowdown(ctx context.Context) ([]*models.Showdown, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetFeaturedShowdown")
	}

	var r0 []*models.Showdown
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) ([]*models.Showdown, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) []*models.Showdown); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.Showdown)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetFeaturedShowdown_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFeaturedShowdown'
type MockQueryResolver_GetFeaturedShowdown_Call struct {
	*mock.Call
}

// GetFeaturedShowdown is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockQueryResolver_Expecter) GetFeaturedShowdown(ctx interface{}) *MockQueryResolver_GetFeaturedShowdown_Call {
	return &MockQueryResolver_GetFeaturedShowdown_Call{Call: _e.mock.On("GetFeaturedShowdown", ctx)}
}

func (_c *MockQueryResolver_GetFeaturedShowdown_Call) Run(run func(ctx context.Context)) *MockQueryResolver_GetFeaturedShowdown_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetFeaturedShowdown_Call) Return(showdowns []*models.Showdown, err error) *MockQueryResolver_GetFeaturedShowdown_Call {
	_c.Call.Return(showdowns, err)
	return _c
}

func (_c *MockQueryResolver_GetFeaturedShowdown_Call) RunAndReturn(run func(ctx context.Context) ([]*models.Showdown, error)) *MockQueryResolver_GetFeaturedShowdown_Call {
	_c.Call.Return(run)
	return _c
}

// GetFicturesByShowdownID provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetFicturesByShowdownID(ctx context.Context, showdownID primitive.ObjectID) (*models.FicturesCollection, error) {
	ret := _mock.Called(ctx, showdownID)

	if len(ret) == 0 {
		panic("no return value specified for GetFicturesByShowdownID")
	}

	var r0 *models.FicturesCollection
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.FicturesCollection, error)); ok {
		return returnFunc(ctx, showdownID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.FicturesCollection); ok {
		r0 = returnFunc(ctx, showdownID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.FicturesCollection)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, showdownID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetFicturesByShowdownID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFicturesByShowdownID'
type MockQueryResolver_GetFicturesByShowdownID_Call struct {
	*mock.Call
}

// GetFicturesByShowdownID is a helper method to define mock.On call
//   - ctx context.Context
//   - showdownID primitive.ObjectID
func (_e *MockQueryResolver_Expecter) GetFicturesByShowdownID(ctx interface{}, showdownID interface{}) *MockQueryResolver_GetFicturesByShowdownID_Call {
	return &MockQueryResolver_GetFicturesByShowdownID_Call{Call: _e.mock.On("GetFicturesByShowdownID", ctx, showdownID)}
}

func (_c *MockQueryResolver_GetFicturesByShowdownID_Call) Run(run func(ctx context.Context, showdownID primitive.ObjectID)) *MockQueryResolver_GetFicturesByShowdownID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetFicturesByShowdownID_Call) Return(ficturesCollection *models.FicturesCollection, err error) *MockQueryResolver_GetFicturesByShowdownID_Call {
	_c.Call.Return(ficturesCollection, err)
	return _c
}

func (_c *MockQueryResolver_GetFicturesByShowdownID_Call) RunAndReturn(run func(ctx context.Context, showdownID primitive.ObjectID) (*models.FicturesCollection, error)) *MockQueryResolver_GetFicturesByShowdownID_Call {
	_c.Call.Return(run)
	return _c
}

// GetFollowers provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetFollowers(ctx context.Context, page *int, pageSize *int) (*models.FollowersAndFolloweePage, error) {
	ret := _mock.Called(ctx, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetFollowers")
	}

	var r0 *models.FollowersAndFolloweePage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int) (*models.FollowersAndFolloweePage, error)); ok {
		return returnFunc(ctx, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int) *models.FollowersAndFolloweePage); ok {
		r0 = returnFunc(ctx, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.FollowersAndFolloweePage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *int, *int) error); ok {
		r1 = returnFunc(ctx, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetFollowers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFollowers'
type MockQueryResolver_GetFollowers_Call struct {
	*mock.Call
}

// GetFollowers is a helper method to define mock.On call
//   - ctx context.Context
//   - page *int
//   - pageSize *int
func (_e *MockQueryResolver_Expecter) GetFollowers(ctx interface{}, page interface{}, pageSize interface{}) *MockQueryResolver_GetFollowers_Call {
	return &MockQueryResolver_GetFollowers_Call{Call: _e.mock.On("GetFollowers", ctx, page, pageSize)}
}

func (_c *MockQueryResolver_GetFollowers_Call) Run(run func(ctx context.Context, page *int, pageSize *int)) *MockQueryResolver_GetFollowers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *int
		if args[1] != nil {
			arg1 = args[1].(*int)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetFollowers_Call) Return(followersAndFolloweePage *models.FollowersAndFolloweePage, err error) *MockQueryResolver_GetFollowers_Call {
	_c.Call.Return(followersAndFolloweePage, err)
	return _c
}

func (_c *MockQueryResolver_GetFollowers_Call) RunAndReturn(run func(ctx context.Context, page *int, pageSize *int) (*models.FollowersAndFolloweePage, error)) *MockQueryResolver_GetFollowers_Call {
	_c.Call.Return(run)
	return _c
}

// GetFollowings provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetFollowings(ctx context.Context, page *int, pageSize *int) (*models.FollowersAndFolloweePage, error) {
	ret := _mock.Called(ctx, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetFollowings")
	}

	var r0 *models.FollowersAndFolloweePage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int) (*models.FollowersAndFolloweePage, error)); ok {
		return returnFunc(ctx, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int) *models.FollowersAndFolloweePage); ok {
		r0 = returnFunc(ctx, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.FollowersAndFolloweePage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *int, *int) error); ok {
		r1 = returnFunc(ctx, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetFollowings_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFollowings'
type MockQueryResolver_GetFollowings_Call struct {
	*mock.Call
}

// GetFollowings is a helper method to define mock.On call
//   - ctx context.Context
//   - page *int
//   - pageSize *int
func (_e *MockQueryResolver_Expecter) GetFollowings(ctx interface{}, page interface{}, pageSize interface{}) *MockQueryResolver_GetFollowings_Call {
	return &MockQueryResolver_GetFollowings_Call{Call: _e.mock.On("GetFollowings", ctx, page, pageSize)}
}

func (_c *MockQueryResolver_GetFollowings_Call) Run(run func(ctx context.Context, page *int, pageSize *int)) *MockQueryResolver_GetFollowings_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *int
		if args[1] != nil {
			arg1 = args[1].(*int)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetFollowings_Call) Return(followersAndFolloweePage *models.FollowersAndFolloweePage, err error) *MockQueryResolver_GetFollowings_Call {
	_c.Call.Return(followersAndFolloweePage, err)
	return _c
}

func (_c *MockQueryResolver_GetFollowings_Call) RunAndReturn(run func(ctx context.Context, page *int, pageSize *int) (*models.FollowersAndFolloweePage, error)) *MockQueryResolver_GetFollowings_Call {
	_c.Call.Return(run)
	return _c
}

// GetFriends provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetFriends(ctx context.Context, page *int, pageSize *int, sortOption *models.SortOptions) (*models.FriendsPage, error) {
	ret := _mock.Called(ctx, page, pageSize, sortOption)

	if len(ret) == 0 {
		panic("no return value specified for GetFriends")
	}

	var r0 *models.FriendsPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int, *models.SortOptions) (*models.FriendsPage, error)); ok {
		return returnFunc(ctx, page, pageSize, sortOption)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int, *models.SortOptions) *models.FriendsPage); ok {
		r0 = returnFunc(ctx, page, pageSize, sortOption)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.FriendsPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *int, *int, *models.SortOptions) error); ok {
		r1 = returnFunc(ctx, page, pageSize, sortOption)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetFriends_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFriends'
type MockQueryResolver_GetFriends_Call struct {
	*mock.Call
}

// GetFriends is a helper method to define mock.On call
//   - ctx context.Context
//   - page *int
//   - pageSize *int
//   - sortOption *models.SortOptions
func (_e *MockQueryResolver_Expecter) GetFriends(ctx interface{}, page interface{}, pageSize interface{}, sortOption interface{}) *MockQueryResolver_GetFriends_Call {
	return &MockQueryResolver_GetFriends_Call{Call: _e.mock.On("GetFriends", ctx, page, pageSize, sortOption)}
}

func (_c *MockQueryResolver_GetFriends_Call) Run(run func(ctx context.Context, page *int, pageSize *int, sortOption *models.SortOptions)) *MockQueryResolver_GetFriends_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *int
		if args[1] != nil {
			arg1 = args[1].(*int)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		var arg3 *models.SortOptions
		if args[3] != nil {
			arg3 = args[3].(*models.SortOptions)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetFriends_Call) Return(friendsPage *models.FriendsPage, err error) *MockQueryResolver_GetFriends_Call {
	_c.Call.Return(friendsPage, err)
	return _c
}

func (_c *MockQueryResolver_GetFriends_Call) RunAndReturn(run func(ctx context.Context, page *int, pageSize *int, sortOption *models.SortOptions) (*models.FriendsPage, error)) *MockQueryResolver_GetFriends_Call {
	_c.Call.Return(run)
	return _c
}

// GetFriendsLeaderboard provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetFriendsLeaderboard(ctx context.Context, page *int, pageSize *int, ratingType *string) (*models.UserLeaderboardPage, error) {
	ret := _mock.Called(ctx, page, pageSize, ratingType)

	if len(ret) == 0 {
		panic("no return value specified for GetFriendsLeaderboard")
	}

	var r0 *models.UserLeaderboardPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int, *string) (*models.UserLeaderboardPage, error)); ok {
		return returnFunc(ctx, page, pageSize, ratingType)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int, *string) *models.UserLeaderboardPage); ok {
		r0 = returnFunc(ctx, page, pageSize, ratingType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserLeaderboardPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *int, *int, *string) error); ok {
		r1 = returnFunc(ctx, page, pageSize, ratingType)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetFriendsLeaderboard_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFriendsLeaderboard'
type MockQueryResolver_GetFriendsLeaderboard_Call struct {
	*mock.Call
}

// GetFriendsLeaderboard is a helper method to define mock.On call
//   - ctx context.Context
//   - page *int
//   - pageSize *int
//   - ratingType *string
func (_e *MockQueryResolver_Expecter) GetFriendsLeaderboard(ctx interface{}, page interface{}, pageSize interface{}, ratingType interface{}) *MockQueryResolver_GetFriendsLeaderboard_Call {
	return &MockQueryResolver_GetFriendsLeaderboard_Call{Call: _e.mock.On("GetFriendsLeaderboard", ctx, page, pageSize, ratingType)}
}

func (_c *MockQueryResolver_GetFriendsLeaderboard_Call) Run(run func(ctx context.Context, page *int, pageSize *int, ratingType *string)) *MockQueryResolver_GetFriendsLeaderboard_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *int
		if args[1] != nil {
			arg1 = args[1].(*int)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		var arg3 *string
		if args[3] != nil {
			arg3 = args[3].(*string)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetFriendsLeaderboard_Call) Return(userLeaderboardPage *models.UserLeaderboardPage, err error) *MockQueryResolver_GetFriendsLeaderboard_Call {
	_c.Call.Return(userLeaderboardPage, err)
	return _c
}

func (_c *MockQueryResolver_GetFriendsLeaderboard_Call) RunAndReturn(run func(ctx context.Context, page *int, pageSize *int, ratingType *string) (*models.UserLeaderboardPage, error)) *MockQueryResolver_GetFriendsLeaderboard_Call {
	_c.Call.Return(run)
	return _c
}

// GetFriendsTop5CrossMathPuzzleRushStats provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetFriendsTop5CrossMathPuzzleRushStats(ctx context.Context) ([]*models.CrossMathPuzzleRushPlayerInfo, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetFriendsTop5CrossMathPuzzleRushStats")
	}

	var r0 []*models.CrossMathPuzzleRushPlayerInfo
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) ([]*models.CrossMathPuzzleRushPlayerInfo, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) []*models.CrossMathPuzzleRushPlayerInfo); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.CrossMathPuzzleRushPlayerInfo)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetFriendsTop5CrossMathPuzzleRushStats_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFriendsTop5CrossMathPuzzleRushStats'
type MockQueryResolver_GetFriendsTop5CrossMathPuzzleRushStats_Call struct {
	*mock.Call
}

// GetFriendsTop5CrossMathPuzzleRushStats is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockQueryResolver_Expecter) GetFriendsTop5CrossMathPuzzleRushStats(ctx interface{}) *MockQueryResolver_GetFriendsTop5CrossMathPuzzleRushStats_Call {
	return &MockQueryResolver_GetFriendsTop5CrossMathPuzzleRushStats_Call{Call: _e.mock.On("GetFriendsTop5CrossMathPuzzleRushStats", ctx)}
}

func (_c *MockQueryResolver_GetFriendsTop5CrossMathPuzzleRushStats_Call) Run(run func(ctx context.Context)) *MockQueryResolver_GetFriendsTop5CrossMathPuzzleRushStats_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetFriendsTop5CrossMathPuzzleRushStats_Call) Return(crossMathPuzzleRushPlayerInfos []*models.CrossMathPuzzleRushPlayerInfo, err error) *MockQueryResolver_GetFriendsTop5CrossMathPuzzleRushStats_Call {
	_c.Call.Return(crossMathPuzzleRushPlayerInfos, err)
	return _c
}

func (_c *MockQueryResolver_GetFriendsTop5CrossMathPuzzleRushStats_Call) RunAndReturn(run func(ctx context.Context) ([]*models.CrossMathPuzzleRushPlayerInfo, error)) *MockQueryResolver_GetFriendsTop5CrossMathPuzzleRushStats_Call {
	_c.Call.Return(run)
	return _c
}

// GetFriendsTopPlayers provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetFriendsTopPlayers(ctx context.Context) (*models.TopPlayersLeaderboard, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetFriendsTopPlayers")
	}

	var r0 *models.TopPlayersLeaderboard
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*models.TopPlayersLeaderboard, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *models.TopPlayersLeaderboard); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.TopPlayersLeaderboard)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetFriendsTopPlayers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFriendsTopPlayers'
type MockQueryResolver_GetFriendsTopPlayers_Call struct {
	*mock.Call
}

// GetFriendsTopPlayers is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockQueryResolver_Expecter) GetFriendsTopPlayers(ctx interface{}) *MockQueryResolver_GetFriendsTopPlayers_Call {
	return &MockQueryResolver_GetFriendsTopPlayers_Call{Call: _e.mock.On("GetFriendsTopPlayers", ctx)}
}

func (_c *MockQueryResolver_GetFriendsTopPlayers_Call) Run(run func(ctx context.Context)) *MockQueryResolver_GetFriendsTopPlayers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetFriendsTopPlayers_Call) Return(topPlayersLeaderboard *models.TopPlayersLeaderboard, err error) *MockQueryResolver_GetFriendsTopPlayers_Call {
	_c.Call.Return(topPlayersLeaderboard, err)
	return _c
}

func (_c *MockQueryResolver_GetFriendsTopPlayers_Call) RunAndReturn(run func(ctx context.Context) (*models.TopPlayersLeaderboard, error)) *MockQueryResolver_GetFriendsTopPlayers_Call {
	_c.Call.Return(run)
	return _c
}

// GetGameByID provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetGameByID(ctx context.Context, gameID *primitive.ObjectID) (*models.Game, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for GetGameByID")
	}

	var r0 *models.Game
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *primitive.ObjectID) (*models.Game, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *primitive.ObjectID) *models.Game); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Game)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetGameByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetGameByID'
type MockQueryResolver_GetGameByID_Call struct {
	*mock.Call
}

// GetGameByID is a helper method to define mock.On call
//   - ctx context.Context
//   - gameID *primitive.ObjectID
func (_e *MockQueryResolver_Expecter) GetGameByID(ctx interface{}, gameID interface{}) *MockQueryResolver_GetGameByID_Call {
	return &MockQueryResolver_GetGameByID_Call{Call: _e.mock.On("GetGameByID", ctx, gameID)}
}

func (_c *MockQueryResolver_GetGameByID_Call) Run(run func(ctx context.Context, gameID *primitive.ObjectID)) *MockQueryResolver_GetGameByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(*primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetGameByID_Call) Return(game *models.Game, err error) *MockQueryResolver_GetGameByID_Call {
	_c.Call.Return(game, err)
	return _c
}

func (_c *MockQueryResolver_GetGameByID_Call) RunAndReturn(run func(ctx context.Context, gameID *primitive.ObjectID) (*models.Game, error)) *MockQueryResolver_GetGameByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetGameDetailedAnalysis provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetGameDetailedAnalysis(ctx context.Context, gameID *primitive.ObjectID) (*models.GameDetailedAnalysis, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for GetGameDetailedAnalysis")
	}

	var r0 *models.GameDetailedAnalysis
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *primitive.ObjectID) (*models.GameDetailedAnalysis, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *primitive.ObjectID) *models.GameDetailedAnalysis); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.GameDetailedAnalysis)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetGameDetailedAnalysis_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetGameDetailedAnalysis'
type MockQueryResolver_GetGameDetailedAnalysis_Call struct {
	*mock.Call
}

// GetGameDetailedAnalysis is a helper method to define mock.On call
//   - ctx context.Context
//   - gameID *primitive.ObjectID
func (_e *MockQueryResolver_Expecter) GetGameDetailedAnalysis(ctx interface{}, gameID interface{}) *MockQueryResolver_GetGameDetailedAnalysis_Call {
	return &MockQueryResolver_GetGameDetailedAnalysis_Call{Call: _e.mock.On("GetGameDetailedAnalysis", ctx, gameID)}
}

func (_c *MockQueryResolver_GetGameDetailedAnalysis_Call) Run(run func(ctx context.Context, gameID *primitive.ObjectID)) *MockQueryResolver_GetGameDetailedAnalysis_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(*primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetGameDetailedAnalysis_Call) Return(gameDetailedAnalysis *models.GameDetailedAnalysis, err error) *MockQueryResolver_GetGameDetailedAnalysis_Call {
	_c.Call.Return(gameDetailedAnalysis, err)
	return _c
}

func (_c *MockQueryResolver_GetGameDetailedAnalysis_Call) RunAndReturn(run func(ctx context.Context, gameID *primitive.ObjectID) (*models.GameDetailedAnalysis, error)) *MockQueryResolver_GetGameDetailedAnalysis_Call {
	_c.Call.Return(run)
	return _c
}

// GetGameSeriesByID provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetGameSeriesByID(ctx context.Context, gameSeriesID primitive.ObjectID) (*models.GameSeries, error) {
	ret := _mock.Called(ctx, gameSeriesID)

	if len(ret) == 0 {
		panic("no return value specified for GetGameSeriesByID")
	}

	var r0 *models.GameSeries
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.GameSeries, error)); ok {
		return returnFunc(ctx, gameSeriesID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.GameSeries); ok {
		r0 = returnFunc(ctx, gameSeriesID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.GameSeries)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameSeriesID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetGameSeriesByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetGameSeriesByID'
type MockQueryResolver_GetGameSeriesByID_Call struct {
	*mock.Call
}

// GetGameSeriesByID is a helper method to define mock.On call
//   - ctx context.Context
//   - gameSeriesID primitive.ObjectID
func (_e *MockQueryResolver_Expecter) GetGameSeriesByID(ctx interface{}, gameSeriesID interface{}) *MockQueryResolver_GetGameSeriesByID_Call {
	return &MockQueryResolver_GetGameSeriesByID_Call{Call: _e.mock.On("GetGameSeriesByID", ctx, gameSeriesID)}
}

func (_c *MockQueryResolver_GetGameSeriesByID_Call) Run(run func(ctx context.Context, gameSeriesID primitive.ObjectID)) *MockQueryResolver_GetGameSeriesByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetGameSeriesByID_Call) Return(gameSeries *models.GameSeries, err error) *MockQueryResolver_GetGameSeriesByID_Call {
	_c.Call.Return(gameSeries, err)
	return _c
}

func (_c *MockQueryResolver_GetGameSeriesByID_Call) RunAndReturn(run func(ctx context.Context, gameSeriesID primitive.ObjectID) (*models.GameSeries, error)) *MockQueryResolver_GetGameSeriesByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetGamesByUser provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetGamesByUser(ctx context.Context, payload *models.GetGamesInput) (*models.GetGamesOutput, error) {
	ret := _mock.Called(ctx, payload)

	if len(ret) == 0 {
		panic("no return value specified for GetGamesByUser")
	}

	var r0 *models.GetGamesOutput
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.GetGamesInput) (*models.GetGamesOutput, error)); ok {
		return returnFunc(ctx, payload)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.GetGamesInput) *models.GetGamesOutput); ok {
		r0 = returnFunc(ctx, payload)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.GetGamesOutput)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.GetGamesInput) error); ok {
		r1 = returnFunc(ctx, payload)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetGamesByUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetGamesByUser'
type MockQueryResolver_GetGamesByUser_Call struct {
	*mock.Call
}

// GetGamesByUser is a helper method to define mock.On call
//   - ctx context.Context
//   - payload *models.GetGamesInput
func (_e *MockQueryResolver_Expecter) GetGamesByUser(ctx interface{}, payload interface{}) *MockQueryResolver_GetGamesByUser_Call {
	return &MockQueryResolver_GetGamesByUser_Call{Call: _e.mock.On("GetGamesByUser", ctx, payload)}
}

func (_c *MockQueryResolver_GetGamesByUser_Call) Run(run func(ctx context.Context, payload *models.GetGamesInput)) *MockQueryResolver_GetGamesByUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.GetGamesInput
		if args[1] != nil {
			arg1 = args[1].(*models.GetGamesInput)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetGamesByUser_Call) Return(getGamesOutput *models.GetGamesOutput, err error) *MockQueryResolver_GetGamesByUser_Call {
	_c.Call.Return(getGamesOutput, err)
	return _c
}

func (_c *MockQueryResolver_GetGamesByUser_Call) RunAndReturn(run func(ctx context.Context, payload *models.GetGamesInput) (*models.GetGamesOutput, error)) *MockQueryResolver_GetGamesByUser_Call {
	_c.Call.Return(run)
	return _c
}

// GetGlobalPresets provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetGlobalPresets(ctx context.Context, page *int, pageSize *int) (*models.GlobalPresets, error) {
	ret := _mock.Called(ctx, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetGlobalPresets")
	}

	var r0 *models.GlobalPresets
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int) (*models.GlobalPresets, error)); ok {
		return returnFunc(ctx, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int) *models.GlobalPresets); ok {
		r0 = returnFunc(ctx, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.GlobalPresets)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *int, *int) error); ok {
		r1 = returnFunc(ctx, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetGlobalPresets_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetGlobalPresets'
type MockQueryResolver_GetGlobalPresets_Call struct {
	*mock.Call
}

// GetGlobalPresets is a helper method to define mock.On call
//   - ctx context.Context
//   - page *int
//   - pageSize *int
func (_e *MockQueryResolver_Expecter) GetGlobalPresets(ctx interface{}, page interface{}, pageSize interface{}) *MockQueryResolver_GetGlobalPresets_Call {
	return &MockQueryResolver_GetGlobalPresets_Call{Call: _e.mock.On("GetGlobalPresets", ctx, page, pageSize)}
}

func (_c *MockQueryResolver_GetGlobalPresets_Call) Run(run func(ctx context.Context, page *int, pageSize *int)) *MockQueryResolver_GetGlobalPresets_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *int
		if args[1] != nil {
			arg1 = args[1].(*int)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetGlobalPresets_Call) Return(globalPresets *models.GlobalPresets, err error) *MockQueryResolver_GetGlobalPresets_Call {
	_c.Call.Return(globalPresets, err)
	return _c
}

func (_c *MockQueryResolver_GetGlobalPresets_Call) RunAndReturn(run func(ctx context.Context, page *int, pageSize *int) (*models.GlobalPresets, error)) *MockQueryResolver_GetGlobalPresets_Call {
	_c.Call.Return(run)
	return _c
}

// GetGlobalPresetsByIdentifier provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetGlobalPresetsByIdentifier(ctx context.Context, identifier *string) (*models.GlobalPreset, error) {
	ret := _mock.Called(ctx, identifier)

	if len(ret) == 0 {
		panic("no return value specified for GetGlobalPresetsByIdentifier")
	}

	var r0 *models.GlobalPreset
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (*models.GlobalPreset, error)); ok {
		return returnFunc(ctx, identifier)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) *models.GlobalPreset); ok {
		r0 = returnFunc(ctx, identifier)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.GlobalPreset)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, identifier)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetGlobalPresetsByIdentifier_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetGlobalPresetsByIdentifier'
type MockQueryResolver_GetGlobalPresetsByIdentifier_Call struct {
	*mock.Call
}

// GetGlobalPresetsByIdentifier is a helper method to define mock.On call
//   - ctx context.Context
//   - identifier *string
func (_e *MockQueryResolver_Expecter) GetGlobalPresetsByIdentifier(ctx interface{}, identifier interface{}) *MockQueryResolver_GetGlobalPresetsByIdentifier_Call {
	return &MockQueryResolver_GetGlobalPresetsByIdentifier_Call{Call: _e.mock.On("GetGlobalPresetsByIdentifier", ctx, identifier)}
}

func (_c *MockQueryResolver_GetGlobalPresetsByIdentifier_Call) Run(run func(ctx context.Context, identifier *string)) *MockQueryResolver_GetGlobalPresetsByIdentifier_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *string
		if args[1] != nil {
			arg1 = args[1].(*string)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetGlobalPresetsByIdentifier_Call) Return(globalPreset *models.GlobalPreset, err error) *MockQueryResolver_GetGlobalPresetsByIdentifier_Call {
	_c.Call.Return(globalPreset, err)
	return _c
}

func (_c *MockQueryResolver_GetGlobalPresetsByIdentifier_Call) RunAndReturn(run func(ctx context.Context, identifier *string) (*models.GlobalPreset, error)) *MockQueryResolver_GetGlobalPresetsByIdentifier_Call {
	_c.Call.Return(run)
	return _c
}

// GetGlobalTop5CrossMathPuzzleRushStats provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetGlobalTop5CrossMathPuzzleRushStats(ctx context.Context) ([]*models.CrossMathPuzzleRushPlayerInfo, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetGlobalTop5CrossMathPuzzleRushStats")
	}

	var r0 []*models.CrossMathPuzzleRushPlayerInfo
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) ([]*models.CrossMathPuzzleRushPlayerInfo, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) []*models.CrossMathPuzzleRushPlayerInfo); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.CrossMathPuzzleRushPlayerInfo)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetGlobalTop5CrossMathPuzzleRushStats_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetGlobalTop5CrossMathPuzzleRushStats'
type MockQueryResolver_GetGlobalTop5CrossMathPuzzleRushStats_Call struct {
	*mock.Call
}

// GetGlobalTop5CrossMathPuzzleRushStats is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockQueryResolver_Expecter) GetGlobalTop5CrossMathPuzzleRushStats(ctx interface{}) *MockQueryResolver_GetGlobalTop5CrossMathPuzzleRushStats_Call {
	return &MockQueryResolver_GetGlobalTop5CrossMathPuzzleRushStats_Call{Call: _e.mock.On("GetGlobalTop5CrossMathPuzzleRushStats", ctx)}
}

func (_c *MockQueryResolver_GetGlobalTop5CrossMathPuzzleRushStats_Call) Run(run func(ctx context.Context)) *MockQueryResolver_GetGlobalTop5CrossMathPuzzleRushStats_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetGlobalTop5CrossMathPuzzleRushStats_Call) Return(crossMathPuzzleRushPlayerInfos []*models.CrossMathPuzzleRushPlayerInfo, err error) *MockQueryResolver_GetGlobalTop5CrossMathPuzzleRushStats_Call {
	_c.Call.Return(crossMathPuzzleRushPlayerInfos, err)
	return _c
}

func (_c *MockQueryResolver_GetGlobalTop5CrossMathPuzzleRushStats_Call) RunAndReturn(run func(ctx context.Context) ([]*models.CrossMathPuzzleRushPlayerInfo, error)) *MockQueryResolver_GetGlobalTop5CrossMathPuzzleRushStats_Call {
	_c.Call.Return(run)
	return _c
}

// GetGlobalTopPlayers provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetGlobalTopPlayers(ctx context.Context) (*models.TopPlayersLeaderboard, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetGlobalTopPlayers")
	}

	var r0 *models.TopPlayersLeaderboard
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*models.TopPlayersLeaderboard, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *models.TopPlayersLeaderboard); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.TopPlayersLeaderboard)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetGlobalTopPlayers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetGlobalTopPlayers'
type MockQueryResolver_GetGlobalTopPlayers_Call struct {
	*mock.Call
}

// GetGlobalTopPlayers is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockQueryResolver_Expecter) GetGlobalTopPlayers(ctx interface{}) *MockQueryResolver_GetGlobalTopPlayers_Call {
	return &MockQueryResolver_GetGlobalTopPlayers_Call{Call: _e.mock.On("GetGlobalTopPlayers", ctx)}
}

func (_c *MockQueryResolver_GetGlobalTopPlayers_Call) Run(run func(ctx context.Context)) *MockQueryResolver_GetGlobalTopPlayers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetGlobalTopPlayers_Call) Return(topPlayersLeaderboard *models.TopPlayersLeaderboard, err error) *MockQueryResolver_GetGlobalTopPlayers_Call {
	_c.Call.Return(topPlayersLeaderboard, err)
	return _c
}

func (_c *MockQueryResolver_GetGlobalTopPlayers_Call) RunAndReturn(run func(ctx context.Context) (*models.TopPlayersLeaderboard, error)) *MockQueryResolver_GetGlobalTopPlayers_Call {
	_c.Call.Return(run)
	return _c
}

// GetLeague provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetLeague(ctx context.Context, id primitive.ObjectID) (*models.League, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetLeague")
	}

	var r0 *models.League
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.League, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.League); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.League)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetLeague_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetLeague'
type MockQueryResolver_GetLeague_Call struct {
	*mock.Call
}

// GetLeague is a helper method to define mock.On call
//   - ctx context.Context
//   - id primitive.ObjectID
func (_e *MockQueryResolver_Expecter) GetLeague(ctx interface{}, id interface{}) *MockQueryResolver_GetLeague_Call {
	return &MockQueryResolver_GetLeague_Call{Call: _e.mock.On("GetLeague", ctx, id)}
}

func (_c *MockQueryResolver_GetLeague_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockQueryResolver_GetLeague_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetLeague_Call) Return(league *models.League, err error) *MockQueryResolver_GetLeague_Call {
	_c.Call.Return(league, err)
	return _c
}

func (_c *MockQueryResolver_GetLeague_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (*models.League, error)) *MockQueryResolver_GetLeague_Call {
	_c.Call.Return(run)
	return _c
}

// GetLeagueLeaderboard provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetLeagueLeaderboard(ctx context.Context, leagueID primitive.ObjectID, page int, pageSize int) (*models.LeagueLeaderboardPage, error) {
	ret := _mock.Called(ctx, leagueID, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetLeagueLeaderboard")
	}

	var r0 *models.LeagueLeaderboardPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int, int) (*models.LeagueLeaderboardPage, error)); ok {
		return returnFunc(ctx, leagueID, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int, int) *models.LeagueLeaderboardPage); ok {
		r0 = returnFunc(ctx, leagueID, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.LeagueLeaderboardPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, int, int) error); ok {
		r1 = returnFunc(ctx, leagueID, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetLeagueLeaderboard_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetLeagueLeaderboard'
type MockQueryResolver_GetLeagueLeaderboard_Call struct {
	*mock.Call
}

// GetLeagueLeaderboard is a helper method to define mock.On call
//   - ctx context.Context
//   - leagueID primitive.ObjectID
//   - page int
//   - pageSize int
func (_e *MockQueryResolver_Expecter) GetLeagueLeaderboard(ctx interface{}, leagueID interface{}, page interface{}, pageSize interface{}) *MockQueryResolver_GetLeagueLeaderboard_Call {
	return &MockQueryResolver_GetLeagueLeaderboard_Call{Call: _e.mock.On("GetLeagueLeaderboard", ctx, leagueID, page, pageSize)}
}

func (_c *MockQueryResolver_GetLeagueLeaderboard_Call) Run(run func(ctx context.Context, leagueID primitive.ObjectID, page int, pageSize int)) *MockQueryResolver_GetLeagueLeaderboard_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 int
		if args[2] != nil {
			arg2 = args[2].(int)
		}
		var arg3 int
		if args[3] != nil {
			arg3 = args[3].(int)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetLeagueLeaderboard_Call) Return(leagueLeaderboardPage *models.LeagueLeaderboardPage, err error) *MockQueryResolver_GetLeagueLeaderboard_Call {
	_c.Call.Return(leagueLeaderboardPage, err)
	return _c
}

func (_c *MockQueryResolver_GetLeagueLeaderboard_Call) RunAndReturn(run func(ctx context.Context, leagueID primitive.ObjectID, page int, pageSize int) (*models.LeagueLeaderboardPage, error)) *MockQueryResolver_GetLeagueLeaderboard_Call {
	_c.Call.Return(run)
	return _c
}

// GetLeaguesByStatus provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetLeaguesByStatus(ctx context.Context, statuses []models.LeagueStatus, page *int, pageSize *int, sortDirection *string) (*models.PaginatedLeagues, error) {
	ret := _mock.Called(ctx, statuses, page, pageSize, sortDirection)

	if len(ret) == 0 {
		panic("no return value specified for GetLeaguesByStatus")
	}

	var r0 *models.PaginatedLeagues
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, []models.LeagueStatus, *int, *int, *string) (*models.PaginatedLeagues, error)); ok {
		return returnFunc(ctx, statuses, page, pageSize, sortDirection)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, []models.LeagueStatus, *int, *int, *string) *models.PaginatedLeagues); ok {
		r0 = returnFunc(ctx, statuses, page, pageSize, sortDirection)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PaginatedLeagues)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, []models.LeagueStatus, *int, *int, *string) error); ok {
		r1 = returnFunc(ctx, statuses, page, pageSize, sortDirection)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetLeaguesByStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetLeaguesByStatus'
type MockQueryResolver_GetLeaguesByStatus_Call struct {
	*mock.Call
}

// GetLeaguesByStatus is a helper method to define mock.On call
//   - ctx context.Context
//   - statuses []models.LeagueStatus
//   - page *int
//   - pageSize *int
//   - sortDirection *string
func (_e *MockQueryResolver_Expecter) GetLeaguesByStatus(ctx interface{}, statuses interface{}, page interface{}, pageSize interface{}, sortDirection interface{}) *MockQueryResolver_GetLeaguesByStatus_Call {
	return &MockQueryResolver_GetLeaguesByStatus_Call{Call: _e.mock.On("GetLeaguesByStatus", ctx, statuses, page, pageSize, sortDirection)}
}

func (_c *MockQueryResolver_GetLeaguesByStatus_Call) Run(run func(ctx context.Context, statuses []models.LeagueStatus, page *int, pageSize *int, sortDirection *string)) *MockQueryResolver_GetLeaguesByStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 []models.LeagueStatus
		if args[1] != nil {
			arg1 = args[1].([]models.LeagueStatus)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		var arg3 *int
		if args[3] != nil {
			arg3 = args[3].(*int)
		}
		var arg4 *string
		if args[4] != nil {
			arg4 = args[4].(*string)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
			arg4,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetLeaguesByStatus_Call) Return(paginatedLeagues *models.PaginatedLeagues, err error) *MockQueryResolver_GetLeaguesByStatus_Call {
	_c.Call.Return(paginatedLeagues, err)
	return _c
}

func (_c *MockQueryResolver_GetLeaguesByStatus_Call) RunAndReturn(run func(ctx context.Context, statuses []models.LeagueStatus, page *int, pageSize *int, sortDirection *string) (*models.PaginatedLeagues, error)) *MockQueryResolver_GetLeaguesByStatus_Call {
	_c.Call.Return(run)
	return _c
}

// GetMessageGroupDetailsByID provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetMessageGroupDetailsByID(ctx context.Context, groupID primitive.ObjectID) (*models.MessageGroup, error) {
	ret := _mock.Called(ctx, groupID)

	if len(ret) == 0 {
		panic("no return value specified for GetMessageGroupDetailsByID")
	}

	var r0 *models.MessageGroup
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.MessageGroup, error)); ok {
		return returnFunc(ctx, groupID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.MessageGroup); ok {
		r0 = returnFunc(ctx, groupID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.MessageGroup)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, groupID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetMessageGroupDetailsByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMessageGroupDetailsByID'
type MockQueryResolver_GetMessageGroupDetailsByID_Call struct {
	*mock.Call
}

// GetMessageGroupDetailsByID is a helper method to define mock.On call
//   - ctx context.Context
//   - groupID primitive.ObjectID
func (_e *MockQueryResolver_Expecter) GetMessageGroupDetailsByID(ctx interface{}, groupID interface{}) *MockQueryResolver_GetMessageGroupDetailsByID_Call {
	return &MockQueryResolver_GetMessageGroupDetailsByID_Call{Call: _e.mock.On("GetMessageGroupDetailsByID", ctx, groupID)}
}

func (_c *MockQueryResolver_GetMessageGroupDetailsByID_Call) Run(run func(ctx context.Context, groupID primitive.ObjectID)) *MockQueryResolver_GetMessageGroupDetailsByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetMessageGroupDetailsByID_Call) Return(messageGroup *models.MessageGroup, err error) *MockQueryResolver_GetMessageGroupDetailsByID_Call {
	_c.Call.Return(messageGroup, err)
	return _c
}

func (_c *MockQueryResolver_GetMessageGroupDetailsByID_Call) RunAndReturn(run func(ctx context.Context, groupID primitive.ObjectID) (*models.MessageGroup, error)) *MockQueryResolver_GetMessageGroupDetailsByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetMessageGroupIDForFriends provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetMessageGroupIDForFriends(ctx context.Context, friendID primitive.ObjectID) (primitive.ObjectID, error) {
	ret := _mock.Called(ctx, friendID)

	if len(ret) == 0 {
		panic("no return value specified for GetMessageGroupIDForFriends")
	}

	var r0 primitive.ObjectID
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (primitive.ObjectID, error)); ok {
		return returnFunc(ctx, friendID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) primitive.ObjectID); ok {
		r0 = returnFunc(ctx, friendID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(primitive.ObjectID)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, friendID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetMessageGroupIDForFriends_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMessageGroupIDForFriends'
type MockQueryResolver_GetMessageGroupIDForFriends_Call struct {
	*mock.Call
}

// GetMessageGroupIDForFriends is a helper method to define mock.On call
//   - ctx context.Context
//   - friendID primitive.ObjectID
func (_e *MockQueryResolver_Expecter) GetMessageGroupIDForFriends(ctx interface{}, friendID interface{}) *MockQueryResolver_GetMessageGroupIDForFriends_Call {
	return &MockQueryResolver_GetMessageGroupIDForFriends_Call{Call: _e.mock.On("GetMessageGroupIDForFriends", ctx, friendID)}
}

func (_c *MockQueryResolver_GetMessageGroupIDForFriends_Call) Run(run func(ctx context.Context, friendID primitive.ObjectID)) *MockQueryResolver_GetMessageGroupIDForFriends_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetMessageGroupIDForFriends_Call) Return(objectID primitive.ObjectID, err error) *MockQueryResolver_GetMessageGroupIDForFriends_Call {
	_c.Call.Return(objectID, err)
	return _c
}

func (_c *MockQueryResolver_GetMessageGroupIDForFriends_Call) RunAndReturn(run func(ctx context.Context, friendID primitive.ObjectID) (primitive.ObjectID, error)) *MockQueryResolver_GetMessageGroupIDForFriends_Call {
	_c.Call.Return(run)
	return _c
}

// GetMessagesByGroupID provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetMessagesByGroupID(ctx context.Context, groupID primitive.ObjectID, lastMessageID *primitive.ObjectID, pageSize *int, sortDirection *models.SortDirection) (*models.PaginatedMessage, error) {
	ret := _mock.Called(ctx, groupID, lastMessageID, pageSize, sortDirection)

	if len(ret) == 0 {
		panic("no return value specified for GetMessagesByGroupID")
	}

	var r0 *models.PaginatedMessage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *primitive.ObjectID, *int, *models.SortDirection) (*models.PaginatedMessage, error)); ok {
		return returnFunc(ctx, groupID, lastMessageID, pageSize, sortDirection)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *primitive.ObjectID, *int, *models.SortDirection) *models.PaginatedMessage); ok {
		r0 = returnFunc(ctx, groupID, lastMessageID, pageSize, sortDirection)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PaginatedMessage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, *primitive.ObjectID, *int, *models.SortDirection) error); ok {
		r1 = returnFunc(ctx, groupID, lastMessageID, pageSize, sortDirection)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetMessagesByGroupID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMessagesByGroupID'
type MockQueryResolver_GetMessagesByGroupID_Call struct {
	*mock.Call
}

// GetMessagesByGroupID is a helper method to define mock.On call
//   - ctx context.Context
//   - groupID primitive.ObjectID
//   - lastMessageID *primitive.ObjectID
//   - pageSize *int
//   - sortDirection *models.SortDirection
func (_e *MockQueryResolver_Expecter) GetMessagesByGroupID(ctx interface{}, groupID interface{}, lastMessageID interface{}, pageSize interface{}, sortDirection interface{}) *MockQueryResolver_GetMessagesByGroupID_Call {
	return &MockQueryResolver_GetMessagesByGroupID_Call{Call: _e.mock.On("GetMessagesByGroupID", ctx, groupID, lastMessageID, pageSize, sortDirection)}
}

func (_c *MockQueryResolver_GetMessagesByGroupID_Call) Run(run func(ctx context.Context, groupID primitive.ObjectID, lastMessageID *primitive.ObjectID, pageSize *int, sortDirection *models.SortDirection)) *MockQueryResolver_GetMessagesByGroupID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 *primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(*primitive.ObjectID)
		}
		var arg3 *int
		if args[3] != nil {
			arg3 = args[3].(*int)
		}
		var arg4 *models.SortDirection
		if args[4] != nil {
			arg4 = args[4].(*models.SortDirection)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
			arg4,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetMessagesByGroupID_Call) Return(paginatedMessage *models.PaginatedMessage, err error) *MockQueryResolver_GetMessagesByGroupID_Call {
	_c.Call.Return(paginatedMessage, err)
	return _c
}

func (_c *MockQueryResolver_GetMessagesByGroupID_Call) RunAndReturn(run func(ctx context.Context, groupID primitive.ObjectID, lastMessageID *primitive.ObjectID, pageSize *int, sortDirection *models.SortDirection) (*models.PaginatedMessage, error)) *MockQueryResolver_GetMessagesByGroupID_Call {
	_c.Call.Return(run)
	return _c
}

// GetMyCrossMathPuzzleRushStats provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetMyCrossMathPuzzleRushStats(ctx context.Context) (*models.CrossMathPuzzleRushStats, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetMyCrossMathPuzzleRushStats")
	}

	var r0 *models.CrossMathPuzzleRushStats
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*models.CrossMathPuzzleRushStats, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *models.CrossMathPuzzleRushStats); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.CrossMathPuzzleRushStats)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetMyCrossMathPuzzleRushStats_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMyCrossMathPuzzleRushStats'
type MockQueryResolver_GetMyCrossMathPuzzleRushStats_Call struct {
	*mock.Call
}

// GetMyCrossMathPuzzleRushStats is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockQueryResolver_Expecter) GetMyCrossMathPuzzleRushStats(ctx interface{}) *MockQueryResolver_GetMyCrossMathPuzzleRushStats_Call {
	return &MockQueryResolver_GetMyCrossMathPuzzleRushStats_Call{Call: _e.mock.On("GetMyCrossMathPuzzleRushStats", ctx)}
}

func (_c *MockQueryResolver_GetMyCrossMathPuzzleRushStats_Call) Run(run func(ctx context.Context)) *MockQueryResolver_GetMyCrossMathPuzzleRushStats_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetMyCrossMathPuzzleRushStats_Call) Return(crossMathPuzzleRushStats *models.CrossMathPuzzleRushStats, err error) *MockQueryResolver_GetMyCrossMathPuzzleRushStats_Call {
	_c.Call.Return(crossMathPuzzleRushStats, err)
	return _c
}

func (_c *MockQueryResolver_GetMyCrossMathPuzzleRushStats_Call) RunAndReturn(run func(ctx context.Context) (*models.CrossMathPuzzleRushStats, error)) *MockQueryResolver_GetMyCrossMathPuzzleRushStats_Call {
	_c.Call.Return(run)
	return _c
}

// GetPaginatedLeaderboard provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetPaginatedLeaderboard(ctx context.Context, input *models.PaginatedLeaderboardInput) (*models.PaginatedLeaderboard, error) {
	ret := _mock.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for GetPaginatedLeaderboard")
	}

	var r0 *models.PaginatedLeaderboard
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.PaginatedLeaderboardInput) (*models.PaginatedLeaderboard, error)); ok {
		return returnFunc(ctx, input)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.PaginatedLeaderboardInput) *models.PaginatedLeaderboard); ok {
		r0 = returnFunc(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PaginatedLeaderboard)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.PaginatedLeaderboardInput) error); ok {
		r1 = returnFunc(ctx, input)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetPaginatedLeaderboard_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPaginatedLeaderboard'
type MockQueryResolver_GetPaginatedLeaderboard_Call struct {
	*mock.Call
}

// GetPaginatedLeaderboard is a helper method to define mock.On call
//   - ctx context.Context
//   - input *models.PaginatedLeaderboardInput
func (_e *MockQueryResolver_Expecter) GetPaginatedLeaderboard(ctx interface{}, input interface{}) *MockQueryResolver_GetPaginatedLeaderboard_Call {
	return &MockQueryResolver_GetPaginatedLeaderboard_Call{Call: _e.mock.On("GetPaginatedLeaderboard", ctx, input)}
}

func (_c *MockQueryResolver_GetPaginatedLeaderboard_Call) Run(run func(ctx context.Context, input *models.PaginatedLeaderboardInput)) *MockQueryResolver_GetPaginatedLeaderboard_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.PaginatedLeaderboardInput
		if args[1] != nil {
			arg1 = args[1].(*models.PaginatedLeaderboardInput)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetPaginatedLeaderboard_Call) Return(paginatedLeaderboard *models.PaginatedLeaderboard, err error) *MockQueryResolver_GetPaginatedLeaderboard_Call {
	_c.Call.Return(paginatedLeaderboard, err)
	return _c
}

func (_c *MockQueryResolver_GetPaginatedLeaderboard_Call) RunAndReturn(run func(ctx context.Context, input *models.PaginatedLeaderboardInput) (*models.PaginatedLeaderboard, error)) *MockQueryResolver_GetPaginatedLeaderboard_Call {
	_c.Call.Return(run)
	return _c
}

// GetPendingFriendRequests provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetPendingFriendRequests(ctx context.Context, page *int, pageSize *int) (*models.FriendRequestPage, error) {
	ret := _mock.Called(ctx, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetPendingFriendRequests")
	}

	var r0 *models.FriendRequestPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int) (*models.FriendRequestPage, error)); ok {
		return returnFunc(ctx, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int) *models.FriendRequestPage); ok {
		r0 = returnFunc(ctx, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.FriendRequestPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *int, *int) error); ok {
		r1 = returnFunc(ctx, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetPendingFriendRequests_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPendingFriendRequests'
type MockQueryResolver_GetPendingFriendRequests_Call struct {
	*mock.Call
}

// GetPendingFriendRequests is a helper method to define mock.On call
//   - ctx context.Context
//   - page *int
//   - pageSize *int
func (_e *MockQueryResolver_Expecter) GetPendingFriendRequests(ctx interface{}, page interface{}, pageSize interface{}) *MockQueryResolver_GetPendingFriendRequests_Call {
	return &MockQueryResolver_GetPendingFriendRequests_Call{Call: _e.mock.On("GetPendingFriendRequests", ctx, page, pageSize)}
}

func (_c *MockQueryResolver_GetPendingFriendRequests_Call) Run(run func(ctx context.Context, page *int, pageSize *int)) *MockQueryResolver_GetPendingFriendRequests_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *int
		if args[1] != nil {
			arg1 = args[1].(*int)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetPendingFriendRequests_Call) Return(friendRequestPage *models.FriendRequestPage, err error) *MockQueryResolver_GetPendingFriendRequests_Call {
	_c.Call.Return(friendRequestPage, err)
	return _c
}

func (_c *MockQueryResolver_GetPendingFriendRequests_Call) RunAndReturn(run func(ctx context.Context, page *int, pageSize *int) (*models.FriendRequestPage, error)) *MockQueryResolver_GetPendingFriendRequests_Call {
	_c.Call.Return(run)
	return _c
}

// GetPlatformStats provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetPlatformStats(ctx context.Context) (*models.PlatformStats, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetPlatformStats")
	}

	var r0 *models.PlatformStats
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*models.PlatformStats, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *models.PlatformStats); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PlatformStats)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetPlatformStats_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPlatformStats'
type MockQueryResolver_GetPlatformStats_Call struct {
	*mock.Call
}

// GetPlatformStats is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockQueryResolver_Expecter) GetPlatformStats(ctx interface{}) *MockQueryResolver_GetPlatformStats_Call {
	return &MockQueryResolver_GetPlatformStats_Call{Call: _e.mock.On("GetPlatformStats", ctx)}
}

func (_c *MockQueryResolver_GetPlatformStats_Call) Run(run func(ctx context.Context)) *MockQueryResolver_GetPlatformStats_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetPlatformStats_Call) Return(platformStats *models.PlatformStats, err error) *MockQueryResolver_GetPlatformStats_Call {
	_c.Call.Return(platformStats, err)
	return _c
}

func (_c *MockQueryResolver_GetPlatformStats_Call) RunAndReturn(run func(ctx context.Context) (*models.PlatformStats, error)) *MockQueryResolver_GetPlatformStats_Call {
	_c.Call.Return(run)
	return _c
}

// GetPuzzleGameByID provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetPuzzleGameByID(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for GetPuzzleGameByID")
	}

	var r0 *models.PuzzleGame
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.PuzzleGame, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.PuzzleGame); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PuzzleGame)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetPuzzleGameByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPuzzleGameByID'
type MockQueryResolver_GetPuzzleGameByID_Call struct {
	*mock.Call
}

// GetPuzzleGameByID is a helper method to define mock.On call
//   - ctx context.Context
//   - gameID primitive.ObjectID
func (_e *MockQueryResolver_Expecter) GetPuzzleGameByID(ctx interface{}, gameID interface{}) *MockQueryResolver_GetPuzzleGameByID_Call {
	return &MockQueryResolver_GetPuzzleGameByID_Call{Call: _e.mock.On("GetPuzzleGameByID", ctx, gameID)}
}

func (_c *MockQueryResolver_GetPuzzleGameByID_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID)) *MockQueryResolver_GetPuzzleGameByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetPuzzleGameByID_Call) Return(puzzleGame *models.PuzzleGame, err error) *MockQueryResolver_GetPuzzleGameByID_Call {
	_c.Call.Return(puzzleGame, err)
	return _c
}

func (_c *MockQueryResolver_GetPuzzleGameByID_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error)) *MockQueryResolver_GetPuzzleGameByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetPuzzleGamesByUser provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetPuzzleGamesByUser(ctx context.Context, payload *models.GetPuzzleGamesInput) (*models.GetPuzzleGamesOutput, error) {
	ret := _mock.Called(ctx, payload)

	if len(ret) == 0 {
		panic("no return value specified for GetPuzzleGamesByUser")
	}

	var r0 *models.GetPuzzleGamesOutput
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.GetPuzzleGamesInput) (*models.GetPuzzleGamesOutput, error)); ok {
		return returnFunc(ctx, payload)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.GetPuzzleGamesInput) *models.GetPuzzleGamesOutput); ok {
		r0 = returnFunc(ctx, payload)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.GetPuzzleGamesOutput)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.GetPuzzleGamesInput) error); ok {
		r1 = returnFunc(ctx, payload)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetPuzzleGamesByUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPuzzleGamesByUser'
type MockQueryResolver_GetPuzzleGamesByUser_Call struct {
	*mock.Call
}

// GetPuzzleGamesByUser is a helper method to define mock.On call
//   - ctx context.Context
//   - payload *models.GetPuzzleGamesInput
func (_e *MockQueryResolver_Expecter) GetPuzzleGamesByUser(ctx interface{}, payload interface{}) *MockQueryResolver_GetPuzzleGamesByUser_Call {
	return &MockQueryResolver_GetPuzzleGamesByUser_Call{Call: _e.mock.On("GetPuzzleGamesByUser", ctx, payload)}
}

func (_c *MockQueryResolver_GetPuzzleGamesByUser_Call) Run(run func(ctx context.Context, payload *models.GetPuzzleGamesInput)) *MockQueryResolver_GetPuzzleGamesByUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.GetPuzzleGamesInput
		if args[1] != nil {
			arg1 = args[1].(*models.GetPuzzleGamesInput)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetPuzzleGamesByUser_Call) Return(getPuzzleGamesOutput *models.GetPuzzleGamesOutput, err error) *MockQueryResolver_GetPuzzleGamesByUser_Call {
	_c.Call.Return(getPuzzleGamesOutput, err)
	return _c
}

func (_c *MockQueryResolver_GetPuzzleGamesByUser_Call) RunAndReturn(run func(ctx context.Context, payload *models.GetPuzzleGamesInput) (*models.GetPuzzleGamesOutput, error)) *MockQueryResolver_GetPuzzleGamesByUser_Call {
	_c.Call.Return(run)
	return _c
}

// GetPuzzleSubmissionsByMonth provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetPuzzleSubmissionsByMonth(ctx context.Context, yearMonths []string) ([]*models.PuzzleMonthlySubmissionReport, error) {
	ret := _mock.Called(ctx, yearMonths)

	if len(ret) == 0 {
		panic("no return value specified for GetPuzzleSubmissionsByMonth")
	}

	var r0 []*models.PuzzleMonthlySubmissionReport
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, []string) ([]*models.PuzzleMonthlySubmissionReport, error)); ok {
		return returnFunc(ctx, yearMonths)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, []string) []*models.PuzzleMonthlySubmissionReport); ok {
		r0 = returnFunc(ctx, yearMonths)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.PuzzleMonthlySubmissionReport)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, []string) error); ok {
		r1 = returnFunc(ctx, yearMonths)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetPuzzleSubmissionsByMonth_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPuzzleSubmissionsByMonth'
type MockQueryResolver_GetPuzzleSubmissionsByMonth_Call struct {
	*mock.Call
}

// GetPuzzleSubmissionsByMonth is a helper method to define mock.On call
//   - ctx context.Context
//   - yearMonths []string
func (_e *MockQueryResolver_Expecter) GetPuzzleSubmissionsByMonth(ctx interface{}, yearMonths interface{}) *MockQueryResolver_GetPuzzleSubmissionsByMonth_Call {
	return &MockQueryResolver_GetPuzzleSubmissionsByMonth_Call{Call: _e.mock.On("GetPuzzleSubmissionsByMonth", ctx, yearMonths)}
}

func (_c *MockQueryResolver_GetPuzzleSubmissionsByMonth_Call) Run(run func(ctx context.Context, yearMonths []string)) *MockQueryResolver_GetPuzzleSubmissionsByMonth_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 []string
		if args[1] != nil {
			arg1 = args[1].([]string)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetPuzzleSubmissionsByMonth_Call) Return(puzzleMonthlySubmissionReports []*models.PuzzleMonthlySubmissionReport, err error) *MockQueryResolver_GetPuzzleSubmissionsByMonth_Call {
	_c.Call.Return(puzzleMonthlySubmissionReports, err)
	return _c
}

func (_c *MockQueryResolver_GetPuzzleSubmissionsByMonth_Call) RunAndReturn(run func(ctx context.Context, yearMonths []string) ([]*models.PuzzleMonthlySubmissionReport, error)) *MockQueryResolver_GetPuzzleSubmissionsByMonth_Call {
	_c.Call.Return(run)
	return _c
}

// GetPuzzleSubmissionsByMonthByType provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetPuzzleSubmissionsByMonthByType(ctx context.Context, yearMonths []string, puzzleType models.PuzzleType) ([]*models.PuzzleMonthlySubmissionReport, error) {
	ret := _mock.Called(ctx, yearMonths, puzzleType)

	if len(ret) == 0 {
		panic("no return value specified for GetPuzzleSubmissionsByMonthByType")
	}

	var r0 []*models.PuzzleMonthlySubmissionReport
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, []string, models.PuzzleType) ([]*models.PuzzleMonthlySubmissionReport, error)); ok {
		return returnFunc(ctx, yearMonths, puzzleType)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, []string, models.PuzzleType) []*models.PuzzleMonthlySubmissionReport); ok {
		r0 = returnFunc(ctx, yearMonths, puzzleType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.PuzzleMonthlySubmissionReport)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, []string, models.PuzzleType) error); ok {
		r1 = returnFunc(ctx, yearMonths, puzzleType)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetPuzzleSubmissionsByMonthByType_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPuzzleSubmissionsByMonthByType'
type MockQueryResolver_GetPuzzleSubmissionsByMonthByType_Call struct {
	*mock.Call
}

// GetPuzzleSubmissionsByMonthByType is a helper method to define mock.On call
//   - ctx context.Context
//   - yearMonths []string
//   - puzzleType models.PuzzleType
func (_e *MockQueryResolver_Expecter) GetPuzzleSubmissionsByMonthByType(ctx interface{}, yearMonths interface{}, puzzleType interface{}) *MockQueryResolver_GetPuzzleSubmissionsByMonthByType_Call {
	return &MockQueryResolver_GetPuzzleSubmissionsByMonthByType_Call{Call: _e.mock.On("GetPuzzleSubmissionsByMonthByType", ctx, yearMonths, puzzleType)}
}

func (_c *MockQueryResolver_GetPuzzleSubmissionsByMonthByType_Call) Run(run func(ctx context.Context, yearMonths []string, puzzleType models.PuzzleType)) *MockQueryResolver_GetPuzzleSubmissionsByMonthByType_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 []string
		if args[1] != nil {
			arg1 = args[1].([]string)
		}
		var arg2 models.PuzzleType
		if args[2] != nil {
			arg2 = args[2].(models.PuzzleType)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetPuzzleSubmissionsByMonthByType_Call) Return(puzzleMonthlySubmissionReports []*models.PuzzleMonthlySubmissionReport, err error) *MockQueryResolver_GetPuzzleSubmissionsByMonthByType_Call {
	_c.Call.Return(puzzleMonthlySubmissionReports, err)
	return _c
}

func (_c *MockQueryResolver_GetPuzzleSubmissionsByMonthByType_Call) RunAndReturn(run func(ctx context.Context, yearMonths []string, puzzleType models.PuzzleType) ([]*models.PuzzleMonthlySubmissionReport, error)) *MockQueryResolver_GetPuzzleSubmissionsByMonthByType_Call {
	_c.Call.Return(run)
	return _c
}

// GetRatingFixtureQuestions provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetRatingFixtureQuestions(ctx context.Context) ([]*models.Question, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetRatingFixtureQuestions")
	}

	var r0 []*models.Question
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) ([]*models.Question, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) []*models.Question); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.Question)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetRatingFixtureQuestions_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRatingFixtureQuestions'
type MockQueryResolver_GetRatingFixtureQuestions_Call struct {
	*mock.Call
}

// GetRatingFixtureQuestions is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockQueryResolver_Expecter) GetRatingFixtureQuestions(ctx interface{}) *MockQueryResolver_GetRatingFixtureQuestions_Call {
	return &MockQueryResolver_GetRatingFixtureQuestions_Call{Call: _e.mock.On("GetRatingFixtureQuestions", ctx)}
}

func (_c *MockQueryResolver_GetRatingFixtureQuestions_Call) Run(run func(ctx context.Context)) *MockQueryResolver_GetRatingFixtureQuestions_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetRatingFixtureQuestions_Call) Return(questions []*models.Question, err error) *MockQueryResolver_GetRatingFixtureQuestions_Call {
	_c.Call.Return(questions, err)
	return _c
}

func (_c *MockQueryResolver_GetRatingFixtureQuestions_Call) RunAndReturn(run func(ctx context.Context) ([]*models.Question, error)) *MockQueryResolver_GetRatingFixtureQuestions_Call {
	_c.Call.Return(run)
	return _c
}

// GetRatingFixtureSubmission provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetRatingFixtureSubmission(ctx context.Context) (*models.UserRatingFixtureSubmission, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetRatingFixtureSubmission")
	}

	var r0 *models.UserRatingFixtureSubmission
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*models.UserRatingFixtureSubmission, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *models.UserRatingFixtureSubmission); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserRatingFixtureSubmission)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetRatingFixtureSubmission_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRatingFixtureSubmission'
type MockQueryResolver_GetRatingFixtureSubmission_Call struct {
	*mock.Call
}

// GetRatingFixtureSubmission is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockQueryResolver_Expecter) GetRatingFixtureSubmission(ctx interface{}) *MockQueryResolver_GetRatingFixtureSubmission_Call {
	return &MockQueryResolver_GetRatingFixtureSubmission_Call{Call: _e.mock.On("GetRatingFixtureSubmission", ctx)}
}

func (_c *MockQueryResolver_GetRatingFixtureSubmission_Call) Run(run func(ctx context.Context)) *MockQueryResolver_GetRatingFixtureSubmission_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetRatingFixtureSubmission_Call) Return(userRatingFixtureSubmission *models.UserRatingFixtureSubmission, err error) *MockQueryResolver_GetRatingFixtureSubmission_Call {
	_c.Call.Return(userRatingFixtureSubmission, err)
	return _c
}

func (_c *MockQueryResolver_GetRatingFixtureSubmission_Call) RunAndReturn(run func(ctx context.Context) (*models.UserRatingFixtureSubmission, error)) *MockQueryResolver_GetRatingFixtureSubmission_Call {
	_c.Call.Return(run)
	return _c
}

// GetRegisteredContests provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetRegisteredContests(ctx context.Context) ([]*models.Contest, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetRegisteredContests")
	}

	var r0 []*models.Contest
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) ([]*models.Contest, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) []*models.Contest); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.Contest)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetRegisteredContests_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRegisteredContests'
type MockQueryResolver_GetRegisteredContests_Call struct {
	*mock.Call
}

// GetRegisteredContests is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockQueryResolver_Expecter) GetRegisteredContests(ctx interface{}) *MockQueryResolver_GetRegisteredContests_Call {
	return &MockQueryResolver_GetRegisteredContests_Call{Call: _e.mock.On("GetRegisteredContests", ctx)}
}

func (_c *MockQueryResolver_GetRegisteredContests_Call) Run(run func(ctx context.Context)) *MockQueryResolver_GetRegisteredContests_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetRegisteredContests_Call) Return(contests []*models.Contest, err error) *MockQueryResolver_GetRegisteredContests_Call {
	_c.Call.Return(contests, err)
	return _c
}

func (_c *MockQueryResolver_GetRegisteredContests_Call) RunAndReturn(run func(ctx context.Context) ([]*models.Contest, error)) *MockQueryResolver_GetRegisteredContests_Call {
	_c.Call.Return(run)
	return _c
}

// GetShowdownByID provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetShowdownByID(ctx context.Context, showdownID primitive.ObjectID) (*models.Showdown, error) {
	ret := _mock.Called(ctx, showdownID)

	if len(ret) == 0 {
		panic("no return value specified for GetShowdownByID")
	}

	var r0 *models.Showdown
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.Showdown, error)); ok {
		return returnFunc(ctx, showdownID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.Showdown); ok {
		r0 = returnFunc(ctx, showdownID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Showdown)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, showdownID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetShowdownByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetShowdownByID'
type MockQueryResolver_GetShowdownByID_Call struct {
	*mock.Call
}

// GetShowdownByID is a helper method to define mock.On call
//   - ctx context.Context
//   - showdownID primitive.ObjectID
func (_e *MockQueryResolver_Expecter) GetShowdownByID(ctx interface{}, showdownID interface{}) *MockQueryResolver_GetShowdownByID_Call {
	return &MockQueryResolver_GetShowdownByID_Call{Call: _e.mock.On("GetShowdownByID", ctx, showdownID)}
}

func (_c *MockQueryResolver_GetShowdownByID_Call) Run(run func(ctx context.Context, showdownID primitive.ObjectID)) *MockQueryResolver_GetShowdownByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetShowdownByID_Call) Return(showdown *models.Showdown, err error) *MockQueryResolver_GetShowdownByID_Call {
	_c.Call.Return(showdown, err)
	return _c
}

func (_c *MockQueryResolver_GetShowdownByID_Call) RunAndReturn(run func(ctx context.Context, showdownID primitive.ObjectID) (*models.Showdown, error)) *MockQueryResolver_GetShowdownByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetShowdownByStatus provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetShowdownByStatus(ctx context.Context, status *models.ShowdownContestStatus) (*models.Showdown, error) {
	ret := _mock.Called(ctx, status)

	if len(ret) == 0 {
		panic("no return value specified for GetShowdownByStatus")
	}

	var r0 *models.Showdown
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.ShowdownContestStatus) (*models.Showdown, error)); ok {
		return returnFunc(ctx, status)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.ShowdownContestStatus) *models.Showdown); ok {
		r0 = returnFunc(ctx, status)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Showdown)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.ShowdownContestStatus) error); ok {
		r1 = returnFunc(ctx, status)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetShowdownByStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetShowdownByStatus'
type MockQueryResolver_GetShowdownByStatus_Call struct {
	*mock.Call
}

// GetShowdownByStatus is a helper method to define mock.On call
//   - ctx context.Context
//   - status *models.ShowdownContestStatus
func (_e *MockQueryResolver_Expecter) GetShowdownByStatus(ctx interface{}, status interface{}) *MockQueryResolver_GetShowdownByStatus_Call {
	return &MockQueryResolver_GetShowdownByStatus_Call{Call: _e.mock.On("GetShowdownByStatus", ctx, status)}
}

func (_c *MockQueryResolver_GetShowdownByStatus_Call) Run(run func(ctx context.Context, status *models.ShowdownContestStatus)) *MockQueryResolver_GetShowdownByStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.ShowdownContestStatus
		if args[1] != nil {
			arg1 = args[1].(*models.ShowdownContestStatus)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetShowdownByStatus_Call) Return(showdown *models.Showdown, err error) *MockQueryResolver_GetShowdownByStatus_Call {
	_c.Call.Return(showdown, err)
	return _c
}

func (_c *MockQueryResolver_GetShowdownByStatus_Call) RunAndReturn(run func(ctx context.Context, status *models.ShowdownContestStatus) (*models.Showdown, error)) *MockQueryResolver_GetShowdownByStatus_Call {
	_c.Call.Return(run)
	return _c
}

// GetShowdownsByStatus provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetShowdownsByStatus(ctx context.Context, statuses []models.ShowdownContestStatus, page *int, pageSize *int, sortDirection *string) (*models.PaginatedShowdowns, error) {
	ret := _mock.Called(ctx, statuses, page, pageSize, sortDirection)

	if len(ret) == 0 {
		panic("no return value specified for GetShowdownsByStatus")
	}

	var r0 *models.PaginatedShowdowns
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, []models.ShowdownContestStatus, *int, *int, *string) (*models.PaginatedShowdowns, error)); ok {
		return returnFunc(ctx, statuses, page, pageSize, sortDirection)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, []models.ShowdownContestStatus, *int, *int, *string) *models.PaginatedShowdowns); ok {
		r0 = returnFunc(ctx, statuses, page, pageSize, sortDirection)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PaginatedShowdowns)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, []models.ShowdownContestStatus, *int, *int, *string) error); ok {
		r1 = returnFunc(ctx, statuses, page, pageSize, sortDirection)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetShowdownsByStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetShowdownsByStatus'
type MockQueryResolver_GetShowdownsByStatus_Call struct {
	*mock.Call
}

// GetShowdownsByStatus is a helper method to define mock.On call
//   - ctx context.Context
//   - statuses []models.ShowdownContestStatus
//   - page *int
//   - pageSize *int
//   - sortDirection *string
func (_e *MockQueryResolver_Expecter) GetShowdownsByStatus(ctx interface{}, statuses interface{}, page interface{}, pageSize interface{}, sortDirection interface{}) *MockQueryResolver_GetShowdownsByStatus_Call {
	return &MockQueryResolver_GetShowdownsByStatus_Call{Call: _e.mock.On("GetShowdownsByStatus", ctx, statuses, page, pageSize, sortDirection)}
}

func (_c *MockQueryResolver_GetShowdownsByStatus_Call) Run(run func(ctx context.Context, statuses []models.ShowdownContestStatus, page *int, pageSize *int, sortDirection *string)) *MockQueryResolver_GetShowdownsByStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 []models.ShowdownContestStatus
		if args[1] != nil {
			arg1 = args[1].([]models.ShowdownContestStatus)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		var arg3 *int
		if args[3] != nil {
			arg3 = args[3].(*int)
		}
		var arg4 *string
		if args[4] != nil {
			arg4 = args[4].(*string)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
			arg4,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetShowdownsByStatus_Call) Return(paginatedShowdowns *models.PaginatedShowdowns, err error) *MockQueryResolver_GetShowdownsByStatus_Call {
	_c.Call.Return(paginatedShowdowns, err)
	return _c
}

func (_c *MockQueryResolver_GetShowdownsByStatus_Call) RunAndReturn(run func(ctx context.Context, statuses []models.ShowdownContestStatus, page *int, pageSize *int, sortDirection *string) (*models.PaginatedShowdowns, error)) *MockQueryResolver_GetShowdownsByStatus_Call {
	_c.Call.Return(run)
	return _c
}

// GetTimeSpentByUser provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetTimeSpentByUser(ctx context.Context, date *string) (int, error) {
	ret := _mock.Called(ctx, date)

	if len(ret) == 0 {
		panic("no return value specified for GetTimeSpentByUser")
	}

	var r0 int
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (int, error)); ok {
		return returnFunc(ctx, date)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) int); ok {
		r0 = returnFunc(ctx, date)
	} else {
		r0 = ret.Get(0).(int)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, date)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetTimeSpentByUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTimeSpentByUser'
type MockQueryResolver_GetTimeSpentByUser_Call struct {
	*mock.Call
}

// GetTimeSpentByUser is a helper method to define mock.On call
//   - ctx context.Context
//   - date *string
func (_e *MockQueryResolver_Expecter) GetTimeSpentByUser(ctx interface{}, date interface{}) *MockQueryResolver_GetTimeSpentByUser_Call {
	return &MockQueryResolver_GetTimeSpentByUser_Call{Call: _e.mock.On("GetTimeSpentByUser", ctx, date)}
}

func (_c *MockQueryResolver_GetTimeSpentByUser_Call) Run(run func(ctx context.Context, date *string)) *MockQueryResolver_GetTimeSpentByUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *string
		if args[1] != nil {
			arg1 = args[1].(*string)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetTimeSpentByUser_Call) Return(n int, err error) *MockQueryResolver_GetTimeSpentByUser_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockQueryResolver_GetTimeSpentByUser_Call) RunAndReturn(run func(ctx context.Context, date *string) (int, error)) *MockQueryResolver_GetTimeSpentByUser_Call {
	_c.Call.Return(run)
	return _c
}

// GetUnreadAnnouncements provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetUnreadAnnouncements(ctx context.Context, limit *int, offset *int) ([]*models.Announcement, error) {
	ret := _mock.Called(ctx, limit, offset)

	if len(ret) == 0 {
		panic("no return value specified for GetUnreadAnnouncements")
	}

	var r0 []*models.Announcement
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int) ([]*models.Announcement, error)); ok {
		return returnFunc(ctx, limit, offset)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int) []*models.Announcement); ok {
		r0 = returnFunc(ctx, limit, offset)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.Announcement)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *int, *int) error); ok {
		r1 = returnFunc(ctx, limit, offset)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetUnreadAnnouncements_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUnreadAnnouncements'
type MockQueryResolver_GetUnreadAnnouncements_Call struct {
	*mock.Call
}

// GetUnreadAnnouncements is a helper method to define mock.On call
//   - ctx context.Context
//   - limit *int
//   - offset *int
func (_e *MockQueryResolver_Expecter) GetUnreadAnnouncements(ctx interface{}, limit interface{}, offset interface{}) *MockQueryResolver_GetUnreadAnnouncements_Call {
	return &MockQueryResolver_GetUnreadAnnouncements_Call{Call: _e.mock.On("GetUnreadAnnouncements", ctx, limit, offset)}
}

func (_c *MockQueryResolver_GetUnreadAnnouncements_Call) Run(run func(ctx context.Context, limit *int, offset *int)) *MockQueryResolver_GetUnreadAnnouncements_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *int
		if args[1] != nil {
			arg1 = args[1].(*int)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetUnreadAnnouncements_Call) Return(announcements []*models.Announcement, err error) *MockQueryResolver_GetUnreadAnnouncements_Call {
	_c.Call.Return(announcements, err)
	return _c
}

func (_c *MockQueryResolver_GetUnreadAnnouncements_Call) RunAndReturn(run func(ctx context.Context, limit *int, offset *int) ([]*models.Announcement, error)) *MockQueryResolver_GetUnreadAnnouncements_Call {
	_c.Call.Return(run)
	return _c
}

// GetUpcomingShowdown provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetUpcomingShowdown(ctx context.Context) (*models.Showdown, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetUpcomingShowdown")
	}

	var r0 *models.Showdown
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*models.Showdown, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *models.Showdown); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Showdown)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetUpcomingShowdown_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUpcomingShowdown'
type MockQueryResolver_GetUpcomingShowdown_Call struct {
	*mock.Call
}

// GetUpcomingShowdown is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockQueryResolver_Expecter) GetUpcomingShowdown(ctx interface{}) *MockQueryResolver_GetUpcomingShowdown_Call {
	return &MockQueryResolver_GetUpcomingShowdown_Call{Call: _e.mock.On("GetUpcomingShowdown", ctx)}
}

func (_c *MockQueryResolver_GetUpcomingShowdown_Call) Run(run func(ctx context.Context)) *MockQueryResolver_GetUpcomingShowdown_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetUpcomingShowdown_Call) Return(showdown *models.Showdown, err error) *MockQueryResolver_GetUpcomingShowdown_Call {
	_c.Call.Return(showdown, err)
	return _c
}

func (_c *MockQueryResolver_GetUpcomingShowdown_Call) RunAndReturn(run func(ctx context.Context) (*models.Showdown, error)) *MockQueryResolver_GetUpcomingShowdown_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserByID provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetUserByID(ctx context.Context, userID primitive.ObjectID) (*models.User, error) {
	ret := _mock.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetUserByID")
	}

	var r0 *models.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.User, error)); ok {
		return returnFunc(ctx, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.User); ok {
		r0 = returnFunc(ctx, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetUserByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserByID'
type MockQueryResolver_GetUserByID_Call struct {
	*mock.Call
}

// GetUserByID is a helper method to define mock.On call
//   - ctx context.Context
//   - userID primitive.ObjectID
func (_e *MockQueryResolver_Expecter) GetUserByID(ctx interface{}, userID interface{}) *MockQueryResolver_GetUserByID_Call {
	return &MockQueryResolver_GetUserByID_Call{Call: _e.mock.On("GetUserByID", ctx, userID)}
}

func (_c *MockQueryResolver_GetUserByID_Call) Run(run func(ctx context.Context, userID primitive.ObjectID)) *MockQueryResolver_GetUserByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetUserByID_Call) Return(user *models.User, err error) *MockQueryResolver_GetUserByID_Call {
	_c.Call.Return(user, err)
	return _c
}

func (_c *MockQueryResolver_GetUserByID_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID) (*models.User, error)) *MockQueryResolver_GetUserByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserByUsername provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetUserByUsername(ctx context.Context, username *string) (*models.SearchUserOutput, error) {
	ret := _mock.Called(ctx, username)

	if len(ret) == 0 {
		panic("no return value specified for GetUserByUsername")
	}

	var r0 *models.SearchUserOutput
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (*models.SearchUserOutput, error)); ok {
		return returnFunc(ctx, username)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) *models.SearchUserOutput); ok {
		r0 = returnFunc(ctx, username)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.SearchUserOutput)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, username)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetUserByUsername_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserByUsername'
type MockQueryResolver_GetUserByUsername_Call struct {
	*mock.Call
}

// GetUserByUsername is a helper method to define mock.On call
//   - ctx context.Context
//   - username *string
func (_e *MockQueryResolver_Expecter) GetUserByUsername(ctx interface{}, username interface{}) *MockQueryResolver_GetUserByUsername_Call {
	return &MockQueryResolver_GetUserByUsername_Call{Call: _e.mock.On("GetUserByUsername", ctx, username)}
}

func (_c *MockQueryResolver_GetUserByUsername_Call) Run(run func(ctx context.Context, username *string)) *MockQueryResolver_GetUserByUsername_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *string
		if args[1] != nil {
			arg1 = args[1].(*string)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetUserByUsername_Call) Return(searchUserOutput *models.SearchUserOutput, err error) *MockQueryResolver_GetUserByUsername_Call {
	_c.Call.Return(searchUserOutput, err)
	return _c
}

func (_c *MockQueryResolver_GetUserByUsername_Call) RunAndReturn(run func(ctx context.Context, username *string) (*models.SearchUserOutput, error)) *MockQueryResolver_GetUserByUsername_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserContestResult provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetUserContestResult(ctx context.Context, contestID primitive.ObjectID) (*models.UserContestResult, error) {
	ret := _mock.Called(ctx, contestID)

	if len(ret) == 0 {
		panic("no return value specified for GetUserContestResult")
	}

	var r0 *models.UserContestResult
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.UserContestResult, error)); ok {
		return returnFunc(ctx, contestID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.UserContestResult); ok {
		r0 = returnFunc(ctx, contestID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserContestResult)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, contestID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetUserContestResult_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserContestResult'
type MockQueryResolver_GetUserContestResult_Call struct {
	*mock.Call
}

// GetUserContestResult is a helper method to define mock.On call
//   - ctx context.Context
//   - contestID primitive.ObjectID
func (_e *MockQueryResolver_Expecter) GetUserContestResult(ctx interface{}, contestID interface{}) *MockQueryResolver_GetUserContestResult_Call {
	return &MockQueryResolver_GetUserContestResult_Call{Call: _e.mock.On("GetUserContestResult", ctx, contestID)}
}

func (_c *MockQueryResolver_GetUserContestResult_Call) Run(run func(ctx context.Context, contestID primitive.ObjectID)) *MockQueryResolver_GetUserContestResult_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetUserContestResult_Call) Return(userContestResult *models.UserContestResult, err error) *MockQueryResolver_GetUserContestResult_Call {
	_c.Call.Return(userContestResult, err)
	return _c
}

func (_c *MockQueryResolver_GetUserContestResult_Call) RunAndReturn(run func(ctx context.Context, contestID primitive.ObjectID) (*models.UserContestResult, error)) *MockQueryResolver_GetUserContestResult_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserContestSubmissions provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetUserContestSubmissions(ctx context.Context, userID *primitive.ObjectID, contestID primitive.ObjectID) (*models.UserContestSubmissions, error) {
	ret := _mock.Called(ctx, userID, contestID)

	if len(ret) == 0 {
		panic("no return value specified for GetUserContestSubmissions")
	}

	var r0 *models.UserContestSubmissions
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *primitive.ObjectID, primitive.ObjectID) (*models.UserContestSubmissions, error)); ok {
		return returnFunc(ctx, userID, contestID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *primitive.ObjectID, primitive.ObjectID) *models.UserContestSubmissions); ok {
		r0 = returnFunc(ctx, userID, contestID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserContestSubmissions)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *primitive.ObjectID, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, userID, contestID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetUserContestSubmissions_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserContestSubmissions'
type MockQueryResolver_GetUserContestSubmissions_Call struct {
	*mock.Call
}

// GetUserContestSubmissions is a helper method to define mock.On call
//   - ctx context.Context
//   - userID *primitive.ObjectID
//   - contestID primitive.ObjectID
func (_e *MockQueryResolver_Expecter) GetUserContestSubmissions(ctx interface{}, userID interface{}, contestID interface{}) *MockQueryResolver_GetUserContestSubmissions_Call {
	return &MockQueryResolver_GetUserContestSubmissions_Call{Call: _e.mock.On("GetUserContestSubmissions", ctx, userID, contestID)}
}

func (_c *MockQueryResolver_GetUserContestSubmissions_Call) Run(run func(ctx context.Context, userID *primitive.ObjectID, contestID primitive.ObjectID)) *MockQueryResolver_GetUserContestSubmissions_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(*primitive.ObjectID)
		}
		var arg2 primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetUserContestSubmissions_Call) Return(userContestSubmissions *models.UserContestSubmissions, err error) *MockQueryResolver_GetUserContestSubmissions_Call {
	_c.Call.Return(userContestSubmissions, err)
	return _c
}

func (_c *MockQueryResolver_GetUserContestSubmissions_Call) RunAndReturn(run func(ctx context.Context, userID *primitive.ObjectID, contestID primitive.ObjectID) (*models.UserContestSubmissions, error)) *MockQueryResolver_GetUserContestSubmissions_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserFeeds provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetUserFeeds(ctx context.Context, lastID *primitive.ObjectID, pageSize *int) (*models.FeedResponse, error) {
	ret := _mock.Called(ctx, lastID, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetUserFeeds")
	}

	var r0 *models.FeedResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *primitive.ObjectID, *int) (*models.FeedResponse, error)); ok {
		return returnFunc(ctx, lastID, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *primitive.ObjectID, *int) *models.FeedResponse); ok {
		r0 = returnFunc(ctx, lastID, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.FeedResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *primitive.ObjectID, *int) error); ok {
		r1 = returnFunc(ctx, lastID, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetUserFeeds_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserFeeds'
type MockQueryResolver_GetUserFeeds_Call struct {
	*mock.Call
}

// GetUserFeeds is a helper method to define mock.On call
//   - ctx context.Context
//   - lastID *primitive.ObjectID
//   - pageSize *int
func (_e *MockQueryResolver_Expecter) GetUserFeeds(ctx interface{}, lastID interface{}, pageSize interface{}) *MockQueryResolver_GetUserFeeds_Call {
	return &MockQueryResolver_GetUserFeeds_Call{Call: _e.mock.On("GetUserFeeds", ctx, lastID, pageSize)}
}

func (_c *MockQueryResolver_GetUserFeeds_Call) Run(run func(ctx context.Context, lastID *primitive.ObjectID, pageSize *int)) *MockQueryResolver_GetUserFeeds_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(*primitive.ObjectID)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetUserFeeds_Call) Return(feedResponse *models.FeedResponse, err error) *MockQueryResolver_GetUserFeeds_Call {
	_c.Call.Return(feedResponse, err)
	return _c
}

func (_c *MockQueryResolver_GetUserFeeds_Call) RunAndReturn(run func(ctx context.Context, lastID *primitive.ObjectID, pageSize *int) (*models.FeedResponse, error)) *MockQueryResolver_GetUserFeeds_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserGamesByRatingType provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetUserGamesByRatingType(ctx context.Context, payload *models.GetGamesByRatingInput) (*models.GetGamesByRatingOutput, error) {
	ret := _mock.Called(ctx, payload)

	if len(ret) == 0 {
		panic("no return value specified for GetUserGamesByRatingType")
	}

	var r0 *models.GetGamesByRatingOutput
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.GetGamesByRatingInput) (*models.GetGamesByRatingOutput, error)); ok {
		return returnFunc(ctx, payload)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.GetGamesByRatingInput) *models.GetGamesByRatingOutput); ok {
		r0 = returnFunc(ctx, payload)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.GetGamesByRatingOutput)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.GetGamesByRatingInput) error); ok {
		r1 = returnFunc(ctx, payload)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetUserGamesByRatingType_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserGamesByRatingType'
type MockQueryResolver_GetUserGamesByRatingType_Call struct {
	*mock.Call
}

// GetUserGamesByRatingType is a helper method to define mock.On call
//   - ctx context.Context
//   - payload *models.GetGamesByRatingInput
func (_e *MockQueryResolver_Expecter) GetUserGamesByRatingType(ctx interface{}, payload interface{}) *MockQueryResolver_GetUserGamesByRatingType_Call {
	return &MockQueryResolver_GetUserGamesByRatingType_Call{Call: _e.mock.On("GetUserGamesByRatingType", ctx, payload)}
}

func (_c *MockQueryResolver_GetUserGamesByRatingType_Call) Run(run func(ctx context.Context, payload *models.GetGamesByRatingInput)) *MockQueryResolver_GetUserGamesByRatingType_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.GetGamesByRatingInput
		if args[1] != nil {
			arg1 = args[1].(*models.GetGamesByRatingInput)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetUserGamesByRatingType_Call) Return(getGamesByRatingOutput *models.GetGamesByRatingOutput, err error) *MockQueryResolver_GetUserGamesByRatingType_Call {
	_c.Call.Return(getGamesByRatingOutput, err)
	return _c
}

func (_c *MockQueryResolver_GetUserGamesByRatingType_Call) RunAndReturn(run func(ctx context.Context, payload *models.GetGamesByRatingInput) (*models.GetGamesByRatingOutput, error)) *MockQueryResolver_GetUserGamesByRatingType_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserLeagueGroupLeaderboard provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetUserLeagueGroupLeaderboard(ctx context.Context, page *int, pageSize *int) (*models.WeeklyLeagueLeaderboardPage, error) {
	ret := _mock.Called(ctx, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetUserLeagueGroupLeaderboard")
	}

	var r0 *models.WeeklyLeagueLeaderboardPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int) (*models.WeeklyLeagueLeaderboardPage, error)); ok {
		return returnFunc(ctx, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int) *models.WeeklyLeagueLeaderboardPage); ok {
		r0 = returnFunc(ctx, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.WeeklyLeagueLeaderboardPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *int, *int) error); ok {
		r1 = returnFunc(ctx, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetUserLeagueGroupLeaderboard_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserLeagueGroupLeaderboard'
type MockQueryResolver_GetUserLeagueGroupLeaderboard_Call struct {
	*mock.Call
}

// GetUserLeagueGroupLeaderboard is a helper method to define mock.On call
//   - ctx context.Context
//   - page *int
//   - pageSize *int
func (_e *MockQueryResolver_Expecter) GetUserLeagueGroupLeaderboard(ctx interface{}, page interface{}, pageSize interface{}) *MockQueryResolver_GetUserLeagueGroupLeaderboard_Call {
	return &MockQueryResolver_GetUserLeagueGroupLeaderboard_Call{Call: _e.mock.On("GetUserLeagueGroupLeaderboard", ctx, page, pageSize)}
}

func (_c *MockQueryResolver_GetUserLeagueGroupLeaderboard_Call) Run(run func(ctx context.Context, page *int, pageSize *int)) *MockQueryResolver_GetUserLeagueGroupLeaderboard_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *int
		if args[1] != nil {
			arg1 = args[1].(*int)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetUserLeagueGroupLeaderboard_Call) Return(weeklyLeagueLeaderboardPage *models.WeeklyLeagueLeaderboardPage, err error) *MockQueryResolver_GetUserLeagueGroupLeaderboard_Call {
	_c.Call.Return(weeklyLeagueLeaderboardPage, err)
	return _c
}

func (_c *MockQueryResolver_GetUserLeagueGroupLeaderboard_Call) RunAndReturn(run func(ctx context.Context, page *int, pageSize *int) (*models.WeeklyLeagueLeaderboardPage, error)) *MockQueryResolver_GetUserLeagueGroupLeaderboard_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserPresetStatsByDate provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetUserPresetStatsByDate(ctx context.Context, username *string, durationFilter *int, identifier *string) ([]*models.UserPresetDayStats, error) {
	ret := _mock.Called(ctx, username, durationFilter, identifier)

	if len(ret) == 0 {
		panic("no return value specified for GetUserPresetStatsByDate")
	}

	var r0 []*models.UserPresetDayStats
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *int, *string) ([]*models.UserPresetDayStats, error)); ok {
		return returnFunc(ctx, username, durationFilter, identifier)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *int, *string) []*models.UserPresetDayStats); ok {
		r0 = returnFunc(ctx, username, durationFilter, identifier)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.UserPresetDayStats)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string, *int, *string) error); ok {
		r1 = returnFunc(ctx, username, durationFilter, identifier)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetUserPresetStatsByDate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserPresetStatsByDate'
type MockQueryResolver_GetUserPresetStatsByDate_Call struct {
	*mock.Call
}

// GetUserPresetStatsByDate is a helper method to define mock.On call
//   - ctx context.Context
//   - username *string
//   - durationFilter *int
//   - identifier *string
func (_e *MockQueryResolver_Expecter) GetUserPresetStatsByDate(ctx interface{}, username interface{}, durationFilter interface{}, identifier interface{}) *MockQueryResolver_GetUserPresetStatsByDate_Call {
	return &MockQueryResolver_GetUserPresetStatsByDate_Call{Call: _e.mock.On("GetUserPresetStatsByDate", ctx, username, durationFilter, identifier)}
}

func (_c *MockQueryResolver_GetUserPresetStatsByDate_Call) Run(run func(ctx context.Context, username *string, durationFilter *int, identifier *string)) *MockQueryResolver_GetUserPresetStatsByDate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *string
		if args[1] != nil {
			arg1 = args[1].(*string)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		var arg3 *string
		if args[3] != nil {
			arg3 = args[3].(*string)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetUserPresetStatsByDate_Call) Return(userPresetDayStatss []*models.UserPresetDayStats, err error) *MockQueryResolver_GetUserPresetStatsByDate_Call {
	_c.Call.Return(userPresetDayStatss, err)
	return _c
}

func (_c *MockQueryResolver_GetUserPresetStatsByDate_Call) RunAndReturn(run func(ctx context.Context, username *string, durationFilter *int, identifier *string) ([]*models.UserPresetDayStats, error)) *MockQueryResolver_GetUserPresetStatsByDate_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserPresetsByIdentifier provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetUserPresetsByIdentifier(ctx context.Context, identifier *string) (*models.UserPreset, error) {
	ret := _mock.Called(ctx, identifier)

	if len(ret) == 0 {
		panic("no return value specified for GetUserPresetsByIdentifier")
	}

	var r0 *models.UserPreset
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (*models.UserPreset, error)); ok {
		return returnFunc(ctx, identifier)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) *models.UserPreset); ok {
		r0 = returnFunc(ctx, identifier)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserPreset)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, identifier)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetUserPresetsByIdentifier_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserPresetsByIdentifier'
type MockQueryResolver_GetUserPresetsByIdentifier_Call struct {
	*mock.Call
}

// GetUserPresetsByIdentifier is a helper method to define mock.On call
//   - ctx context.Context
//   - identifier *string
func (_e *MockQueryResolver_Expecter) GetUserPresetsByIdentifier(ctx interface{}, identifier interface{}) *MockQueryResolver_GetUserPresetsByIdentifier_Call {
	return &MockQueryResolver_GetUserPresetsByIdentifier_Call{Call: _e.mock.On("GetUserPresetsByIdentifier", ctx, identifier)}
}

func (_c *MockQueryResolver_GetUserPresetsByIdentifier_Call) Run(run func(ctx context.Context, identifier *string)) *MockQueryResolver_GetUserPresetsByIdentifier_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *string
		if args[1] != nil {
			arg1 = args[1].(*string)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetUserPresetsByIdentifier_Call) Return(userPreset *models.UserPreset, err error) *MockQueryResolver_GetUserPresetsByIdentifier_Call {
	_c.Call.Return(userPreset, err)
	return _c
}

func (_c *MockQueryResolver_GetUserPresetsByIdentifier_Call) RunAndReturn(run func(ctx context.Context, identifier *string) (*models.UserPreset, error)) *MockQueryResolver_GetUserPresetsByIdentifier_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserPuzzleStats provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetUserPuzzleStats(ctx context.Context) (*models.PuzzleUserStats, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetUserPuzzleStats")
	}

	var r0 *models.PuzzleUserStats
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*models.PuzzleUserStats, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *models.PuzzleUserStats); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PuzzleUserStats)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetUserPuzzleStats_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserPuzzleStats'
type MockQueryResolver_GetUserPuzzleStats_Call struct {
	*mock.Call
}

// GetUserPuzzleStats is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockQueryResolver_Expecter) GetUserPuzzleStats(ctx interface{}) *MockQueryResolver_GetUserPuzzleStats_Call {
	return &MockQueryResolver_GetUserPuzzleStats_Call{Call: _e.mock.On("GetUserPuzzleStats", ctx)}
}

func (_c *MockQueryResolver_GetUserPuzzleStats_Call) Run(run func(ctx context.Context)) *MockQueryResolver_GetUserPuzzleStats_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetUserPuzzleStats_Call) Return(puzzleUserStats *models.PuzzleUserStats, err error) *MockQueryResolver_GetUserPuzzleStats_Call {
	_c.Call.Return(puzzleUserStats, err)
	return _c
}

func (_c *MockQueryResolver_GetUserPuzzleStats_Call) RunAndReturn(run func(ctx context.Context) (*models.PuzzleUserStats, error)) *MockQueryResolver_GetUserPuzzleStats_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserPuzzleStatsByType provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetUserPuzzleStatsByType(ctx context.Context, puzzleType models.PuzzleType) (*models.PuzzleUserStats, error) {
	ret := _mock.Called(ctx, puzzleType)

	if len(ret) == 0 {
		panic("no return value specified for GetUserPuzzleStatsByType")
	}

	var r0 *models.PuzzleUserStats
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.PuzzleType) (*models.PuzzleUserStats, error)); ok {
		return returnFunc(ctx, puzzleType)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.PuzzleType) *models.PuzzleUserStats); ok {
		r0 = returnFunc(ctx, puzzleType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PuzzleUserStats)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.PuzzleType) error); ok {
		r1 = returnFunc(ctx, puzzleType)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetUserPuzzleStatsByType_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserPuzzleStatsByType'
type MockQueryResolver_GetUserPuzzleStatsByType_Call struct {
	*mock.Call
}

// GetUserPuzzleStatsByType is a helper method to define mock.On call
//   - ctx context.Context
//   - puzzleType models.PuzzleType
func (_e *MockQueryResolver_Expecter) GetUserPuzzleStatsByType(ctx interface{}, puzzleType interface{}) *MockQueryResolver_GetUserPuzzleStatsByType_Call {
	return &MockQueryResolver_GetUserPuzzleStatsByType_Call{Call: _e.mock.On("GetUserPuzzleStatsByType", ctx, puzzleType)}
}

func (_c *MockQueryResolver_GetUserPuzzleStatsByType_Call) Run(run func(ctx context.Context, puzzleType models.PuzzleType)) *MockQueryResolver_GetUserPuzzleStatsByType_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 models.PuzzleType
		if args[1] != nil {
			arg1 = args[1].(models.PuzzleType)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetUserPuzzleStatsByType_Call) Return(puzzleUserStats *models.PuzzleUserStats, err error) *MockQueryResolver_GetUserPuzzleStatsByType_Call {
	_c.Call.Return(puzzleUserStats, err)
	return _c
}

func (_c *MockQueryResolver_GetUserPuzzleStatsByType_Call) RunAndReturn(run func(ctx context.Context, puzzleType models.PuzzleType) (*models.PuzzleUserStats, error)) *MockQueryResolver_GetUserPuzzleStatsByType_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserRecentPresets provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetUserRecentPresets(ctx context.Context) (*models.UserPresets, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetUserRecentPresets")
	}

	var r0 *models.UserPresets
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*models.UserPresets, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *models.UserPresets); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserPresets)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetUserRecentPresets_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserRecentPresets'
type MockQueryResolver_GetUserRecentPresets_Call struct {
	*mock.Call
}

// GetUserRecentPresets is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockQueryResolver_Expecter) GetUserRecentPresets(ctx interface{}) *MockQueryResolver_GetUserRecentPresets_Call {
	return &MockQueryResolver_GetUserRecentPresets_Call{Call: _e.mock.On("GetUserRecentPresets", ctx)}
}

func (_c *MockQueryResolver_GetUserRecentPresets_Call) Run(run func(ctx context.Context)) *MockQueryResolver_GetUserRecentPresets_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetUserRecentPresets_Call) Return(userPresets *models.UserPresets, err error) *MockQueryResolver_GetUserRecentPresets_Call {
	_c.Call.Return(userPresets, err)
	return _c
}

func (_c *MockQueryResolver_GetUserRecentPresets_Call) RunAndReturn(run func(ctx context.Context) (*models.UserPresets, error)) *MockQueryResolver_GetUserRecentPresets_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserResolution provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetUserResolution(ctx context.Context) (*models.UserResolution, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetUserResolution")
	}

	var r0 *models.UserResolution
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*models.UserResolution, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *models.UserResolution); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserResolution)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetUserResolution_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserResolution'
type MockQueryResolver_GetUserResolution_Call struct {
	*mock.Call
}

// GetUserResolution is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockQueryResolver_Expecter) GetUserResolution(ctx interface{}) *MockQueryResolver_GetUserResolution_Call {
	return &MockQueryResolver_GetUserResolution_Call{Call: _e.mock.On("GetUserResolution", ctx)}
}

func (_c *MockQueryResolver_GetUserResolution_Call) Run(run func(ctx context.Context)) *MockQueryResolver_GetUserResolution_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetUserResolution_Call) Return(userResolution *models.UserResolution, err error) *MockQueryResolver_GetUserResolution_Call {
	_c.Call.Return(userResolution, err)
	return _c
}

func (_c *MockQueryResolver_GetUserResolution_Call) RunAndReturn(run func(ctx context.Context) (*models.UserResolution, error)) *MockQueryResolver_GetUserResolution_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserResult provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetUserResult(ctx context.Context, challengeNumber *int) (*models.UserResult, error) {
	ret := _mock.Called(ctx, challengeNumber)

	if len(ret) == 0 {
		panic("no return value specified for GetUserResult")
	}

	var r0 *models.UserResult
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int) (*models.UserResult, error)); ok {
		return returnFunc(ctx, challengeNumber)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int) *models.UserResult); ok {
		r0 = returnFunc(ctx, challengeNumber)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserResult)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *int) error); ok {
		r1 = returnFunc(ctx, challengeNumber)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetUserResult_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserResult'
type MockQueryResolver_GetUserResult_Call struct {
	*mock.Call
}

// GetUserResult is a helper method to define mock.On call
//   - ctx context.Context
//   - challengeNumber *int
func (_e *MockQueryResolver_Expecter) GetUserResult(ctx interface{}, challengeNumber interface{}) *MockQueryResolver_GetUserResult_Call {
	return &MockQueryResolver_GetUserResult_Call{Call: _e.mock.On("GetUserResult", ctx, challengeNumber)}
}

func (_c *MockQueryResolver_GetUserResult_Call) Run(run func(ctx context.Context, challengeNumber *int)) *MockQueryResolver_GetUserResult_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *int
		if args[1] != nil {
			arg1 = args[1].(*int)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetUserResult_Call) Return(userResult *models.UserResult, err error) *MockQueryResolver_GetUserResult_Call {
	_c.Call.Return(userResult, err)
	return _c
}

func (_c *MockQueryResolver_GetUserResult_Call) RunAndReturn(run func(ctx context.Context, challengeNumber *int) (*models.UserResult, error)) *MockQueryResolver_GetUserResult_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserResultByDailyChallengeID provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetUserResultByDailyChallengeID(ctx context.Context, challengeID primitive.ObjectID) (*models.UserDailyChallengeResultWithStats, error) {
	ret := _mock.Called(ctx, challengeID)

	if len(ret) == 0 {
		panic("no return value specified for GetUserResultByDailyChallengeID")
	}

	var r0 *models.UserDailyChallengeResultWithStats
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.UserDailyChallengeResultWithStats, error)); ok {
		return returnFunc(ctx, challengeID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.UserDailyChallengeResultWithStats); ok {
		r0 = returnFunc(ctx, challengeID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserDailyChallengeResultWithStats)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, challengeID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetUserResultByDailyChallengeID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserResultByDailyChallengeID'
type MockQueryResolver_GetUserResultByDailyChallengeID_Call struct {
	*mock.Call
}

// GetUserResultByDailyChallengeID is a helper method to define mock.On call
//   - ctx context.Context
//   - challengeID primitive.ObjectID
func (_e *MockQueryResolver_Expecter) GetUserResultByDailyChallengeID(ctx interface{}, challengeID interface{}) *MockQueryResolver_GetUserResultByDailyChallengeID_Call {
	return &MockQueryResolver_GetUserResultByDailyChallengeID_Call{Call: _e.mock.On("GetUserResultByDailyChallengeID", ctx, challengeID)}
}

func (_c *MockQueryResolver_GetUserResultByDailyChallengeID_Call) Run(run func(ctx context.Context, challengeID primitive.ObjectID)) *MockQueryResolver_GetUserResultByDailyChallengeID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetUserResultByDailyChallengeID_Call) Return(userDailyChallengeResultWithStats *models.UserDailyChallengeResultWithStats, err error) *MockQueryResolver_GetUserResultByDailyChallengeID_Call {
	_c.Call.Return(userDailyChallengeResultWithStats, err)
	return _c
}

func (_c *MockQueryResolver_GetUserResultByDailyChallengeID_Call) RunAndReturn(run func(ctx context.Context, challengeID primitive.ObjectID) (*models.UserDailyChallengeResultWithStats, error)) *MockQueryResolver_GetUserResultByDailyChallengeID_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserResultByDivision provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetUserResultByDivision(ctx context.Context, dateStr *string, division *models.ChallengeDivision) (*models.UserResult, error) {
	ret := _mock.Called(ctx, dateStr, division)

	if len(ret) == 0 {
		panic("no return value specified for GetUserResultByDivision")
	}

	var r0 *models.UserResult
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *models.ChallengeDivision) (*models.UserResult, error)); ok {
		return returnFunc(ctx, dateStr, division)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *models.ChallengeDivision) *models.UserResult); ok {
		r0 = returnFunc(ctx, dateStr, division)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserResult)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string, *models.ChallengeDivision) error); ok {
		r1 = returnFunc(ctx, dateStr, division)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetUserResultByDivision_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserResultByDivision'
type MockQueryResolver_GetUserResultByDivision_Call struct {
	*mock.Call
}

// GetUserResultByDivision is a helper method to define mock.On call
//   - ctx context.Context
//   - dateStr *string
//   - division *models.ChallengeDivision
func (_e *MockQueryResolver_Expecter) GetUserResultByDivision(ctx interface{}, dateStr interface{}, division interface{}) *MockQueryResolver_GetUserResultByDivision_Call {
	return &MockQueryResolver_GetUserResultByDivision_Call{Call: _e.mock.On("GetUserResultByDivision", ctx, dateStr, division)}
}

func (_c *MockQueryResolver_GetUserResultByDivision_Call) Run(run func(ctx context.Context, dateStr *string, division *models.ChallengeDivision)) *MockQueryResolver_GetUserResultByDivision_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *string
		if args[1] != nil {
			arg1 = args[1].(*string)
		}
		var arg2 *models.ChallengeDivision
		if args[2] != nil {
			arg2 = args[2].(*models.ChallengeDivision)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetUserResultByDivision_Call) Return(userResult *models.UserResult, err error) *MockQueryResolver_GetUserResultByDivision_Call {
	_c.Call.Return(userResult, err)
	return _c
}

func (_c *MockQueryResolver_GetUserResultByDivision_Call) RunAndReturn(run func(ctx context.Context, dateStr *string, division *models.ChallengeDivision) (*models.UserResult, error)) *MockQueryResolver_GetUserResultByDivision_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserResultByDivison provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetUserResultByDivison(ctx context.Context, dateStr *string, division *models.ChallengeDivision) (*models.UserResult, error) {
	ret := _mock.Called(ctx, dateStr, division)

	if len(ret) == 0 {
		panic("no return value specified for GetUserResultByDivison")
	}

	var r0 *models.UserResult
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *models.ChallengeDivision) (*models.UserResult, error)); ok {
		return returnFunc(ctx, dateStr, division)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *models.ChallengeDivision) *models.UserResult); ok {
		r0 = returnFunc(ctx, dateStr, division)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserResult)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string, *models.ChallengeDivision) error); ok {
		r1 = returnFunc(ctx, dateStr, division)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetUserResultByDivison_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserResultByDivison'
type MockQueryResolver_GetUserResultByDivison_Call struct {
	*mock.Call
}

// GetUserResultByDivison is a helper method to define mock.On call
//   - ctx context.Context
//   - dateStr *string
//   - division *models.ChallengeDivision
func (_e *MockQueryResolver_Expecter) GetUserResultByDivison(ctx interface{}, dateStr interface{}, division interface{}) *MockQueryResolver_GetUserResultByDivison_Call {
	return &MockQueryResolver_GetUserResultByDivison_Call{Call: _e.mock.On("GetUserResultByDivison", ctx, dateStr, division)}
}

func (_c *MockQueryResolver_GetUserResultByDivison_Call) Run(run func(ctx context.Context, dateStr *string, division *models.ChallengeDivision)) *MockQueryResolver_GetUserResultByDivison_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *string
		if args[1] != nil {
			arg1 = args[1].(*string)
		}
		var arg2 *models.ChallengeDivision
		if args[2] != nil {
			arg2 = args[2].(*models.ChallengeDivision)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetUserResultByDivison_Call) Return(userResult *models.UserResult, err error) *MockQueryResolver_GetUserResultByDivison_Call {
	_c.Call.Return(userResult, err)
	return _c
}

func (_c *MockQueryResolver_GetUserResultByDivison_Call) RunAndReturn(run func(ctx context.Context, dateStr *string, division *models.ChallengeDivision) (*models.UserResult, error)) *MockQueryResolver_GetUserResultByDivison_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserSavedPresets provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetUserSavedPresets(ctx context.Context, page *int, pageSize *int) (*models.UserPresets, error) {
	ret := _mock.Called(ctx, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetUserSavedPresets")
	}

	var r0 *models.UserPresets
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int) (*models.UserPresets, error)); ok {
		return returnFunc(ctx, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int) *models.UserPresets); ok {
		r0 = returnFunc(ctx, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserPresets)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *int, *int) error); ok {
		r1 = returnFunc(ctx, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetUserSavedPresets_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserSavedPresets'
type MockQueryResolver_GetUserSavedPresets_Call struct {
	*mock.Call
}

// GetUserSavedPresets is a helper method to define mock.On call
//   - ctx context.Context
//   - page *int
//   - pageSize *int
func (_e *MockQueryResolver_Expecter) GetUserSavedPresets(ctx interface{}, page interface{}, pageSize interface{}) *MockQueryResolver_GetUserSavedPresets_Call {
	return &MockQueryResolver_GetUserSavedPresets_Call{Call: _e.mock.On("GetUserSavedPresets", ctx, page, pageSize)}
}

func (_c *MockQueryResolver_GetUserSavedPresets_Call) Run(run func(ctx context.Context, page *int, pageSize *int)) *MockQueryResolver_GetUserSavedPresets_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *int
		if args[1] != nil {
			arg1 = args[1].(*int)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetUserSavedPresets_Call) Return(userPresets *models.UserPresets, err error) *MockQueryResolver_GetUserSavedPresets_Call {
	_c.Call.Return(userPresets, err)
	return _c
}

func (_c *MockQueryResolver_GetUserSavedPresets_Call) RunAndReturn(run func(ctx context.Context, page *int, pageSize *int) (*models.UserPresets, error)) *MockQueryResolver_GetUserSavedPresets_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserSettings provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetUserSettings(ctx context.Context) (*models.UserSettings, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetUserSettings")
	}

	var r0 *models.UserSettings
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*models.UserSettings, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *models.UserSettings); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserSettings)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetUserSettings_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserSettings'
type MockQueryResolver_GetUserSettings_Call struct {
	*mock.Call
}

// GetUserSettings is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockQueryResolver_Expecter) GetUserSettings(ctx interface{}) *MockQueryResolver_GetUserSettings_Call {
	return &MockQueryResolver_GetUserSettings_Call{Call: _e.mock.On("GetUserSettings", ctx)}
}

func (_c *MockQueryResolver_GetUserSettings_Call) Run(run func(ctx context.Context)) *MockQueryResolver_GetUserSettings_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetUserSettings_Call) Return(userSettings *models.UserSettings, err error) *MockQueryResolver_GetUserSettings_Call {
	_c.Call.Return(userSettings, err)
	return _c
}

func (_c *MockQueryResolver_GetUserSettings_Call) RunAndReturn(run func(ctx context.Context) (*models.UserSettings, error)) *MockQueryResolver_GetUserSettings_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserStreakHistoryByMonth provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetUserStreakHistoryByMonth(ctx context.Context, yearMonths []string) ([]*models.StreakEntry, error) {
	ret := _mock.Called(ctx, yearMonths)

	if len(ret) == 0 {
		panic("no return value specified for GetUserStreakHistoryByMonth")
	}

	var r0 []*models.StreakEntry
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, []string) ([]*models.StreakEntry, error)); ok {
		return returnFunc(ctx, yearMonths)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, []string) []*models.StreakEntry); ok {
		r0 = returnFunc(ctx, yearMonths)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.StreakEntry)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, []string) error); ok {
		r1 = returnFunc(ctx, yearMonths)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetUserStreakHistoryByMonth_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserStreakHistoryByMonth'
type MockQueryResolver_GetUserStreakHistoryByMonth_Call struct {
	*mock.Call
}

// GetUserStreakHistoryByMonth is a helper method to define mock.On call
//   - ctx context.Context
//   - yearMonths []string
func (_e *MockQueryResolver_Expecter) GetUserStreakHistoryByMonth(ctx interface{}, yearMonths interface{}) *MockQueryResolver_GetUserStreakHistoryByMonth_Call {
	return &MockQueryResolver_GetUserStreakHistoryByMonth_Call{Call: _e.mock.On("GetUserStreakHistoryByMonth", ctx, yearMonths)}
}

func (_c *MockQueryResolver_GetUserStreakHistoryByMonth_Call) Run(run func(ctx context.Context, yearMonths []string)) *MockQueryResolver_GetUserStreakHistoryByMonth_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 []string
		if args[1] != nil {
			arg1 = args[1].([]string)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetUserStreakHistoryByMonth_Call) Return(streakEntrys []*models.StreakEntry, err error) *MockQueryResolver_GetUserStreakHistoryByMonth_Call {
	_c.Call.Return(streakEntrys, err)
	return _c
}

func (_c *MockQueryResolver_GetUserStreakHistoryByMonth_Call) RunAndReturn(run func(ctx context.Context, yearMonths []string) ([]*models.StreakEntry, error)) *MockQueryResolver_GetUserStreakHistoryByMonth_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserStreakShieldTransactions provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetUserStreakShieldTransactions(ctx context.Context, page *int, pageSize *int) (*models.StreakShieldTransactionPage, error) {
	ret := _mock.Called(ctx, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetUserStreakShieldTransactions")
	}

	var r0 *models.StreakShieldTransactionPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int) (*models.StreakShieldTransactionPage, error)); ok {
		return returnFunc(ctx, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int) *models.StreakShieldTransactionPage); ok {
		r0 = returnFunc(ctx, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.StreakShieldTransactionPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *int, *int) error); ok {
		r1 = returnFunc(ctx, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetUserStreakShieldTransactions_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserStreakShieldTransactions'
type MockQueryResolver_GetUserStreakShieldTransactions_Call struct {
	*mock.Call
}

// GetUserStreakShieldTransactions is a helper method to define mock.On call
//   - ctx context.Context
//   - page *int
//   - pageSize *int
func (_e *MockQueryResolver_Expecter) GetUserStreakShieldTransactions(ctx interface{}, page interface{}, pageSize interface{}) *MockQueryResolver_GetUserStreakShieldTransactions_Call {
	return &MockQueryResolver_GetUserStreakShieldTransactions_Call{Call: _e.mock.On("GetUserStreakShieldTransactions", ctx, page, pageSize)}
}

func (_c *MockQueryResolver_GetUserStreakShieldTransactions_Call) Run(run func(ctx context.Context, page *int, pageSize *int)) *MockQueryResolver_GetUserStreakShieldTransactions_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *int
		if args[1] != nil {
			arg1 = args[1].(*int)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetUserStreakShieldTransactions_Call) Return(streakShieldTransactionPage *models.StreakShieldTransactionPage, err error) *MockQueryResolver_GetUserStreakShieldTransactions_Call {
	_c.Call.Return(streakShieldTransactionPage, err)
	return _c
}

func (_c *MockQueryResolver_GetUserStreakShieldTransactions_Call) RunAndReturn(run func(ctx context.Context, page *int, pageSize *int) (*models.StreakShieldTransactionPage, error)) *MockQueryResolver_GetUserStreakShieldTransactions_Call {
	_c.Call.Return(run)
	return _c
}

// GetUsersAllPlayedPresets provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetUsersAllPlayedPresets(ctx context.Context, username *string) (*models.AllPlayedPresetsOutput, error) {
	ret := _mock.Called(ctx, username)

	if len(ret) == 0 {
		panic("no return value specified for GetUsersAllPlayedPresets")
	}

	var r0 *models.AllPlayedPresetsOutput
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) (*models.AllPlayedPresetsOutput, error)); ok {
		return returnFunc(ctx, username)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string) *models.AllPlayedPresetsOutput); ok {
		r0 = returnFunc(ctx, username)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.AllPlayedPresetsOutput)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string) error); ok {
		r1 = returnFunc(ctx, username)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetUsersAllPlayedPresets_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUsersAllPlayedPresets'
type MockQueryResolver_GetUsersAllPlayedPresets_Call struct {
	*mock.Call
}

// GetUsersAllPlayedPresets is a helper method to define mock.On call
//   - ctx context.Context
//   - username *string
func (_e *MockQueryResolver_Expecter) GetUsersAllPlayedPresets(ctx interface{}, username interface{}) *MockQueryResolver_GetUsersAllPlayedPresets_Call {
	return &MockQueryResolver_GetUsersAllPlayedPresets_Call{Call: _e.mock.On("GetUsersAllPlayedPresets", ctx, username)}
}

func (_c *MockQueryResolver_GetUsersAllPlayedPresets_Call) Run(run func(ctx context.Context, username *string)) *MockQueryResolver_GetUsersAllPlayedPresets_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *string
		if args[1] != nil {
			arg1 = args[1].(*string)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetUsersAllPlayedPresets_Call) Return(allPlayedPresetsOutput *models.AllPlayedPresetsOutput, err error) *MockQueryResolver_GetUsersAllPlayedPresets_Call {
	_c.Call.Return(allPlayedPresetsOutput, err)
	return _c
}

func (_c *MockQueryResolver_GetUsersAllPlayedPresets_Call) RunAndReturn(run func(ctx context.Context, username *string) (*models.AllPlayedPresetsOutput, error)) *MockQueryResolver_GetUsersAllPlayedPresets_Call {
	_c.Call.Return(run)
	return _c
}

// GetUsersOfMyInstitute provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetUsersOfMyInstitute(ctx context.Context, page *int, pageSize *int) (*models.MyInstituteUsersPage, error) {
	ret := _mock.Called(ctx, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetUsersOfMyInstitute")
	}

	var r0 *models.MyInstituteUsersPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int) (*models.MyInstituteUsersPage, error)); ok {
		return returnFunc(ctx, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *int, *int) *models.MyInstituteUsersPage); ok {
		r0 = returnFunc(ctx, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.MyInstituteUsersPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *int, *int) error); ok {
		r1 = returnFunc(ctx, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetUsersOfMyInstitute_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUsersOfMyInstitute'
type MockQueryResolver_GetUsersOfMyInstitute_Call struct {
	*mock.Call
}

// GetUsersOfMyInstitute is a helper method to define mock.On call
//   - ctx context.Context
//   - page *int
//   - pageSize *int
func (_e *MockQueryResolver_Expecter) GetUsersOfMyInstitute(ctx interface{}, page interface{}, pageSize interface{}) *MockQueryResolver_GetUsersOfMyInstitute_Call {
	return &MockQueryResolver_GetUsersOfMyInstitute_Call{Call: _e.mock.On("GetUsersOfMyInstitute", ctx, page, pageSize)}
}

func (_c *MockQueryResolver_GetUsersOfMyInstitute_Call) Run(run func(ctx context.Context, page *int, pageSize *int)) *MockQueryResolver_GetUsersOfMyInstitute_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *int
		if args[1] != nil {
			arg1 = args[1].(*int)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetUsersOfMyInstitute_Call) Return(myInstituteUsersPage *models.MyInstituteUsersPage, err error) *MockQueryResolver_GetUsersOfMyInstitute_Call {
	_c.Call.Return(myInstituteUsersPage, err)
	return _c
}

func (_c *MockQueryResolver_GetUsersOfMyInstitute_Call) RunAndReturn(run func(ctx context.Context, page *int, pageSize *int) (*models.MyInstituteUsersPage, error)) *MockQueryResolver_GetUsersOfMyInstitute_Call {
	_c.Call.Return(run)
	return _c
}

// GetUsersWeeklyStatikCoins provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetUsersWeeklyStatikCoins(ctx context.Context) (int, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetUsersWeeklyStatikCoins")
	}

	var r0 int
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (int, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) int); ok {
		r0 = returnFunc(ctx)
	} else {
		r0 = ret.Get(0).(int)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetUsersWeeklyStatikCoins_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUsersWeeklyStatikCoins'
type MockQueryResolver_GetUsersWeeklyStatikCoins_Call struct {
	*mock.Call
}

// GetUsersWeeklyStatikCoins is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockQueryResolver_Expecter) GetUsersWeeklyStatikCoins(ctx interface{}) *MockQueryResolver_GetUsersWeeklyStatikCoins_Call {
	return &MockQueryResolver_GetUsersWeeklyStatikCoins_Call{Call: _e.mock.On("GetUsersWeeklyStatikCoins", ctx)}
}

func (_c *MockQueryResolver_GetUsersWeeklyStatikCoins_Call) Run(run func(ctx context.Context)) *MockQueryResolver_GetUsersWeeklyStatikCoins_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetUsersWeeklyStatikCoins_Call) Return(n int, err error) *MockQueryResolver_GetUsersWeeklyStatikCoins_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockQueryResolver_GetUsersWeeklyStatikCoins_Call) RunAndReturn(run func(ctx context.Context) (int, error)) *MockQueryResolver_GetUsersWeeklyStatikCoins_Call {
	_c.Call.Return(run)
	return _c
}

// GetUsersWeeklyStatikCoinsV2 provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) GetUsersWeeklyStatikCoinsV2(ctx context.Context, userID primitive.ObjectID) (*models.UsersWeeklyStatikCoinsOutput, error) {
	ret := _mock.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetUsersWeeklyStatikCoinsV2")
	}

	var r0 *models.UsersWeeklyStatikCoinsOutput
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.UsersWeeklyStatikCoinsOutput, error)); ok {
		return returnFunc(ctx, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.UsersWeeklyStatikCoinsOutput); ok {
		r0 = returnFunc(ctx, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UsersWeeklyStatikCoinsOutput)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_GetUsersWeeklyStatikCoinsV2_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUsersWeeklyStatikCoinsV2'
type MockQueryResolver_GetUsersWeeklyStatikCoinsV2_Call struct {
	*mock.Call
}

// GetUsersWeeklyStatikCoinsV2 is a helper method to define mock.On call
//   - ctx context.Context
//   - userID primitive.ObjectID
func (_e *MockQueryResolver_Expecter) GetUsersWeeklyStatikCoinsV2(ctx interface{}, userID interface{}) *MockQueryResolver_GetUsersWeeklyStatikCoinsV2_Call {
	return &MockQueryResolver_GetUsersWeeklyStatikCoinsV2_Call{Call: _e.mock.On("GetUsersWeeklyStatikCoinsV2", ctx, userID)}
}

func (_c *MockQueryResolver_GetUsersWeeklyStatikCoinsV2_Call) Run(run func(ctx context.Context, userID primitive.ObjectID)) *MockQueryResolver_GetUsersWeeklyStatikCoinsV2_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockQueryResolver_GetUsersWeeklyStatikCoinsV2_Call) Return(usersWeeklyStatikCoinsOutput *models.UsersWeeklyStatikCoinsOutput, err error) *MockQueryResolver_GetUsersWeeklyStatikCoinsV2_Call {
	_c.Call.Return(usersWeeklyStatikCoinsOutput, err)
	return _c
}

func (_c *MockQueryResolver_GetUsersWeeklyStatikCoinsV2_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID) (*models.UsersWeeklyStatikCoinsOutput, error)) *MockQueryResolver_GetUsersWeeklyStatikCoinsV2_Call {
	_c.Call.Return(run)
	return _c
}

// IsUsernameAvailable provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) IsUsernameAvailable(ctx context.Context, username string) (bool, error) {
	ret := _mock.Called(ctx, username)

	if len(ret) == 0 {
		panic("no return value specified for IsUsernameAvailable")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (bool, error)); ok {
		return returnFunc(ctx, username)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) bool); ok {
		r0 = returnFunc(ctx, username)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, username)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_IsUsernameAvailable_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsUsernameAvailable'
type MockQueryResolver_IsUsernameAvailable_Call struct {
	*mock.Call
}

// IsUsernameAvailable is a helper method to define mock.On call
//   - ctx context.Context
//   - username string
func (_e *MockQueryResolver_Expecter) IsUsernameAvailable(ctx interface{}, username interface{}) *MockQueryResolver_IsUsernameAvailable_Call {
	return &MockQueryResolver_IsUsernameAvailable_Call{Call: _e.mock.On("IsUsernameAvailable", ctx, username)}
}

func (_c *MockQueryResolver_IsUsernameAvailable_Call) Run(run func(ctx context.Context, username string)) *MockQueryResolver_IsUsernameAvailable_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockQueryResolver_IsUsernameAvailable_Call) Return(b bool, err error) *MockQueryResolver_IsUsernameAvailable_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockQueryResolver_IsUsernameAvailable_Call) RunAndReturn(run func(ctx context.Context, username string) (bool, error)) *MockQueryResolver_IsUsernameAvailable_Call {
	_c.Call.Return(run)
	return _c
}

// Leaderboard provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) Leaderboard(ctx context.Context, countryCode *string, searchKey *string, first *int, after *string) (*models.LeaderboardConnection, error) {
	ret := _mock.Called(ctx, countryCode, searchKey, first, after)

	if len(ret) == 0 {
		panic("no return value specified for Leaderboard")
	}

	var r0 *models.LeaderboardConnection
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *string, *int, *string) (*models.LeaderboardConnection, error)); ok {
		return returnFunc(ctx, countryCode, searchKey, first, after)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *string, *int, *string) *models.LeaderboardConnection); ok {
		r0 = returnFunc(ctx, countryCode, searchKey, first, after)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.LeaderboardConnection)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string, *string, *int, *string) error); ok {
		r1 = returnFunc(ctx, countryCode, searchKey, first, after)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_Leaderboard_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Leaderboard'
type MockQueryResolver_Leaderboard_Call struct {
	*mock.Call
}

// Leaderboard is a helper method to define mock.On call
//   - ctx context.Context
//   - countryCode *string
//   - searchKey *string
//   - first *int
//   - after *string
func (_e *MockQueryResolver_Expecter) Leaderboard(ctx interface{}, countryCode interface{}, searchKey interface{}, first interface{}, after interface{}) *MockQueryResolver_Leaderboard_Call {
	return &MockQueryResolver_Leaderboard_Call{Call: _e.mock.On("Leaderboard", ctx, countryCode, searchKey, first, after)}
}

func (_c *MockQueryResolver_Leaderboard_Call) Run(run func(ctx context.Context, countryCode *string, searchKey *string, first *int, after *string)) *MockQueryResolver_Leaderboard_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *string
		if args[1] != nil {
			arg1 = args[1].(*string)
		}
		var arg2 *string
		if args[2] != nil {
			arg2 = args[2].(*string)
		}
		var arg3 *int
		if args[3] != nil {
			arg3 = args[3].(*int)
		}
		var arg4 *string
		if args[4] != nil {
			arg4 = args[4].(*string)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
			arg4,
		)
	})
	return _c
}

func (_c *MockQueryResolver_Leaderboard_Call) Return(leaderboardConnection *models.LeaderboardConnection, err error) *MockQueryResolver_Leaderboard_Call {
	_c.Call.Return(leaderboardConnection, err)
	return _c
}

func (_c *MockQueryResolver_Leaderboard_Call) RunAndReturn(run func(ctx context.Context, countryCode *string, searchKey *string, first *int, after *string) (*models.LeaderboardConnection, error)) *MockQueryResolver_Leaderboard_Call {
	_c.Call.Return(run)
	return _c
}

// LeaderboardNew provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) LeaderboardNew(ctx context.Context, countryCode *string, searchKey *string, page *int, limit *int, ratingType *string) (*models.UserLeaderboardPage, error) {
	ret := _mock.Called(ctx, countryCode, searchKey, page, limit, ratingType)

	if len(ret) == 0 {
		panic("no return value specified for LeaderboardNew")
	}

	var r0 *models.UserLeaderboardPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *string, *int, *int, *string) (*models.UserLeaderboardPage, error)); ok {
		return returnFunc(ctx, countryCode, searchKey, page, limit, ratingType)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *string, *int, *int, *string) *models.UserLeaderboardPage); ok {
		r0 = returnFunc(ctx, countryCode, searchKey, page, limit, ratingType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserLeaderboardPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string, *string, *int, *int, *string) error); ok {
		r1 = returnFunc(ctx, countryCode, searchKey, page, limit, ratingType)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_LeaderboardNew_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LeaderboardNew'
type MockQueryResolver_LeaderboardNew_Call struct {
	*mock.Call
}

// LeaderboardNew is a helper method to define mock.On call
//   - ctx context.Context
//   - countryCode *string
//   - searchKey *string
//   - page *int
//   - limit *int
//   - ratingType *string
func (_e *MockQueryResolver_Expecter) LeaderboardNew(ctx interface{}, countryCode interface{}, searchKey interface{}, page interface{}, limit interface{}, ratingType interface{}) *MockQueryResolver_LeaderboardNew_Call {
	return &MockQueryResolver_LeaderboardNew_Call{Call: _e.mock.On("LeaderboardNew", ctx, countryCode, searchKey, page, limit, ratingType)}
}

func (_c *MockQueryResolver_LeaderboardNew_Call) Run(run func(ctx context.Context, countryCode *string, searchKey *string, page *int, limit *int, ratingType *string)) *MockQueryResolver_LeaderboardNew_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *string
		if args[1] != nil {
			arg1 = args[1].(*string)
		}
		var arg2 *string
		if args[2] != nil {
			arg2 = args[2].(*string)
		}
		var arg3 *int
		if args[3] != nil {
			arg3 = args[3].(*int)
		}
		var arg4 *int
		if args[4] != nil {
			arg4 = args[4].(*int)
		}
		var arg5 *string
		if args[5] != nil {
			arg5 = args[5].(*string)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
			arg4,
			arg5,
		)
	})
	return _c
}

func (_c *MockQueryResolver_LeaderboardNew_Call) Return(userLeaderboardPage *models.UserLeaderboardPage, err error) *MockQueryResolver_LeaderboardNew_Call {
	_c.Call.Return(userLeaderboardPage, err)
	return _c
}

func (_c *MockQueryResolver_LeaderboardNew_Call) RunAndReturn(run func(ctx context.Context, countryCode *string, searchKey *string, page *int, limit *int, ratingType *string) (*models.UserLeaderboardPage, error)) *MockQueryResolver_LeaderboardNew_Call {
	_c.Call.Return(run)
	return _c
}

// LeaderboardV3 provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) LeaderboardV3(ctx context.Context, countryCode *string, searchKey *string, page *int, limit *int) (*models.UserLeaderboardPage, error) {
	ret := _mock.Called(ctx, countryCode, searchKey, page, limit)

	if len(ret) == 0 {
		panic("no return value specified for LeaderboardV3")
	}

	var r0 *models.UserLeaderboardPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *string, *int, *int) (*models.UserLeaderboardPage, error)); ok {
		return returnFunc(ctx, countryCode, searchKey, page, limit)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *string, *int, *int) *models.UserLeaderboardPage); ok {
		r0 = returnFunc(ctx, countryCode, searchKey, page, limit)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserLeaderboardPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string, *string, *int, *int) error); ok {
		r1 = returnFunc(ctx, countryCode, searchKey, page, limit)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_LeaderboardV3_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LeaderboardV3'
type MockQueryResolver_LeaderboardV3_Call struct {
	*mock.Call
}

// LeaderboardV3 is a helper method to define mock.On call
//   - ctx context.Context
//   - countryCode *string
//   - searchKey *string
//   - page *int
//   - limit *int
func (_e *MockQueryResolver_Expecter) LeaderboardV3(ctx interface{}, countryCode interface{}, searchKey interface{}, page interface{}, limit interface{}) *MockQueryResolver_LeaderboardV3_Call {
	return &MockQueryResolver_LeaderboardV3_Call{Call: _e.mock.On("LeaderboardV3", ctx, countryCode, searchKey, page, limit)}
}

func (_c *MockQueryResolver_LeaderboardV3_Call) Run(run func(ctx context.Context, countryCode *string, searchKey *string, page *int, limit *int)) *MockQueryResolver_LeaderboardV3_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *string
		if args[1] != nil {
			arg1 = args[1].(*string)
		}
		var arg2 *string
		if args[2] != nil {
			arg2 = args[2].(*string)
		}
		var arg3 *int
		if args[3] != nil {
			arg3 = args[3].(*int)
		}
		var arg4 *int
		if args[4] != nil {
			arg4 = args[4].(*int)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
			arg4,
		)
	})
	return _c
}

func (_c *MockQueryResolver_LeaderboardV3_Call) Return(userLeaderboardPage *models.UserLeaderboardPage, err error) *MockQueryResolver_LeaderboardV3_Call {
	_c.Call.Return(userLeaderboardPage, err)
	return _c
}

func (_c *MockQueryResolver_LeaderboardV3_Call) RunAndReturn(run func(ctx context.Context, countryCode *string, searchKey *string, page *int, limit *int) (*models.UserLeaderboardPage, error)) *MockQueryResolver_LeaderboardV3_Call {
	_c.Call.Return(run)
	return _c
}

// Login provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) Login(ctx context.Context, email string, password string) (*models.User, error) {
	ret := _mock.Called(ctx, email, password)

	if len(ret) == 0 {
		panic("no return value specified for Login")
	}

	var r0 *models.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string) (*models.User, error)); ok {
		return returnFunc(ctx, email, password)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string) *models.User); ok {
		r0 = returnFunc(ctx, email, password)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = returnFunc(ctx, email, password)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_Login_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Login'
type MockQueryResolver_Login_Call struct {
	*mock.Call
}

// Login is a helper method to define mock.On call
//   - ctx context.Context
//   - email string
//   - password string
func (_e *MockQueryResolver_Expecter) Login(ctx interface{}, email interface{}, password interface{}) *MockQueryResolver_Login_Call {
	return &MockQueryResolver_Login_Call{Call: _e.mock.On("Login", ctx, email, password)}
}

func (_c *MockQueryResolver_Login_Call) Run(run func(ctx context.Context, email string, password string)) *MockQueryResolver_Login_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		var arg2 string
		if args[2] != nil {
			arg2 = args[2].(string)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockQueryResolver_Login_Call) Return(user *models.User, err error) *MockQueryResolver_Login_Call {
	_c.Call.Return(user, err)
	return _c
}

func (_c *MockQueryResolver_Login_Call) RunAndReturn(run func(ctx context.Context, email string, password string) (*models.User, error)) *MockQueryResolver_Login_Call {
	_c.Call.Return(run)
	return _c
}

// OnlineUsers provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) OnlineUsers(ctx context.Context, page int, pageSize int) (*models.OnlineUsersPage, error) {
	ret := _mock.Called(ctx, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for OnlineUsers")
	}

	var r0 *models.OnlineUsersPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, int, int) (*models.OnlineUsersPage, error)); ok {
		return returnFunc(ctx, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, int, int) *models.OnlineUsersPage); ok {
		r0 = returnFunc(ctx, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.OnlineUsersPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, int, int) error); ok {
		r1 = returnFunc(ctx, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_OnlineUsers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'OnlineUsers'
type MockQueryResolver_OnlineUsers_Call struct {
	*mock.Call
}

// OnlineUsers is a helper method to define mock.On call
//   - ctx context.Context
//   - page int
//   - pageSize int
func (_e *MockQueryResolver_Expecter) OnlineUsers(ctx interface{}, page interface{}, pageSize interface{}) *MockQueryResolver_OnlineUsers_Call {
	return &MockQueryResolver_OnlineUsers_Call{Call: _e.mock.On("OnlineUsers", ctx, page, pageSize)}
}

func (_c *MockQueryResolver_OnlineUsers_Call) Run(run func(ctx context.Context, page int, pageSize int)) *MockQueryResolver_OnlineUsers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 int
		if args[1] != nil {
			arg1 = args[1].(int)
		}
		var arg2 int
		if args[2] != nil {
			arg2 = args[2].(int)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockQueryResolver_OnlineUsers_Call) Return(onlineUsersPage *models.OnlineUsersPage, err error) *MockQueryResolver_OnlineUsers_Call {
	_c.Call.Return(onlineUsersPage, err)
	return _c
}

func (_c *MockQueryResolver_OnlineUsers_Call) RunAndReturn(run func(ctx context.Context, page int, pageSize int) (*models.OnlineUsersPage, error)) *MockQueryResolver_OnlineUsers_Call {
	_c.Call.Return(run)
	return _c
}

// SearchInstitutions provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) SearchInstitutions(ctx context.Context, query string, limit *int) ([]*models.Institution, error) {
	ret := _mock.Called(ctx, query, limit)

	if len(ret) == 0 {
		panic("no return value specified for SearchInstitutions")
	}

	var r0 []*models.Institution
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, *int) ([]*models.Institution, error)); ok {
		return returnFunc(ctx, query, limit)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, *int) []*models.Institution); ok {
		r0 = returnFunc(ctx, query, limit)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.Institution)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, *int) error); ok {
		r1 = returnFunc(ctx, query, limit)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_SearchInstitutions_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SearchInstitutions'
type MockQueryResolver_SearchInstitutions_Call struct {
	*mock.Call
}

// SearchInstitutions is a helper method to define mock.On call
//   - ctx context.Context
//   - query string
//   - limit *int
func (_e *MockQueryResolver_Expecter) SearchInstitutions(ctx interface{}, query interface{}, limit interface{}) *MockQueryResolver_SearchInstitutions_Call {
	return &MockQueryResolver_SearchInstitutions_Call{Call: _e.mock.On("SearchInstitutions", ctx, query, limit)}
}

func (_c *MockQueryResolver_SearchInstitutions_Call) Run(run func(ctx context.Context, query string, limit *int)) *MockQueryResolver_SearchInstitutions_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockQueryResolver_SearchInstitutions_Call) Return(institutions []*models.Institution, err error) *MockQueryResolver_SearchInstitutions_Call {
	_c.Call.Return(institutions, err)
	return _c
}

func (_c *MockQueryResolver_SearchInstitutions_Call) RunAndReturn(run func(ctx context.Context, query string, limit *int) ([]*models.Institution, error)) *MockQueryResolver_SearchInstitutions_Call {
	_c.Call.Return(run)
	return _c
}

// SearchUsersInMyInstitute provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) SearchUsersInMyInstitute(ctx context.Context, searchKey *string, page *int, pageSize *int) (*models.MyInstituteUsersPage, error) {
	ret := _mock.Called(ctx, searchKey, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for SearchUsersInMyInstitute")
	}

	var r0 *models.MyInstituteUsersPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *int, *int) (*models.MyInstituteUsersPage, error)); ok {
		return returnFunc(ctx, searchKey, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *string, *int, *int) *models.MyInstituteUsersPage); ok {
		r0 = returnFunc(ctx, searchKey, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.MyInstituteUsersPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *string, *int, *int) error); ok {
		r1 = returnFunc(ctx, searchKey, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_SearchUsersInMyInstitute_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SearchUsersInMyInstitute'
type MockQueryResolver_SearchUsersInMyInstitute_Call struct {
	*mock.Call
}

// SearchUsersInMyInstitute is a helper method to define mock.On call
//   - ctx context.Context
//   - searchKey *string
//   - page *int
//   - pageSize *int
func (_e *MockQueryResolver_Expecter) SearchUsersInMyInstitute(ctx interface{}, searchKey interface{}, page interface{}, pageSize interface{}) *MockQueryResolver_SearchUsersInMyInstitute_Call {
	return &MockQueryResolver_SearchUsersInMyInstitute_Call{Call: _e.mock.On("SearchUsersInMyInstitute", ctx, searchKey, page, pageSize)}
}

func (_c *MockQueryResolver_SearchUsersInMyInstitute_Call) Run(run func(ctx context.Context, searchKey *string, page *int, pageSize *int)) *MockQueryResolver_SearchUsersInMyInstitute_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *string
		if args[1] != nil {
			arg1 = args[1].(*string)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		var arg3 *int
		if args[3] != nil {
			arg3 = args[3].(*int)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockQueryResolver_SearchUsersInMyInstitute_Call) Return(myInstituteUsersPage *models.MyInstituteUsersPage, err error) *MockQueryResolver_SearchUsersInMyInstitute_Call {
	_c.Call.Return(myInstituteUsersPage, err)
	return _c
}

func (_c *MockQueryResolver_SearchUsersInMyInstitute_Call) RunAndReturn(run func(ctx context.Context, searchKey *string, page *int, pageSize *int) (*models.MyInstituteUsersPage, error)) *MockQueryResolver_SearchUsersInMyInstitute_Call {
	_c.Call.Return(run)
	return _c
}

// StatikCoinsLeaderboard provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) StatikCoinsLeaderboard(ctx context.Context, page int, pageSize *int, leaderboardType *models.StatikCoinLeaderboardType) (*models.StatikCoinLeaderboardPage, error) {
	ret := _mock.Called(ctx, page, pageSize, leaderboardType)

	if len(ret) == 0 {
		panic("no return value specified for StatikCoinsLeaderboard")
	}

	var r0 *models.StatikCoinLeaderboardPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, int, *int, *models.StatikCoinLeaderboardType) (*models.StatikCoinLeaderboardPage, error)); ok {
		return returnFunc(ctx, page, pageSize, leaderboardType)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, int, *int, *models.StatikCoinLeaderboardType) *models.StatikCoinLeaderboardPage); ok {
		r0 = returnFunc(ctx, page, pageSize, leaderboardType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.StatikCoinLeaderboardPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, int, *int, *models.StatikCoinLeaderboardType) error); ok {
		r1 = returnFunc(ctx, page, pageSize, leaderboardType)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_StatikCoinsLeaderboard_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'StatikCoinsLeaderboard'
type MockQueryResolver_StatikCoinsLeaderboard_Call struct {
	*mock.Call
}

// StatikCoinsLeaderboard is a helper method to define mock.On call
//   - ctx context.Context
//   - page int
//   - pageSize *int
//   - leaderboardType *models.StatikCoinLeaderboardType
func (_e *MockQueryResolver_Expecter) StatikCoinsLeaderboard(ctx interface{}, page interface{}, pageSize interface{}, leaderboardType interface{}) *MockQueryResolver_StatikCoinsLeaderboard_Call {
	return &MockQueryResolver_StatikCoinsLeaderboard_Call{Call: _e.mock.On("StatikCoinsLeaderboard", ctx, page, pageSize, leaderboardType)}
}

func (_c *MockQueryResolver_StatikCoinsLeaderboard_Call) Run(run func(ctx context.Context, page int, pageSize *int, leaderboardType *models.StatikCoinLeaderboardType)) *MockQueryResolver_StatikCoinsLeaderboard_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 int
		if args[1] != nil {
			arg1 = args[1].(int)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		var arg3 *models.StatikCoinLeaderboardType
		if args[3] != nil {
			arg3 = args[3].(*models.StatikCoinLeaderboardType)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockQueryResolver_StatikCoinsLeaderboard_Call) Return(statikCoinLeaderboardPage *models.StatikCoinLeaderboardPage, err error) *MockQueryResolver_StatikCoinsLeaderboard_Call {
	_c.Call.Return(statikCoinLeaderboardPage, err)
	return _c
}

func (_c *MockQueryResolver_StatikCoinsLeaderboard_Call) RunAndReturn(run func(ctx context.Context, page int, pageSize *int, leaderboardType *models.StatikCoinLeaderboardType) (*models.StatikCoinLeaderboardPage, error)) *MockQueryResolver_StatikCoinsLeaderboard_Call {
	_c.Call.Return(run)
	return _c
}

// VerifyToken provides a mock function for the type MockQueryResolver
func (_mock *MockQueryResolver) VerifyToken(ctx context.Context, token string) (*models.User, error) {
	ret := _mock.Called(ctx, token)

	if len(ret) == 0 {
		panic("no return value specified for VerifyToken")
	}

	var r0 *models.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (*models.User, error)); ok {
		return returnFunc(ctx, token)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) *models.User); ok {
		r0 = returnFunc(ctx, token)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, token)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueryResolver_VerifyToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'VerifyToken'
type MockQueryResolver_VerifyToken_Call struct {
	*mock.Call
}

// VerifyToken is a helper method to define mock.On call
//   - ctx context.Context
//   - token string
func (_e *MockQueryResolver_Expecter) VerifyToken(ctx interface{}, token interface{}) *MockQueryResolver_VerifyToken_Call {
	return &MockQueryResolver_VerifyToken_Call{Call: _e.mock.On("VerifyToken", ctx, token)}
}

func (_c *MockQueryResolver_VerifyToken_Call) Run(run func(ctx context.Context, token string)) *MockQueryResolver_VerifyToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockQueryResolver_VerifyToken_Call) Return(user *models.User, err error) *MockQueryResolver_VerifyToken_Call {
	_c.Call.Return(user, err)
	return _c
}

func (_c *MockQueryResolver_VerifyToken_Call) RunAndReturn(run func(ctx context.Context, token string) (*models.User, error)) *MockQueryResolver_VerifyToken_Call {
	_c.Call.Return(run)
	return _c
}
