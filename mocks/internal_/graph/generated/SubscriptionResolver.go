// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package generated

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockSubscriptionResolver creates a new instance of MockSubscriptionResolver. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockSubscriptionResolver(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockSubscriptionResolver {
	mock := &MockSubscriptionResolver{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockSubscriptionResolver is an autogenerated mock type for the SubscriptionResolver type
type MockSubscriptionResolver struct {
	mock.Mock
}

type MockSubscriptionResolver_Expecter struct {
	mock *mock.Mock
}

func (_m *MockSubscriptionResolver) EXPECT() *MockSubscriptionResolver_Expecter {
	return &MockSubscriptionResolver_Expecter{mock: &_m.Mock}
}

// ContestLeaderboardUpdated provides a mock function for the type MockSubscriptionResolver
func (_mock *MockSubscriptionResolver) ContestLeaderboardUpdated(ctx context.Context, contestID primitive.ObjectID) (<-chan *models.ContestLeaderboard, error) {
	ret := _mock.Called(ctx, contestID)

	if len(ret) == 0 {
		panic("no return value specified for ContestLeaderboardUpdated")
	}

	var r0 <-chan *models.ContestLeaderboard
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (<-chan *models.ContestLeaderboard, error)); ok {
		return returnFunc(ctx, contestID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) <-chan *models.ContestLeaderboard); ok {
		r0 = returnFunc(ctx, contestID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(<-chan *models.ContestLeaderboard)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, contestID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockSubscriptionResolver_ContestLeaderboardUpdated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ContestLeaderboardUpdated'
type MockSubscriptionResolver_ContestLeaderboardUpdated_Call struct {
	*mock.Call
}

// ContestLeaderboardUpdated is a helper method to define mock.On call
//   - ctx context.Context
//   - contestID primitive.ObjectID
func (_e *MockSubscriptionResolver_Expecter) ContestLeaderboardUpdated(ctx interface{}, contestID interface{}) *MockSubscriptionResolver_ContestLeaderboardUpdated_Call {
	return &MockSubscriptionResolver_ContestLeaderboardUpdated_Call{Call: _e.mock.On("ContestLeaderboardUpdated", ctx, contestID)}
}

func (_c *MockSubscriptionResolver_ContestLeaderboardUpdated_Call) Run(run func(ctx context.Context, contestID primitive.ObjectID)) *MockSubscriptionResolver_ContestLeaderboardUpdated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockSubscriptionResolver_ContestLeaderboardUpdated_Call) Return(contestLeaderboardCh <-chan *models.ContestLeaderboard, err error) *MockSubscriptionResolver_ContestLeaderboardUpdated_Call {
	_c.Call.Return(contestLeaderboardCh, err)
	return _c
}

func (_c *MockSubscriptionResolver_ContestLeaderboardUpdated_Call) RunAndReturn(run func(ctx context.Context, contestID primitive.ObjectID) (<-chan *models.ContestLeaderboard, error)) *MockSubscriptionResolver_ContestLeaderboardUpdated_Call {
	_c.Call.Return(run)
	return _c
}

// GameEvent provides a mock function for the type MockSubscriptionResolver
func (_mock *MockSubscriptionResolver) GameEvent(ctx context.Context, gameID *primitive.ObjectID) (<-chan *models.SubscriptionOutput, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for GameEvent")
	}

	var r0 <-chan *models.SubscriptionOutput
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *primitive.ObjectID) (<-chan *models.SubscriptionOutput, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *primitive.ObjectID) <-chan *models.SubscriptionOutput); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(<-chan *models.SubscriptionOutput)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockSubscriptionResolver_GameEvent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GameEvent'
type MockSubscriptionResolver_GameEvent_Call struct {
	*mock.Call
}

// GameEvent is a helper method to define mock.On call
//   - ctx context.Context
//   - gameID *primitive.ObjectID
func (_e *MockSubscriptionResolver_Expecter) GameEvent(ctx interface{}, gameID interface{}) *MockSubscriptionResolver_GameEvent_Call {
	return &MockSubscriptionResolver_GameEvent_Call{Call: _e.mock.On("GameEvent", ctx, gameID)}
}

func (_c *MockSubscriptionResolver_GameEvent_Call) Run(run func(ctx context.Context, gameID *primitive.ObjectID)) *MockSubscriptionResolver_GameEvent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(*primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockSubscriptionResolver_GameEvent_Call) Return(subscriptionOutputCh <-chan *models.SubscriptionOutput, err error) *MockSubscriptionResolver_GameEvent_Call {
	_c.Call.Return(subscriptionOutputCh, err)
	return _c
}

func (_c *MockSubscriptionResolver_GameEvent_Call) RunAndReturn(run func(ctx context.Context, gameID *primitive.ObjectID) (<-chan *models.SubscriptionOutput, error)) *MockSubscriptionResolver_GameEvent_Call {
	_c.Call.Return(run)
	return _c
}

// RematchRequest provides a mock function for the type MockSubscriptionResolver
func (_mock *MockSubscriptionResolver) RematchRequest(ctx context.Context, gameID primitive.ObjectID) (<-chan *models.RematchRequestOutput, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for RematchRequest")
	}

	var r0 <-chan *models.RematchRequestOutput
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (<-chan *models.RematchRequestOutput, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) <-chan *models.RematchRequestOutput); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(<-chan *models.RematchRequestOutput)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockSubscriptionResolver_RematchRequest_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RematchRequest'
type MockSubscriptionResolver_RematchRequest_Call struct {
	*mock.Call
}

// RematchRequest is a helper method to define mock.On call
//   - ctx context.Context
//   - gameID primitive.ObjectID
func (_e *MockSubscriptionResolver_Expecter) RematchRequest(ctx interface{}, gameID interface{}) *MockSubscriptionResolver_RematchRequest_Call {
	return &MockSubscriptionResolver_RematchRequest_Call{Call: _e.mock.On("RematchRequest", ctx, gameID)}
}

func (_c *MockSubscriptionResolver_RematchRequest_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID)) *MockSubscriptionResolver_RematchRequest_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockSubscriptionResolver_RematchRequest_Call) Return(rematchRequestOutputCh <-chan *models.RematchRequestOutput, err error) *MockSubscriptionResolver_RematchRequest_Call {
	_c.Call.Return(rematchRequestOutputCh, err)
	return _c
}

func (_c *MockSubscriptionResolver_RematchRequest_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID) (<-chan *models.RematchRequestOutput, error)) *MockSubscriptionResolver_RematchRequest_Call {
	_c.Call.Return(run)
	return _c
}

// SearchPlayer provides a mock function for the type MockSubscriptionResolver
func (_mock *MockSubscriptionResolver) SearchPlayer(ctx context.Context, userID *primitive.ObjectID) (<-chan *models.SearchSubscriptionOutput, error) {
	ret := _mock.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for SearchPlayer")
	}

	var r0 <-chan *models.SearchSubscriptionOutput
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *primitive.ObjectID) (<-chan *models.SearchSubscriptionOutput, error)); ok {
		return returnFunc(ctx, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *primitive.ObjectID) <-chan *models.SearchSubscriptionOutput); ok {
		r0 = returnFunc(ctx, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(<-chan *models.SearchSubscriptionOutput)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockSubscriptionResolver_SearchPlayer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SearchPlayer'
type MockSubscriptionResolver_SearchPlayer_Call struct {
	*mock.Call
}

// SearchPlayer is a helper method to define mock.On call
//   - ctx context.Context
//   - userID *primitive.ObjectID
func (_e *MockSubscriptionResolver_Expecter) SearchPlayer(ctx interface{}, userID interface{}) *MockSubscriptionResolver_SearchPlayer_Call {
	return &MockSubscriptionResolver_SearchPlayer_Call{Call: _e.mock.On("SearchPlayer", ctx, userID)}
}

func (_c *MockSubscriptionResolver_SearchPlayer_Call) Run(run func(ctx context.Context, userID *primitive.ObjectID)) *MockSubscriptionResolver_SearchPlayer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(*primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockSubscriptionResolver_SearchPlayer_Call) Return(searchSubscriptionOutputCh <-chan *models.SearchSubscriptionOutput, err error) *MockSubscriptionResolver_SearchPlayer_Call {
	_c.Call.Return(searchSubscriptionOutputCh, err)
	return _c
}

func (_c *MockSubscriptionResolver_SearchPlayer_Call) RunAndReturn(run func(ctx context.Context, userID *primitive.ObjectID) (<-chan *models.SearchSubscriptionOutput, error)) *MockSubscriptionResolver_SearchPlayer_Call {
	_c.Call.Return(run)
	return _c
}

// UserEvents provides a mock function for the type MockSubscriptionResolver
func (_mock *MockSubscriptionResolver) UserEvents(ctx context.Context, userID *primitive.ObjectID) (<-chan models.UserEvent, error) {
	ret := _mock.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for UserEvents")
	}

	var r0 <-chan models.UserEvent
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *primitive.ObjectID) (<-chan models.UserEvent, error)); ok {
		return returnFunc(ctx, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *primitive.ObjectID) <-chan models.UserEvent); ok {
		r0 = returnFunc(ctx, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(<-chan models.UserEvent)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockSubscriptionResolver_UserEvents_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UserEvents'
type MockSubscriptionResolver_UserEvents_Call struct {
	*mock.Call
}

// UserEvents is a helper method to define mock.On call
//   - ctx context.Context
//   - userID *primitive.ObjectID
func (_e *MockSubscriptionResolver_Expecter) UserEvents(ctx interface{}, userID interface{}) *MockSubscriptionResolver_UserEvents_Call {
	return &MockSubscriptionResolver_UserEvents_Call{Call: _e.mock.On("UserEvents", ctx, userID)}
}

func (_c *MockSubscriptionResolver_UserEvents_Call) Run(run func(ctx context.Context, userID *primitive.ObjectID)) *MockSubscriptionResolver_UserEvents_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(*primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockSubscriptionResolver_UserEvents_Call) Return(userEventCh <-chan models.UserEvent, err error) *MockSubscriptionResolver_UserEvents_Call {
	_c.Call.Return(userEventCh, err)
	return _c
}

func (_c *MockSubscriptionResolver_UserEvents_Call) RunAndReturn(run func(ctx context.Context, userID *primitive.ObjectID) (<-chan models.UserEvent, error)) *MockSubscriptionResolver_UserEvents_Call {
	_c.Call.Return(run)
	return _c
}
