// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package cache

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockContestCache creates a new instance of MockContestCache. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockContestCache(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockContestCache {
	mock := &MockContestCache{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockContestCache is an autogenerated mock type for the ContestCache type
type MockContestCache struct {
	mock.Mock
}

type MockContestCache_Expecter struct {
	mock *mock.Mock
}

func (_m *MockContestCache) EXPECT() *MockContestCache_Expecter {
	return &MockContestCache_Expecter{mock: &_m.Mock}
}

// DeleteAllContests provides a mock function for the type MockContestCache
func (_mock *MockContestCache) DeleteAllContests(ctx context.Context) error {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for DeleteAllContests")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = returnFunc(ctx)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockContestCache_DeleteAllContests_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteAllContests'
type MockContestCache_DeleteAllContests_Call struct {
	*mock.Call
}

// DeleteAllContests is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockContestCache_Expecter) DeleteAllContests(ctx interface{}) *MockContestCache_DeleteAllContests_Call {
	return &MockContestCache_DeleteAllContests_Call{Call: _e.mock.On("DeleteAllContests", ctx)}
}

func (_c *MockContestCache_DeleteAllContests_Call) Run(run func(ctx context.Context)) *MockContestCache_DeleteAllContests_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockContestCache_DeleteAllContests_Call) Return(err error) *MockContestCache_DeleteAllContests_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockContestCache_DeleteAllContests_Call) RunAndReturn(run func(ctx context.Context) error) *MockContestCache_DeleteAllContests_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteContest provides a mock function for the type MockContestCache
func (_mock *MockContestCache) DeleteContest(ctx context.Context, contestID primitive.ObjectID) error {
	ret := _mock.Called(ctx, contestID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteContest")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, contestID)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockContestCache_DeleteContest_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteContest'
type MockContestCache_DeleteContest_Call struct {
	*mock.Call
}

// DeleteContest is a helper method to define mock.On call
//   - ctx context.Context
//   - contestID primitive.ObjectID
func (_e *MockContestCache_Expecter) DeleteContest(ctx interface{}, contestID interface{}) *MockContestCache_DeleteContest_Call {
	return &MockContestCache_DeleteContest_Call{Call: _e.mock.On("DeleteContest", ctx, contestID)}
}

func (_c *MockContestCache_DeleteContest_Call) Run(run func(ctx context.Context, contestID primitive.ObjectID)) *MockContestCache_DeleteContest_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockContestCache_DeleteContest_Call) Return(err error) *MockContestCache_DeleteContest_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockContestCache_DeleteContest_Call) RunAndReturn(run func(ctx context.Context, contestID primitive.ObjectID) error) *MockContestCache_DeleteContest_Call {
	_c.Call.Return(run)
	return _c
}

// GetContest provides a mock function for the type MockContestCache
func (_mock *MockContestCache) GetContest(ctx context.Context, contestID primitive.ObjectID) (*models.Contest, error) {
	ret := _mock.Called(ctx, contestID)

	if len(ret) == 0 {
		panic("no return value specified for GetContest")
	}

	var r0 *models.Contest
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.Contest, error)); ok {
		return returnFunc(ctx, contestID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.Contest); ok {
		r0 = returnFunc(ctx, contestID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Contest)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, contestID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockContestCache_GetContest_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetContest'
type MockContestCache_GetContest_Call struct {
	*mock.Call
}

// GetContest is a helper method to define mock.On call
//   - ctx context.Context
//   - contestID primitive.ObjectID
func (_e *MockContestCache_Expecter) GetContest(ctx interface{}, contestID interface{}) *MockContestCache_GetContest_Call {
	return &MockContestCache_GetContest_Call{Call: _e.mock.On("GetContest", ctx, contestID)}
}

func (_c *MockContestCache_GetContest_Call) Run(run func(ctx context.Context, contestID primitive.ObjectID)) *MockContestCache_GetContest_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockContestCache_GetContest_Call) Return(contest *models.Contest, err error) *MockContestCache_GetContest_Call {
	_c.Call.Return(contest, err)
	return _c
}

func (_c *MockContestCache_GetContest_Call) RunAndReturn(run func(ctx context.Context, contestID primitive.ObjectID) (*models.Contest, error)) *MockContestCache_GetContest_Call {
	_c.Call.Return(run)
	return _c
}

// SetContest provides a mock function for the type MockContestCache
func (_mock *MockContestCache) SetContest(ctx context.Context, contest *models.Contest) error {
	ret := _mock.Called(ctx, contest)

	if len(ret) == 0 {
		panic("no return value specified for SetContest")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.Contest) error); ok {
		r0 = returnFunc(ctx, contest)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockContestCache_SetContest_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetContest'
type MockContestCache_SetContest_Call struct {
	*mock.Call
}

// SetContest is a helper method to define mock.On call
//   - ctx context.Context
//   - contest *models.Contest
func (_e *MockContestCache_Expecter) SetContest(ctx interface{}, contest interface{}) *MockContestCache_SetContest_Call {
	return &MockContestCache_SetContest_Call{Call: _e.mock.On("SetContest", ctx, contest)}
}

func (_c *MockContestCache_SetContest_Call) Run(run func(ctx context.Context, contest *models.Contest)) *MockContestCache_SetContest_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.Contest
		if args[1] != nil {
			arg1 = args[1].(*models.Contest)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockContestCache_SetContest_Call) Return(err error) *MockContestCache_SetContest_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockContestCache_SetContest_Call) RunAndReturn(run func(ctx context.Context, contest *models.Contest) error) *MockContestCache_SetContest_Call {
	_c.Call.Return(run)
	return _c
}
