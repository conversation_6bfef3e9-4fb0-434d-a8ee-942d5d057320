// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package cache

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockPresetsCache creates a new instance of MockPresetsCache. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockPresetsCache(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockPresetsCache {
	mock := &MockPresetsCache{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockPresetsCache is an autogenerated mock type for the PresetsCache type
type MockPresetsCache struct {
	mock.Mock
}

type MockPresetsCache_Expecter struct {
	mock *mock.Mock
}

func (_m *MockPresetsCache) EXPECT() *MockPresetsCache_Expecter {
	return &MockPresetsCache_Expecter{mock: &_m.Mock}
}

// DeleteAllGlobalPresets provides a mock function for the type MockPresetsCache
func (_mock *MockPresetsCache) DeleteAllGlobalPresets(ctx context.Context, userID primitive.ObjectID) error {
	ret := _mock.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteAllGlobalPresets")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, userID)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockPresetsCache_DeleteAllGlobalPresets_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteAllGlobalPresets'
type MockPresetsCache_DeleteAllGlobalPresets_Call struct {
	*mock.Call
}

// DeleteAllGlobalPresets is a helper method to define mock.On call
//   - ctx context.Context
//   - userID primitive.ObjectID
func (_e *MockPresetsCache_Expecter) DeleteAllGlobalPresets(ctx interface{}, userID interface{}) *MockPresetsCache_DeleteAllGlobalPresets_Call {
	return &MockPresetsCache_DeleteAllGlobalPresets_Call{Call: _e.mock.On("DeleteAllGlobalPresets", ctx, userID)}
}

func (_c *MockPresetsCache_DeleteAllGlobalPresets_Call) Run(run func(ctx context.Context, userID primitive.ObjectID)) *MockPresetsCache_DeleteAllGlobalPresets_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockPresetsCache_DeleteAllGlobalPresets_Call) Return(err error) *MockPresetsCache_DeleteAllGlobalPresets_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockPresetsCache_DeleteAllGlobalPresets_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID) error) *MockPresetsCache_DeleteAllGlobalPresets_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteAllUserPresetStats provides a mock function for the type MockPresetsCache
func (_mock *MockPresetsCache) DeleteAllUserPresetStats(ctx context.Context, userID primitive.ObjectID) error {
	ret := _mock.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteAllUserPresetStats")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, userID)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockPresetsCache_DeleteAllUserPresetStats_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteAllUserPresetStats'
type MockPresetsCache_DeleteAllUserPresetStats_Call struct {
	*mock.Call
}

// DeleteAllUserPresetStats is a helper method to define mock.On call
//   - ctx context.Context
//   - userID primitive.ObjectID
func (_e *MockPresetsCache_Expecter) DeleteAllUserPresetStats(ctx interface{}, userID interface{}) *MockPresetsCache_DeleteAllUserPresetStats_Call {
	return &MockPresetsCache_DeleteAllUserPresetStats_Call{Call: _e.mock.On("DeleteAllUserPresetStats", ctx, userID)}
}

func (_c *MockPresetsCache_DeleteAllUserPresetStats_Call) Run(run func(ctx context.Context, userID primitive.ObjectID)) *MockPresetsCache_DeleteAllUserPresetStats_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockPresetsCache_DeleteAllUserPresetStats_Call) Return(err error) *MockPresetsCache_DeleteAllUserPresetStats_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockPresetsCache_DeleteAllUserPresetStats_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID) error) *MockPresetsCache_DeleteAllUserPresetStats_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteAllUserPresets provides a mock function for the type MockPresetsCache
func (_mock *MockPresetsCache) DeleteAllUserPresets(ctx context.Context, userID primitive.ObjectID) error {
	ret := _mock.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteAllUserPresets")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, userID)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockPresetsCache_DeleteAllUserPresets_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteAllUserPresets'
type MockPresetsCache_DeleteAllUserPresets_Call struct {
	*mock.Call
}

// DeleteAllUserPresets is a helper method to define mock.On call
//   - ctx context.Context
//   - userID primitive.ObjectID
func (_e *MockPresetsCache_Expecter) DeleteAllUserPresets(ctx interface{}, userID interface{}) *MockPresetsCache_DeleteAllUserPresets_Call {
	return &MockPresetsCache_DeleteAllUserPresets_Call{Call: _e.mock.On("DeleteAllUserPresets", ctx, userID)}
}

func (_c *MockPresetsCache_DeleteAllUserPresets_Call) Run(run func(ctx context.Context, userID primitive.ObjectID)) *MockPresetsCache_DeleteAllUserPresets_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockPresetsCache_DeleteAllUserPresets_Call) Return(err error) *MockPresetsCache_DeleteAllUserPresets_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockPresetsCache_DeleteAllUserPresets_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID) error) *MockPresetsCache_DeleteAllUserPresets_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteGlobalPreset provides a mock function for the type MockPresetsCache
func (_mock *MockPresetsCache) DeleteGlobalPreset(ctx context.Context, identifier string, userID primitive.ObjectID) error {
	ret := _mock.Called(ctx, identifier, userID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteGlobalPreset")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, identifier, userID)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockPresetsCache_DeleteGlobalPreset_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteGlobalPreset'
type MockPresetsCache_DeleteGlobalPreset_Call struct {
	*mock.Call
}

// DeleteGlobalPreset is a helper method to define mock.On call
//   - ctx context.Context
//   - identifier string
//   - userID primitive.ObjectID
func (_e *MockPresetsCache_Expecter) DeleteGlobalPreset(ctx interface{}, identifier interface{}, userID interface{}) *MockPresetsCache_DeleteGlobalPreset_Call {
	return &MockPresetsCache_DeleteGlobalPreset_Call{Call: _e.mock.On("DeleteGlobalPreset", ctx, identifier, userID)}
}

func (_c *MockPresetsCache_DeleteGlobalPreset_Call) Run(run func(ctx context.Context, identifier string, userID primitive.ObjectID)) *MockPresetsCache_DeleteGlobalPreset_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		var arg2 primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockPresetsCache_DeleteGlobalPreset_Call) Return(err error) *MockPresetsCache_DeleteGlobalPreset_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockPresetsCache_DeleteGlobalPreset_Call) RunAndReturn(run func(ctx context.Context, identifier string, userID primitive.ObjectID) error) *MockPresetsCache_DeleteGlobalPreset_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteUserPreset provides a mock function for the type MockPresetsCache
func (_mock *MockPresetsCache) DeleteUserPreset(ctx context.Context, identifier string, userID primitive.ObjectID) error {
	ret := _mock.Called(ctx, identifier, userID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteUserPreset")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, identifier, userID)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockPresetsCache_DeleteUserPreset_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteUserPreset'
type MockPresetsCache_DeleteUserPreset_Call struct {
	*mock.Call
}

// DeleteUserPreset is a helper method to define mock.On call
//   - ctx context.Context
//   - identifier string
//   - userID primitive.ObjectID
func (_e *MockPresetsCache_Expecter) DeleteUserPreset(ctx interface{}, identifier interface{}, userID interface{}) *MockPresetsCache_DeleteUserPreset_Call {
	return &MockPresetsCache_DeleteUserPreset_Call{Call: _e.mock.On("DeleteUserPreset", ctx, identifier, userID)}
}

func (_c *MockPresetsCache_DeleteUserPreset_Call) Run(run func(ctx context.Context, identifier string, userID primitive.ObjectID)) *MockPresetsCache_DeleteUserPreset_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		var arg2 primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockPresetsCache_DeleteUserPreset_Call) Return(err error) *MockPresetsCache_DeleteUserPreset_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockPresetsCache_DeleteUserPreset_Call) RunAndReturn(run func(ctx context.Context, identifier string, userID primitive.ObjectID) error) *MockPresetsCache_DeleteUserPreset_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteUserPresetStats provides a mock function for the type MockPresetsCache
func (_mock *MockPresetsCache) DeleteUserPresetStats(ctx context.Context, identifier string, userID primitive.ObjectID) error {
	ret := _mock.Called(ctx, identifier, userID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteUserPresetStats")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, identifier, userID)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockPresetsCache_DeleteUserPresetStats_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteUserPresetStats'
type MockPresetsCache_DeleteUserPresetStats_Call struct {
	*mock.Call
}

// DeleteUserPresetStats is a helper method to define mock.On call
//   - ctx context.Context
//   - identifier string
//   - userID primitive.ObjectID
func (_e *MockPresetsCache_Expecter) DeleteUserPresetStats(ctx interface{}, identifier interface{}, userID interface{}) *MockPresetsCache_DeleteUserPresetStats_Call {
	return &MockPresetsCache_DeleteUserPresetStats_Call{Call: _e.mock.On("DeleteUserPresetStats", ctx, identifier, userID)}
}

func (_c *MockPresetsCache_DeleteUserPresetStats_Call) Run(run func(ctx context.Context, identifier string, userID primitive.ObjectID)) *MockPresetsCache_DeleteUserPresetStats_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		var arg2 primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockPresetsCache_DeleteUserPresetStats_Call) Return(err error) *MockPresetsCache_DeleteUserPresetStats_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockPresetsCache_DeleteUserPresetStats_Call) RunAndReturn(run func(ctx context.Context, identifier string, userID primitive.ObjectID) error) *MockPresetsCache_DeleteUserPresetStats_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllGlobalPresets provides a mock function for the type MockPresetsCache
func (_mock *MockPresetsCache) GetAllGlobalPresets(ctx context.Context, userID primitive.ObjectID) ([]*models.GlobalPreset, error) {
	ret := _mock.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetAllGlobalPresets")
	}

	var r0 []*models.GlobalPreset
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) ([]*models.GlobalPreset, error)); ok {
		return returnFunc(ctx, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) []*models.GlobalPreset); ok {
		r0 = returnFunc(ctx, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.GlobalPreset)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPresetsCache_GetAllGlobalPresets_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllGlobalPresets'
type MockPresetsCache_GetAllGlobalPresets_Call struct {
	*mock.Call
}

// GetAllGlobalPresets is a helper method to define mock.On call
//   - ctx context.Context
//   - userID primitive.ObjectID
func (_e *MockPresetsCache_Expecter) GetAllGlobalPresets(ctx interface{}, userID interface{}) *MockPresetsCache_GetAllGlobalPresets_Call {
	return &MockPresetsCache_GetAllGlobalPresets_Call{Call: _e.mock.On("GetAllGlobalPresets", ctx, userID)}
}

func (_c *MockPresetsCache_GetAllGlobalPresets_Call) Run(run func(ctx context.Context, userID primitive.ObjectID)) *MockPresetsCache_GetAllGlobalPresets_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockPresetsCache_GetAllGlobalPresets_Call) Return(globalPresets []*models.GlobalPreset, err error) *MockPresetsCache_GetAllGlobalPresets_Call {
	_c.Call.Return(globalPresets, err)
	return _c
}

func (_c *MockPresetsCache_GetAllGlobalPresets_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID) ([]*models.GlobalPreset, error)) *MockPresetsCache_GetAllGlobalPresets_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllUserPresetStats provides a mock function for the type MockPresetsCache
func (_mock *MockPresetsCache) GetAllUserPresetStats(ctx context.Context, userID primitive.ObjectID) ([]*models.UserPresetStats, error) {
	ret := _mock.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetAllUserPresetStats")
	}

	var r0 []*models.UserPresetStats
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) ([]*models.UserPresetStats, error)); ok {
		return returnFunc(ctx, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) []*models.UserPresetStats); ok {
		r0 = returnFunc(ctx, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.UserPresetStats)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPresetsCache_GetAllUserPresetStats_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllUserPresetStats'
type MockPresetsCache_GetAllUserPresetStats_Call struct {
	*mock.Call
}

// GetAllUserPresetStats is a helper method to define mock.On call
//   - ctx context.Context
//   - userID primitive.ObjectID
func (_e *MockPresetsCache_Expecter) GetAllUserPresetStats(ctx interface{}, userID interface{}) *MockPresetsCache_GetAllUserPresetStats_Call {
	return &MockPresetsCache_GetAllUserPresetStats_Call{Call: _e.mock.On("GetAllUserPresetStats", ctx, userID)}
}

func (_c *MockPresetsCache_GetAllUserPresetStats_Call) Run(run func(ctx context.Context, userID primitive.ObjectID)) *MockPresetsCache_GetAllUserPresetStats_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockPresetsCache_GetAllUserPresetStats_Call) Return(userPresetStatss []*models.UserPresetStats, err error) *MockPresetsCache_GetAllUserPresetStats_Call {
	_c.Call.Return(userPresetStatss, err)
	return _c
}

func (_c *MockPresetsCache_GetAllUserPresetStats_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID) ([]*models.UserPresetStats, error)) *MockPresetsCache_GetAllUserPresetStats_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllUserPresets provides a mock function for the type MockPresetsCache
func (_mock *MockPresetsCache) GetAllUserPresets(ctx context.Context, userID primitive.ObjectID) ([]*models.UserPreset, error) {
	ret := _mock.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetAllUserPresets")
	}

	var r0 []*models.UserPreset
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) ([]*models.UserPreset, error)); ok {
		return returnFunc(ctx, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) []*models.UserPreset); ok {
		r0 = returnFunc(ctx, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.UserPreset)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPresetsCache_GetAllUserPresets_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllUserPresets'
type MockPresetsCache_GetAllUserPresets_Call struct {
	*mock.Call
}

// GetAllUserPresets is a helper method to define mock.On call
//   - ctx context.Context
//   - userID primitive.ObjectID
func (_e *MockPresetsCache_Expecter) GetAllUserPresets(ctx interface{}, userID interface{}) *MockPresetsCache_GetAllUserPresets_Call {
	return &MockPresetsCache_GetAllUserPresets_Call{Call: _e.mock.On("GetAllUserPresets", ctx, userID)}
}

func (_c *MockPresetsCache_GetAllUserPresets_Call) Run(run func(ctx context.Context, userID primitive.ObjectID)) *MockPresetsCache_GetAllUserPresets_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockPresetsCache_GetAllUserPresets_Call) Return(userPresets []*models.UserPreset, err error) *MockPresetsCache_GetAllUserPresets_Call {
	_c.Call.Return(userPresets, err)
	return _c
}

func (_c *MockPresetsCache_GetAllUserPresets_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID) ([]*models.UserPreset, error)) *MockPresetsCache_GetAllUserPresets_Call {
	_c.Call.Return(run)
	return _c
}

// GetGlobalPreset provides a mock function for the type MockPresetsCache
func (_mock *MockPresetsCache) GetGlobalPreset(ctx context.Context, identifier string, userID primitive.ObjectID) (*models.GlobalPreset, error) {
	ret := _mock.Called(ctx, identifier, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetGlobalPreset")
	}

	var r0 *models.GlobalPreset
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, primitive.ObjectID) (*models.GlobalPreset, error)); ok {
		return returnFunc(ctx, identifier, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, primitive.ObjectID) *models.GlobalPreset); ok {
		r0 = returnFunc(ctx, identifier, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.GlobalPreset)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, identifier, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPresetsCache_GetGlobalPreset_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetGlobalPreset'
type MockPresetsCache_GetGlobalPreset_Call struct {
	*mock.Call
}

// GetGlobalPreset is a helper method to define mock.On call
//   - ctx context.Context
//   - identifier string
//   - userID primitive.ObjectID
func (_e *MockPresetsCache_Expecter) GetGlobalPreset(ctx interface{}, identifier interface{}, userID interface{}) *MockPresetsCache_GetGlobalPreset_Call {
	return &MockPresetsCache_GetGlobalPreset_Call{Call: _e.mock.On("GetGlobalPreset", ctx, identifier, userID)}
}

func (_c *MockPresetsCache_GetGlobalPreset_Call) Run(run func(ctx context.Context, identifier string, userID primitive.ObjectID)) *MockPresetsCache_GetGlobalPreset_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		var arg2 primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockPresetsCache_GetGlobalPreset_Call) Return(globalPreset *models.GlobalPreset, err error) *MockPresetsCache_GetGlobalPreset_Call {
	_c.Call.Return(globalPreset, err)
	return _c
}

func (_c *MockPresetsCache_GetGlobalPreset_Call) RunAndReturn(run func(ctx context.Context, identifier string, userID primitive.ObjectID) (*models.GlobalPreset, error)) *MockPresetsCache_GetGlobalPreset_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserPreset provides a mock function for the type MockPresetsCache
func (_mock *MockPresetsCache) GetUserPreset(ctx context.Context, identifier string, userID primitive.ObjectID) (*models.UserPreset, error) {
	ret := _mock.Called(ctx, identifier, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetUserPreset")
	}

	var r0 *models.UserPreset
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, primitive.ObjectID) (*models.UserPreset, error)); ok {
		return returnFunc(ctx, identifier, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, primitive.ObjectID) *models.UserPreset); ok {
		r0 = returnFunc(ctx, identifier, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserPreset)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, identifier, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPresetsCache_GetUserPreset_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserPreset'
type MockPresetsCache_GetUserPreset_Call struct {
	*mock.Call
}

// GetUserPreset is a helper method to define mock.On call
//   - ctx context.Context
//   - identifier string
//   - userID primitive.ObjectID
func (_e *MockPresetsCache_Expecter) GetUserPreset(ctx interface{}, identifier interface{}, userID interface{}) *MockPresetsCache_GetUserPreset_Call {
	return &MockPresetsCache_GetUserPreset_Call{Call: _e.mock.On("GetUserPreset", ctx, identifier, userID)}
}

func (_c *MockPresetsCache_GetUserPreset_Call) Run(run func(ctx context.Context, identifier string, userID primitive.ObjectID)) *MockPresetsCache_GetUserPreset_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		var arg2 primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockPresetsCache_GetUserPreset_Call) Return(userPreset *models.UserPreset, err error) *MockPresetsCache_GetUserPreset_Call {
	_c.Call.Return(userPreset, err)
	return _c
}

func (_c *MockPresetsCache_GetUserPreset_Call) RunAndReturn(run func(ctx context.Context, identifier string, userID primitive.ObjectID) (*models.UserPreset, error)) *MockPresetsCache_GetUserPreset_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserPresetStats provides a mock function for the type MockPresetsCache
func (_mock *MockPresetsCache) GetUserPresetStats(ctx context.Context, identifier string, userID primitive.ObjectID) (*models.UserPresetStats, error) {
	ret := _mock.Called(ctx, identifier, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetUserPresetStats")
	}

	var r0 *models.UserPresetStats
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, primitive.ObjectID) (*models.UserPresetStats, error)); ok {
		return returnFunc(ctx, identifier, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, primitive.ObjectID) *models.UserPresetStats); ok {
		r0 = returnFunc(ctx, identifier, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserPresetStats)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, identifier, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPresetsCache_GetUserPresetStats_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserPresetStats'
type MockPresetsCache_GetUserPresetStats_Call struct {
	*mock.Call
}

// GetUserPresetStats is a helper method to define mock.On call
//   - ctx context.Context
//   - identifier string
//   - userID primitive.ObjectID
func (_e *MockPresetsCache_Expecter) GetUserPresetStats(ctx interface{}, identifier interface{}, userID interface{}) *MockPresetsCache_GetUserPresetStats_Call {
	return &MockPresetsCache_GetUserPresetStats_Call{Call: _e.mock.On("GetUserPresetStats", ctx, identifier, userID)}
}

func (_c *MockPresetsCache_GetUserPresetStats_Call) Run(run func(ctx context.Context, identifier string, userID primitive.ObjectID)) *MockPresetsCache_GetUserPresetStats_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		var arg2 primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockPresetsCache_GetUserPresetStats_Call) Return(userPresetStats *models.UserPresetStats, err error) *MockPresetsCache_GetUserPresetStats_Call {
	_c.Call.Return(userPresetStats, err)
	return _c
}

func (_c *MockPresetsCache_GetUserPresetStats_Call) RunAndReturn(run func(ctx context.Context, identifier string, userID primitive.ObjectID) (*models.UserPresetStats, error)) *MockPresetsCache_GetUserPresetStats_Call {
	_c.Call.Return(run)
	return _c
}

// SetGlobalPreset provides a mock function for the type MockPresetsCache
func (_mock *MockPresetsCache) SetGlobalPreset(ctx context.Context, preset *models.GlobalPreset, userID primitive.ObjectID) error {
	ret := _mock.Called(ctx, preset, userID)

	if len(ret) == 0 {
		panic("no return value specified for SetGlobalPreset")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.GlobalPreset, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, preset, userID)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockPresetsCache_SetGlobalPreset_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetGlobalPreset'
type MockPresetsCache_SetGlobalPreset_Call struct {
	*mock.Call
}

// SetGlobalPreset is a helper method to define mock.On call
//   - ctx context.Context
//   - preset *models.GlobalPreset
//   - userID primitive.ObjectID
func (_e *MockPresetsCache_Expecter) SetGlobalPreset(ctx interface{}, preset interface{}, userID interface{}) *MockPresetsCache_SetGlobalPreset_Call {
	return &MockPresetsCache_SetGlobalPreset_Call{Call: _e.mock.On("SetGlobalPreset", ctx, preset, userID)}
}

func (_c *MockPresetsCache_SetGlobalPreset_Call) Run(run func(ctx context.Context, preset *models.GlobalPreset, userID primitive.ObjectID)) *MockPresetsCache_SetGlobalPreset_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.GlobalPreset
		if args[1] != nil {
			arg1 = args[1].(*models.GlobalPreset)
		}
		var arg2 primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockPresetsCache_SetGlobalPreset_Call) Return(err error) *MockPresetsCache_SetGlobalPreset_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockPresetsCache_SetGlobalPreset_Call) RunAndReturn(run func(ctx context.Context, preset *models.GlobalPreset, userID primitive.ObjectID) error) *MockPresetsCache_SetGlobalPreset_Call {
	_c.Call.Return(run)
	return _c
}

// SetUserPreset provides a mock function for the type MockPresetsCache
func (_mock *MockPresetsCache) SetUserPreset(ctx context.Context, preset *models.UserPreset, userID primitive.ObjectID) error {
	ret := _mock.Called(ctx, preset, userID)

	if len(ret) == 0 {
		panic("no return value specified for SetUserPreset")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UserPreset, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, preset, userID)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockPresetsCache_SetUserPreset_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetUserPreset'
type MockPresetsCache_SetUserPreset_Call struct {
	*mock.Call
}

// SetUserPreset is a helper method to define mock.On call
//   - ctx context.Context
//   - preset *models.UserPreset
//   - userID primitive.ObjectID
func (_e *MockPresetsCache_Expecter) SetUserPreset(ctx interface{}, preset interface{}, userID interface{}) *MockPresetsCache_SetUserPreset_Call {
	return &MockPresetsCache_SetUserPreset_Call{Call: _e.mock.On("SetUserPreset", ctx, preset, userID)}
}

func (_c *MockPresetsCache_SetUserPreset_Call) Run(run func(ctx context.Context, preset *models.UserPreset, userID primitive.ObjectID)) *MockPresetsCache_SetUserPreset_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.UserPreset
		if args[1] != nil {
			arg1 = args[1].(*models.UserPreset)
		}
		var arg2 primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockPresetsCache_SetUserPreset_Call) Return(err error) *MockPresetsCache_SetUserPreset_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockPresetsCache_SetUserPreset_Call) RunAndReturn(run func(ctx context.Context, preset *models.UserPreset, userID primitive.ObjectID) error) *MockPresetsCache_SetUserPreset_Call {
	_c.Call.Return(run)
	return _c
}

// SetUserPresetStats provides a mock function for the type MockPresetsCache
func (_mock *MockPresetsCache) SetUserPresetStats(ctx context.Context, stats *models.UserPresetStats, userID primitive.ObjectID) error {
	ret := _mock.Called(ctx, stats, userID)

	if len(ret) == 0 {
		panic("no return value specified for SetUserPresetStats")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UserPresetStats, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, stats, userID)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockPresetsCache_SetUserPresetStats_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetUserPresetStats'
type MockPresetsCache_SetUserPresetStats_Call struct {
	*mock.Call
}

// SetUserPresetStats is a helper method to define mock.On call
//   - ctx context.Context
//   - stats *models.UserPresetStats
//   - userID primitive.ObjectID
func (_e *MockPresetsCache_Expecter) SetUserPresetStats(ctx interface{}, stats interface{}, userID interface{}) *MockPresetsCache_SetUserPresetStats_Call {
	return &MockPresetsCache_SetUserPresetStats_Call{Call: _e.mock.On("SetUserPresetStats", ctx, stats, userID)}
}

func (_c *MockPresetsCache_SetUserPresetStats_Call) Run(run func(ctx context.Context, stats *models.UserPresetStats, userID primitive.ObjectID)) *MockPresetsCache_SetUserPresetStats_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.UserPresetStats
		if args[1] != nil {
			arg1 = args[1].(*models.UserPresetStats)
		}
		var arg2 primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockPresetsCache_SetUserPresetStats_Call) Return(err error) *MockPresetsCache_SetUserPresetStats_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockPresetsCache_SetUserPresetStats_Call) RunAndReturn(run func(ctx context.Context, stats *models.UserPresetStats, userID primitive.ObjectID) error) *MockPresetsCache_SetUserPresetStats_Call {
	_c.Call.Return(run)
	return _c
}
