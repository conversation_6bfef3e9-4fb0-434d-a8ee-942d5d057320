// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package cache

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/infrastructure/cache"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockShowdownCache creates a new instance of MockShowdownCache. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockShowdownCache(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockShowdownCache {
	mock := &MockShowdownCache{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockShowdownCache is an autogenerated mock type for the ShowdownCache type
type MockShowdownCache struct {
	mock.Mock
}

type MockShowdownCache_Expecter struct {
	mock *mock.Mock
}

func (_m *MockShowdownCache) EXPECT() *MockShowdownCache_Expecter {
	return &MockShowdownCache_Expecter{mock: &_m.Mock}
}

// DeleteAllShowdowns provides a mock function for the type MockShowdownCache
func (_mock *MockShowdownCache) DeleteAllShowdowns(ctx context.Context) error {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for DeleteAllShowdowns")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = returnFunc(ctx)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockShowdownCache_DeleteAllShowdowns_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteAllShowdowns'
type MockShowdownCache_DeleteAllShowdowns_Call struct {
	*mock.Call
}

// DeleteAllShowdowns is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockShowdownCache_Expecter) DeleteAllShowdowns(ctx interface{}) *MockShowdownCache_DeleteAllShowdowns_Call {
	return &MockShowdownCache_DeleteAllShowdowns_Call{Call: _e.mock.On("DeleteAllShowdowns", ctx)}
}

func (_c *MockShowdownCache_DeleteAllShowdowns_Call) Run(run func(ctx context.Context)) *MockShowdownCache_DeleteAllShowdowns_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockShowdownCache_DeleteAllShowdowns_Call) Return(err error) *MockShowdownCache_DeleteAllShowdowns_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockShowdownCache_DeleteAllShowdowns_Call) RunAndReturn(run func(ctx context.Context) error) *MockShowdownCache_DeleteAllShowdowns_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteShowdown provides a mock function for the type MockShowdownCache
func (_mock *MockShowdownCache) DeleteShowdown(ctx context.Context, showdownId primitive.ObjectID) error {
	ret := _mock.Called(ctx, showdownId)

	if len(ret) == 0 {
		panic("no return value specified for DeleteShowdown")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, showdownId)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockShowdownCache_DeleteShowdown_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteShowdown'
type MockShowdownCache_DeleteShowdown_Call struct {
	*mock.Call
}

// DeleteShowdown is a helper method to define mock.On call
//   - ctx context.Context
//   - showdownId primitive.ObjectID
func (_e *MockShowdownCache_Expecter) DeleteShowdown(ctx interface{}, showdownId interface{}) *MockShowdownCache_DeleteShowdown_Call {
	return &MockShowdownCache_DeleteShowdown_Call{Call: _e.mock.On("DeleteShowdown", ctx, showdownId)}
}

func (_c *MockShowdownCache_DeleteShowdown_Call) Run(run func(ctx context.Context, showdownId primitive.ObjectID)) *MockShowdownCache_DeleteShowdown_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockShowdownCache_DeleteShowdown_Call) Return(err error) *MockShowdownCache_DeleteShowdown_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockShowdownCache_DeleteShowdown_Call) RunAndReturn(run func(ctx context.Context, showdownId primitive.ObjectID) error) *MockShowdownCache_DeleteShowdown_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteShowdownParticipant provides a mock function for the type MockShowdownCache
func (_mock *MockShowdownCache) DeleteShowdownParticipant(ctx context.Context, showdownId primitive.ObjectID, participantId primitive.ObjectID) error {
	ret := _mock.Called(ctx, showdownId, participantId)

	if len(ret) == 0 {
		panic("no return value specified for DeleteShowdownParticipant")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, showdownId, participantId)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockShowdownCache_DeleteShowdownParticipant_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteShowdownParticipant'
type MockShowdownCache_DeleteShowdownParticipant_Call struct {
	*mock.Call
}

// DeleteShowdownParticipant is a helper method to define mock.On call
//   - ctx context.Context
//   - showdownId primitive.ObjectID
//   - participantId primitive.ObjectID
func (_e *MockShowdownCache_Expecter) DeleteShowdownParticipant(ctx interface{}, showdownId interface{}, participantId interface{}) *MockShowdownCache_DeleteShowdownParticipant_Call {
	return &MockShowdownCache_DeleteShowdownParticipant_Call{Call: _e.mock.On("DeleteShowdownParticipant", ctx, showdownId, participantId)}
}

func (_c *MockShowdownCache_DeleteShowdownParticipant_Call) Run(run func(ctx context.Context, showdownId primitive.ObjectID, participantId primitive.ObjectID)) *MockShowdownCache_DeleteShowdownParticipant_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockShowdownCache_DeleteShowdownParticipant_Call) Return(err error) *MockShowdownCache_DeleteShowdownParticipant_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockShowdownCache_DeleteShowdownParticipant_Call) RunAndReturn(run func(ctx context.Context, showdownId primitive.ObjectID, participantId primitive.ObjectID) error) *MockShowdownCache_DeleteShowdownParticipant_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllLeaderboardEntities provides a mock function for the type MockShowdownCache
func (_mock *MockShowdownCache) GetAllLeaderboardEntities(ctx context.Context, showdownId primitive.ObjectID) ([]*cache.LeaderboardEntity, error) {
	ret := _mock.Called(ctx, showdownId)

	if len(ret) == 0 {
		panic("no return value specified for GetAllLeaderboardEntities")
	}

	var r0 []*cache.LeaderboardEntity
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) ([]*cache.LeaderboardEntity, error)); ok {
		return returnFunc(ctx, showdownId)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) []*cache.LeaderboardEntity); ok {
		r0 = returnFunc(ctx, showdownId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*cache.LeaderboardEntity)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, showdownId)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownCache_GetAllLeaderboardEntities_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllLeaderboardEntities'
type MockShowdownCache_GetAllLeaderboardEntities_Call struct {
	*mock.Call
}

// GetAllLeaderboardEntities is a helper method to define mock.On call
//   - ctx context.Context
//   - showdownId primitive.ObjectID
func (_e *MockShowdownCache_Expecter) GetAllLeaderboardEntities(ctx interface{}, showdownId interface{}) *MockShowdownCache_GetAllLeaderboardEntities_Call {
	return &MockShowdownCache_GetAllLeaderboardEntities_Call{Call: _e.mock.On("GetAllLeaderboardEntities", ctx, showdownId)}
}

func (_c *MockShowdownCache_GetAllLeaderboardEntities_Call) Run(run func(ctx context.Context, showdownId primitive.ObjectID)) *MockShowdownCache_GetAllLeaderboardEntities_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockShowdownCache_GetAllLeaderboardEntities_Call) Return(leaderboardEntitys []*cache.LeaderboardEntity, err error) *MockShowdownCache_GetAllLeaderboardEntities_Call {
	_c.Call.Return(leaderboardEntitys, err)
	return _c
}

func (_c *MockShowdownCache_GetAllLeaderboardEntities_Call) RunAndReturn(run func(ctx context.Context, showdownId primitive.ObjectID) ([]*cache.LeaderboardEntity, error)) *MockShowdownCache_GetAllLeaderboardEntities_Call {
	_c.Call.Return(run)
	return _c
}

// GetFixtures provides a mock function for the type MockShowdownCache
func (_mock *MockShowdownCache) GetFixtures(ctx context.Context, showdownId primitive.ObjectID) (*models.FicturesCollection, error) {
	ret := _mock.Called(ctx, showdownId)

	if len(ret) == 0 {
		panic("no return value specified for GetFixtures")
	}

	var r0 *models.FicturesCollection
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.FicturesCollection, error)); ok {
		return returnFunc(ctx, showdownId)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.FicturesCollection); ok {
		r0 = returnFunc(ctx, showdownId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.FicturesCollection)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, showdownId)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownCache_GetFixtures_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFixtures'
type MockShowdownCache_GetFixtures_Call struct {
	*mock.Call
}

// GetFixtures is a helper method to define mock.On call
//   - ctx context.Context
//   - showdownId primitive.ObjectID
func (_e *MockShowdownCache_Expecter) GetFixtures(ctx interface{}, showdownId interface{}) *MockShowdownCache_GetFixtures_Call {
	return &MockShowdownCache_GetFixtures_Call{Call: _e.mock.On("GetFixtures", ctx, showdownId)}
}

func (_c *MockShowdownCache_GetFixtures_Call) Run(run func(ctx context.Context, showdownId primitive.ObjectID)) *MockShowdownCache_GetFixtures_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockShowdownCache_GetFixtures_Call) Return(ficturesCollection *models.FicturesCollection, err error) *MockShowdownCache_GetFixtures_Call {
	_c.Call.Return(ficturesCollection, err)
	return _c
}

func (_c *MockShowdownCache_GetFixtures_Call) RunAndReturn(run func(ctx context.Context, showdownId primitive.ObjectID) (*models.FicturesCollection, error)) *MockShowdownCache_GetFixtures_Call {
	_c.Call.Return(run)
	return _c
}

// GetLeaderboardEntitiesCount provides a mock function for the type MockShowdownCache
func (_mock *MockShowdownCache) GetLeaderboardEntitiesCount(ctx context.Context, showdownId primitive.ObjectID) (int64, error) {
	ret := _mock.Called(ctx, showdownId)

	if len(ret) == 0 {
		panic("no return value specified for GetLeaderboardEntitiesCount")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (int64, error)); ok {
		return returnFunc(ctx, showdownId)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) int64); ok {
		r0 = returnFunc(ctx, showdownId)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, showdownId)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownCache_GetLeaderboardEntitiesCount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetLeaderboardEntitiesCount'
type MockShowdownCache_GetLeaderboardEntitiesCount_Call struct {
	*mock.Call
}

// GetLeaderboardEntitiesCount is a helper method to define mock.On call
//   - ctx context.Context
//   - showdownId primitive.ObjectID
func (_e *MockShowdownCache_Expecter) GetLeaderboardEntitiesCount(ctx interface{}, showdownId interface{}) *MockShowdownCache_GetLeaderboardEntitiesCount_Call {
	return &MockShowdownCache_GetLeaderboardEntitiesCount_Call{Call: _e.mock.On("GetLeaderboardEntitiesCount", ctx, showdownId)}
}

func (_c *MockShowdownCache_GetLeaderboardEntitiesCount_Call) Run(run func(ctx context.Context, showdownId primitive.ObjectID)) *MockShowdownCache_GetLeaderboardEntitiesCount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockShowdownCache_GetLeaderboardEntitiesCount_Call) Return(n int64, err error) *MockShowdownCache_GetLeaderboardEntitiesCount_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockShowdownCache_GetLeaderboardEntitiesCount_Call) RunAndReturn(run func(ctx context.Context, showdownId primitive.ObjectID) (int64, error)) *MockShowdownCache_GetLeaderboardEntitiesCount_Call {
	_c.Call.Return(run)
	return _c
}

// GetPaginatedLeaderboard provides a mock function for the type MockShowdownCache
func (_mock *MockShowdownCache) GetPaginatedLeaderboard(ctx context.Context, showdownId primitive.ObjectID, page int, pageSize int) ([]*cache.LeaderboardEntity, error) {
	ret := _mock.Called(ctx, showdownId, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetPaginatedLeaderboard")
	}

	var r0 []*cache.LeaderboardEntity
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int, int) ([]*cache.LeaderboardEntity, error)); ok {
		return returnFunc(ctx, showdownId, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int, int) []*cache.LeaderboardEntity); ok {
		r0 = returnFunc(ctx, showdownId, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*cache.LeaderboardEntity)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, int, int) error); ok {
		r1 = returnFunc(ctx, showdownId, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownCache_GetPaginatedLeaderboard_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPaginatedLeaderboard'
type MockShowdownCache_GetPaginatedLeaderboard_Call struct {
	*mock.Call
}

// GetPaginatedLeaderboard is a helper method to define mock.On call
//   - ctx context.Context
//   - showdownId primitive.ObjectID
//   - page int
//   - pageSize int
func (_e *MockShowdownCache_Expecter) GetPaginatedLeaderboard(ctx interface{}, showdownId interface{}, page interface{}, pageSize interface{}) *MockShowdownCache_GetPaginatedLeaderboard_Call {
	return &MockShowdownCache_GetPaginatedLeaderboard_Call{Call: _e.mock.On("GetPaginatedLeaderboard", ctx, showdownId, page, pageSize)}
}

func (_c *MockShowdownCache_GetPaginatedLeaderboard_Call) Run(run func(ctx context.Context, showdownId primitive.ObjectID, page int, pageSize int)) *MockShowdownCache_GetPaginatedLeaderboard_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 int
		if args[2] != nil {
			arg2 = args[2].(int)
		}
		var arg3 int
		if args[3] != nil {
			arg3 = args[3].(int)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockShowdownCache_GetPaginatedLeaderboard_Call) Return(leaderboardEntitys []*cache.LeaderboardEntity, err error) *MockShowdownCache_GetPaginatedLeaderboard_Call {
	_c.Call.Return(leaderboardEntitys, err)
	return _c
}

func (_c *MockShowdownCache_GetPaginatedLeaderboard_Call) RunAndReturn(run func(ctx context.Context, showdownId primitive.ObjectID, page int, pageSize int) ([]*cache.LeaderboardEntity, error)) *MockShowdownCache_GetPaginatedLeaderboard_Call {
	_c.Call.Return(run)
	return _c
}

// GetPaginatedLeaderboardRev provides a mock function for the type MockShowdownCache
func (_mock *MockShowdownCache) GetPaginatedLeaderboardRev(ctx context.Context, showdownId primitive.ObjectID, page int, pageSize int) ([]*cache.LeaderboardEntity, error) {
	ret := _mock.Called(ctx, showdownId, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetPaginatedLeaderboardRev")
	}

	var r0 []*cache.LeaderboardEntity
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int, int) ([]*cache.LeaderboardEntity, error)); ok {
		return returnFunc(ctx, showdownId, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int, int) []*cache.LeaderboardEntity); ok {
		r0 = returnFunc(ctx, showdownId, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*cache.LeaderboardEntity)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, int, int) error); ok {
		r1 = returnFunc(ctx, showdownId, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownCache_GetPaginatedLeaderboardRev_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPaginatedLeaderboardRev'
type MockShowdownCache_GetPaginatedLeaderboardRev_Call struct {
	*mock.Call
}

// GetPaginatedLeaderboardRev is a helper method to define mock.On call
//   - ctx context.Context
//   - showdownId primitive.ObjectID
//   - page int
//   - pageSize int
func (_e *MockShowdownCache_Expecter) GetPaginatedLeaderboardRev(ctx interface{}, showdownId interface{}, page interface{}, pageSize interface{}) *MockShowdownCache_GetPaginatedLeaderboardRev_Call {
	return &MockShowdownCache_GetPaginatedLeaderboardRev_Call{Call: _e.mock.On("GetPaginatedLeaderboardRev", ctx, showdownId, page, pageSize)}
}

func (_c *MockShowdownCache_GetPaginatedLeaderboardRev_Call) Run(run func(ctx context.Context, showdownId primitive.ObjectID, page int, pageSize int)) *MockShowdownCache_GetPaginatedLeaderboardRev_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 int
		if args[2] != nil {
			arg2 = args[2].(int)
		}
		var arg3 int
		if args[3] != nil {
			arg3 = args[3].(int)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockShowdownCache_GetPaginatedLeaderboardRev_Call) Return(leaderboardEntitys []*cache.LeaderboardEntity, err error) *MockShowdownCache_GetPaginatedLeaderboardRev_Call {
	_c.Call.Return(leaderboardEntitys, err)
	return _c
}

func (_c *MockShowdownCache_GetPaginatedLeaderboardRev_Call) RunAndReturn(run func(ctx context.Context, showdownId primitive.ObjectID, page int, pageSize int) ([]*cache.LeaderboardEntity, error)) *MockShowdownCache_GetPaginatedLeaderboardRev_Call {
	_c.Call.Return(run)
	return _c
}

// GetShowdown provides a mock function for the type MockShowdownCache
func (_mock *MockShowdownCache) GetShowdown(ctx context.Context, showdownId primitive.ObjectID) (*models.Showdown, error) {
	ret := _mock.Called(ctx, showdownId)

	if len(ret) == 0 {
		panic("no return value specified for GetShowdown")
	}

	var r0 *models.Showdown
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.Showdown, error)); ok {
		return returnFunc(ctx, showdownId)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.Showdown); ok {
		r0 = returnFunc(ctx, showdownId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Showdown)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, showdownId)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownCache_GetShowdown_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetShowdown'
type MockShowdownCache_GetShowdown_Call struct {
	*mock.Call
}

// GetShowdown is a helper method to define mock.On call
//   - ctx context.Context
//   - showdownId primitive.ObjectID
func (_e *MockShowdownCache_Expecter) GetShowdown(ctx interface{}, showdownId interface{}) *MockShowdownCache_GetShowdown_Call {
	return &MockShowdownCache_GetShowdown_Call{Call: _e.mock.On("GetShowdown", ctx, showdownId)}
}

func (_c *MockShowdownCache_GetShowdown_Call) Run(run func(ctx context.Context, showdownId primitive.ObjectID)) *MockShowdownCache_GetShowdown_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockShowdownCache_GetShowdown_Call) Return(showdown *models.Showdown, err error) *MockShowdownCache_GetShowdown_Call {
	_c.Call.Return(showdown, err)
	return _c
}

func (_c *MockShowdownCache_GetShowdown_Call) RunAndReturn(run func(ctx context.Context, showdownId primitive.ObjectID) (*models.Showdown, error)) *MockShowdownCache_GetShowdown_Call {
	_c.Call.Return(run)
	return _c
}

// GetShowdownParticipant provides a mock function for the type MockShowdownCache
func (_mock *MockShowdownCache) GetShowdownParticipant(ctx context.Context, showdownId primitive.ObjectID, participantId primitive.ObjectID) (*models.ShowdownParticipant, error) {
	ret := _mock.Called(ctx, showdownId, participantId)

	if len(ret) == 0 {
		panic("no return value specified for GetShowdownParticipant")
	}

	var r0 *models.ShowdownParticipant
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) (*models.ShowdownParticipant, error)); ok {
		return returnFunc(ctx, showdownId, participantId)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) *models.ShowdownParticipant); ok {
		r0 = returnFunc(ctx, showdownId, participantId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ShowdownParticipant)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, showdownId, participantId)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownCache_GetShowdownParticipant_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetShowdownParticipant'
type MockShowdownCache_GetShowdownParticipant_Call struct {
	*mock.Call
}

// GetShowdownParticipant is a helper method to define mock.On call
//   - ctx context.Context
//   - showdownId primitive.ObjectID
//   - participantId primitive.ObjectID
func (_e *MockShowdownCache_Expecter) GetShowdownParticipant(ctx interface{}, showdownId interface{}, participantId interface{}) *MockShowdownCache_GetShowdownParticipant_Call {
	return &MockShowdownCache_GetShowdownParticipant_Call{Call: _e.mock.On("GetShowdownParticipant", ctx, showdownId, participantId)}
}

func (_c *MockShowdownCache_GetShowdownParticipant_Call) Run(run func(ctx context.Context, showdownId primitive.ObjectID, participantId primitive.ObjectID)) *MockShowdownCache_GetShowdownParticipant_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockShowdownCache_GetShowdownParticipant_Call) Return(showdownParticipant *models.ShowdownParticipant, err error) *MockShowdownCache_GetShowdownParticipant_Call {
	_c.Call.Return(showdownParticipant, err)
	return _c
}

func (_c *MockShowdownCache_GetShowdownParticipant_Call) RunAndReturn(run func(ctx context.Context, showdownId primitive.ObjectID, participantId primitive.ObjectID) (*models.ShowdownParticipant, error)) *MockShowdownCache_GetShowdownParticipant_Call {
	_c.Call.Return(run)
	return _c
}

// GetShowdownParticipantIdFromUserId provides a mock function for the type MockShowdownCache
func (_mock *MockShowdownCache) GetShowdownParticipantIdFromUserId(ctx context.Context, showdownId primitive.ObjectID, userID primitive.ObjectID) (primitive.ObjectID, error) {
	ret := _mock.Called(ctx, showdownId, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetShowdownParticipantIdFromUserId")
	}

	var r0 primitive.ObjectID
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) (primitive.ObjectID, error)); ok {
		return returnFunc(ctx, showdownId, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) primitive.ObjectID); ok {
		r0 = returnFunc(ctx, showdownId, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(primitive.ObjectID)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, showdownId, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownCache_GetShowdownParticipantIdFromUserId_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetShowdownParticipantIdFromUserId'
type MockShowdownCache_GetShowdownParticipantIdFromUserId_Call struct {
	*mock.Call
}

// GetShowdownParticipantIdFromUserId is a helper method to define mock.On call
//   - ctx context.Context
//   - showdownId primitive.ObjectID
//   - userID primitive.ObjectID
func (_e *MockShowdownCache_Expecter) GetShowdownParticipantIdFromUserId(ctx interface{}, showdownId interface{}, userID interface{}) *MockShowdownCache_GetShowdownParticipantIdFromUserId_Call {
	return &MockShowdownCache_GetShowdownParticipantIdFromUserId_Call{Call: _e.mock.On("GetShowdownParticipantIdFromUserId", ctx, showdownId, userID)}
}

func (_c *MockShowdownCache_GetShowdownParticipantIdFromUserId_Call) Run(run func(ctx context.Context, showdownId primitive.ObjectID, userID primitive.ObjectID)) *MockShowdownCache_GetShowdownParticipantIdFromUserId_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockShowdownCache_GetShowdownParticipantIdFromUserId_Call) Return(objectID primitive.ObjectID, err error) *MockShowdownCache_GetShowdownParticipantIdFromUserId_Call {
	_c.Call.Return(objectID, err)
	return _c
}

func (_c *MockShowdownCache_GetShowdownParticipantIdFromUserId_Call) RunAndReturn(run func(ctx context.Context, showdownId primitive.ObjectID, userID primitive.ObjectID) (primitive.ObjectID, error)) *MockShowdownCache_GetShowdownParticipantIdFromUserId_Call {
	_c.Call.Return(run)
	return _c
}

// SetFixtures provides a mock function for the type MockShowdownCache
func (_mock *MockShowdownCache) SetFixtures(ctx context.Context, showdownId primitive.ObjectID, fictures *models.FicturesCollection) error {
	ret := _mock.Called(ctx, showdownId, fictures)

	if len(ret) == 0 {
		panic("no return value specified for SetFixtures")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *models.FicturesCollection) error); ok {
		r0 = returnFunc(ctx, showdownId, fictures)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockShowdownCache_SetFixtures_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetFixtures'
type MockShowdownCache_SetFixtures_Call struct {
	*mock.Call
}

// SetFixtures is a helper method to define mock.On call
//   - ctx context.Context
//   - showdownId primitive.ObjectID
//   - fictures *models.FicturesCollection
func (_e *MockShowdownCache_Expecter) SetFixtures(ctx interface{}, showdownId interface{}, fictures interface{}) *MockShowdownCache_SetFixtures_Call {
	return &MockShowdownCache_SetFixtures_Call{Call: _e.mock.On("SetFixtures", ctx, showdownId, fictures)}
}

func (_c *MockShowdownCache_SetFixtures_Call) Run(run func(ctx context.Context, showdownId primitive.ObjectID, fictures *models.FicturesCollection)) *MockShowdownCache_SetFixtures_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 *models.FicturesCollection
		if args[2] != nil {
			arg2 = args[2].(*models.FicturesCollection)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockShowdownCache_SetFixtures_Call) Return(err error) *MockShowdownCache_SetFixtures_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockShowdownCache_SetFixtures_Call) RunAndReturn(run func(ctx context.Context, showdownId primitive.ObjectID, fictures *models.FicturesCollection) error) *MockShowdownCache_SetFixtures_Call {
	_c.Call.Return(run)
	return _c
}

// SetShowdown provides a mock function for the type MockShowdownCache
func (_mock *MockShowdownCache) SetShowdown(ctx context.Context, showdown *models.Showdown) error {
	ret := _mock.Called(ctx, showdown)

	if len(ret) == 0 {
		panic("no return value specified for SetShowdown")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.Showdown) error); ok {
		r0 = returnFunc(ctx, showdown)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockShowdownCache_SetShowdown_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetShowdown'
type MockShowdownCache_SetShowdown_Call struct {
	*mock.Call
}

// SetShowdown is a helper method to define mock.On call
//   - ctx context.Context
//   - showdown *models.Showdown
func (_e *MockShowdownCache_Expecter) SetShowdown(ctx interface{}, showdown interface{}) *MockShowdownCache_SetShowdown_Call {
	return &MockShowdownCache_SetShowdown_Call{Call: _e.mock.On("SetShowdown", ctx, showdown)}
}

func (_c *MockShowdownCache_SetShowdown_Call) Run(run func(ctx context.Context, showdown *models.Showdown)) *MockShowdownCache_SetShowdown_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.Showdown
		if args[1] != nil {
			arg1 = args[1].(*models.Showdown)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockShowdownCache_SetShowdown_Call) Return(err error) *MockShowdownCache_SetShowdown_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockShowdownCache_SetShowdown_Call) RunAndReturn(run func(ctx context.Context, showdown *models.Showdown) error) *MockShowdownCache_SetShowdown_Call {
	_c.Call.Return(run)
	return _c
}

// SetShowdownParticipant provides a mock function for the type MockShowdownCache
func (_mock *MockShowdownCache) SetShowdownParticipant(ctx context.Context, showdownParticipant *models.ShowdownParticipant) (int64, error) {
	ret := _mock.Called(ctx, showdownParticipant)

	if len(ret) == 0 {
		panic("no return value specified for SetShowdownParticipant")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.ShowdownParticipant) (int64, error)); ok {
		return returnFunc(ctx, showdownParticipant)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.ShowdownParticipant) int64); ok {
		r0 = returnFunc(ctx, showdownParticipant)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.ShowdownParticipant) error); ok {
		r1 = returnFunc(ctx, showdownParticipant)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownCache_SetShowdownParticipant_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetShowdownParticipant'
type MockShowdownCache_SetShowdownParticipant_Call struct {
	*mock.Call
}

// SetShowdownParticipant is a helper method to define mock.On call
//   - ctx context.Context
//   - showdownParticipant *models.ShowdownParticipant
func (_e *MockShowdownCache_Expecter) SetShowdownParticipant(ctx interface{}, showdownParticipant interface{}) *MockShowdownCache_SetShowdownParticipant_Call {
	return &MockShowdownCache_SetShowdownParticipant_Call{Call: _e.mock.On("SetShowdownParticipant", ctx, showdownParticipant)}
}

func (_c *MockShowdownCache_SetShowdownParticipant_Call) Run(run func(ctx context.Context, showdownParticipant *models.ShowdownParticipant)) *MockShowdownCache_SetShowdownParticipant_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.ShowdownParticipant
		if args[1] != nil {
			arg1 = args[1].(*models.ShowdownParticipant)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockShowdownCache_SetShowdownParticipant_Call) Return(n int64, err error) *MockShowdownCache_SetShowdownParticipant_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockShowdownCache_SetShowdownParticipant_Call) RunAndReturn(run func(ctx context.Context, showdownParticipant *models.ShowdownParticipant) (int64, error)) *MockShowdownCache_SetShowdownParticipant_Call {
	_c.Call.Return(run)
	return _c
}

// SetShowdownParticipantIdOnUserId provides a mock function for the type MockShowdownCache
func (_mock *MockShowdownCache) SetShowdownParticipantIdOnUserId(ctx context.Context, showdownId primitive.ObjectID, participantId primitive.ObjectID, userID primitive.ObjectID) error {
	ret := _mock.Called(ctx, showdownId, participantId, userID)

	if len(ret) == 0 {
		panic("no return value specified for SetShowdownParticipantIdOnUserId")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, showdownId, participantId, userID)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockShowdownCache_SetShowdownParticipantIdOnUserId_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetShowdownParticipantIdOnUserId'
type MockShowdownCache_SetShowdownParticipantIdOnUserId_Call struct {
	*mock.Call
}

// SetShowdownParticipantIdOnUserId is a helper method to define mock.On call
//   - ctx context.Context
//   - showdownId primitive.ObjectID
//   - participantId primitive.ObjectID
//   - userID primitive.ObjectID
func (_e *MockShowdownCache_Expecter) SetShowdownParticipantIdOnUserId(ctx interface{}, showdownId interface{}, participantId interface{}, userID interface{}) *MockShowdownCache_SetShowdownParticipantIdOnUserId_Call {
	return &MockShowdownCache_SetShowdownParticipantIdOnUserId_Call{Call: _e.mock.On("SetShowdownParticipantIdOnUserId", ctx, showdownId, participantId, userID)}
}

func (_c *MockShowdownCache_SetShowdownParticipantIdOnUserId_Call) Run(run func(ctx context.Context, showdownId primitive.ObjectID, participantId primitive.ObjectID, userID primitive.ObjectID)) *MockShowdownCache_SetShowdownParticipantIdOnUserId_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(primitive.ObjectID)
		}
		var arg3 primitive.ObjectID
		if args[3] != nil {
			arg3 = args[3].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockShowdownCache_SetShowdownParticipantIdOnUserId_Call) Return(err error) *MockShowdownCache_SetShowdownParticipantIdOnUserId_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockShowdownCache_SetShowdownParticipantIdOnUserId_Call) RunAndReturn(run func(ctx context.Context, showdownId primitive.ObjectID, participantId primitive.ObjectID, userID primitive.ObjectID) error) *MockShowdownCache_SetShowdownParticipantIdOnUserId_Call {
	_c.Call.Return(run)
	return _c
}
