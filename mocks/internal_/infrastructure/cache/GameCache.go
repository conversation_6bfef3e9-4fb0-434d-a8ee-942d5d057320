// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package cache

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
)

// NewMockGameCache creates a new instance of MockGameCache. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockGameCache(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockGameCache {
	mock := &MockGameCache{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockGameCache is an autogenerated mock type for the GameCache type
type MockGameCache struct {
	mock.Mock
}

type MockGameCache_Expecter struct {
	mock *mock.Mock
}

func (_m *MockGameCache) EXPECT() *MockGameCache_Expecter {
	return &MockGameCache_Expecter{mock: &_m.<PERSON>}
}

// DeleteGame provides a mock function for the type MockGameCache
func (_mock *MockGameCache) DeleteGame(ctx context.Context, gameID string) error {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteGame")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockGameCache_DeleteGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteGame'
type MockGameCache_DeleteGame_Call struct {
	*mock.Call
}

// DeleteGame is a helper method to define mock.On call
//   - ctx context.Context
//   - gameID string
func (_e *MockGameCache_Expecter) DeleteGame(ctx interface{}, gameID interface{}) *MockGameCache_DeleteGame_Call {
	return &MockGameCache_DeleteGame_Call{Call: _e.mock.On("DeleteGame", ctx, gameID)}
}

func (_c *MockGameCache_DeleteGame_Call) Run(run func(ctx context.Context, gameID string)) *MockGameCache_DeleteGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockGameCache_DeleteGame_Call) Return(err error) *MockGameCache_DeleteGame_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockGameCache_DeleteGame_Call) RunAndReturn(run func(ctx context.Context, gameID string) error) *MockGameCache_DeleteGame_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteSearchEntry provides a mock function for the type MockGameCache
func (_mock *MockGameCache) DeleteSearchEntry(ctx context.Context, userID string) error {
	ret := _mock.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteSearchEntry")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = returnFunc(ctx, userID)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockGameCache_DeleteSearchEntry_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteSearchEntry'
type MockGameCache_DeleteSearchEntry_Call struct {
	*mock.Call
}

// DeleteSearchEntry is a helper method to define mock.On call
//   - ctx context.Context
//   - userID string
func (_e *MockGameCache_Expecter) DeleteSearchEntry(ctx interface{}, userID interface{}) *MockGameCache_DeleteSearchEntry_Call {
	return &MockGameCache_DeleteSearchEntry_Call{Call: _e.mock.On("DeleteSearchEntry", ctx, userID)}
}

func (_c *MockGameCache_DeleteSearchEntry_Call) Run(run func(ctx context.Context, userID string)) *MockGameCache_DeleteSearchEntry_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockGameCache_DeleteSearchEntry_Call) Return(err error) *MockGameCache_DeleteSearchEntry_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockGameCache_DeleteSearchEntry_Call) RunAndReturn(run func(ctx context.Context, userID string) error) *MockGameCache_DeleteSearchEntry_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllGames provides a mock function for the type MockGameCache
func (_mock *MockGameCache) GetAllGames(ctx context.Context) ([]*models.Game, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetAllGames")
	}

	var r0 []*models.Game
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) ([]*models.Game, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) []*models.Game); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.Game)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameCache_GetAllGames_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllGames'
type MockGameCache_GetAllGames_Call struct {
	*mock.Call
}

// GetAllGames is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockGameCache_Expecter) GetAllGames(ctx interface{}) *MockGameCache_GetAllGames_Call {
	return &MockGameCache_GetAllGames_Call{Call: _e.mock.On("GetAllGames", ctx)}
}

func (_c *MockGameCache_GetAllGames_Call) Run(run func(ctx context.Context)) *MockGameCache_GetAllGames_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockGameCache_GetAllGames_Call) Return(games []*models.Game, err error) *MockGameCache_GetAllGames_Call {
	_c.Call.Return(games, err)
	return _c
}

func (_c *MockGameCache_GetAllGames_Call) RunAndReturn(run func(ctx context.Context) ([]*models.Game, error)) *MockGameCache_GetAllGames_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllSearchEntries provides a mock function for the type MockGameCache
func (_mock *MockGameCache) GetAllSearchEntries(ctx context.Context) ([]*models.SearchEntry, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetAllSearchEntries")
	}

	var r0 []*models.SearchEntry
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) ([]*models.SearchEntry, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) []*models.SearchEntry); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.SearchEntry)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameCache_GetAllSearchEntries_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllSearchEntries'
type MockGameCache_GetAllSearchEntries_Call struct {
	*mock.Call
}

// GetAllSearchEntries is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockGameCache_Expecter) GetAllSearchEntries(ctx interface{}) *MockGameCache_GetAllSearchEntries_Call {
	return &MockGameCache_GetAllSearchEntries_Call{Call: _e.mock.On("GetAllSearchEntries", ctx)}
}

func (_c *MockGameCache_GetAllSearchEntries_Call) Run(run func(ctx context.Context)) *MockGameCache_GetAllSearchEntries_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockGameCache_GetAllSearchEntries_Call) Return(searchEntrys []*models.SearchEntry, err error) *MockGameCache_GetAllSearchEntries_Call {
	_c.Call.Return(searchEntrys, err)
	return _c
}

func (_c *MockGameCache_GetAllSearchEntries_Call) RunAndReturn(run func(ctx context.Context) ([]*models.SearchEntry, error)) *MockGameCache_GetAllSearchEntries_Call {
	_c.Call.Return(run)
	return _c
}

// GetGame provides a mock function for the type MockGameCache
func (_mock *MockGameCache) GetGame(ctx context.Context, gameID string) (*models.Game, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for GetGame")
	}

	var r0 *models.Game
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (*models.Game, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) *models.Game); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Game)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameCache_GetGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetGame'
type MockGameCache_GetGame_Call struct {
	*mock.Call
}

// GetGame is a helper method to define mock.On call
//   - ctx context.Context
//   - gameID string
func (_e *MockGameCache_Expecter) GetGame(ctx interface{}, gameID interface{}) *MockGameCache_GetGame_Call {
	return &MockGameCache_GetGame_Call{Call: _e.mock.On("GetGame", ctx, gameID)}
}

func (_c *MockGameCache_GetGame_Call) Run(run func(ctx context.Context, gameID string)) *MockGameCache_GetGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockGameCache_GetGame_Call) Return(game *models.Game, err error) *MockGameCache_GetGame_Call {
	_c.Call.Return(game, err)
	return _c
}

func (_c *MockGameCache_GetGame_Call) RunAndReturn(run func(ctx context.Context, gameID string) (*models.Game, error)) *MockGameCache_GetGame_Call {
	_c.Call.Return(run)
	return _c
}

// GetSearchEntry provides a mock function for the type MockGameCache
func (_mock *MockGameCache) GetSearchEntry(ctx context.Context, userID string) (*models.SearchEntry, error) {
	ret := _mock.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetSearchEntry")
	}

	var r0 *models.SearchEntry
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (*models.SearchEntry, error)); ok {
		return returnFunc(ctx, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) *models.SearchEntry); ok {
		r0 = returnFunc(ctx, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.SearchEntry)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameCache_GetSearchEntry_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetSearchEntry'
type MockGameCache_GetSearchEntry_Call struct {
	*mock.Call
}

// GetSearchEntry is a helper method to define mock.On call
//   - ctx context.Context
//   - userID string
func (_e *MockGameCache_Expecter) GetSearchEntry(ctx interface{}, userID interface{}) *MockGameCache_GetSearchEntry_Call {
	return &MockGameCache_GetSearchEntry_Call{Call: _e.mock.On("GetSearchEntry", ctx, userID)}
}

func (_c *MockGameCache_GetSearchEntry_Call) Run(run func(ctx context.Context, userID string)) *MockGameCache_GetSearchEntry_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockGameCache_GetSearchEntry_Call) Return(searchEntry *models.SearchEntry, err error) *MockGameCache_GetSearchEntry_Call {
	_c.Call.Return(searchEntry, err)
	return _c
}

func (_c *MockGameCache_GetSearchEntry_Call) RunAndReturn(run func(ctx context.Context, userID string) (*models.SearchEntry, error)) *MockGameCache_GetSearchEntry_Call {
	_c.Call.Return(run)
	return _c
}

// SetGame provides a mock function for the type MockGameCache
func (_mock *MockGameCache) SetGame(ctx context.Context, game *models.Game) error {
	ret := _mock.Called(ctx, game)

	if len(ret) == 0 {
		panic("no return value specified for SetGame")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.Game) error); ok {
		r0 = returnFunc(ctx, game)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockGameCache_SetGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetGame'
type MockGameCache_SetGame_Call struct {
	*mock.Call
}

// SetGame is a helper method to define mock.On call
//   - ctx context.Context
//   - game *models.Game
func (_e *MockGameCache_Expecter) SetGame(ctx interface{}, game interface{}) *MockGameCache_SetGame_Call {
	return &MockGameCache_SetGame_Call{Call: _e.mock.On("SetGame", ctx, game)}
}

func (_c *MockGameCache_SetGame_Call) Run(run func(ctx context.Context, game *models.Game)) *MockGameCache_SetGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.Game
		if args[1] != nil {
			arg1 = args[1].(*models.Game)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockGameCache_SetGame_Call) Return(err error) *MockGameCache_SetGame_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockGameCache_SetGame_Call) RunAndReturn(run func(ctx context.Context, game *models.Game) error) *MockGameCache_SetGame_Call {
	_c.Call.Return(run)
	return _c
}

// SetSearchEntry provides a mock function for the type MockGameCache
func (_mock *MockGameCache) SetSearchEntry(ctx context.Context, searchEntry *models.SearchEntry) error {
	ret := _mock.Called(ctx, searchEntry)

	if len(ret) == 0 {
		panic("no return value specified for SetSearchEntry")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.SearchEntry) error); ok {
		r0 = returnFunc(ctx, searchEntry)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockGameCache_SetSearchEntry_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetSearchEntry'
type MockGameCache_SetSearchEntry_Call struct {
	*mock.Call
}

// SetSearchEntry is a helper method to define mock.On call
//   - ctx context.Context
//   - searchEntry *models.SearchEntry
func (_e *MockGameCache_Expecter) SetSearchEntry(ctx interface{}, searchEntry interface{}) *MockGameCache_SetSearchEntry_Call {
	return &MockGameCache_SetSearchEntry_Call{Call: _e.mock.On("SetSearchEntry", ctx, searchEntry)}
}

func (_c *MockGameCache_SetSearchEntry_Call) Run(run func(ctx context.Context, searchEntry *models.SearchEntry)) *MockGameCache_SetSearchEntry_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.SearchEntry
		if args[1] != nil {
			arg1 = args[1].(*models.SearchEntry)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockGameCache_SetSearchEntry_Call) Return(err error) *MockGameCache_SetSearchEntry_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockGameCache_SetSearchEntry_Call) RunAndReturn(run func(ctx context.Context, searchEntry *models.SearchEntry) error) *MockGameCache_SetSearchEntry_Call {
	_c.Call.Return(run)
	return _c
}
