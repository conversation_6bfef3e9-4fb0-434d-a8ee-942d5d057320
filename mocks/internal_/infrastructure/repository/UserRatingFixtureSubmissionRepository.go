// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// NewMockUserRatingFixtureSubmissionRepository creates a new instance of MockUserRatingFixtureSubmissionRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockUserRatingFixtureSubmissionRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockUserRatingFixtureSubmissionRepository {
	mock := &MockUserRatingFixtureSubmissionRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockUserRatingFixtureSubmissionRepository is an autogenerated mock type for the UserRatingFixtureSubmissionRepository type
type MockUserRatingFixtureSubmissionRepository struct {
	mock.Mock
}

type MockUserRatingFixtureSubmissionRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockUserRatingFixtureSubmissionRepository) EXPECT() *MockUserRatingFixtureSubmissionRepository_Expecter {
	return &MockUserRatingFixtureSubmissionRepository_Expecter{mock: &_m.Mock}
}

// FindByUserID provides a mock function for the type MockUserRatingFixtureSubmissionRepository
func (_mock *MockUserRatingFixtureSubmissionRepository) FindByUserID(ctx context.Context, userID primitive.ObjectID) (*models.UserRatingFixtureSubmission, error) {
	ret := _mock.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for FindByUserID")
	}

	var r0 *models.UserRatingFixtureSubmission
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.UserRatingFixtureSubmission, error)); ok {
		return returnFunc(ctx, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.UserRatingFixtureSubmission); ok {
		r0 = returnFunc(ctx, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserRatingFixtureSubmission)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserRatingFixtureSubmissionRepository_FindByUserID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindByUserID'
type MockUserRatingFixtureSubmissionRepository_FindByUserID_Call struct {
	*mock.Call
}

// FindByUserID is a helper method to define mock.On call
//   - ctx context.Context
//   - userID primitive.ObjectID
func (_e *MockUserRatingFixtureSubmissionRepository_Expecter) FindByUserID(ctx interface{}, userID interface{}) *MockUserRatingFixtureSubmissionRepository_FindByUserID_Call {
	return &MockUserRatingFixtureSubmissionRepository_FindByUserID_Call{Call: _e.mock.On("FindByUserID", ctx, userID)}
}

func (_c *MockUserRatingFixtureSubmissionRepository_FindByUserID_Call) Run(run func(ctx context.Context, userID primitive.ObjectID)) *MockUserRatingFixtureSubmissionRepository_FindByUserID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockUserRatingFixtureSubmissionRepository_FindByUserID_Call) Return(userRatingFixtureSubmission *models.UserRatingFixtureSubmission, err error) *MockUserRatingFixtureSubmissionRepository_FindByUserID_Call {
	_c.Call.Return(userRatingFixtureSubmission, err)
	return _c
}

func (_c *MockUserRatingFixtureSubmissionRepository_FindByUserID_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID) (*models.UserRatingFixtureSubmission, error)) *MockUserRatingFixtureSubmissionRepository_FindByUserID_Call {
	_c.Call.Return(run)
	return _c
}

// InsertOne provides a mock function for the type MockUserRatingFixtureSubmissionRepository
func (_mock *MockUserRatingFixtureSubmissionRepository) InsertOne(ctx context.Context, document *models.UserRatingFixtureSubmission) (*mongo.InsertOneResult, error) {
	ret := _mock.Called(ctx, document)

	if len(ret) == 0 {
		panic("no return value specified for InsertOne")
	}

	var r0 *mongo.InsertOneResult
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UserRatingFixtureSubmission) (*mongo.InsertOneResult, error)); ok {
		return returnFunc(ctx, document)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UserRatingFixtureSubmission) *mongo.InsertOneResult); ok {
		r0 = returnFunc(ctx, document)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*mongo.InsertOneResult)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.UserRatingFixtureSubmission) error); ok {
		r1 = returnFunc(ctx, document)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserRatingFixtureSubmissionRepository_InsertOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'InsertOne'
type MockUserRatingFixtureSubmissionRepository_InsertOne_Call struct {
	*mock.Call
}

// InsertOne is a helper method to define mock.On call
//   - ctx context.Context
//   - document *models.UserRatingFixtureSubmission
func (_e *MockUserRatingFixtureSubmissionRepository_Expecter) InsertOne(ctx interface{}, document interface{}) *MockUserRatingFixtureSubmissionRepository_InsertOne_Call {
	return &MockUserRatingFixtureSubmissionRepository_InsertOne_Call{Call: _e.mock.On("InsertOne", ctx, document)}
}

func (_c *MockUserRatingFixtureSubmissionRepository_InsertOne_Call) Run(run func(ctx context.Context, document *models.UserRatingFixtureSubmission)) *MockUserRatingFixtureSubmissionRepository_InsertOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.UserRatingFixtureSubmission
		if args[1] != nil {
			arg1 = args[1].(*models.UserRatingFixtureSubmission)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockUserRatingFixtureSubmissionRepository_InsertOne_Call) Return(insertOneResult *mongo.InsertOneResult, err error) *MockUserRatingFixtureSubmissionRepository_InsertOne_Call {
	_c.Call.Return(insertOneResult, err)
	return _c
}

func (_c *MockUserRatingFixtureSubmissionRepository_InsertOne_Call) RunAndReturn(run func(ctx context.Context, document *models.UserRatingFixtureSubmission) (*mongo.InsertOneResult, error)) *MockUserRatingFixtureSubmissionRepository_InsertOne_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateOne provides a mock function for the type MockUserRatingFixtureSubmissionRepository
func (_mock *MockUserRatingFixtureSubmissionRepository) UpdateOne(ctx context.Context, submission *models.UserRatingFixtureSubmission) (*mongo.UpdateResult, error) {
	ret := _mock.Called(ctx, submission)

	if len(ret) == 0 {
		panic("no return value specified for UpdateOne")
	}

	var r0 *mongo.UpdateResult
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UserRatingFixtureSubmission) (*mongo.UpdateResult, error)); ok {
		return returnFunc(ctx, submission)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UserRatingFixtureSubmission) *mongo.UpdateResult); ok {
		r0 = returnFunc(ctx, submission)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*mongo.UpdateResult)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.UserRatingFixtureSubmission) error); ok {
		r1 = returnFunc(ctx, submission)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserRatingFixtureSubmissionRepository_UpdateOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateOne'
type MockUserRatingFixtureSubmissionRepository_UpdateOne_Call struct {
	*mock.Call
}

// UpdateOne is a helper method to define mock.On call
//   - ctx context.Context
//   - submission *models.UserRatingFixtureSubmission
func (_e *MockUserRatingFixtureSubmissionRepository_Expecter) UpdateOne(ctx interface{}, submission interface{}) *MockUserRatingFixtureSubmissionRepository_UpdateOne_Call {
	return &MockUserRatingFixtureSubmissionRepository_UpdateOne_Call{Call: _e.mock.On("UpdateOne", ctx, submission)}
}

func (_c *MockUserRatingFixtureSubmissionRepository_UpdateOne_Call) Run(run func(ctx context.Context, submission *models.UserRatingFixtureSubmission)) *MockUserRatingFixtureSubmissionRepository_UpdateOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.UserRatingFixtureSubmission
		if args[1] != nil {
			arg1 = args[1].(*models.UserRatingFixtureSubmission)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockUserRatingFixtureSubmissionRepository_UpdateOne_Call) Return(updateResult *mongo.UpdateResult, err error) *MockUserRatingFixtureSubmissionRepository_UpdateOne_Call {
	_c.Call.Return(updateResult, err)
	return _c
}

func (_c *MockUserRatingFixtureSubmissionRepository_UpdateOne_Call) RunAndReturn(run func(ctx context.Context, submission *models.UserRatingFixtureSubmission) (*mongo.UpdateResult, error)) *MockUserRatingFixtureSubmissionRepository_UpdateOne_Call {
	_c.Call.Return(run)
	return _c
}
