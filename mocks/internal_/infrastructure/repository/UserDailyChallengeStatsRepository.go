// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// NewMockUserDailyChallengeStatsRepository creates a new instance of MockUserDailyChallengeStatsRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockUserDailyChallengeStatsRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockUserDailyChallengeStatsRepository {
	mock := &MockUserDailyChallengeStatsRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockUserDailyChallengeStatsRepository is an autogenerated mock type for the UserDailyChallengeStatsRepository type
type MockUserDailyChallengeStatsRepository struct {
	mock.Mock
}

type MockUserDailyChallengeStatsRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockUserDailyChallengeStatsRepository) EXPECT() *MockUserDailyChallengeStatsRepository_Expecter {
	return &MockUserDailyChallengeStatsRepository_Expecter{mock: &_m.Mock}
}

// Collection provides a mock function for the type MockUserDailyChallengeStatsRepository
func (_mock *MockUserDailyChallengeStatsRepository) Collection() *mongo.Collection {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for Collection")
	}

	var r0 *mongo.Collection
	if returnFunc, ok := ret.Get(0).(func() *mongo.Collection); ok {
		r0 = returnFunc()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*mongo.Collection)
		}
	}
	return r0
}

// MockUserDailyChallengeStatsRepository_Collection_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Collection'
type MockUserDailyChallengeStatsRepository_Collection_Call struct {
	*mock.Call
}

// Collection is a helper method to define mock.On call
func (_e *MockUserDailyChallengeStatsRepository_Expecter) Collection() *MockUserDailyChallengeStatsRepository_Collection_Call {
	return &MockUserDailyChallengeStatsRepository_Collection_Call{Call: _e.mock.On("Collection")}
}

func (_c *MockUserDailyChallengeStatsRepository_Collection_Call) Run(run func()) *MockUserDailyChallengeStatsRepository_Collection_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockUserDailyChallengeStatsRepository_Collection_Call) Return(collection *mongo.Collection) *MockUserDailyChallengeStatsRepository_Collection_Call {
	_c.Call.Return(collection)
	return _c
}

func (_c *MockUserDailyChallengeStatsRepository_Collection_Call) RunAndReturn(run func() *mongo.Collection) *MockUserDailyChallengeStatsRepository_Collection_Call {
	_c.Call.Return(run)
	return _c
}

// Create provides a mock function for the type MockUserDailyChallengeStatsRepository
func (_mock *MockUserDailyChallengeStatsRepository) Create(ctx context.Context, stats *models.UserDailyChallengeStats) error {
	ret := _mock.Called(ctx, stats)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UserDailyChallengeStats) error); ok {
		r0 = returnFunc(ctx, stats)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockUserDailyChallengeStatsRepository_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type MockUserDailyChallengeStatsRepository_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx context.Context
//   - stats *models.UserDailyChallengeStats
func (_e *MockUserDailyChallengeStatsRepository_Expecter) Create(ctx interface{}, stats interface{}) *MockUserDailyChallengeStatsRepository_Create_Call {
	return &MockUserDailyChallengeStatsRepository_Create_Call{Call: _e.mock.On("Create", ctx, stats)}
}

func (_c *MockUserDailyChallengeStatsRepository_Create_Call) Run(run func(ctx context.Context, stats *models.UserDailyChallengeStats)) *MockUserDailyChallengeStatsRepository_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.UserDailyChallengeStats
		if args[1] != nil {
			arg1 = args[1].(*models.UserDailyChallengeStats)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockUserDailyChallengeStatsRepository_Create_Call) Return(err error) *MockUserDailyChallengeStatsRepository_Create_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockUserDailyChallengeStatsRepository_Create_Call) RunAndReturn(run func(ctx context.Context, stats *models.UserDailyChallengeStats) error) *MockUserDailyChallengeStatsRepository_Create_Call {
	_c.Call.Return(run)
	return _c
}

// Delete provides a mock function for the type MockUserDailyChallengeStatsRepository
func (_mock *MockUserDailyChallengeStatsRepository) Delete(ctx context.Context, id primitive.ObjectID) error {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, id)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockUserDailyChallengeStatsRepository_Delete_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Delete'
type MockUserDailyChallengeStatsRepository_Delete_Call struct {
	*mock.Call
}

// Delete is a helper method to define mock.On call
//   - ctx context.Context
//   - id primitive.ObjectID
func (_e *MockUserDailyChallengeStatsRepository_Expecter) Delete(ctx interface{}, id interface{}) *MockUserDailyChallengeStatsRepository_Delete_Call {
	return &MockUserDailyChallengeStatsRepository_Delete_Call{Call: _e.mock.On("Delete", ctx, id)}
}

func (_c *MockUserDailyChallengeStatsRepository_Delete_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockUserDailyChallengeStatsRepository_Delete_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockUserDailyChallengeStatsRepository_Delete_Call) Return(err error) *MockUserDailyChallengeStatsRepository_Delete_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockUserDailyChallengeStatsRepository_Delete_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) error) *MockUserDailyChallengeStatsRepository_Delete_Call {
	_c.Call.Return(run)
	return _c
}

// FindByID provides a mock function for the type MockUserDailyChallengeStatsRepository
func (_mock *MockUserDailyChallengeStatsRepository) FindByID(ctx context.Context, id primitive.ObjectID) (*models.UserDailyChallengeStats, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for FindByID")
	}

	var r0 *models.UserDailyChallengeStats
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.UserDailyChallengeStats, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.UserDailyChallengeStats); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserDailyChallengeStats)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserDailyChallengeStatsRepository_FindByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindByID'
type MockUserDailyChallengeStatsRepository_FindByID_Call struct {
	*mock.Call
}

// FindByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id primitive.ObjectID
func (_e *MockUserDailyChallengeStatsRepository_Expecter) FindByID(ctx interface{}, id interface{}) *MockUserDailyChallengeStatsRepository_FindByID_Call {
	return &MockUserDailyChallengeStatsRepository_FindByID_Call{Call: _e.mock.On("FindByID", ctx, id)}
}

func (_c *MockUserDailyChallengeStatsRepository_FindByID_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockUserDailyChallengeStatsRepository_FindByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockUserDailyChallengeStatsRepository_FindByID_Call) Return(userDailyChallengeStats *models.UserDailyChallengeStats, err error) *MockUserDailyChallengeStatsRepository_FindByID_Call {
	_c.Call.Return(userDailyChallengeStats, err)
	return _c
}

func (_c *MockUserDailyChallengeStatsRepository_FindByID_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (*models.UserDailyChallengeStats, error)) *MockUserDailyChallengeStatsRepository_FindByID_Call {
	_c.Call.Return(run)
	return _c
}

// FindByUserIDAndDivision provides a mock function for the type MockUserDailyChallengeStatsRepository
func (_mock *MockUserDailyChallengeStatsRepository) FindByUserIDAndDivision(ctx context.Context, userID primitive.ObjectID, division models.ChallengeDivision) (*models.UserDailyChallengeStats, error) {
	ret := _mock.Called(ctx, userID, division)

	if len(ret) == 0 {
		panic("no return value specified for FindByUserIDAndDivision")
	}

	var r0 *models.UserDailyChallengeStats
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, models.ChallengeDivision) (*models.UserDailyChallengeStats, error)); ok {
		return returnFunc(ctx, userID, division)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, models.ChallengeDivision) *models.UserDailyChallengeStats); ok {
		r0 = returnFunc(ctx, userID, division)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserDailyChallengeStats)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, models.ChallengeDivision) error); ok {
		r1 = returnFunc(ctx, userID, division)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserDailyChallengeStatsRepository_FindByUserIDAndDivision_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindByUserIDAndDivision'
type MockUserDailyChallengeStatsRepository_FindByUserIDAndDivision_Call struct {
	*mock.Call
}

// FindByUserIDAndDivision is a helper method to define mock.On call
//   - ctx context.Context
//   - userID primitive.ObjectID
//   - division models.ChallengeDivision
func (_e *MockUserDailyChallengeStatsRepository_Expecter) FindByUserIDAndDivision(ctx interface{}, userID interface{}, division interface{}) *MockUserDailyChallengeStatsRepository_FindByUserIDAndDivision_Call {
	return &MockUserDailyChallengeStatsRepository_FindByUserIDAndDivision_Call{Call: _e.mock.On("FindByUserIDAndDivision", ctx, userID, division)}
}

func (_c *MockUserDailyChallengeStatsRepository_FindByUserIDAndDivision_Call) Run(run func(ctx context.Context, userID primitive.ObjectID, division models.ChallengeDivision)) *MockUserDailyChallengeStatsRepository_FindByUserIDAndDivision_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 models.ChallengeDivision
		if args[2] != nil {
			arg2 = args[2].(models.ChallengeDivision)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockUserDailyChallengeStatsRepository_FindByUserIDAndDivision_Call) Return(userDailyChallengeStats *models.UserDailyChallengeStats, err error) *MockUserDailyChallengeStatsRepository_FindByUserIDAndDivision_Call {
	_c.Call.Return(userDailyChallengeStats, err)
	return _c
}

func (_c *MockUserDailyChallengeStatsRepository_FindByUserIDAndDivision_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID, division models.ChallengeDivision) (*models.UserDailyChallengeStats, error)) *MockUserDailyChallengeStatsRepository_FindByUserIDAndDivision_Call {
	_c.Call.Return(run)
	return _c
}

// List provides a mock function for the type MockUserDailyChallengeStatsRepository
func (_mock *MockUserDailyChallengeStatsRepository) List(ctx context.Context, filter bson.M, opts *options.FindOptions) ([]*models.UserDailyChallengeStats, error) {
	ret := _mock.Called(ctx, filter, opts)

	if len(ret) == 0 {
		panic("no return value specified for List")
	}

	var r0 []*models.UserDailyChallengeStats
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, *options.FindOptions) ([]*models.UserDailyChallengeStats, error)); ok {
		return returnFunc(ctx, filter, opts)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, *options.FindOptions) []*models.UserDailyChallengeStats); ok {
		r0 = returnFunc(ctx, filter, opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.UserDailyChallengeStats)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, bson.M, *options.FindOptions) error); ok {
		r1 = returnFunc(ctx, filter, opts)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserDailyChallengeStatsRepository_List_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'List'
type MockUserDailyChallengeStatsRepository_List_Call struct {
	*mock.Call
}

// List is a helper method to define mock.On call
//   - ctx context.Context
//   - filter bson.M
//   - opts *options.FindOptions
func (_e *MockUserDailyChallengeStatsRepository_Expecter) List(ctx interface{}, filter interface{}, opts interface{}) *MockUserDailyChallengeStatsRepository_List_Call {
	return &MockUserDailyChallengeStatsRepository_List_Call{Call: _e.mock.On("List", ctx, filter, opts)}
}

func (_c *MockUserDailyChallengeStatsRepository_List_Call) Run(run func(ctx context.Context, filter bson.M, opts *options.FindOptions)) *MockUserDailyChallengeStatsRepository_List_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 bson.M
		if args[1] != nil {
			arg1 = args[1].(bson.M)
		}
		var arg2 *options.FindOptions
		if args[2] != nil {
			arg2 = args[2].(*options.FindOptions)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockUserDailyChallengeStatsRepository_List_Call) Return(userDailyChallengeStatss []*models.UserDailyChallengeStats, err error) *MockUserDailyChallengeStatsRepository_List_Call {
	_c.Call.Return(userDailyChallengeStatss, err)
	return _c
}

func (_c *MockUserDailyChallengeStatsRepository_List_Call) RunAndReturn(run func(ctx context.Context, filter bson.M, opts *options.FindOptions) ([]*models.UserDailyChallengeStats, error)) *MockUserDailyChallengeStatsRepository_List_Call {
	_c.Call.Return(run)
	return _c
}

// Update provides a mock function for the type MockUserDailyChallengeStatsRepository
func (_mock *MockUserDailyChallengeStatsRepository) Update(ctx context.Context, stats *models.UserDailyChallengeStats) error {
	ret := _mock.Called(ctx, stats)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UserDailyChallengeStats) error); ok {
		r0 = returnFunc(ctx, stats)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockUserDailyChallengeStatsRepository_Update_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Update'
type MockUserDailyChallengeStatsRepository_Update_Call struct {
	*mock.Call
}

// Update is a helper method to define mock.On call
//   - ctx context.Context
//   - stats *models.UserDailyChallengeStats
func (_e *MockUserDailyChallengeStatsRepository_Expecter) Update(ctx interface{}, stats interface{}) *MockUserDailyChallengeStatsRepository_Update_Call {
	return &MockUserDailyChallengeStatsRepository_Update_Call{Call: _e.mock.On("Update", ctx, stats)}
}

func (_c *MockUserDailyChallengeStatsRepository_Update_Call) Run(run func(ctx context.Context, stats *models.UserDailyChallengeStats)) *MockUserDailyChallengeStatsRepository_Update_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.UserDailyChallengeStats
		if args[1] != nil {
			arg1 = args[1].(*models.UserDailyChallengeStats)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockUserDailyChallengeStatsRepository_Update_Call) Return(err error) *MockUserDailyChallengeStatsRepository_Update_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockUserDailyChallengeStatsRepository_Update_Call) RunAndReturn(run func(ctx context.Context, stats *models.UserDailyChallengeStats) error) *MockUserDailyChallengeStatsRepository_Update_Call {
	_c.Call.Return(run)
	return _c
}
