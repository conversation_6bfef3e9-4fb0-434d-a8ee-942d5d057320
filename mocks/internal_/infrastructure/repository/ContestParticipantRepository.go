// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// NewMockContestParticipantRepository creates a new instance of MockContestParticipantRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockContestParticipantRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockContestParticipantRepository {
	mock := &MockContestParticipantRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockContestParticipantRepository is an autogenerated mock type for the ContestParticipantRepository type
type MockContestParticipantRepository struct {
	mock.Mock
}

type MockContestParticipantRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockContestParticipantRepository) EXPECT() *MockContestParticipantRepository_Expecter {
	return &MockContestParticipantRepository_Expecter{mock: &_m.Mock}
}

// AddSubmission provides a mock function for the type MockContestParticipantRepository
func (_mock *MockContestParticipantRepository) AddSubmission(ctx context.Context, participantID primitive.ObjectID, submission models.Submission) error {
	ret := _mock.Called(ctx, participantID, submission)

	if len(ret) == 0 {
		panic("no return value specified for AddSubmission")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, models.Submission) error); ok {
		r0 = returnFunc(ctx, participantID, submission)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockContestParticipantRepository_AddSubmission_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AddSubmission'
type MockContestParticipantRepository_AddSubmission_Call struct {
	*mock.Call
}

// AddSubmission is a helper method to define mock.On call
//   - ctx context.Context
//   - participantID primitive.ObjectID
//   - submission models.Submission
func (_e *MockContestParticipantRepository_Expecter) AddSubmission(ctx interface{}, participantID interface{}, submission interface{}) *MockContestParticipantRepository_AddSubmission_Call {
	return &MockContestParticipantRepository_AddSubmission_Call{Call: _e.mock.On("AddSubmission", ctx, participantID, submission)}
}

func (_c *MockContestParticipantRepository_AddSubmission_Call) Run(run func(ctx context.Context, participantID primitive.ObjectID, submission models.Submission)) *MockContestParticipantRepository_AddSubmission_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 models.Submission
		if args[2] != nil {
			arg2 = args[2].(models.Submission)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockContestParticipantRepository_AddSubmission_Call) Return(err error) *MockContestParticipantRepository_AddSubmission_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockContestParticipantRepository_AddSubmission_Call) RunAndReturn(run func(ctx context.Context, participantID primitive.ObjectID, submission models.Submission) error) *MockContestParticipantRepository_AddSubmission_Call {
	_c.Call.Return(run)
	return _c
}

// Aggregate provides a mock function for the type MockContestParticipantRepository
func (_mock *MockContestParticipantRepository) Aggregate(ctx context.Context, pipeline mongo.Pipeline) ([]*models.ContestParticipant, error) {
	ret := _mock.Called(ctx, pipeline)

	if len(ret) == 0 {
		panic("no return value specified for Aggregate")
	}

	var r0 []*models.ContestParticipant
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, mongo.Pipeline) ([]*models.ContestParticipant, error)); ok {
		return returnFunc(ctx, pipeline)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, mongo.Pipeline) []*models.ContestParticipant); ok {
		r0 = returnFunc(ctx, pipeline)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.ContestParticipant)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, mongo.Pipeline) error); ok {
		r1 = returnFunc(ctx, pipeline)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockContestParticipantRepository_Aggregate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Aggregate'
type MockContestParticipantRepository_Aggregate_Call struct {
	*mock.Call
}

// Aggregate is a helper method to define mock.On call
//   - ctx context.Context
//   - pipeline mongo.Pipeline
func (_e *MockContestParticipantRepository_Expecter) Aggregate(ctx interface{}, pipeline interface{}) *MockContestParticipantRepository_Aggregate_Call {
	return &MockContestParticipantRepository_Aggregate_Call{Call: _e.mock.On("Aggregate", ctx, pipeline)}
}

func (_c *MockContestParticipantRepository_Aggregate_Call) Run(run func(ctx context.Context, pipeline mongo.Pipeline)) *MockContestParticipantRepository_Aggregate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 mongo.Pipeline
		if args[1] != nil {
			arg1 = args[1].(mongo.Pipeline)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockContestParticipantRepository_Aggregate_Call) Return(contestParticipants []*models.ContestParticipant, err error) *MockContestParticipantRepository_Aggregate_Call {
	_c.Call.Return(contestParticipants, err)
	return _c
}

func (_c *MockContestParticipantRepository_Aggregate_Call) RunAndReturn(run func(ctx context.Context, pipeline mongo.Pipeline) ([]*models.ContestParticipant, error)) *MockContestParticipantRepository_Aggregate_Call {
	_c.Call.Return(run)
	return _c
}

// AggregateProjected provides a mock function for the type MockContestParticipantRepository
func (_mock *MockContestParticipantRepository) AggregateProjected(ctx context.Context, pipeline mongo.Pipeline) (*mongo.Cursor, error) {
	ret := _mock.Called(ctx, pipeline)

	if len(ret) == 0 {
		panic("no return value specified for AggregateProjected")
	}

	var r0 *mongo.Cursor
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, mongo.Pipeline) (*mongo.Cursor, error)); ok {
		return returnFunc(ctx, pipeline)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, mongo.Pipeline) *mongo.Cursor); ok {
		r0 = returnFunc(ctx, pipeline)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*mongo.Cursor)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, mongo.Pipeline) error); ok {
		r1 = returnFunc(ctx, pipeline)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockContestParticipantRepository_AggregateProjected_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AggregateProjected'
type MockContestParticipantRepository_AggregateProjected_Call struct {
	*mock.Call
}

// AggregateProjected is a helper method to define mock.On call
//   - ctx context.Context
//   - pipeline mongo.Pipeline
func (_e *MockContestParticipantRepository_Expecter) AggregateProjected(ctx interface{}, pipeline interface{}) *MockContestParticipantRepository_AggregateProjected_Call {
	return &MockContestParticipantRepository_AggregateProjected_Call{Call: _e.mock.On("AggregateProjected", ctx, pipeline)}
}

func (_c *MockContestParticipantRepository_AggregateProjected_Call) Run(run func(ctx context.Context, pipeline mongo.Pipeline)) *MockContestParticipantRepository_AggregateProjected_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 mongo.Pipeline
		if args[1] != nil {
			arg1 = args[1].(mongo.Pipeline)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockContestParticipantRepository_AggregateProjected_Call) Return(cursor *mongo.Cursor, err error) *MockContestParticipantRepository_AggregateProjected_Call {
	_c.Call.Return(cursor, err)
	return _c
}

func (_c *MockContestParticipantRepository_AggregateProjected_Call) RunAndReturn(run func(ctx context.Context, pipeline mongo.Pipeline) (*mongo.Cursor, error)) *MockContestParticipantRepository_AggregateProjected_Call {
	_c.Call.Return(run)
	return _c
}

// Count provides a mock function for the type MockContestParticipantRepository
func (_mock *MockContestParticipantRepository) Count(ctx context.Context, filter bson.M) (int64, error) {
	ret := _mock.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for Count")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M) (int64, error)); ok {
		return returnFunc(ctx, filter)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M) int64); ok {
		r0 = returnFunc(ctx, filter)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, bson.M) error); ok {
		r1 = returnFunc(ctx, filter)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockContestParticipantRepository_Count_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Count'
type MockContestParticipantRepository_Count_Call struct {
	*mock.Call
}

// Count is a helper method to define mock.On call
//   - ctx context.Context
//   - filter bson.M
func (_e *MockContestParticipantRepository_Expecter) Count(ctx interface{}, filter interface{}) *MockContestParticipantRepository_Count_Call {
	return &MockContestParticipantRepository_Count_Call{Call: _e.mock.On("Count", ctx, filter)}
}

func (_c *MockContestParticipantRepository_Count_Call) Run(run func(ctx context.Context, filter bson.M)) *MockContestParticipantRepository_Count_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 bson.M
		if args[1] != nil {
			arg1 = args[1].(bson.M)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockContestParticipantRepository_Count_Call) Return(n int64, err error) *MockContestParticipantRepository_Count_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockContestParticipantRepository_Count_Call) RunAndReturn(run func(ctx context.Context, filter bson.M) (int64, error)) *MockContestParticipantRepository_Count_Call {
	_c.Call.Return(run)
	return _c
}

// Create provides a mock function for the type MockContestParticipantRepository
func (_mock *MockContestParticipantRepository) Create(ctx context.Context, participant *models.ContestParticipant) error {
	ret := _mock.Called(ctx, participant)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.ContestParticipant) error); ok {
		r0 = returnFunc(ctx, participant)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockContestParticipantRepository_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type MockContestParticipantRepository_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx context.Context
//   - participant *models.ContestParticipant
func (_e *MockContestParticipantRepository_Expecter) Create(ctx interface{}, participant interface{}) *MockContestParticipantRepository_Create_Call {
	return &MockContestParticipantRepository_Create_Call{Call: _e.mock.On("Create", ctx, participant)}
}

func (_c *MockContestParticipantRepository_Create_Call) Run(run func(ctx context.Context, participant *models.ContestParticipant)) *MockContestParticipantRepository_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.ContestParticipant
		if args[1] != nil {
			arg1 = args[1].(*models.ContestParticipant)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockContestParticipantRepository_Create_Call) Return(err error) *MockContestParticipantRepository_Create_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockContestParticipantRepository_Create_Call) RunAndReturn(run func(ctx context.Context, participant *models.ContestParticipant) error) *MockContestParticipantRepository_Create_Call {
	_c.Call.Return(run)
	return _c
}

// Delete provides a mock function for the type MockContestParticipantRepository
func (_mock *MockContestParticipantRepository) Delete(ctx context.Context, id primitive.ObjectID) error {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, id)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockContestParticipantRepository_Delete_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Delete'
type MockContestParticipantRepository_Delete_Call struct {
	*mock.Call
}

// Delete is a helper method to define mock.On call
//   - ctx context.Context
//   - id primitive.ObjectID
func (_e *MockContestParticipantRepository_Expecter) Delete(ctx interface{}, id interface{}) *MockContestParticipantRepository_Delete_Call {
	return &MockContestParticipantRepository_Delete_Call{Call: _e.mock.On("Delete", ctx, id)}
}

func (_c *MockContestParticipantRepository_Delete_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockContestParticipantRepository_Delete_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockContestParticipantRepository_Delete_Call) Return(err error) *MockContestParticipantRepository_Delete_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockContestParticipantRepository_Delete_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) error) *MockContestParticipantRepository_Delete_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteByContestAndUser provides a mock function for the type MockContestParticipantRepository
func (_mock *MockContestParticipantRepository) DeleteByContestAndUser(ctx context.Context, contestID primitive.ObjectID, userID primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, contestID, userID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteByContestAndUser")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, contestID, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, contestID, userID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, contestID, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockContestParticipantRepository_DeleteByContestAndUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteByContestAndUser'
type MockContestParticipantRepository_DeleteByContestAndUser_Call struct {
	*mock.Call
}

// DeleteByContestAndUser is a helper method to define mock.On call
//   - ctx context.Context
//   - contestID primitive.ObjectID
//   - userID primitive.ObjectID
func (_e *MockContestParticipantRepository_Expecter) DeleteByContestAndUser(ctx interface{}, contestID interface{}, userID interface{}) *MockContestParticipantRepository_DeleteByContestAndUser_Call {
	return &MockContestParticipantRepository_DeleteByContestAndUser_Call{Call: _e.mock.On("DeleteByContestAndUser", ctx, contestID, userID)}
}

func (_c *MockContestParticipantRepository_DeleteByContestAndUser_Call) Run(run func(ctx context.Context, contestID primitive.ObjectID, userID primitive.ObjectID)) *MockContestParticipantRepository_DeleteByContestAndUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockContestParticipantRepository_DeleteByContestAndUser_Call) Return(b bool, err error) *MockContestParticipantRepository_DeleteByContestAndUser_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockContestParticipantRepository_DeleteByContestAndUser_Call) RunAndReturn(run func(ctx context.Context, contestID primitive.ObjectID, userID primitive.ObjectID) (bool, error)) *MockContestParticipantRepository_DeleteByContestAndUser_Call {
	_c.Call.Return(run)
	return _c
}

// GetByContestAndUser provides a mock function for the type MockContestParticipantRepository
func (_mock *MockContestParticipantRepository) GetByContestAndUser(ctx context.Context, contestID primitive.ObjectID, userID primitive.ObjectID) (*models.ContestParticipant, error) {
	ret := _mock.Called(ctx, contestID, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetByContestAndUser")
	}

	var r0 *models.ContestParticipant
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) (*models.ContestParticipant, error)); ok {
		return returnFunc(ctx, contestID, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) *models.ContestParticipant); ok {
		r0 = returnFunc(ctx, contestID, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ContestParticipant)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, contestID, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockContestParticipantRepository_GetByContestAndUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetByContestAndUser'
type MockContestParticipantRepository_GetByContestAndUser_Call struct {
	*mock.Call
}

// GetByContestAndUser is a helper method to define mock.On call
//   - ctx context.Context
//   - contestID primitive.ObjectID
//   - userID primitive.ObjectID
func (_e *MockContestParticipantRepository_Expecter) GetByContestAndUser(ctx interface{}, contestID interface{}, userID interface{}) *MockContestParticipantRepository_GetByContestAndUser_Call {
	return &MockContestParticipantRepository_GetByContestAndUser_Call{Call: _e.mock.On("GetByContestAndUser", ctx, contestID, userID)}
}

func (_c *MockContestParticipantRepository_GetByContestAndUser_Call) Run(run func(ctx context.Context, contestID primitive.ObjectID, userID primitive.ObjectID)) *MockContestParticipantRepository_GetByContestAndUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockContestParticipantRepository_GetByContestAndUser_Call) Return(contestParticipant *models.ContestParticipant, err error) *MockContestParticipantRepository_GetByContestAndUser_Call {
	_c.Call.Return(contestParticipant, err)
	return _c
}

func (_c *MockContestParticipantRepository_GetByContestAndUser_Call) RunAndReturn(run func(ctx context.Context, contestID primitive.ObjectID, userID primitive.ObjectID) (*models.ContestParticipant, error)) *MockContestParticipantRepository_GetByContestAndUser_Call {
	_c.Call.Return(run)
	return _c
}

// GetByID provides a mock function for the type MockContestParticipantRepository
func (_mock *MockContestParticipantRepository) GetByID(ctx context.Context, id primitive.ObjectID) (*models.ContestParticipant, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetByID")
	}

	var r0 *models.ContestParticipant
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.ContestParticipant, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.ContestParticipant); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ContestParticipant)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockContestParticipantRepository_GetByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetByID'
type MockContestParticipantRepository_GetByID_Call struct {
	*mock.Call
}

// GetByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id primitive.ObjectID
func (_e *MockContestParticipantRepository_Expecter) GetByID(ctx interface{}, id interface{}) *MockContestParticipantRepository_GetByID_Call {
	return &MockContestParticipantRepository_GetByID_Call{Call: _e.mock.On("GetByID", ctx, id)}
}

func (_c *MockContestParticipantRepository_GetByID_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockContestParticipantRepository_GetByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockContestParticipantRepository_GetByID_Call) Return(contestParticipant *models.ContestParticipant, err error) *MockContestParticipantRepository_GetByID_Call {
	_c.Call.Return(contestParticipant, err)
	return _c
}

func (_c *MockContestParticipantRepository_GetByID_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (*models.ContestParticipant, error)) *MockContestParticipantRepository_GetByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetByUserID provides a mock function for the type MockContestParticipantRepository
func (_mock *MockContestParticipantRepository) GetByUserID(ctx context.Context, userID primitive.ObjectID) ([]*models.ContestParticipant, error) {
	ret := _mock.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetByUserID")
	}

	var r0 []*models.ContestParticipant
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) ([]*models.ContestParticipant, error)); ok {
		return returnFunc(ctx, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) []*models.ContestParticipant); ok {
		r0 = returnFunc(ctx, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.ContestParticipant)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockContestParticipantRepository_GetByUserID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetByUserID'
type MockContestParticipantRepository_GetByUserID_Call struct {
	*mock.Call
}

// GetByUserID is a helper method to define mock.On call
//   - ctx context.Context
//   - userID primitive.ObjectID
func (_e *MockContestParticipantRepository_Expecter) GetByUserID(ctx interface{}, userID interface{}) *MockContestParticipantRepository_GetByUserID_Call {
	return &MockContestParticipantRepository_GetByUserID_Call{Call: _e.mock.On("GetByUserID", ctx, userID)}
}

func (_c *MockContestParticipantRepository_GetByUserID_Call) Run(run func(ctx context.Context, userID primitive.ObjectID)) *MockContestParticipantRepository_GetByUserID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockContestParticipantRepository_GetByUserID_Call) Return(contestParticipants []*models.ContestParticipant, err error) *MockContestParticipantRepository_GetByUserID_Call {
	_c.Call.Return(contestParticipants, err)
	return _c
}

func (_c *MockContestParticipantRepository_GetByUserID_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID) ([]*models.ContestParticipant, error)) *MockContestParticipantRepository_GetByUserID_Call {
	_c.Call.Return(run)
	return _c
}

// GetParticipantByUserAndContest provides a mock function for the type MockContestParticipantRepository
func (_mock *MockContestParticipantRepository) GetParticipantByUserAndContest(ctx context.Context, contestId []*models.ObjectID, userId primitive.ObjectID) ([]*models.ContestParticipant, error) {
	ret := _mock.Called(ctx, contestId, userId)

	if len(ret) == 0 {
		panic("no return value specified for GetParticipantByUserAndContest")
	}

	var r0 []*models.ContestParticipant
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, []*models.ObjectID, primitive.ObjectID) ([]*models.ContestParticipant, error)); ok {
		return returnFunc(ctx, contestId, userId)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, []*models.ObjectID, primitive.ObjectID) []*models.ContestParticipant); ok {
		r0 = returnFunc(ctx, contestId, userId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.ContestParticipant)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, []*models.ObjectID, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, contestId, userId)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockContestParticipantRepository_GetParticipantByUserAndContest_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetParticipantByUserAndContest'
type MockContestParticipantRepository_GetParticipantByUserAndContest_Call struct {
	*mock.Call
}

// GetParticipantByUserAndContest is a helper method to define mock.On call
//   - ctx context.Context
//   - contestId []*models.ObjectID
//   - userId primitive.ObjectID
func (_e *MockContestParticipantRepository_Expecter) GetParticipantByUserAndContest(ctx interface{}, contestId interface{}, userId interface{}) *MockContestParticipantRepository_GetParticipantByUserAndContest_Call {
	return &MockContestParticipantRepository_GetParticipantByUserAndContest_Call{Call: _e.mock.On("GetParticipantByUserAndContest", ctx, contestId, userId)}
}

func (_c *MockContestParticipantRepository_GetParticipantByUserAndContest_Call) Run(run func(ctx context.Context, contestId []*models.ObjectID, userId primitive.ObjectID)) *MockContestParticipantRepository_GetParticipantByUserAndContest_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 []*models.ObjectID
		if args[1] != nil {
			arg1 = args[1].([]*models.ObjectID)
		}
		var arg2 primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockContestParticipantRepository_GetParticipantByUserAndContest_Call) Return(contestParticipants []*models.ContestParticipant, err error) *MockContestParticipantRepository_GetParticipantByUserAndContest_Call {
	_c.Call.Return(contestParticipants, err)
	return _c
}

func (_c *MockContestParticipantRepository_GetParticipantByUserAndContest_Call) RunAndReturn(run func(ctx context.Context, contestId []*models.ObjectID, userId primitive.ObjectID) ([]*models.ContestParticipant, error)) *MockContestParticipantRepository_GetParticipantByUserAndContest_Call {
	_c.Call.Return(run)
	return _c
}

// GetTopParticipantsByContest provides a mock function for the type MockContestParticipantRepository
func (_mock *MockContestParticipantRepository) GetTopParticipantsByContest(ctx context.Context, contestID primitive.ObjectID, limit int64) ([]*models.ContestParticipant, error) {
	ret := _mock.Called(ctx, contestID, limit)

	if len(ret) == 0 {
		panic("no return value specified for GetTopParticipantsByContest")
	}

	var r0 []*models.ContestParticipant
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int64) ([]*models.ContestParticipant, error)); ok {
		return returnFunc(ctx, contestID, limit)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int64) []*models.ContestParticipant); ok {
		r0 = returnFunc(ctx, contestID, limit)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.ContestParticipant)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, int64) error); ok {
		r1 = returnFunc(ctx, contestID, limit)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockContestParticipantRepository_GetTopParticipantsByContest_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTopParticipantsByContest'
type MockContestParticipantRepository_GetTopParticipantsByContest_Call struct {
	*mock.Call
}

// GetTopParticipantsByContest is a helper method to define mock.On call
//   - ctx context.Context
//   - contestID primitive.ObjectID
//   - limit int64
func (_e *MockContestParticipantRepository_Expecter) GetTopParticipantsByContest(ctx interface{}, contestID interface{}, limit interface{}) *MockContestParticipantRepository_GetTopParticipantsByContest_Call {
	return &MockContestParticipantRepository_GetTopParticipantsByContest_Call{Call: _e.mock.On("GetTopParticipantsByContest", ctx, contestID, limit)}
}

func (_c *MockContestParticipantRepository_GetTopParticipantsByContest_Call) Run(run func(ctx context.Context, contestID primitive.ObjectID, limit int64)) *MockContestParticipantRepository_GetTopParticipantsByContest_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 int64
		if args[2] != nil {
			arg2 = args[2].(int64)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockContestParticipantRepository_GetTopParticipantsByContest_Call) Return(contestParticipants []*models.ContestParticipant, err error) *MockContestParticipantRepository_GetTopParticipantsByContest_Call {
	_c.Call.Return(contestParticipants, err)
	return _c
}

func (_c *MockContestParticipantRepository_GetTopParticipantsByContest_Call) RunAndReturn(run func(ctx context.Context, contestID primitive.ObjectID, limit int64) ([]*models.ContestParticipant, error)) *MockContestParticipantRepository_GetTopParticipantsByContest_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateOne provides a mock function for the type MockContestParticipantRepository
func (_mock *MockContestParticipantRepository) UpdateOne(ctx context.Context, filter bson.M, update bson.M) error {
	ret := _mock.Called(ctx, filter, update)

	if len(ret) == 0 {
		panic("no return value specified for UpdateOne")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, bson.M) error); ok {
		r0 = returnFunc(ctx, filter, update)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockContestParticipantRepository_UpdateOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateOne'
type MockContestParticipantRepository_UpdateOne_Call struct {
	*mock.Call
}

// UpdateOne is a helper method to define mock.On call
//   - ctx context.Context
//   - filter bson.M
//   - update bson.M
func (_e *MockContestParticipantRepository_Expecter) UpdateOne(ctx interface{}, filter interface{}, update interface{}) *MockContestParticipantRepository_UpdateOne_Call {
	return &MockContestParticipantRepository_UpdateOne_Call{Call: _e.mock.On("UpdateOne", ctx, filter, update)}
}

func (_c *MockContestParticipantRepository_UpdateOne_Call) Run(run func(ctx context.Context, filter bson.M, update bson.M)) *MockContestParticipantRepository_UpdateOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 bson.M
		if args[1] != nil {
			arg1 = args[1].(bson.M)
		}
		var arg2 bson.M
		if args[2] != nil {
			arg2 = args[2].(bson.M)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockContestParticipantRepository_UpdateOne_Call) Return(err error) *MockContestParticipantRepository_UpdateOne_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockContestParticipantRepository_UpdateOne_Call) RunAndReturn(run func(ctx context.Context, filter bson.M, update bson.M) error) *MockContestParticipantRepository_UpdateOne_Call {
	_c.Call.Return(run)
	return _c
}
