// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// NewMockShowdownRepository creates a new instance of MockShowdownRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockShowdownRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockShowdownRepository {
	mock := &MockShowdownRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockShowdownRepository is an autogenerated mock type for the ShowdownRepository type
type MockShowdownRepository struct {
	mock.Mock
}

type MockShowdownRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockShowdownRepository) EXPECT() *MockShowdownRepository_Expecter {
	return &MockShowdownRepository_Expecter{mock: &_m.Mock}
}

// CreateNewShowdown provides a mock function for the type MockShowdownRepository
func (_mock *MockShowdownRepository) CreateNewShowdown(ctx context.Context, sumdayShowDown *models.Showdown) (*models.Showdown, error) {
	ret := _mock.Called(ctx, sumdayShowDown)

	if len(ret) == 0 {
		panic("no return value specified for CreateNewShowdown")
	}

	var r0 *models.Showdown
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.Showdown) (*models.Showdown, error)); ok {
		return returnFunc(ctx, sumdayShowDown)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.Showdown) *models.Showdown); ok {
		r0 = returnFunc(ctx, sumdayShowDown)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Showdown)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.Showdown) error); ok {
		r1 = returnFunc(ctx, sumdayShowDown)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownRepository_CreateNewShowdown_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateNewShowdown'
type MockShowdownRepository_CreateNewShowdown_Call struct {
	*mock.Call
}

// CreateNewShowdown is a helper method to define mock.On call
//   - ctx context.Context
//   - sumdayShowDown *models.Showdown
func (_e *MockShowdownRepository_Expecter) CreateNewShowdown(ctx interface{}, sumdayShowDown interface{}) *MockShowdownRepository_CreateNewShowdown_Call {
	return &MockShowdownRepository_CreateNewShowdown_Call{Call: _e.mock.On("CreateNewShowdown", ctx, sumdayShowDown)}
}

func (_c *MockShowdownRepository_CreateNewShowdown_Call) Run(run func(ctx context.Context, sumdayShowDown *models.Showdown)) *MockShowdownRepository_CreateNewShowdown_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.Showdown
		if args[1] != nil {
			arg1 = args[1].(*models.Showdown)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockShowdownRepository_CreateNewShowdown_Call) Return(showdown *models.Showdown, err error) *MockShowdownRepository_CreateNewShowdown_Call {
	_c.Call.Return(showdown, err)
	return _c
}

func (_c *MockShowdownRepository_CreateNewShowdown_Call) RunAndReturn(run func(ctx context.Context, sumdayShowDown *models.Showdown) (*models.Showdown, error)) *MockShowdownRepository_CreateNewShowdown_Call {
	_c.Call.Return(run)
	return _c
}

// DecrementRegistrationCount provides a mock function for the type MockShowdownRepository
func (_mock *MockShowdownRepository) DecrementRegistrationCount(ctx context.Context, showdownID primitive.ObjectID) (*models.Showdown, error) {
	ret := _mock.Called(ctx, showdownID)

	if len(ret) == 0 {
		panic("no return value specified for DecrementRegistrationCount")
	}

	var r0 *models.Showdown
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.Showdown, error)); ok {
		return returnFunc(ctx, showdownID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.Showdown); ok {
		r0 = returnFunc(ctx, showdownID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Showdown)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, showdownID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownRepository_DecrementRegistrationCount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DecrementRegistrationCount'
type MockShowdownRepository_DecrementRegistrationCount_Call struct {
	*mock.Call
}

// DecrementRegistrationCount is a helper method to define mock.On call
//   - ctx context.Context
//   - showdownID primitive.ObjectID
func (_e *MockShowdownRepository_Expecter) DecrementRegistrationCount(ctx interface{}, showdownID interface{}) *MockShowdownRepository_DecrementRegistrationCount_Call {
	return &MockShowdownRepository_DecrementRegistrationCount_Call{Call: _e.mock.On("DecrementRegistrationCount", ctx, showdownID)}
}

func (_c *MockShowdownRepository_DecrementRegistrationCount_Call) Run(run func(ctx context.Context, showdownID primitive.ObjectID)) *MockShowdownRepository_DecrementRegistrationCount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockShowdownRepository_DecrementRegistrationCount_Call) Return(showdown *models.Showdown, err error) *MockShowdownRepository_DecrementRegistrationCount_Call {
	_c.Call.Return(showdown, err)
	return _c
}

func (_c *MockShowdownRepository_DecrementRegistrationCount_Call) RunAndReturn(run func(ctx context.Context, showdownID primitive.ObjectID) (*models.Showdown, error)) *MockShowdownRepository_DecrementRegistrationCount_Call {
	_c.Call.Return(run)
	return _c
}

// Find provides a mock function for the type MockShowdownRepository
func (_mock *MockShowdownRepository) Find(ctx context.Context, filter bson.M, opts ...*options.FindOptions) ([]*models.Showdown, error) {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, filter, opts)
	} else {
		tmpRet = _mock.Called(ctx, filter)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for Find")
	}

	var r0 []*models.Showdown
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, ...*options.FindOptions) ([]*models.Showdown, error)); ok {
		return returnFunc(ctx, filter, opts...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, ...*options.FindOptions) []*models.Showdown); ok {
		r0 = returnFunc(ctx, filter, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.Showdown)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, bson.M, ...*options.FindOptions) error); ok {
		r1 = returnFunc(ctx, filter, opts...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownRepository_Find_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Find'
type MockShowdownRepository_Find_Call struct {
	*mock.Call
}

// Find is a helper method to define mock.On call
//   - ctx context.Context
//   - filter bson.M
//   - opts ...*options.FindOptions
func (_e *MockShowdownRepository_Expecter) Find(ctx interface{}, filter interface{}, opts ...interface{}) *MockShowdownRepository_Find_Call {
	return &MockShowdownRepository_Find_Call{Call: _e.mock.On("Find",
		append([]interface{}{ctx, filter}, opts...)...)}
}

func (_c *MockShowdownRepository_Find_Call) Run(run func(ctx context.Context, filter bson.M, opts ...*options.FindOptions)) *MockShowdownRepository_Find_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 bson.M
		if args[1] != nil {
			arg1 = args[1].(bson.M)
		}
		var arg2 []*options.FindOptions
		var variadicArgs []*options.FindOptions
		if len(args) > 2 {
			variadicArgs = args[2].([]*options.FindOptions)
		}
		arg2 = variadicArgs
		run(
			arg0,
			arg1,
			arg2...,
		)
	})
	return _c
}

func (_c *MockShowdownRepository_Find_Call) Return(showdowns []*models.Showdown, err error) *MockShowdownRepository_Find_Call {
	_c.Call.Return(showdowns, err)
	return _c
}

func (_c *MockShowdownRepository_Find_Call) RunAndReturn(run func(ctx context.Context, filter bson.M, opts ...*options.FindOptions) ([]*models.Showdown, error)) *MockShowdownRepository_Find_Call {
	_c.Call.Return(run)
	return _c
}

// FindByID provides a mock function for the type MockShowdownRepository
func (_mock *MockShowdownRepository) FindByID(ctx context.Context, id primitive.ObjectID) (*models.Showdown, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for FindByID")
	}

	var r0 *models.Showdown
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.Showdown, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.Showdown); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Showdown)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownRepository_FindByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindByID'
type MockShowdownRepository_FindByID_Call struct {
	*mock.Call
}

// FindByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id primitive.ObjectID
func (_e *MockShowdownRepository_Expecter) FindByID(ctx interface{}, id interface{}) *MockShowdownRepository_FindByID_Call {
	return &MockShowdownRepository_FindByID_Call{Call: _e.mock.On("FindByID", ctx, id)}
}

func (_c *MockShowdownRepository_FindByID_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockShowdownRepository_FindByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockShowdownRepository_FindByID_Call) Return(showdown *models.Showdown, err error) *MockShowdownRepository_FindByID_Call {
	_c.Call.Return(showdown, err)
	return _c
}

func (_c *MockShowdownRepository_FindByID_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (*models.Showdown, error)) *MockShowdownRepository_FindByID_Call {
	_c.Call.Return(run)
	return _c
}

// FindOne provides a mock function for the type MockShowdownRepository
func (_mock *MockShowdownRepository) FindOne(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions) (*models.Showdown, error) {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, filter, opts)
	} else {
		tmpRet = _mock.Called(ctx, filter)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for FindOne")
	}

	var r0 *models.Showdown
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, ...*options.FindOneOptions) (*models.Showdown, error)); ok {
		return returnFunc(ctx, filter, opts...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, ...*options.FindOneOptions) *models.Showdown); ok {
		r0 = returnFunc(ctx, filter, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Showdown)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, bson.M, ...*options.FindOneOptions) error); ok {
		r1 = returnFunc(ctx, filter, opts...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownRepository_FindOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindOne'
type MockShowdownRepository_FindOne_Call struct {
	*mock.Call
}

// FindOne is a helper method to define mock.On call
//   - ctx context.Context
//   - filter bson.M
//   - opts ...*options.FindOneOptions
func (_e *MockShowdownRepository_Expecter) FindOne(ctx interface{}, filter interface{}, opts ...interface{}) *MockShowdownRepository_FindOne_Call {
	return &MockShowdownRepository_FindOne_Call{Call: _e.mock.On("FindOne",
		append([]interface{}{ctx, filter}, opts...)...)}
}

func (_c *MockShowdownRepository_FindOne_Call) Run(run func(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions)) *MockShowdownRepository_FindOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 bson.M
		if args[1] != nil {
			arg1 = args[1].(bson.M)
		}
		var arg2 []*options.FindOneOptions
		var variadicArgs []*options.FindOneOptions
		if len(args) > 2 {
			variadicArgs = args[2].([]*options.FindOneOptions)
		}
		arg2 = variadicArgs
		run(
			arg0,
			arg1,
			arg2...,
		)
	})
	return _c
}

func (_c *MockShowdownRepository_FindOne_Call) Return(showdown *models.Showdown, err error) *MockShowdownRepository_FindOne_Call {
	_c.Call.Return(showdown, err)
	return _c
}

func (_c *MockShowdownRepository_FindOne_Call) RunAndReturn(run func(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions) (*models.Showdown, error)) *MockShowdownRepository_FindOne_Call {
	_c.Call.Return(run)
	return _c
}

// GetLatestShowdownByStartTime provides a mock function for the type MockShowdownRepository
func (_mock *MockShowdownRepository) GetLatestShowdownByStartTime(ctx context.Context, limit int) ([]*models.Showdown, error) {
	ret := _mock.Called(ctx, limit)

	if len(ret) == 0 {
		panic("no return value specified for GetLatestShowdownByStartTime")
	}

	var r0 []*models.Showdown
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, int) ([]*models.Showdown, error)); ok {
		return returnFunc(ctx, limit)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, int) []*models.Showdown); ok {
		r0 = returnFunc(ctx, limit)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.Showdown)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, int) error); ok {
		r1 = returnFunc(ctx, limit)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownRepository_GetLatestShowdownByStartTime_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetLatestShowdownByStartTime'
type MockShowdownRepository_GetLatestShowdownByStartTime_Call struct {
	*mock.Call
}

// GetLatestShowdownByStartTime is a helper method to define mock.On call
//   - ctx context.Context
//   - limit int
func (_e *MockShowdownRepository_Expecter) GetLatestShowdownByStartTime(ctx interface{}, limit interface{}) *MockShowdownRepository_GetLatestShowdownByStartTime_Call {
	return &MockShowdownRepository_GetLatestShowdownByStartTime_Call{Call: _e.mock.On("GetLatestShowdownByStartTime", ctx, limit)}
}

func (_c *MockShowdownRepository_GetLatestShowdownByStartTime_Call) Run(run func(ctx context.Context, limit int)) *MockShowdownRepository_GetLatestShowdownByStartTime_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 int
		if args[1] != nil {
			arg1 = args[1].(int)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockShowdownRepository_GetLatestShowdownByStartTime_Call) Return(showdowns []*models.Showdown, err error) *MockShowdownRepository_GetLatestShowdownByStartTime_Call {
	_c.Call.Return(showdowns, err)
	return _c
}

func (_c *MockShowdownRepository_GetLatestShowdownByStartTime_Call) RunAndReturn(run func(ctx context.Context, limit int) ([]*models.Showdown, error)) *MockShowdownRepository_GetLatestShowdownByStartTime_Call {
	_c.Call.Return(run)
	return _c
}

// GetLatestShowdownByStatus provides a mock function for the type MockShowdownRepository
func (_mock *MockShowdownRepository) GetLatestShowdownByStatus(ctx context.Context, status models.ShowdownContestStatus) (*models.Showdown, error) {
	ret := _mock.Called(ctx, status)

	if len(ret) == 0 {
		panic("no return value specified for GetLatestShowdownByStatus")
	}

	var r0 *models.Showdown
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.ShowdownContestStatus) (*models.Showdown, error)); ok {
		return returnFunc(ctx, status)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.ShowdownContestStatus) *models.Showdown); ok {
		r0 = returnFunc(ctx, status)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Showdown)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.ShowdownContestStatus) error); ok {
		r1 = returnFunc(ctx, status)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownRepository_GetLatestShowdownByStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetLatestShowdownByStatus'
type MockShowdownRepository_GetLatestShowdownByStatus_Call struct {
	*mock.Call
}

// GetLatestShowdownByStatus is a helper method to define mock.On call
//   - ctx context.Context
//   - status models.ShowdownContestStatus
func (_e *MockShowdownRepository_Expecter) GetLatestShowdownByStatus(ctx interface{}, status interface{}) *MockShowdownRepository_GetLatestShowdownByStatus_Call {
	return &MockShowdownRepository_GetLatestShowdownByStatus_Call{Call: _e.mock.On("GetLatestShowdownByStatus", ctx, status)}
}

func (_c *MockShowdownRepository_GetLatestShowdownByStatus_Call) Run(run func(ctx context.Context, status models.ShowdownContestStatus)) *MockShowdownRepository_GetLatestShowdownByStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 models.ShowdownContestStatus
		if args[1] != nil {
			arg1 = args[1].(models.ShowdownContestStatus)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockShowdownRepository_GetLatestShowdownByStatus_Call) Return(showdown *models.Showdown, err error) *MockShowdownRepository_GetLatestShowdownByStatus_Call {
	_c.Call.Return(showdown, err)
	return _c
}

func (_c *MockShowdownRepository_GetLatestShowdownByStatus_Call) RunAndReturn(run func(ctx context.Context, status models.ShowdownContestStatus) (*models.Showdown, error)) *MockShowdownRepository_GetLatestShowdownByStatus_Call {
	_c.Call.Return(run)
	return _c
}

// GetLiveShowdown provides a mock function for the type MockShowdownRepository
func (_mock *MockShowdownRepository) GetLiveShowdown(ctx context.Context) (*models.Showdown, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetLiveShowdown")
	}

	var r0 *models.Showdown
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*models.Showdown, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *models.Showdown); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Showdown)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownRepository_GetLiveShowdown_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetLiveShowdown'
type MockShowdownRepository_GetLiveShowdown_Call struct {
	*mock.Call
}

// GetLiveShowdown is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockShowdownRepository_Expecter) GetLiveShowdown(ctx interface{}) *MockShowdownRepository_GetLiveShowdown_Call {
	return &MockShowdownRepository_GetLiveShowdown_Call{Call: _e.mock.On("GetLiveShowdown", ctx)}
}

func (_c *MockShowdownRepository_GetLiveShowdown_Call) Run(run func(ctx context.Context)) *MockShowdownRepository_GetLiveShowdown_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockShowdownRepository_GetLiveShowdown_Call) Return(showdown *models.Showdown, err error) *MockShowdownRepository_GetLiveShowdown_Call {
	_c.Call.Return(showdown, err)
	return _c
}

func (_c *MockShowdownRepository_GetLiveShowdown_Call) RunAndReturn(run func(ctx context.Context) (*models.Showdown, error)) *MockShowdownRepository_GetLiveShowdown_Call {
	_c.Call.Return(run)
	return _c
}

// GetShowdownsByStatus provides a mock function for the type MockShowdownRepository
func (_mock *MockShowdownRepository) GetShowdownsByStatus(ctx context.Context, statuses []models.ShowdownContestStatus, page *int, pageSize *int, sortDirection *string) (*models.PaginatedShowdowns, error) {
	ret := _mock.Called(ctx, statuses, page, pageSize, sortDirection)

	if len(ret) == 0 {
		panic("no return value specified for GetShowdownsByStatus")
	}

	var r0 *models.PaginatedShowdowns
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, []models.ShowdownContestStatus, *int, *int, *string) (*models.PaginatedShowdowns, error)); ok {
		return returnFunc(ctx, statuses, page, pageSize, sortDirection)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, []models.ShowdownContestStatus, *int, *int, *string) *models.PaginatedShowdowns); ok {
		r0 = returnFunc(ctx, statuses, page, pageSize, sortDirection)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PaginatedShowdowns)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, []models.ShowdownContestStatus, *int, *int, *string) error); ok {
		r1 = returnFunc(ctx, statuses, page, pageSize, sortDirection)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownRepository_GetShowdownsByStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetShowdownsByStatus'
type MockShowdownRepository_GetShowdownsByStatus_Call struct {
	*mock.Call
}

// GetShowdownsByStatus is a helper method to define mock.On call
//   - ctx context.Context
//   - statuses []models.ShowdownContestStatus
//   - page *int
//   - pageSize *int
//   - sortDirection *string
func (_e *MockShowdownRepository_Expecter) GetShowdownsByStatus(ctx interface{}, statuses interface{}, page interface{}, pageSize interface{}, sortDirection interface{}) *MockShowdownRepository_GetShowdownsByStatus_Call {
	return &MockShowdownRepository_GetShowdownsByStatus_Call{Call: _e.mock.On("GetShowdownsByStatus", ctx, statuses, page, pageSize, sortDirection)}
}

func (_c *MockShowdownRepository_GetShowdownsByStatus_Call) Run(run func(ctx context.Context, statuses []models.ShowdownContestStatus, page *int, pageSize *int, sortDirection *string)) *MockShowdownRepository_GetShowdownsByStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 []models.ShowdownContestStatus
		if args[1] != nil {
			arg1 = args[1].([]models.ShowdownContestStatus)
		}
		var arg2 *int
		if args[2] != nil {
			arg2 = args[2].(*int)
		}
		var arg3 *int
		if args[3] != nil {
			arg3 = args[3].(*int)
		}
		var arg4 *string
		if args[4] != nil {
			arg4 = args[4].(*string)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
			arg4,
		)
	})
	return _c
}

func (_c *MockShowdownRepository_GetShowdownsByStatus_Call) Return(paginatedShowdowns *models.PaginatedShowdowns, err error) *MockShowdownRepository_GetShowdownsByStatus_Call {
	_c.Call.Return(paginatedShowdowns, err)
	return _c
}

func (_c *MockShowdownRepository_GetShowdownsByStatus_Call) RunAndReturn(run func(ctx context.Context, statuses []models.ShowdownContestStatus, page *int, pageSize *int, sortDirection *string) (*models.PaginatedShowdowns, error)) *MockShowdownRepository_GetShowdownsByStatus_Call {
	_c.Call.Return(run)
	return _c
}

// IncrementRegistrationCount provides a mock function for the type MockShowdownRepository
func (_mock *MockShowdownRepository) IncrementRegistrationCount(ctx context.Context, showdownID primitive.ObjectID) (*models.Showdown, error) {
	ret := _mock.Called(ctx, showdownID)

	if len(ret) == 0 {
		panic("no return value specified for IncrementRegistrationCount")
	}

	var r0 *models.Showdown
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.Showdown, error)); ok {
		return returnFunc(ctx, showdownID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.Showdown); ok {
		r0 = returnFunc(ctx, showdownID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Showdown)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, showdownID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockShowdownRepository_IncrementRegistrationCount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IncrementRegistrationCount'
type MockShowdownRepository_IncrementRegistrationCount_Call struct {
	*mock.Call
}

// IncrementRegistrationCount is a helper method to define mock.On call
//   - ctx context.Context
//   - showdownID primitive.ObjectID
func (_e *MockShowdownRepository_Expecter) IncrementRegistrationCount(ctx interface{}, showdownID interface{}) *MockShowdownRepository_IncrementRegistrationCount_Call {
	return &MockShowdownRepository_IncrementRegistrationCount_Call{Call: _e.mock.On("IncrementRegistrationCount", ctx, showdownID)}
}

func (_c *MockShowdownRepository_IncrementRegistrationCount_Call) Run(run func(ctx context.Context, showdownID primitive.ObjectID)) *MockShowdownRepository_IncrementRegistrationCount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockShowdownRepository_IncrementRegistrationCount_Call) Return(showdown *models.Showdown, err error) *MockShowdownRepository_IncrementRegistrationCount_Call {
	_c.Call.Return(showdown, err)
	return _c
}

func (_c *MockShowdownRepository_IncrementRegistrationCount_Call) RunAndReturn(run func(ctx context.Context, showdownID primitive.ObjectID) (*models.Showdown, error)) *MockShowdownRepository_IncrementRegistrationCount_Call {
	_c.Call.Return(run)
	return _c
}

// Update provides a mock function for the type MockShowdownRepository
func (_mock *MockShowdownRepository) Update(ctx context.Context, showdown *models.Showdown) error {
	ret := _mock.Called(ctx, showdown)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.Showdown) error); ok {
		r0 = returnFunc(ctx, showdown)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockShowdownRepository_Update_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Update'
type MockShowdownRepository_Update_Call struct {
	*mock.Call
}

// Update is a helper method to define mock.On call
//   - ctx context.Context
//   - showdown *models.Showdown
func (_e *MockShowdownRepository_Expecter) Update(ctx interface{}, showdown interface{}) *MockShowdownRepository_Update_Call {
	return &MockShowdownRepository_Update_Call{Call: _e.mock.On("Update", ctx, showdown)}
}

func (_c *MockShowdownRepository_Update_Call) Run(run func(ctx context.Context, showdown *models.Showdown)) *MockShowdownRepository_Update_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.Showdown
		if args[1] != nil {
			arg1 = args[1].(*models.Showdown)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockShowdownRepository_Update_Call) Return(err error) *MockShowdownRepository_Update_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockShowdownRepository_Update_Call) RunAndReturn(run func(ctx context.Context, showdown *models.Showdown) error) *MockShowdownRepository_Update_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateOne provides a mock function for the type MockShowdownRepository
func (_mock *MockShowdownRepository) UpdateOne(ctx context.Context, filter bson.M, update bson.M) error {
	ret := _mock.Called(ctx, filter, update)

	if len(ret) == 0 {
		panic("no return value specified for UpdateOne")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, bson.M) error); ok {
		r0 = returnFunc(ctx, filter, update)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockShowdownRepository_UpdateOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateOne'
type MockShowdownRepository_UpdateOne_Call struct {
	*mock.Call
}

// UpdateOne is a helper method to define mock.On call
//   - ctx context.Context
//   - filter bson.M
//   - update bson.M
func (_e *MockShowdownRepository_Expecter) UpdateOne(ctx interface{}, filter interface{}, update interface{}) *MockShowdownRepository_UpdateOne_Call {
	return &MockShowdownRepository_UpdateOne_Call{Call: _e.mock.On("UpdateOne", ctx, filter, update)}
}

func (_c *MockShowdownRepository_UpdateOne_Call) Run(run func(ctx context.Context, filter bson.M, update bson.M)) *MockShowdownRepository_UpdateOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 bson.M
		if args[1] != nil {
			arg1 = args[1].(bson.M)
		}
		var arg2 bson.M
		if args[2] != nil {
			arg2 = args[2].(bson.M)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockShowdownRepository_UpdateOne_Call) Return(err error) *MockShowdownRepository_UpdateOne_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockShowdownRepository_UpdateOne_Call) RunAndReturn(run func(ctx context.Context, filter bson.M, update bson.M) error) *MockShowdownRepository_UpdateOne_Call {
	_c.Call.Return(run)
	return _c
}
