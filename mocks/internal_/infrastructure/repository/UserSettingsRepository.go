// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// NewMockUserSettingsRepository creates a new instance of MockUserSettingsRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockUserSettingsRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockUserSettingsRepository {
	mock := &MockUserSettingsRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockUserSettingsRepository is an autogenerated mock type for the UserSettingsRepository type
type MockUserSettingsRepository struct {
	mock.Mock
}

type MockUserSettingsRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockUserSettingsRepository) EXPECT() *MockUserSettingsRepository_Expecter {
	return &MockUserSettingsRepository_Expecter{mock: &_m.Mock}
}

// CreateUserSettings provides a mock function for the type MockUserSettingsRepository
func (_mock *MockUserSettingsRepository) CreateUserSettings(ctx context.Context, settings models.CreateSettingsInput) error {
	ret := _mock.Called(ctx, settings)

	if len(ret) == 0 {
		panic("no return value specified for CreateUserSettings")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CreateSettingsInput) error); ok {
		r0 = returnFunc(ctx, settings)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockUserSettingsRepository_CreateUserSettings_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateUserSettings'
type MockUserSettingsRepository_CreateUserSettings_Call struct {
	*mock.Call
}

// CreateUserSettings is a helper method to define mock.On call
//   - ctx context.Context
//   - settings models.CreateSettingsInput
func (_e *MockUserSettingsRepository_Expecter) CreateUserSettings(ctx interface{}, settings interface{}) *MockUserSettingsRepository_CreateUserSettings_Call {
	return &MockUserSettingsRepository_CreateUserSettings_Call{Call: _e.mock.On("CreateUserSettings", ctx, settings)}
}

func (_c *MockUserSettingsRepository_CreateUserSettings_Call) Run(run func(ctx context.Context, settings models.CreateSettingsInput)) *MockUserSettingsRepository_CreateUserSettings_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 models.CreateSettingsInput
		if args[1] != nil {
			arg1 = args[1].(models.CreateSettingsInput)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockUserSettingsRepository_CreateUserSettings_Call) Return(err error) *MockUserSettingsRepository_CreateUserSettings_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockUserSettingsRepository_CreateUserSettings_Call) RunAndReturn(run func(ctx context.Context, settings models.CreateSettingsInput) error) *MockUserSettingsRepository_CreateUserSettings_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserSettings provides a mock function for the type MockUserSettingsRepository
func (_mock *MockUserSettingsRepository) GetUserSettings(ctx context.Context, userID primitive.ObjectID) (*models.UserSettings, error) {
	ret := _mock.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetUserSettings")
	}

	var r0 *models.UserSettings
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.UserSettings, error)); ok {
		return returnFunc(ctx, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.UserSettings); ok {
		r0 = returnFunc(ctx, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserSettings)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserSettingsRepository_GetUserSettings_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserSettings'
type MockUserSettingsRepository_GetUserSettings_Call struct {
	*mock.Call
}

// GetUserSettings is a helper method to define mock.On call
//   - ctx context.Context
//   - userID primitive.ObjectID
func (_e *MockUserSettingsRepository_Expecter) GetUserSettings(ctx interface{}, userID interface{}) *MockUserSettingsRepository_GetUserSettings_Call {
	return &MockUserSettingsRepository_GetUserSettings_Call{Call: _e.mock.On("GetUserSettings", ctx, userID)}
}

func (_c *MockUserSettingsRepository_GetUserSettings_Call) Run(run func(ctx context.Context, userID primitive.ObjectID)) *MockUserSettingsRepository_GetUserSettings_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockUserSettingsRepository_GetUserSettings_Call) Return(userSettings *models.UserSettings, err error) *MockUserSettingsRepository_GetUserSettings_Call {
	_c.Call.Return(userSettings, err)
	return _c
}

func (_c *MockUserSettingsRepository_GetUserSettings_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID) (*models.UserSettings, error)) *MockUserSettingsRepository_GetUserSettings_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateOne provides a mock function for the type MockUserSettingsRepository
func (_mock *MockUserSettingsRepository) UpdateOne(ctx context.Context, filter bson.M, update bson.M, opts ...*options.UpdateOptions) error {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, filter, update, opts)
	} else {
		tmpRet = _mock.Called(ctx, filter, update)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for UpdateOne")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, bson.M, ...*options.UpdateOptions) error); ok {
		r0 = returnFunc(ctx, filter, update, opts...)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockUserSettingsRepository_UpdateOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateOne'
type MockUserSettingsRepository_UpdateOne_Call struct {
	*mock.Call
}

// UpdateOne is a helper method to define mock.On call
//   - ctx context.Context
//   - filter bson.M
//   - update bson.M
//   - opts ...*options.UpdateOptions
func (_e *MockUserSettingsRepository_Expecter) UpdateOne(ctx interface{}, filter interface{}, update interface{}, opts ...interface{}) *MockUserSettingsRepository_UpdateOne_Call {
	return &MockUserSettingsRepository_UpdateOne_Call{Call: _e.mock.On("UpdateOne",
		append([]interface{}{ctx, filter, update}, opts...)...)}
}

func (_c *MockUserSettingsRepository_UpdateOne_Call) Run(run func(ctx context.Context, filter bson.M, update bson.M, opts ...*options.UpdateOptions)) *MockUserSettingsRepository_UpdateOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 bson.M
		if args[1] != nil {
			arg1 = args[1].(bson.M)
		}
		var arg2 bson.M
		if args[2] != nil {
			arg2 = args[2].(bson.M)
		}
		var arg3 []*options.UpdateOptions
		var variadicArgs []*options.UpdateOptions
		if len(args) > 3 {
			variadicArgs = args[3].([]*options.UpdateOptions)
		}
		arg3 = variadicArgs
		run(
			arg0,
			arg1,
			arg2,
			arg3...,
		)
	})
	return _c
}

func (_c *MockUserSettingsRepository_UpdateOne_Call) Return(err error) *MockUserSettingsRepository_UpdateOne_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockUserSettingsRepository_UpdateOne_Call) RunAndReturn(run func(ctx context.Context, filter bson.M, update bson.M, opts ...*options.UpdateOptions) error) *MockUserSettingsRepository_UpdateOne_Call {
	_c.Call.Return(run)
	return _c
}
