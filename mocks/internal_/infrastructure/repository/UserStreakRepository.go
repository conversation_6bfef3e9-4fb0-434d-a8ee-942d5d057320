// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockUserStreakRepository creates a new instance of MockUserStreakRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockUserStreakRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockUserStreakRepository {
	mock := &MockUserStreakRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockUserStreakRepository is an autogenerated mock type for the UserStreakRepository type
type MockUserStreakRepository struct {
	mock.Mock
}

type MockUserStreakRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockUserStreakRepository) EXPECT() *MockUserStreakRepository_Expecter {
	return &MockUserStreakRepository_Expecter{mock: &_m.Mock}
}

// GetStreakHistoryByUserID provides a mock function for the type MockUserStreakRepository
func (_mock *MockUserStreakRepository) GetStreakHistoryByUserID(ctx context.Context, userID primitive.ObjectID) (*models.StreakHistory, error) {
	ret := _mock.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetStreakHistoryByUserID")
	}

	var r0 *models.StreakHistory
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.StreakHistory, error)); ok {
		return returnFunc(ctx, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.StreakHistory); ok {
		r0 = returnFunc(ctx, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.StreakHistory)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserStreakRepository_GetStreakHistoryByUserID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetStreakHistoryByUserID'
type MockUserStreakRepository_GetStreakHistoryByUserID_Call struct {
	*mock.Call
}

// GetStreakHistoryByUserID is a helper method to define mock.On call
//   - ctx context.Context
//   - userID primitive.ObjectID
func (_e *MockUserStreakRepository_Expecter) GetStreakHistoryByUserID(ctx interface{}, userID interface{}) *MockUserStreakRepository_GetStreakHistoryByUserID_Call {
	return &MockUserStreakRepository_GetStreakHistoryByUserID_Call{Call: _e.mock.On("GetStreakHistoryByUserID", ctx, userID)}
}

func (_c *MockUserStreakRepository_GetStreakHistoryByUserID_Call) Run(run func(ctx context.Context, userID primitive.ObjectID)) *MockUserStreakRepository_GetStreakHistoryByUserID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockUserStreakRepository_GetStreakHistoryByUserID_Call) Return(streakHistory *models.StreakHistory, err error) *MockUserStreakRepository_GetStreakHistoryByUserID_Call {
	_c.Call.Return(streakHistory, err)
	return _c
}

func (_c *MockUserStreakRepository_GetStreakHistoryByUserID_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID) (*models.StreakHistory, error)) *MockUserStreakRepository_GetStreakHistoryByUserID_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateOne provides a mock function for the type MockUserStreakRepository
func (_mock *MockUserStreakRepository) UpdateOne(ctx context.Context, filter bson.M, update bson.M) error {
	ret := _mock.Called(ctx, filter, update)

	if len(ret) == 0 {
		panic("no return value specified for UpdateOne")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, bson.M) error); ok {
		r0 = returnFunc(ctx, filter, update)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockUserStreakRepository_UpdateOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateOne'
type MockUserStreakRepository_UpdateOne_Call struct {
	*mock.Call
}

// UpdateOne is a helper method to define mock.On call
//   - ctx context.Context
//   - filter bson.M
//   - update bson.M
func (_e *MockUserStreakRepository_Expecter) UpdateOne(ctx interface{}, filter interface{}, update interface{}) *MockUserStreakRepository_UpdateOne_Call {
	return &MockUserStreakRepository_UpdateOne_Call{Call: _e.mock.On("UpdateOne", ctx, filter, update)}
}

func (_c *MockUserStreakRepository_UpdateOne_Call) Run(run func(ctx context.Context, filter bson.M, update bson.M)) *MockUserStreakRepository_UpdateOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 bson.M
		if args[1] != nil {
			arg1 = args[1].(bson.M)
		}
		var arg2 bson.M
		if args[2] != nil {
			arg2 = args[2].(bson.M)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockUserStreakRepository_UpdateOne_Call) Return(err error) *MockUserStreakRepository_UpdateOne_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockUserStreakRepository_UpdateOne_Call) RunAndReturn(run func(ctx context.Context, filter bson.M, update bson.M) error) *MockUserStreakRepository_UpdateOne_Call {
	_c.Call.Return(run)
	return _c
}

// UpsertStreakHistoryEntries provides a mock function for the type MockUserStreakRepository
func (_mock *MockUserStreakRepository) UpsertStreakHistoryEntries(ctx context.Context, userId primitive.ObjectID, entries []*models.StreakEntry) error {
	ret := _mock.Called(ctx, userId, entries)

	if len(ret) == 0 {
		panic("no return value specified for UpsertStreakHistoryEntries")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, []*models.StreakEntry) error); ok {
		r0 = returnFunc(ctx, userId, entries)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockUserStreakRepository_UpsertStreakHistoryEntries_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpsertStreakHistoryEntries'
type MockUserStreakRepository_UpsertStreakHistoryEntries_Call struct {
	*mock.Call
}

// UpsertStreakHistoryEntries is a helper method to define mock.On call
//   - ctx context.Context
//   - userId primitive.ObjectID
//   - entries []*models.StreakEntry
func (_e *MockUserStreakRepository_Expecter) UpsertStreakHistoryEntries(ctx interface{}, userId interface{}, entries interface{}) *MockUserStreakRepository_UpsertStreakHistoryEntries_Call {
	return &MockUserStreakRepository_UpsertStreakHistoryEntries_Call{Call: _e.mock.On("UpsertStreakHistoryEntries", ctx, userId, entries)}
}

func (_c *MockUserStreakRepository_UpsertStreakHistoryEntries_Call) Run(run func(ctx context.Context, userId primitive.ObjectID, entries []*models.StreakEntry)) *MockUserStreakRepository_UpsertStreakHistoryEntries_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 []*models.StreakEntry
		if args[2] != nil {
			arg2 = args[2].([]*models.StreakEntry)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockUserStreakRepository_UpsertStreakHistoryEntries_Call) Return(err error) *MockUserStreakRepository_UpsertStreakHistoryEntries_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockUserStreakRepository_UpsertStreakHistoryEntries_Call) RunAndReturn(run func(ctx context.Context, userId primitive.ObjectID, entries []*models.StreakEntry) error) *MockUserStreakRepository_UpsertStreakHistoryEntries_Call {
	_c.Call.Return(run)
	return _c
}
