// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// NewMockFriendsAndFollowersRepository creates a new instance of MockFriendsAndFollowersRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockFriendsAndFollowersRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockFriendsAndFollowersRepository {
	mock := &MockFriendsAndFollowersRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockFriendsAndFollowersRepository is an autogenerated mock type for the FriendsAndFollowersRepository type
type MockFriendsAndFollowersRepository struct {
	mock.Mock
}

type MockFriendsAndFollowersRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockFriendsAndFollowersRepository) EXPECT() *MockFriendsAndFollowersRepository_Expecter {
	return &MockFriendsAndFollowersRepository_Expecter{mock: &_m.Mock}
}

// AddFollower provides a mock function for the type MockFriendsAndFollowersRepository
func (_mock *MockFriendsAndFollowersRepository) AddFollower(ctx context.Context, followRequest models.FollowersAndFollowee) (bool, error) {
	ret := _mock.Called(ctx, followRequest)

	if len(ret) == 0 {
		panic("no return value specified for AddFollower")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.FollowersAndFollowee) (bool, error)); ok {
		return returnFunc(ctx, followRequest)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.FollowersAndFollowee) bool); ok {
		r0 = returnFunc(ctx, followRequest)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.FollowersAndFollowee) error); ok {
		r1 = returnFunc(ctx, followRequest)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockFriendsAndFollowersRepository_AddFollower_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AddFollower'
type MockFriendsAndFollowersRepository_AddFollower_Call struct {
	*mock.Call
}

// AddFollower is a helper method to define mock.On call
//   - ctx context.Context
//   - followRequest models.FollowersAndFollowee
func (_e *MockFriendsAndFollowersRepository_Expecter) AddFollower(ctx interface{}, followRequest interface{}) *MockFriendsAndFollowersRepository_AddFollower_Call {
	return &MockFriendsAndFollowersRepository_AddFollower_Call{Call: _e.mock.On("AddFollower", ctx, followRequest)}
}

func (_c *MockFriendsAndFollowersRepository_AddFollower_Call) Run(run func(ctx context.Context, followRequest models.FollowersAndFollowee)) *MockFriendsAndFollowersRepository_AddFollower_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 models.FollowersAndFollowee
		if args[1] != nil {
			arg1 = args[1].(models.FollowersAndFollowee)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockFriendsAndFollowersRepository_AddFollower_Call) Return(b bool, err error) *MockFriendsAndFollowersRepository_AddFollower_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockFriendsAndFollowersRepository_AddFollower_Call) RunAndReturn(run func(ctx context.Context, followRequest models.FollowersAndFollowee) (bool, error)) *MockFriendsAndFollowersRepository_AddFollower_Call {
	_c.Call.Return(run)
	return _c
}

// AggregateFollowers provides a mock function for the type MockFriendsAndFollowersRepository
func (_mock *MockFriendsAndFollowersRepository) AggregateFollowers(ctx context.Context, pipeline mongo.Pipeline) (*mongo.Cursor, error) {
	ret := _mock.Called(ctx, pipeline)

	if len(ret) == 0 {
		panic("no return value specified for AggregateFollowers")
	}

	var r0 *mongo.Cursor
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, mongo.Pipeline) (*mongo.Cursor, error)); ok {
		return returnFunc(ctx, pipeline)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, mongo.Pipeline) *mongo.Cursor); ok {
		r0 = returnFunc(ctx, pipeline)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*mongo.Cursor)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, mongo.Pipeline) error); ok {
		r1 = returnFunc(ctx, pipeline)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockFriendsAndFollowersRepository_AggregateFollowers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AggregateFollowers'
type MockFriendsAndFollowersRepository_AggregateFollowers_Call struct {
	*mock.Call
}

// AggregateFollowers is a helper method to define mock.On call
//   - ctx context.Context
//   - pipeline mongo.Pipeline
func (_e *MockFriendsAndFollowersRepository_Expecter) AggregateFollowers(ctx interface{}, pipeline interface{}) *MockFriendsAndFollowersRepository_AggregateFollowers_Call {
	return &MockFriendsAndFollowersRepository_AggregateFollowers_Call{Call: _e.mock.On("AggregateFollowers", ctx, pipeline)}
}

func (_c *MockFriendsAndFollowersRepository_AggregateFollowers_Call) Run(run func(ctx context.Context, pipeline mongo.Pipeline)) *MockFriendsAndFollowersRepository_AggregateFollowers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 mongo.Pipeline
		if args[1] != nil {
			arg1 = args[1].(mongo.Pipeline)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockFriendsAndFollowersRepository_AggregateFollowers_Call) Return(cursor *mongo.Cursor, err error) *MockFriendsAndFollowersRepository_AggregateFollowers_Call {
	_c.Call.Return(cursor, err)
	return _c
}

func (_c *MockFriendsAndFollowersRepository_AggregateFollowers_Call) RunAndReturn(run func(ctx context.Context, pipeline mongo.Pipeline) (*mongo.Cursor, error)) *MockFriendsAndFollowersRepository_AggregateFollowers_Call {
	_c.Call.Return(run)
	return _c
}

// AggregateFriendRequests provides a mock function for the type MockFriendsAndFollowersRepository
func (_mock *MockFriendsAndFollowersRepository) AggregateFriendRequests(ctx context.Context, pipeline mongo.Pipeline) (*mongo.Cursor, error) {
	ret := _mock.Called(ctx, pipeline)

	if len(ret) == 0 {
		panic("no return value specified for AggregateFriendRequests")
	}

	var r0 *mongo.Cursor
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, mongo.Pipeline) (*mongo.Cursor, error)); ok {
		return returnFunc(ctx, pipeline)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, mongo.Pipeline) *mongo.Cursor); ok {
		r0 = returnFunc(ctx, pipeline)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*mongo.Cursor)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, mongo.Pipeline) error); ok {
		r1 = returnFunc(ctx, pipeline)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockFriendsAndFollowersRepository_AggregateFriendRequests_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AggregateFriendRequests'
type MockFriendsAndFollowersRepository_AggregateFriendRequests_Call struct {
	*mock.Call
}

// AggregateFriendRequests is a helper method to define mock.On call
//   - ctx context.Context
//   - pipeline mongo.Pipeline
func (_e *MockFriendsAndFollowersRepository_Expecter) AggregateFriendRequests(ctx interface{}, pipeline interface{}) *MockFriendsAndFollowersRepository_AggregateFriendRequests_Call {
	return &MockFriendsAndFollowersRepository_AggregateFriendRequests_Call{Call: _e.mock.On("AggregateFriendRequests", ctx, pipeline)}
}

func (_c *MockFriendsAndFollowersRepository_AggregateFriendRequests_Call) Run(run func(ctx context.Context, pipeline mongo.Pipeline)) *MockFriendsAndFollowersRepository_AggregateFriendRequests_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 mongo.Pipeline
		if args[1] != nil {
			arg1 = args[1].(mongo.Pipeline)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockFriendsAndFollowersRepository_AggregateFriendRequests_Call) Return(cursor *mongo.Cursor, err error) *MockFriendsAndFollowersRepository_AggregateFriendRequests_Call {
	_c.Call.Return(cursor, err)
	return _c
}

func (_c *MockFriendsAndFollowersRepository_AggregateFriendRequests_Call) RunAndReturn(run func(ctx context.Context, pipeline mongo.Pipeline) (*mongo.Cursor, error)) *MockFriendsAndFollowersRepository_AggregateFriendRequests_Call {
	_c.Call.Return(run)
	return _c
}

// AggregateFriends provides a mock function for the type MockFriendsAndFollowersRepository
func (_mock *MockFriendsAndFollowersRepository) AggregateFriends(ctx context.Context, pipeline mongo.Pipeline) (*mongo.Cursor, error) {
	ret := _mock.Called(ctx, pipeline)

	if len(ret) == 0 {
		panic("no return value specified for AggregateFriends")
	}

	var r0 *mongo.Cursor
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, mongo.Pipeline) (*mongo.Cursor, error)); ok {
		return returnFunc(ctx, pipeline)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, mongo.Pipeline) *mongo.Cursor); ok {
		r0 = returnFunc(ctx, pipeline)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*mongo.Cursor)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, mongo.Pipeline) error); ok {
		r1 = returnFunc(ctx, pipeline)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockFriendsAndFollowersRepository_AggregateFriends_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AggregateFriends'
type MockFriendsAndFollowersRepository_AggregateFriends_Call struct {
	*mock.Call
}

// AggregateFriends is a helper method to define mock.On call
//   - ctx context.Context
//   - pipeline mongo.Pipeline
func (_e *MockFriendsAndFollowersRepository_Expecter) AggregateFriends(ctx interface{}, pipeline interface{}) *MockFriendsAndFollowersRepository_AggregateFriends_Call {
	return &MockFriendsAndFollowersRepository_AggregateFriends_Call{Call: _e.mock.On("AggregateFriends", ctx, pipeline)}
}

func (_c *MockFriendsAndFollowersRepository_AggregateFriends_Call) Run(run func(ctx context.Context, pipeline mongo.Pipeline)) *MockFriendsAndFollowersRepository_AggregateFriends_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 mongo.Pipeline
		if args[1] != nil {
			arg1 = args[1].(mongo.Pipeline)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockFriendsAndFollowersRepository_AggregateFriends_Call) Return(cursor *mongo.Cursor, err error) *MockFriendsAndFollowersRepository_AggregateFriends_Call {
	_c.Call.Return(cursor, err)
	return _c
}

func (_c *MockFriendsAndFollowersRepository_AggregateFriends_Call) RunAndReturn(run func(ctx context.Context, pipeline mongo.Pipeline) (*mongo.Cursor, error)) *MockFriendsAndFollowersRepository_AggregateFriends_Call {
	_c.Call.Return(run)
	return _c
}

// CheckFollowingAndFriendsStatusConcurrent provides a mock function for the type MockFriendsAndFollowersRepository
func (_mock *MockFriendsAndFollowersRepository) CheckFollowingAndFriendsStatusConcurrent(ctx context.Context, user1Id primitive.ObjectID, user2Id primitive.ObjectID) (bool, models.FriendshipStatus, error) {
	ret := _mock.Called(ctx, user1Id, user2Id)

	if len(ret) == 0 {
		panic("no return value specified for CheckFollowingAndFriendsStatusConcurrent")
	}

	var r0 bool
	var r1 models.FriendshipStatus
	var r2 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) (bool, models.FriendshipStatus, error)); ok {
		return returnFunc(ctx, user1Id, user2Id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, user1Id, user2Id)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, primitive.ObjectID) models.FriendshipStatus); ok {
		r1 = returnFunc(ctx, user1Id, user2Id)
	} else {
		r1 = ret.Get(1).(models.FriendshipStatus)
	}
	if returnFunc, ok := ret.Get(2).(func(context.Context, primitive.ObjectID, primitive.ObjectID) error); ok {
		r2 = returnFunc(ctx, user1Id, user2Id)
	} else {
		r2 = ret.Error(2)
	}
	return r0, r1, r2
}

// MockFriendsAndFollowersRepository_CheckFollowingAndFriendsStatusConcurrent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CheckFollowingAndFriendsStatusConcurrent'
type MockFriendsAndFollowersRepository_CheckFollowingAndFriendsStatusConcurrent_Call struct {
	*mock.Call
}

// CheckFollowingAndFriendsStatusConcurrent is a helper method to define mock.On call
//   - ctx context.Context
//   - user1Id primitive.ObjectID
//   - user2Id primitive.ObjectID
func (_e *MockFriendsAndFollowersRepository_Expecter) CheckFollowingAndFriendsStatusConcurrent(ctx interface{}, user1Id interface{}, user2Id interface{}) *MockFriendsAndFollowersRepository_CheckFollowingAndFriendsStatusConcurrent_Call {
	return &MockFriendsAndFollowersRepository_CheckFollowingAndFriendsStatusConcurrent_Call{Call: _e.mock.On("CheckFollowingAndFriendsStatusConcurrent", ctx, user1Id, user2Id)}
}

func (_c *MockFriendsAndFollowersRepository_CheckFollowingAndFriendsStatusConcurrent_Call) Run(run func(ctx context.Context, user1Id primitive.ObjectID, user2Id primitive.ObjectID)) *MockFriendsAndFollowersRepository_CheckFollowingAndFriendsStatusConcurrent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockFriendsAndFollowersRepository_CheckFollowingAndFriendsStatusConcurrent_Call) Return(b bool, friendshipStatus models.FriendshipStatus, err error) *MockFriendsAndFollowersRepository_CheckFollowingAndFriendsStatusConcurrent_Call {
	_c.Call.Return(b, friendshipStatus, err)
	return _c
}

func (_c *MockFriendsAndFollowersRepository_CheckFollowingAndFriendsStatusConcurrent_Call) RunAndReturn(run func(ctx context.Context, user1Id primitive.ObjectID, user2Id primitive.ObjectID) (bool, models.FriendshipStatus, error)) *MockFriendsAndFollowersRepository_CheckFollowingAndFriendsStatusConcurrent_Call {
	_c.Call.Return(run)
	return _c
}

// CheckIfAlreadyFollowing provides a mock function for the type MockFriendsAndFollowersRepository
func (_mock *MockFriendsAndFollowersRepository) CheckIfAlreadyFollowing(ctx context.Context, followerId primitive.ObjectID, followeeId primitive.ObjectID) (bool, *models.FollowersAndFollowee, error) {
	ret := _mock.Called(ctx, followerId, followeeId)

	if len(ret) == 0 {
		panic("no return value specified for CheckIfAlreadyFollowing")
	}

	var r0 bool
	var r1 *models.FollowersAndFollowee
	var r2 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) (bool, *models.FollowersAndFollowee, error)); ok {
		return returnFunc(ctx, followerId, followeeId)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, followerId, followeeId)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, primitive.ObjectID) *models.FollowersAndFollowee); ok {
		r1 = returnFunc(ctx, followerId, followeeId)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).(*models.FollowersAndFollowee)
		}
	}
	if returnFunc, ok := ret.Get(2).(func(context.Context, primitive.ObjectID, primitive.ObjectID) error); ok {
		r2 = returnFunc(ctx, followerId, followeeId)
	} else {
		r2 = ret.Error(2)
	}
	return r0, r1, r2
}

// MockFriendsAndFollowersRepository_CheckIfAlreadyFollowing_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CheckIfAlreadyFollowing'
type MockFriendsAndFollowersRepository_CheckIfAlreadyFollowing_Call struct {
	*mock.Call
}

// CheckIfAlreadyFollowing is a helper method to define mock.On call
//   - ctx context.Context
//   - followerId primitive.ObjectID
//   - followeeId primitive.ObjectID
func (_e *MockFriendsAndFollowersRepository_Expecter) CheckIfAlreadyFollowing(ctx interface{}, followerId interface{}, followeeId interface{}) *MockFriendsAndFollowersRepository_CheckIfAlreadyFollowing_Call {
	return &MockFriendsAndFollowersRepository_CheckIfAlreadyFollowing_Call{Call: _e.mock.On("CheckIfAlreadyFollowing", ctx, followerId, followeeId)}
}

func (_c *MockFriendsAndFollowersRepository_CheckIfAlreadyFollowing_Call) Run(run func(ctx context.Context, followerId primitive.ObjectID, followeeId primitive.ObjectID)) *MockFriendsAndFollowersRepository_CheckIfAlreadyFollowing_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockFriendsAndFollowersRepository_CheckIfAlreadyFollowing_Call) Return(b bool, followersAndFollowee *models.FollowersAndFollowee, err error) *MockFriendsAndFollowersRepository_CheckIfAlreadyFollowing_Call {
	_c.Call.Return(b, followersAndFollowee, err)
	return _c
}

func (_c *MockFriendsAndFollowersRepository_CheckIfAlreadyFollowing_Call) RunAndReturn(run func(ctx context.Context, followerId primitive.ObjectID, followeeId primitive.ObjectID) (bool, *models.FollowersAndFollowee, error)) *MockFriendsAndFollowersRepository_CheckIfAlreadyFollowing_Call {
	_c.Call.Return(run)
	return _c
}

// CheckIfAlreadyFriends provides a mock function for the type MockFriendsAndFollowersRepository
func (_mock *MockFriendsAndFollowersRepository) CheckIfAlreadyFriends(ctx context.Context, user1Id primitive.ObjectID, user2Id primitive.ObjectID) (bool, *models.Friends, error) {
	ret := _mock.Called(ctx, user1Id, user2Id)

	if len(ret) == 0 {
		panic("no return value specified for CheckIfAlreadyFriends")
	}

	var r0 bool
	var r1 *models.Friends
	var r2 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) (bool, *models.Friends, error)); ok {
		return returnFunc(ctx, user1Id, user2Id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, user1Id, user2Id)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, primitive.ObjectID) *models.Friends); ok {
		r1 = returnFunc(ctx, user1Id, user2Id)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).(*models.Friends)
		}
	}
	if returnFunc, ok := ret.Get(2).(func(context.Context, primitive.ObjectID, primitive.ObjectID) error); ok {
		r2 = returnFunc(ctx, user1Id, user2Id)
	} else {
		r2 = ret.Error(2)
	}
	return r0, r1, r2
}

// MockFriendsAndFollowersRepository_CheckIfAlreadyFriends_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CheckIfAlreadyFriends'
type MockFriendsAndFollowersRepository_CheckIfAlreadyFriends_Call struct {
	*mock.Call
}

// CheckIfAlreadyFriends is a helper method to define mock.On call
//   - ctx context.Context
//   - user1Id primitive.ObjectID
//   - user2Id primitive.ObjectID
func (_e *MockFriendsAndFollowersRepository_Expecter) CheckIfAlreadyFriends(ctx interface{}, user1Id interface{}, user2Id interface{}) *MockFriendsAndFollowersRepository_CheckIfAlreadyFriends_Call {
	return &MockFriendsAndFollowersRepository_CheckIfAlreadyFriends_Call{Call: _e.mock.On("CheckIfAlreadyFriends", ctx, user1Id, user2Id)}
}

func (_c *MockFriendsAndFollowersRepository_CheckIfAlreadyFriends_Call) Run(run func(ctx context.Context, user1Id primitive.ObjectID, user2Id primitive.ObjectID)) *MockFriendsAndFollowersRepository_CheckIfAlreadyFriends_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockFriendsAndFollowersRepository_CheckIfAlreadyFriends_Call) Return(b bool, friends *models.Friends, err error) *MockFriendsAndFollowersRepository_CheckIfAlreadyFriends_Call {
	_c.Call.Return(b, friends, err)
	return _c
}

func (_c *MockFriendsAndFollowersRepository_CheckIfAlreadyFriends_Call) RunAndReturn(run func(ctx context.Context, user1Id primitive.ObjectID, user2Id primitive.ObjectID) (bool, *models.Friends, error)) *MockFriendsAndFollowersRepository_CheckIfAlreadyFriends_Call {
	_c.Call.Return(run)
	return _c
}

// CheckIfFriendRequestSent provides a mock function for the type MockFriendsAndFollowersRepository
func (_mock *MockFriendsAndFollowersRepository) CheckIfFriendRequestSent(ctx context.Context, senderId primitive.ObjectID, receiverId primitive.ObjectID) (bool, *models.FriendRequest, error) {
	ret := _mock.Called(ctx, senderId, receiverId)

	if len(ret) == 0 {
		panic("no return value specified for CheckIfFriendRequestSent")
	}

	var r0 bool
	var r1 *models.FriendRequest
	var r2 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) (bool, *models.FriendRequest, error)); ok {
		return returnFunc(ctx, senderId, receiverId)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, senderId, receiverId)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, primitive.ObjectID) *models.FriendRequest); ok {
		r1 = returnFunc(ctx, senderId, receiverId)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).(*models.FriendRequest)
		}
	}
	if returnFunc, ok := ret.Get(2).(func(context.Context, primitive.ObjectID, primitive.ObjectID) error); ok {
		r2 = returnFunc(ctx, senderId, receiverId)
	} else {
		r2 = ret.Error(2)
	}
	return r0, r1, r2
}

// MockFriendsAndFollowersRepository_CheckIfFriendRequestSent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CheckIfFriendRequestSent'
type MockFriendsAndFollowersRepository_CheckIfFriendRequestSent_Call struct {
	*mock.Call
}

// CheckIfFriendRequestSent is a helper method to define mock.On call
//   - ctx context.Context
//   - senderId primitive.ObjectID
//   - receiverId primitive.ObjectID
func (_e *MockFriendsAndFollowersRepository_Expecter) CheckIfFriendRequestSent(ctx interface{}, senderId interface{}, receiverId interface{}) *MockFriendsAndFollowersRepository_CheckIfFriendRequestSent_Call {
	return &MockFriendsAndFollowersRepository_CheckIfFriendRequestSent_Call{Call: _e.mock.On("CheckIfFriendRequestSent", ctx, senderId, receiverId)}
}

func (_c *MockFriendsAndFollowersRepository_CheckIfFriendRequestSent_Call) Run(run func(ctx context.Context, senderId primitive.ObjectID, receiverId primitive.ObjectID)) *MockFriendsAndFollowersRepository_CheckIfFriendRequestSent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockFriendsAndFollowersRepository_CheckIfFriendRequestSent_Call) Return(b bool, friendRequest *models.FriendRequest, err error) *MockFriendsAndFollowersRepository_CheckIfFriendRequestSent_Call {
	_c.Call.Return(b, friendRequest, err)
	return _c
}

func (_c *MockFriendsAndFollowersRepository_CheckIfFriendRequestSent_Call) RunAndReturn(run func(ctx context.Context, senderId primitive.ObjectID, receiverId primitive.ObjectID) (bool, *models.FriendRequest, error)) *MockFriendsAndFollowersRepository_CheckIfFriendRequestSent_Call {
	_c.Call.Return(run)
	return _c
}

// CreateFriendRequest provides a mock function for the type MockFriendsAndFollowersRepository
func (_mock *MockFriendsAndFollowersRepository) CreateFriendRequest(ctx context.Context, friendRequest models.FriendRequest) (bool, error) {
	ret := _mock.Called(ctx, friendRequest)

	if len(ret) == 0 {
		panic("no return value specified for CreateFriendRequest")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.FriendRequest) (bool, error)); ok {
		return returnFunc(ctx, friendRequest)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.FriendRequest) bool); ok {
		r0 = returnFunc(ctx, friendRequest)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.FriendRequest) error); ok {
		r1 = returnFunc(ctx, friendRequest)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockFriendsAndFollowersRepository_CreateFriendRequest_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateFriendRequest'
type MockFriendsAndFollowersRepository_CreateFriendRequest_Call struct {
	*mock.Call
}

// CreateFriendRequest is a helper method to define mock.On call
//   - ctx context.Context
//   - friendRequest models.FriendRequest
func (_e *MockFriendsAndFollowersRepository_Expecter) CreateFriendRequest(ctx interface{}, friendRequest interface{}) *MockFriendsAndFollowersRepository_CreateFriendRequest_Call {
	return &MockFriendsAndFollowersRepository_CreateFriendRequest_Call{Call: _e.mock.On("CreateFriendRequest", ctx, friendRequest)}
}

func (_c *MockFriendsAndFollowersRepository_CreateFriendRequest_Call) Run(run func(ctx context.Context, friendRequest models.FriendRequest)) *MockFriendsAndFollowersRepository_CreateFriendRequest_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 models.FriendRequest
		if args[1] != nil {
			arg1 = args[1].(models.FriendRequest)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockFriendsAndFollowersRepository_CreateFriendRequest_Call) Return(b bool, err error) *MockFriendsAndFollowersRepository_CreateFriendRequest_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockFriendsAndFollowersRepository_CreateFriendRequest_Call) RunAndReturn(run func(ctx context.Context, friendRequest models.FriendRequest) (bool, error)) *MockFriendsAndFollowersRepository_CreateFriendRequest_Call {
	_c.Call.Return(run)
	return _c
}

// CreateFriends provides a mock function for the type MockFriendsAndFollowersRepository
func (_mock *MockFriendsAndFollowersRepository) CreateFriends(ctx context.Context, friend models.Friends) (bool, error) {
	ret := _mock.Called(ctx, friend)

	if len(ret) == 0 {
		panic("no return value specified for CreateFriends")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.Friends) (bool, error)); ok {
		return returnFunc(ctx, friend)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.Friends) bool); ok {
		r0 = returnFunc(ctx, friend)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, models.Friends) error); ok {
		r1 = returnFunc(ctx, friend)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockFriendsAndFollowersRepository_CreateFriends_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateFriends'
type MockFriendsAndFollowersRepository_CreateFriends_Call struct {
	*mock.Call
}

// CreateFriends is a helper method to define mock.On call
//   - ctx context.Context
//   - friend models.Friends
func (_e *MockFriendsAndFollowersRepository_Expecter) CreateFriends(ctx interface{}, friend interface{}) *MockFriendsAndFollowersRepository_CreateFriends_Call {
	return &MockFriendsAndFollowersRepository_CreateFriends_Call{Call: _e.mock.On("CreateFriends", ctx, friend)}
}

func (_c *MockFriendsAndFollowersRepository_CreateFriends_Call) Run(run func(ctx context.Context, friend models.Friends)) *MockFriendsAndFollowersRepository_CreateFriends_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 models.Friends
		if args[1] != nil {
			arg1 = args[1].(models.Friends)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockFriendsAndFollowersRepository_CreateFriends_Call) Return(b bool, err error) *MockFriendsAndFollowersRepository_CreateFriends_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockFriendsAndFollowersRepository_CreateFriends_Call) RunAndReturn(run func(ctx context.Context, friend models.Friends) (bool, error)) *MockFriendsAndFollowersRepository_CreateFriends_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteFriend provides a mock function for the type MockFriendsAndFollowersRepository
func (_mock *MockFriendsAndFollowersRepository) DeleteFriend(ctx context.Context, filter bson.M) (bool, error) {
	ret := _mock.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for DeleteFriend")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M) (bool, error)); ok {
		return returnFunc(ctx, filter)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M) bool); ok {
		r0 = returnFunc(ctx, filter)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, bson.M) error); ok {
		r1 = returnFunc(ctx, filter)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockFriendsAndFollowersRepository_DeleteFriend_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteFriend'
type MockFriendsAndFollowersRepository_DeleteFriend_Call struct {
	*mock.Call
}

// DeleteFriend is a helper method to define mock.On call
//   - ctx context.Context
//   - filter bson.M
func (_e *MockFriendsAndFollowersRepository_Expecter) DeleteFriend(ctx interface{}, filter interface{}) *MockFriendsAndFollowersRepository_DeleteFriend_Call {
	return &MockFriendsAndFollowersRepository_DeleteFriend_Call{Call: _e.mock.On("DeleteFriend", ctx, filter)}
}

func (_c *MockFriendsAndFollowersRepository_DeleteFriend_Call) Run(run func(ctx context.Context, filter bson.M)) *MockFriendsAndFollowersRepository_DeleteFriend_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 bson.M
		if args[1] != nil {
			arg1 = args[1].(bson.M)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockFriendsAndFollowersRepository_DeleteFriend_Call) Return(b bool, err error) *MockFriendsAndFollowersRepository_DeleteFriend_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockFriendsAndFollowersRepository_DeleteFriend_Call) RunAndReturn(run func(ctx context.Context, filter bson.M) (bool, error)) *MockFriendsAndFollowersRepository_DeleteFriend_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteFriendRequest provides a mock function for the type MockFriendsAndFollowersRepository
func (_mock *MockFriendsAndFollowersRepository) DeleteFriendRequest(ctx context.Context, filter bson.M) (bool, error) {
	ret := _mock.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for DeleteFriendRequest")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M) (bool, error)); ok {
		return returnFunc(ctx, filter)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M) bool); ok {
		r0 = returnFunc(ctx, filter)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, bson.M) error); ok {
		r1 = returnFunc(ctx, filter)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockFriendsAndFollowersRepository_DeleteFriendRequest_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteFriendRequest'
type MockFriendsAndFollowersRepository_DeleteFriendRequest_Call struct {
	*mock.Call
}

// DeleteFriendRequest is a helper method to define mock.On call
//   - ctx context.Context
//   - filter bson.M
func (_e *MockFriendsAndFollowersRepository_Expecter) DeleteFriendRequest(ctx interface{}, filter interface{}) *MockFriendsAndFollowersRepository_DeleteFriendRequest_Call {
	return &MockFriendsAndFollowersRepository_DeleteFriendRequest_Call{Call: _e.mock.On("DeleteFriendRequest", ctx, filter)}
}

func (_c *MockFriendsAndFollowersRepository_DeleteFriendRequest_Call) Run(run func(ctx context.Context, filter bson.M)) *MockFriendsAndFollowersRepository_DeleteFriendRequest_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 bson.M
		if args[1] != nil {
			arg1 = args[1].(bson.M)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockFriendsAndFollowersRepository_DeleteFriendRequest_Call) Return(b bool, err error) *MockFriendsAndFollowersRepository_DeleteFriendRequest_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockFriendsAndFollowersRepository_DeleteFriendRequest_Call) RunAndReturn(run func(ctx context.Context, filter bson.M) (bool, error)) *MockFriendsAndFollowersRepository_DeleteFriendRequest_Call {
	_c.Call.Return(run)
	return _c
}

// GetFriendsUserIds provides a mock function for the type MockFriendsAndFollowersRepository
func (_mock *MockFriendsAndFollowersRepository) GetFriendsUserIds(ctx context.Context, userId primitive.ObjectID) ([]primitive.ObjectID, error) {
	ret := _mock.Called(ctx, userId)

	if len(ret) == 0 {
		panic("no return value specified for GetFriendsUserIds")
	}

	var r0 []primitive.ObjectID
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) ([]primitive.ObjectID, error)); ok {
		return returnFunc(ctx, userId)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) []primitive.ObjectID); ok {
		r0 = returnFunc(ctx, userId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]primitive.ObjectID)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, userId)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockFriendsAndFollowersRepository_GetFriendsUserIds_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFriendsUserIds'
type MockFriendsAndFollowersRepository_GetFriendsUserIds_Call struct {
	*mock.Call
}

// GetFriendsUserIds is a helper method to define mock.On call
//   - ctx context.Context
//   - userId primitive.ObjectID
func (_e *MockFriendsAndFollowersRepository_Expecter) GetFriendsUserIds(ctx interface{}, userId interface{}) *MockFriendsAndFollowersRepository_GetFriendsUserIds_Call {
	return &MockFriendsAndFollowersRepository_GetFriendsUserIds_Call{Call: _e.mock.On("GetFriendsUserIds", ctx, userId)}
}

func (_c *MockFriendsAndFollowersRepository_GetFriendsUserIds_Call) Run(run func(ctx context.Context, userId primitive.ObjectID)) *MockFriendsAndFollowersRepository_GetFriendsUserIds_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockFriendsAndFollowersRepository_GetFriendsUserIds_Call) Return(objectIDs []primitive.ObjectID, err error) *MockFriendsAndFollowersRepository_GetFriendsUserIds_Call {
	_c.Call.Return(objectIDs, err)
	return _c
}

func (_c *MockFriendsAndFollowersRepository_GetFriendsUserIds_Call) RunAndReturn(run func(ctx context.Context, userId primitive.ObjectID) ([]primitive.ObjectID, error)) *MockFriendsAndFollowersRepository_GetFriendsUserIds_Call {
	_c.Call.Return(run)
	return _c
}

// RemoveFollower provides a mock function for the type MockFriendsAndFollowersRepository
func (_mock *MockFriendsAndFollowersRepository) RemoveFollower(ctx context.Context, id primitive.ObjectID) (bool, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for RemoveFollower")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (bool, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) bool); ok {
		r0 = returnFunc(ctx, id)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockFriendsAndFollowersRepository_RemoveFollower_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RemoveFollower'
type MockFriendsAndFollowersRepository_RemoveFollower_Call struct {
	*mock.Call
}

// RemoveFollower is a helper method to define mock.On call
//   - ctx context.Context
//   - id primitive.ObjectID
func (_e *MockFriendsAndFollowersRepository_Expecter) RemoveFollower(ctx interface{}, id interface{}) *MockFriendsAndFollowersRepository_RemoveFollower_Call {
	return &MockFriendsAndFollowersRepository_RemoveFollower_Call{Call: _e.mock.On("RemoveFollower", ctx, id)}
}

func (_c *MockFriendsAndFollowersRepository_RemoveFollower_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockFriendsAndFollowersRepository_RemoveFollower_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockFriendsAndFollowersRepository_RemoveFollower_Call) Return(b bool, err error) *MockFriendsAndFollowersRepository_RemoveFollower_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockFriendsAndFollowersRepository_RemoveFollower_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (bool, error)) *MockFriendsAndFollowersRepository_RemoveFollower_Call {
	_c.Call.Return(run)
	return _c
}
