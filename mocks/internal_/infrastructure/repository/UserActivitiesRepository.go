// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/models"
	"time"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// NewMockUserActivitiesRepository creates a new instance of MockUserActivitiesRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockUserActivitiesRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockUserActivitiesRepository {
	mock := &MockUserActivitiesRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockUserActivitiesRepository is an autogenerated mock type for the UserActivitiesRepository type
type MockUserActivitiesRepository struct {
	mock.Mock
}

type MockUserActivitiesRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockUserActivitiesRepository) EXPECT() *MockUserActivitiesRepository_Expecter {
	return &MockUserActivitiesRepository_Expecter{mock: &_m.Mock}
}

// Aggregate provides a mock function for the type MockUserActivitiesRepository
func (_mock *MockUserActivitiesRepository) Aggregate(ctx context.Context, pipeline mongo.Pipeline, opts ...*options.AggregateOptions) ([]*models.UserActivity, error) {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, pipeline, opts)
	} else {
		tmpRet = _mock.Called(ctx, pipeline)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for Aggregate")
	}

	var r0 []*models.UserActivity
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, mongo.Pipeline, ...*options.AggregateOptions) ([]*models.UserActivity, error)); ok {
		return returnFunc(ctx, pipeline, opts...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, mongo.Pipeline, ...*options.AggregateOptions) []*models.UserActivity); ok {
		r0 = returnFunc(ctx, pipeline, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.UserActivity)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, mongo.Pipeline, ...*options.AggregateOptions) error); ok {
		r1 = returnFunc(ctx, pipeline, opts...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserActivitiesRepository_Aggregate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Aggregate'
type MockUserActivitiesRepository_Aggregate_Call struct {
	*mock.Call
}

// Aggregate is a helper method to define mock.On call
//   - ctx context.Context
//   - pipeline mongo.Pipeline
//   - opts ...*options.AggregateOptions
func (_e *MockUserActivitiesRepository_Expecter) Aggregate(ctx interface{}, pipeline interface{}, opts ...interface{}) *MockUserActivitiesRepository_Aggregate_Call {
	return &MockUserActivitiesRepository_Aggregate_Call{Call: _e.mock.On("Aggregate",
		append([]interface{}{ctx, pipeline}, opts...)...)}
}

func (_c *MockUserActivitiesRepository_Aggregate_Call) Run(run func(ctx context.Context, pipeline mongo.Pipeline, opts ...*options.AggregateOptions)) *MockUserActivitiesRepository_Aggregate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 mongo.Pipeline
		if args[1] != nil {
			arg1 = args[1].(mongo.Pipeline)
		}
		var arg2 []*options.AggregateOptions
		var variadicArgs []*options.AggregateOptions
		if len(args) > 2 {
			variadicArgs = args[2].([]*options.AggregateOptions)
		}
		arg2 = variadicArgs
		run(
			arg0,
			arg1,
			arg2...,
		)
	})
	return _c
}

func (_c *MockUserActivitiesRepository_Aggregate_Call) Return(userActivitys []*models.UserActivity, err error) *MockUserActivitiesRepository_Aggregate_Call {
	_c.Call.Return(userActivitys, err)
	return _c
}

func (_c *MockUserActivitiesRepository_Aggregate_Call) RunAndReturn(run func(ctx context.Context, pipeline mongo.Pipeline, opts ...*options.AggregateOptions) ([]*models.UserActivity, error)) *MockUserActivitiesRepository_Aggregate_Call {
	_c.Call.Return(run)
	return _c
}

// Count provides a mock function for the type MockUserActivitiesRepository
func (_mock *MockUserActivitiesRepository) Count(ctx context.Context, filter bson.M) (int64, error) {
	ret := _mock.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for Count")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M) (int64, error)); ok {
		return returnFunc(ctx, filter)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M) int64); ok {
		r0 = returnFunc(ctx, filter)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, bson.M) error); ok {
		r1 = returnFunc(ctx, filter)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserActivitiesRepository_Count_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Count'
type MockUserActivitiesRepository_Count_Call struct {
	*mock.Call
}

// Count is a helper method to define mock.On call
//   - ctx context.Context
//   - filter bson.M
func (_e *MockUserActivitiesRepository_Expecter) Count(ctx interface{}, filter interface{}) *MockUserActivitiesRepository_Count_Call {
	return &MockUserActivitiesRepository_Count_Call{Call: _e.mock.On("Count", ctx, filter)}
}

func (_c *MockUserActivitiesRepository_Count_Call) Run(run func(ctx context.Context, filter bson.M)) *MockUserActivitiesRepository_Count_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 bson.M
		if args[1] != nil {
			arg1 = args[1].(bson.M)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockUserActivitiesRepository_Count_Call) Return(n int64, err error) *MockUserActivitiesRepository_Count_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockUserActivitiesRepository_Count_Call) RunAndReturn(run func(ctx context.Context, filter bson.M) (int64, error)) *MockUserActivitiesRepository_Count_Call {
	_c.Call.Return(run)
	return _c
}

// Create provides a mock function for the type MockUserActivitiesRepository
func (_mock *MockUserActivitiesRepository) Create(ctx context.Context, activity *models.UserActivity) error {
	ret := _mock.Called(ctx, activity)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UserActivity) error); ok {
		r0 = returnFunc(ctx, activity)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockUserActivitiesRepository_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type MockUserActivitiesRepository_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx context.Context
//   - activity *models.UserActivity
func (_e *MockUserActivitiesRepository_Expecter) Create(ctx interface{}, activity interface{}) *MockUserActivitiesRepository_Create_Call {
	return &MockUserActivitiesRepository_Create_Call{Call: _e.mock.On("Create", ctx, activity)}
}

func (_c *MockUserActivitiesRepository_Create_Call) Run(run func(ctx context.Context, activity *models.UserActivity)) *MockUserActivitiesRepository_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.UserActivity
		if args[1] != nil {
			arg1 = args[1].(*models.UserActivity)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockUserActivitiesRepository_Create_Call) Return(err error) *MockUserActivitiesRepository_Create_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockUserActivitiesRepository_Create_Call) RunAndReturn(run func(ctx context.Context, activity *models.UserActivity) error) *MockUserActivitiesRepository_Create_Call {
	_c.Call.Return(run)
	return _c
}

// FindByActivityType provides a mock function for the type MockUserActivitiesRepository
func (_mock *MockUserActivitiesRepository) FindByActivityType(ctx context.Context, activityType constants.ActivityType, limit int64, skip int64) ([]*models.UserActivity, error) {
	ret := _mock.Called(ctx, activityType, limit, skip)

	if len(ret) == 0 {
		panic("no return value specified for FindByActivityType")
	}

	var r0 []*models.UserActivity
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, constants.ActivityType, int64, int64) ([]*models.UserActivity, error)); ok {
		return returnFunc(ctx, activityType, limit, skip)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, constants.ActivityType, int64, int64) []*models.UserActivity); ok {
		r0 = returnFunc(ctx, activityType, limit, skip)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.UserActivity)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, constants.ActivityType, int64, int64) error); ok {
		r1 = returnFunc(ctx, activityType, limit, skip)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserActivitiesRepository_FindByActivityType_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindByActivityType'
type MockUserActivitiesRepository_FindByActivityType_Call struct {
	*mock.Call
}

// FindByActivityType is a helper method to define mock.On call
//   - ctx context.Context
//   - activityType constants.ActivityType
//   - limit int64
//   - skip int64
func (_e *MockUserActivitiesRepository_Expecter) FindByActivityType(ctx interface{}, activityType interface{}, limit interface{}, skip interface{}) *MockUserActivitiesRepository_FindByActivityType_Call {
	return &MockUserActivitiesRepository_FindByActivityType_Call{Call: _e.mock.On("FindByActivityType", ctx, activityType, limit, skip)}
}

func (_c *MockUserActivitiesRepository_FindByActivityType_Call) Run(run func(ctx context.Context, activityType constants.ActivityType, limit int64, skip int64)) *MockUserActivitiesRepository_FindByActivityType_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 constants.ActivityType
		if args[1] != nil {
			arg1 = args[1].(constants.ActivityType)
		}
		var arg2 int64
		if args[2] != nil {
			arg2 = args[2].(int64)
		}
		var arg3 int64
		if args[3] != nil {
			arg3 = args[3].(int64)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockUserActivitiesRepository_FindByActivityType_Call) Return(userActivitys []*models.UserActivity, err error) *MockUserActivitiesRepository_FindByActivityType_Call {
	_c.Call.Return(userActivitys, err)
	return _c
}

func (_c *MockUserActivitiesRepository_FindByActivityType_Call) RunAndReturn(run func(ctx context.Context, activityType constants.ActivityType, limit int64, skip int64) ([]*models.UserActivity, error)) *MockUserActivitiesRepository_FindByActivityType_Call {
	_c.Call.Return(run)
	return _c
}

// FindByID provides a mock function for the type MockUserActivitiesRepository
func (_mock *MockUserActivitiesRepository) FindByID(ctx context.Context, id primitive.ObjectID) (*models.UserActivity, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for FindByID")
	}

	var r0 *models.UserActivity
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.UserActivity, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.UserActivity); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserActivity)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserActivitiesRepository_FindByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindByID'
type MockUserActivitiesRepository_FindByID_Call struct {
	*mock.Call
}

// FindByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id primitive.ObjectID
func (_e *MockUserActivitiesRepository_Expecter) FindByID(ctx interface{}, id interface{}) *MockUserActivitiesRepository_FindByID_Call {
	return &MockUserActivitiesRepository_FindByID_Call{Call: _e.mock.On("FindByID", ctx, id)}
}

func (_c *MockUserActivitiesRepository_FindByID_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockUserActivitiesRepository_FindByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockUserActivitiesRepository_FindByID_Call) Return(userActivity *models.UserActivity, err error) *MockUserActivitiesRepository_FindByID_Call {
	_c.Call.Return(userActivity, err)
	return _c
}

func (_c *MockUserActivitiesRepository_FindByID_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (*models.UserActivity, error)) *MockUserActivitiesRepository_FindByID_Call {
	_c.Call.Return(run)
	return _c
}

// FindByTimeRange provides a mock function for the type MockUserActivitiesRepository
func (_mock *MockUserActivitiesRepository) FindByTimeRange(ctx context.Context, startTime time.Time, endTime time.Time, limit int64, skip int64) ([]*models.UserActivity, error) {
	ret := _mock.Called(ctx, startTime, endTime, limit, skip)

	if len(ret) == 0 {
		panic("no return value specified for FindByTimeRange")
	}

	var r0 []*models.UserActivity
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, time.Time, time.Time, int64, int64) ([]*models.UserActivity, error)); ok {
		return returnFunc(ctx, startTime, endTime, limit, skip)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, time.Time, time.Time, int64, int64) []*models.UserActivity); ok {
		r0 = returnFunc(ctx, startTime, endTime, limit, skip)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.UserActivity)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, time.Time, time.Time, int64, int64) error); ok {
		r1 = returnFunc(ctx, startTime, endTime, limit, skip)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserActivitiesRepository_FindByTimeRange_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindByTimeRange'
type MockUserActivitiesRepository_FindByTimeRange_Call struct {
	*mock.Call
}

// FindByTimeRange is a helper method to define mock.On call
//   - ctx context.Context
//   - startTime time.Time
//   - endTime time.Time
//   - limit int64
//   - skip int64
func (_e *MockUserActivitiesRepository_Expecter) FindByTimeRange(ctx interface{}, startTime interface{}, endTime interface{}, limit interface{}, skip interface{}) *MockUserActivitiesRepository_FindByTimeRange_Call {
	return &MockUserActivitiesRepository_FindByTimeRange_Call{Call: _e.mock.On("FindByTimeRange", ctx, startTime, endTime, limit, skip)}
}

func (_c *MockUserActivitiesRepository_FindByTimeRange_Call) Run(run func(ctx context.Context, startTime time.Time, endTime time.Time, limit int64, skip int64)) *MockUserActivitiesRepository_FindByTimeRange_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 time.Time
		if args[1] != nil {
			arg1 = args[1].(time.Time)
		}
		var arg2 time.Time
		if args[2] != nil {
			arg2 = args[2].(time.Time)
		}
		var arg3 int64
		if args[3] != nil {
			arg3 = args[3].(int64)
		}
		var arg4 int64
		if args[4] != nil {
			arg4 = args[4].(int64)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
			arg4,
		)
	})
	return _c
}

func (_c *MockUserActivitiesRepository_FindByTimeRange_Call) Return(userActivitys []*models.UserActivity, err error) *MockUserActivitiesRepository_FindByTimeRange_Call {
	_c.Call.Return(userActivitys, err)
	return _c
}

func (_c *MockUserActivitiesRepository_FindByTimeRange_Call) RunAndReturn(run func(ctx context.Context, startTime time.Time, endTime time.Time, limit int64, skip int64) ([]*models.UserActivity, error)) *MockUserActivitiesRepository_FindByTimeRange_Call {
	_c.Call.Return(run)
	return _c
}

// FindByUserID provides a mock function for the type MockUserActivitiesRepository
func (_mock *MockUserActivitiesRepository) FindByUserID(ctx context.Context, userID primitive.ObjectID, limit int64, skip int64) ([]*models.UserActivity, error) {
	ret := _mock.Called(ctx, userID, limit, skip)

	if len(ret) == 0 {
		panic("no return value specified for FindByUserID")
	}

	var r0 []*models.UserActivity
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int64, int64) ([]*models.UserActivity, error)); ok {
		return returnFunc(ctx, userID, limit, skip)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int64, int64) []*models.UserActivity); ok {
		r0 = returnFunc(ctx, userID, limit, skip)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.UserActivity)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, int64, int64) error); ok {
		r1 = returnFunc(ctx, userID, limit, skip)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserActivitiesRepository_FindByUserID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindByUserID'
type MockUserActivitiesRepository_FindByUserID_Call struct {
	*mock.Call
}

// FindByUserID is a helper method to define mock.On call
//   - ctx context.Context
//   - userID primitive.ObjectID
//   - limit int64
//   - skip int64
func (_e *MockUserActivitiesRepository_Expecter) FindByUserID(ctx interface{}, userID interface{}, limit interface{}, skip interface{}) *MockUserActivitiesRepository_FindByUserID_Call {
	return &MockUserActivitiesRepository_FindByUserID_Call{Call: _e.mock.On("FindByUserID", ctx, userID, limit, skip)}
}

func (_c *MockUserActivitiesRepository_FindByUserID_Call) Run(run func(ctx context.Context, userID primitive.ObjectID, limit int64, skip int64)) *MockUserActivitiesRepository_FindByUserID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 int64
		if args[2] != nil {
			arg2 = args[2].(int64)
		}
		var arg3 int64
		if args[3] != nil {
			arg3 = args[3].(int64)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockUserActivitiesRepository_FindByUserID_Call) Return(userActivitys []*models.UserActivity, err error) *MockUserActivitiesRepository_FindByUserID_Call {
	_c.Call.Return(userActivitys, err)
	return _c
}

func (_c *MockUserActivitiesRepository_FindByUserID_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID, limit int64, skip int64) ([]*models.UserActivity, error)) *MockUserActivitiesRepository_FindByUserID_Call {
	_c.Call.Return(run)
	return _c
}

// FindByUserIDAndActivityType provides a mock function for the type MockUserActivitiesRepository
func (_mock *MockUserActivitiesRepository) FindByUserIDAndActivityType(ctx context.Context, userID primitive.ObjectID, activityType constants.ActivityType, limit int64, skip int64) ([]*models.UserActivity, error) {
	ret := _mock.Called(ctx, userID, activityType, limit, skip)

	if len(ret) == 0 {
		panic("no return value specified for FindByUserIDAndActivityType")
	}

	var r0 []*models.UserActivity
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, constants.ActivityType, int64, int64) ([]*models.UserActivity, error)); ok {
		return returnFunc(ctx, userID, activityType, limit, skip)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, constants.ActivityType, int64, int64) []*models.UserActivity); ok {
		r0 = returnFunc(ctx, userID, activityType, limit, skip)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.UserActivity)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, constants.ActivityType, int64, int64) error); ok {
		r1 = returnFunc(ctx, userID, activityType, limit, skip)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserActivitiesRepository_FindByUserIDAndActivityType_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindByUserIDAndActivityType'
type MockUserActivitiesRepository_FindByUserIDAndActivityType_Call struct {
	*mock.Call
}

// FindByUserIDAndActivityType is a helper method to define mock.On call
//   - ctx context.Context
//   - userID primitive.ObjectID
//   - activityType constants.ActivityType
//   - limit int64
//   - skip int64
func (_e *MockUserActivitiesRepository_Expecter) FindByUserIDAndActivityType(ctx interface{}, userID interface{}, activityType interface{}, limit interface{}, skip interface{}) *MockUserActivitiesRepository_FindByUserIDAndActivityType_Call {
	return &MockUserActivitiesRepository_FindByUserIDAndActivityType_Call{Call: _e.mock.On("FindByUserIDAndActivityType", ctx, userID, activityType, limit, skip)}
}

func (_c *MockUserActivitiesRepository_FindByUserIDAndActivityType_Call) Run(run func(ctx context.Context, userID primitive.ObjectID, activityType constants.ActivityType, limit int64, skip int64)) *MockUserActivitiesRepository_FindByUserIDAndActivityType_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 constants.ActivityType
		if args[2] != nil {
			arg2 = args[2].(constants.ActivityType)
		}
		var arg3 int64
		if args[3] != nil {
			arg3 = args[3].(int64)
		}
		var arg4 int64
		if args[4] != nil {
			arg4 = args[4].(int64)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
			arg4,
		)
	})
	return _c
}

func (_c *MockUserActivitiesRepository_FindByUserIDAndActivityType_Call) Return(userActivitys []*models.UserActivity, err error) *MockUserActivitiesRepository_FindByUserIDAndActivityType_Call {
	_c.Call.Return(userActivitys, err)
	return _c
}

func (_c *MockUserActivitiesRepository_FindByUserIDAndActivityType_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID, activityType constants.ActivityType, limit int64, skip int64) ([]*models.UserActivity, error)) *MockUserActivitiesRepository_FindByUserIDAndActivityType_Call {
	_c.Call.Return(run)
	return _c
}

// GetActivitiesByActivityType provides a mock function for the type MockUserActivitiesRepository
func (_mock *MockUserActivitiesRepository) GetActivitiesByActivityType(ctx context.Context, userID primitive.ObjectID, activityTypes []constants.ActivityType, skip int64, limit int64) ([]*models.UserActivity, error) {
	ret := _mock.Called(ctx, userID, activityTypes, skip, limit)

	if len(ret) == 0 {
		panic("no return value specified for GetActivitiesByActivityType")
	}

	var r0 []*models.UserActivity
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, []constants.ActivityType, int64, int64) ([]*models.UserActivity, error)); ok {
		return returnFunc(ctx, userID, activityTypes, skip, limit)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, []constants.ActivityType, int64, int64) []*models.UserActivity); ok {
		r0 = returnFunc(ctx, userID, activityTypes, skip, limit)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.UserActivity)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, []constants.ActivityType, int64, int64) error); ok {
		r1 = returnFunc(ctx, userID, activityTypes, skip, limit)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserActivitiesRepository_GetActivitiesByActivityType_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetActivitiesByActivityType'
type MockUserActivitiesRepository_GetActivitiesByActivityType_Call struct {
	*mock.Call
}

// GetActivitiesByActivityType is a helper method to define mock.On call
//   - ctx context.Context
//   - userID primitive.ObjectID
//   - activityTypes []constants.ActivityType
//   - skip int64
//   - limit int64
func (_e *MockUserActivitiesRepository_Expecter) GetActivitiesByActivityType(ctx interface{}, userID interface{}, activityTypes interface{}, skip interface{}, limit interface{}) *MockUserActivitiesRepository_GetActivitiesByActivityType_Call {
	return &MockUserActivitiesRepository_GetActivitiesByActivityType_Call{Call: _e.mock.On("GetActivitiesByActivityType", ctx, userID, activityTypes, skip, limit)}
}

func (_c *MockUserActivitiesRepository_GetActivitiesByActivityType_Call) Run(run func(ctx context.Context, userID primitive.ObjectID, activityTypes []constants.ActivityType, skip int64, limit int64)) *MockUserActivitiesRepository_GetActivitiesByActivityType_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 []constants.ActivityType
		if args[2] != nil {
			arg2 = args[2].([]constants.ActivityType)
		}
		var arg3 int64
		if args[3] != nil {
			arg3 = args[3].(int64)
		}
		var arg4 int64
		if args[4] != nil {
			arg4 = args[4].(int64)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
			arg4,
		)
	})
	return _c
}

func (_c *MockUserActivitiesRepository_GetActivitiesByActivityType_Call) Return(userActivitys []*models.UserActivity, err error) *MockUserActivitiesRepository_GetActivitiesByActivityType_Call {
	_c.Call.Return(userActivitys, err)
	return _c
}

func (_c *MockUserActivitiesRepository_GetActivitiesByActivityType_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID, activityTypes []constants.ActivityType, skip int64, limit int64) ([]*models.UserActivity, error)) *MockUserActivitiesRepository_GetActivitiesByActivityType_Call {
	_c.Call.Return(run)
	return _c
}
