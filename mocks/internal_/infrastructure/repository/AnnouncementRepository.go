// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"
	"time"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockAnnouncementRepository creates a new instance of MockAnnouncementRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockAnnouncementRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockAnnouncementRepository {
	mock := &MockAnnouncementRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockAnnouncementRepository is an autogenerated mock type for the AnnouncementRepository type
type MockAnnouncementRepository struct {
	mock.Mock
}

type MockAnnouncementRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockAnnouncementRepository) EXPECT() *MockAnnouncementRepository_Expecter {
	return &MockAnnouncementRepository_Expecter{mock: &_m.Mock}
}

// CreateAnnouncement provides a mock function for the type MockAnnouncementRepository
func (_mock *MockAnnouncementRepository) CreateAnnouncement(ctx context.Context, announcement *models.Announcement) (primitive.ObjectID, error) {
	ret := _mock.Called(ctx, announcement)

	if len(ret) == 0 {
		panic("no return value specified for CreateAnnouncement")
	}

	var r0 primitive.ObjectID
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.Announcement) (primitive.ObjectID, error)); ok {
		return returnFunc(ctx, announcement)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.Announcement) primitive.ObjectID); ok {
		r0 = returnFunc(ctx, announcement)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(primitive.ObjectID)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.Announcement) error); ok {
		r1 = returnFunc(ctx, announcement)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockAnnouncementRepository_CreateAnnouncement_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateAnnouncement'
type MockAnnouncementRepository_CreateAnnouncement_Call struct {
	*mock.Call
}

// CreateAnnouncement is a helper method to define mock.On call
//   - ctx context.Context
//   - announcement *models.Announcement
func (_e *MockAnnouncementRepository_Expecter) CreateAnnouncement(ctx interface{}, announcement interface{}) *MockAnnouncementRepository_CreateAnnouncement_Call {
	return &MockAnnouncementRepository_CreateAnnouncement_Call{Call: _e.mock.On("CreateAnnouncement", ctx, announcement)}
}

func (_c *MockAnnouncementRepository_CreateAnnouncement_Call) Run(run func(ctx context.Context, announcement *models.Announcement)) *MockAnnouncementRepository_CreateAnnouncement_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.Announcement
		if args[1] != nil {
			arg1 = args[1].(*models.Announcement)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockAnnouncementRepository_CreateAnnouncement_Call) Return(objectID primitive.ObjectID, err error) *MockAnnouncementRepository_CreateAnnouncement_Call {
	_c.Call.Return(objectID, err)
	return _c
}

func (_c *MockAnnouncementRepository_CreateAnnouncement_Call) RunAndReturn(run func(ctx context.Context, announcement *models.Announcement) (primitive.ObjectID, error)) *MockAnnouncementRepository_CreateAnnouncement_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteAnnouncement provides a mock function for the type MockAnnouncementRepository
func (_mock *MockAnnouncementRepository) DeleteAnnouncement(ctx context.Context, id primitive.ObjectID) error {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteAnnouncement")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, id)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockAnnouncementRepository_DeleteAnnouncement_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteAnnouncement'
type MockAnnouncementRepository_DeleteAnnouncement_Call struct {
	*mock.Call
}

// DeleteAnnouncement is a helper method to define mock.On call
//   - ctx context.Context
//   - id primitive.ObjectID
func (_e *MockAnnouncementRepository_Expecter) DeleteAnnouncement(ctx interface{}, id interface{}) *MockAnnouncementRepository_DeleteAnnouncement_Call {
	return &MockAnnouncementRepository_DeleteAnnouncement_Call{Call: _e.mock.On("DeleteAnnouncement", ctx, id)}
}

func (_c *MockAnnouncementRepository_DeleteAnnouncement_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockAnnouncementRepository_DeleteAnnouncement_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockAnnouncementRepository_DeleteAnnouncement_Call) Return(err error) *MockAnnouncementRepository_DeleteAnnouncement_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockAnnouncementRepository_DeleteAnnouncement_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) error) *MockAnnouncementRepository_DeleteAnnouncement_Call {
	_c.Call.Return(run)
	return _c
}

// FindActiveAnnouncements provides a mock function for the type MockAnnouncementRepository
func (_mock *MockAnnouncementRepository) FindActiveAnnouncements(ctx context.Context, now time.Time) ([]*models.Announcement, error) {
	ret := _mock.Called(ctx, now)

	if len(ret) == 0 {
		panic("no return value specified for FindActiveAnnouncements")
	}

	var r0 []*models.Announcement
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, time.Time) ([]*models.Announcement, error)); ok {
		return returnFunc(ctx, now)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, time.Time) []*models.Announcement); ok {
		r0 = returnFunc(ctx, now)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.Announcement)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, time.Time) error); ok {
		r1 = returnFunc(ctx, now)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockAnnouncementRepository_FindActiveAnnouncements_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindActiveAnnouncements'
type MockAnnouncementRepository_FindActiveAnnouncements_Call struct {
	*mock.Call
}

// FindActiveAnnouncements is a helper method to define mock.On call
//   - ctx context.Context
//   - now time.Time
func (_e *MockAnnouncementRepository_Expecter) FindActiveAnnouncements(ctx interface{}, now interface{}) *MockAnnouncementRepository_FindActiveAnnouncements_Call {
	return &MockAnnouncementRepository_FindActiveAnnouncements_Call{Call: _e.mock.On("FindActiveAnnouncements", ctx, now)}
}

func (_c *MockAnnouncementRepository_FindActiveAnnouncements_Call) Run(run func(ctx context.Context, now time.Time)) *MockAnnouncementRepository_FindActiveAnnouncements_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 time.Time
		if args[1] != nil {
			arg1 = args[1].(time.Time)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockAnnouncementRepository_FindActiveAnnouncements_Call) Return(announcements []*models.Announcement, err error) *MockAnnouncementRepository_FindActiveAnnouncements_Call {
	_c.Call.Return(announcements, err)
	return _c
}

func (_c *MockAnnouncementRepository_FindActiveAnnouncements_Call) RunAndReturn(run func(ctx context.Context, now time.Time) ([]*models.Announcement, error)) *MockAnnouncementRepository_FindActiveAnnouncements_Call {
	_c.Call.Return(run)
	return _c
}

// FindAnnouncements provides a mock function for the type MockAnnouncementRepository
func (_mock *MockAnnouncementRepository) FindAnnouncements(ctx context.Context, filter map[string]interface{}, limit int64, offset int64) ([]*models.Announcement, error) {
	ret := _mock.Called(ctx, filter, limit, offset)

	if len(ret) == 0 {
		panic("no return value specified for FindAnnouncements")
	}

	var r0 []*models.Announcement
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, map[string]interface{}, int64, int64) ([]*models.Announcement, error)); ok {
		return returnFunc(ctx, filter, limit, offset)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, map[string]interface{}, int64, int64) []*models.Announcement); ok {
		r0 = returnFunc(ctx, filter, limit, offset)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.Announcement)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, map[string]interface{}, int64, int64) error); ok {
		r1 = returnFunc(ctx, filter, limit, offset)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockAnnouncementRepository_FindAnnouncements_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindAnnouncements'
type MockAnnouncementRepository_FindAnnouncements_Call struct {
	*mock.Call
}

// FindAnnouncements is a helper method to define mock.On call
//   - ctx context.Context
//   - filter map[string]interface{}
//   - limit int64
//   - offset int64
func (_e *MockAnnouncementRepository_Expecter) FindAnnouncements(ctx interface{}, filter interface{}, limit interface{}, offset interface{}) *MockAnnouncementRepository_FindAnnouncements_Call {
	return &MockAnnouncementRepository_FindAnnouncements_Call{Call: _e.mock.On("FindAnnouncements", ctx, filter, limit, offset)}
}

func (_c *MockAnnouncementRepository_FindAnnouncements_Call) Run(run func(ctx context.Context, filter map[string]interface{}, limit int64, offset int64)) *MockAnnouncementRepository_FindAnnouncements_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 map[string]interface{}
		if args[1] != nil {
			arg1 = args[1].(map[string]interface{})
		}
		var arg2 int64
		if args[2] != nil {
			arg2 = args[2].(int64)
		}
		var arg3 int64
		if args[3] != nil {
			arg3 = args[3].(int64)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockAnnouncementRepository_FindAnnouncements_Call) Return(announcements []*models.Announcement, err error) *MockAnnouncementRepository_FindAnnouncements_Call {
	_c.Call.Return(announcements, err)
	return _c
}

func (_c *MockAnnouncementRepository_FindAnnouncements_Call) RunAndReturn(run func(ctx context.Context, filter map[string]interface{}, limit int64, offset int64) ([]*models.Announcement, error)) *MockAnnouncementRepository_FindAnnouncements_Call {
	_c.Call.Return(run)
	return _c
}

// GetAnnouncementByID provides a mock function for the type MockAnnouncementRepository
func (_mock *MockAnnouncementRepository) GetAnnouncementByID(ctx context.Context, id primitive.ObjectID) (*models.Announcement, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetAnnouncementByID")
	}

	var r0 *models.Announcement
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.Announcement, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.Announcement); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Announcement)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockAnnouncementRepository_GetAnnouncementByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAnnouncementByID'
type MockAnnouncementRepository_GetAnnouncementByID_Call struct {
	*mock.Call
}

// GetAnnouncementByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id primitive.ObjectID
func (_e *MockAnnouncementRepository_Expecter) GetAnnouncementByID(ctx interface{}, id interface{}) *MockAnnouncementRepository_GetAnnouncementByID_Call {
	return &MockAnnouncementRepository_GetAnnouncementByID_Call{Call: _e.mock.On("GetAnnouncementByID", ctx, id)}
}

func (_c *MockAnnouncementRepository_GetAnnouncementByID_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockAnnouncementRepository_GetAnnouncementByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockAnnouncementRepository_GetAnnouncementByID_Call) Return(announcement *models.Announcement, err error) *MockAnnouncementRepository_GetAnnouncementByID_Call {
	_c.Call.Return(announcement, err)
	return _c
}

func (_c *MockAnnouncementRepository_GetAnnouncementByID_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (*models.Announcement, error)) *MockAnnouncementRepository_GetAnnouncementByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetReadAnnouncementIDs provides a mock function for the type MockAnnouncementRepository
func (_mock *MockAnnouncementRepository) GetReadAnnouncementIDs(ctx context.Context, userID primitive.ObjectID) (map[primitive.ObjectID]bool, error) {
	ret := _mock.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetReadAnnouncementIDs")
	}

	var r0 map[primitive.ObjectID]bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (map[primitive.ObjectID]bool, error)); ok {
		return returnFunc(ctx, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) map[primitive.ObjectID]bool); ok {
		r0 = returnFunc(ctx, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[primitive.ObjectID]bool)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockAnnouncementRepository_GetReadAnnouncementIDs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetReadAnnouncementIDs'
type MockAnnouncementRepository_GetReadAnnouncementIDs_Call struct {
	*mock.Call
}

// GetReadAnnouncementIDs is a helper method to define mock.On call
//   - ctx context.Context
//   - userID primitive.ObjectID
func (_e *MockAnnouncementRepository_Expecter) GetReadAnnouncementIDs(ctx interface{}, userID interface{}) *MockAnnouncementRepository_GetReadAnnouncementIDs_Call {
	return &MockAnnouncementRepository_GetReadAnnouncementIDs_Call{Call: _e.mock.On("GetReadAnnouncementIDs", ctx, userID)}
}

func (_c *MockAnnouncementRepository_GetReadAnnouncementIDs_Call) Run(run func(ctx context.Context, userID primitive.ObjectID)) *MockAnnouncementRepository_GetReadAnnouncementIDs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockAnnouncementRepository_GetReadAnnouncementIDs_Call) Return(objectIDToBool map[primitive.ObjectID]bool, err error) *MockAnnouncementRepository_GetReadAnnouncementIDs_Call {
	_c.Call.Return(objectIDToBool, err)
	return _c
}

func (_c *MockAnnouncementRepository_GetReadAnnouncementIDs_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID) (map[primitive.ObjectID]bool, error)) *MockAnnouncementRepository_GetReadAnnouncementIDs_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserReadStatus provides a mock function for the type MockAnnouncementRepository
func (_mock *MockAnnouncementRepository) GetUserReadStatus(ctx context.Context, userID primitive.ObjectID, announcementID primitive.ObjectID) (*models.UserAnnouncementStatus, error) {
	ret := _mock.Called(ctx, userID, announcementID)

	if len(ret) == 0 {
		panic("no return value specified for GetUserReadStatus")
	}

	var r0 *models.UserAnnouncementStatus
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) (*models.UserAnnouncementStatus, error)); ok {
		return returnFunc(ctx, userID, announcementID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) *models.UserAnnouncementStatus); ok {
		r0 = returnFunc(ctx, userID, announcementID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserAnnouncementStatus)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, userID, announcementID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockAnnouncementRepository_GetUserReadStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserReadStatus'
type MockAnnouncementRepository_GetUserReadStatus_Call struct {
	*mock.Call
}

// GetUserReadStatus is a helper method to define mock.On call
//   - ctx context.Context
//   - userID primitive.ObjectID
//   - announcementID primitive.ObjectID
func (_e *MockAnnouncementRepository_Expecter) GetUserReadStatus(ctx interface{}, userID interface{}, announcementID interface{}) *MockAnnouncementRepository_GetUserReadStatus_Call {
	return &MockAnnouncementRepository_GetUserReadStatus_Call{Call: _e.mock.On("GetUserReadStatus", ctx, userID, announcementID)}
}

func (_c *MockAnnouncementRepository_GetUserReadStatus_Call) Run(run func(ctx context.Context, userID primitive.ObjectID, announcementID primitive.ObjectID)) *MockAnnouncementRepository_GetUserReadStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockAnnouncementRepository_GetUserReadStatus_Call) Return(userAnnouncementStatus *models.UserAnnouncementStatus, err error) *MockAnnouncementRepository_GetUserReadStatus_Call {
	_c.Call.Return(userAnnouncementStatus, err)
	return _c
}

func (_c *MockAnnouncementRepository_GetUserReadStatus_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID, announcementID primitive.ObjectID) (*models.UserAnnouncementStatus, error)) *MockAnnouncementRepository_GetUserReadStatus_Call {
	_c.Call.Return(run)
	return _c
}

// MarkAsRead provides a mock function for the type MockAnnouncementRepository
func (_mock *MockAnnouncementRepository) MarkAsRead(ctx context.Context, userID primitive.ObjectID, announcementID primitive.ObjectID) error {
	ret := _mock.Called(ctx, userID, announcementID)

	if len(ret) == 0 {
		panic("no return value specified for MarkAsRead")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, userID, announcementID)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockAnnouncementRepository_MarkAsRead_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'MarkAsRead'
type MockAnnouncementRepository_MarkAsRead_Call struct {
	*mock.Call
}

// MarkAsRead is a helper method to define mock.On call
//   - ctx context.Context
//   - userID primitive.ObjectID
//   - announcementID primitive.ObjectID
func (_e *MockAnnouncementRepository_Expecter) MarkAsRead(ctx interface{}, userID interface{}, announcementID interface{}) *MockAnnouncementRepository_MarkAsRead_Call {
	return &MockAnnouncementRepository_MarkAsRead_Call{Call: _e.mock.On("MarkAsRead", ctx, userID, announcementID)}
}

func (_c *MockAnnouncementRepository_MarkAsRead_Call) Run(run func(ctx context.Context, userID primitive.ObjectID, announcementID primitive.ObjectID)) *MockAnnouncementRepository_MarkAsRead_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockAnnouncementRepository_MarkAsRead_Call) Return(err error) *MockAnnouncementRepository_MarkAsRead_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockAnnouncementRepository_MarkAsRead_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID, announcementID primitive.ObjectID) error) *MockAnnouncementRepository_MarkAsRead_Call {
	_c.Call.Return(run)
	return _c
}

// MarkMultipleAsRead provides a mock function for the type MockAnnouncementRepository
func (_mock *MockAnnouncementRepository) MarkMultipleAsRead(ctx context.Context, userID primitive.ObjectID, announcementIDs []primitive.ObjectID) error {
	ret := _mock.Called(ctx, userID, announcementIDs)

	if len(ret) == 0 {
		panic("no return value specified for MarkMultipleAsRead")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, []primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, userID, announcementIDs)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockAnnouncementRepository_MarkMultipleAsRead_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'MarkMultipleAsRead'
type MockAnnouncementRepository_MarkMultipleAsRead_Call struct {
	*mock.Call
}

// MarkMultipleAsRead is a helper method to define mock.On call
//   - ctx context.Context
//   - userID primitive.ObjectID
//   - announcementIDs []primitive.ObjectID
func (_e *MockAnnouncementRepository_Expecter) MarkMultipleAsRead(ctx interface{}, userID interface{}, announcementIDs interface{}) *MockAnnouncementRepository_MarkMultipleAsRead_Call {
	return &MockAnnouncementRepository_MarkMultipleAsRead_Call{Call: _e.mock.On("MarkMultipleAsRead", ctx, userID, announcementIDs)}
}

func (_c *MockAnnouncementRepository_MarkMultipleAsRead_Call) Run(run func(ctx context.Context, userID primitive.ObjectID, announcementIDs []primitive.ObjectID)) *MockAnnouncementRepository_MarkMultipleAsRead_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 []primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].([]primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockAnnouncementRepository_MarkMultipleAsRead_Call) Return(err error) *MockAnnouncementRepository_MarkMultipleAsRead_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockAnnouncementRepository_MarkMultipleAsRead_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID, announcementIDs []primitive.ObjectID) error) *MockAnnouncementRepository_MarkMultipleAsRead_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateAnnouncement provides a mock function for the type MockAnnouncementRepository
func (_mock *MockAnnouncementRepository) UpdateAnnouncement(ctx context.Context, id primitive.ObjectID, updates bson.M) error {
	ret := _mock.Called(ctx, id, updates)

	if len(ret) == 0 {
		panic("no return value specified for UpdateAnnouncement")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, bson.M) error); ok {
		r0 = returnFunc(ctx, id, updates)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockAnnouncementRepository_UpdateAnnouncement_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateAnnouncement'
type MockAnnouncementRepository_UpdateAnnouncement_Call struct {
	*mock.Call
}

// UpdateAnnouncement is a helper method to define mock.On call
//   - ctx context.Context
//   - id primitive.ObjectID
//   - updates bson.M
func (_e *MockAnnouncementRepository_Expecter) UpdateAnnouncement(ctx interface{}, id interface{}, updates interface{}) *MockAnnouncementRepository_UpdateAnnouncement_Call {
	return &MockAnnouncementRepository_UpdateAnnouncement_Call{Call: _e.mock.On("UpdateAnnouncement", ctx, id, updates)}
}

func (_c *MockAnnouncementRepository_UpdateAnnouncement_Call) Run(run func(ctx context.Context, id primitive.ObjectID, updates bson.M)) *MockAnnouncementRepository_UpdateAnnouncement_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 bson.M
		if args[2] != nil {
			arg2 = args[2].(bson.M)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockAnnouncementRepository_UpdateAnnouncement_Call) Return(err error) *MockAnnouncementRepository_UpdateAnnouncement_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockAnnouncementRepository_UpdateAnnouncement_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID, updates bson.M) error) *MockAnnouncementRepository_UpdateAnnouncement_Call {
	_c.Call.Return(run)
	return _c
}
