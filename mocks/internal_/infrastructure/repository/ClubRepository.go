// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// NewMockClubRepository creates a new instance of MockClubRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockClubRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockClubRepository {
	mock := &MockClubRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockClubRepository is an autogenerated mock type for the ClubRepository type
type MockClubRepository struct {
	mock.Mock
}

type MockClubRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockClubRepository) EXPECT() *MockClubRepository_Expecter {
	return &MockClubRepository_Expecter{mock: &_m.Mock}
}

// CountClubs provides a mock function for the type MockClubRepository
func (_mock *MockClubRepository) CountClubs(ctx context.Context, filter bson.M) (int64, error) {
	ret := _mock.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for CountClubs")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M) (int64, error)); ok {
		return returnFunc(ctx, filter)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M) int64); ok {
		r0 = returnFunc(ctx, filter)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, bson.M) error); ok {
		r1 = returnFunc(ctx, filter)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubRepository_CountClubs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountClubs'
type MockClubRepository_CountClubs_Call struct {
	*mock.Call
}

// CountClubs is a helper method to define mock.On call
//   - ctx context.Context
//   - filter bson.M
func (_e *MockClubRepository_Expecter) CountClubs(ctx interface{}, filter interface{}) *MockClubRepository_CountClubs_Call {
	return &MockClubRepository_CountClubs_Call{Call: _e.mock.On("CountClubs", ctx, filter)}
}

func (_c *MockClubRepository_CountClubs_Call) Run(run func(ctx context.Context, filter bson.M)) *MockClubRepository_CountClubs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 bson.M
		if args[1] != nil {
			arg1 = args[1].(bson.M)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockClubRepository_CountClubs_Call) Return(n int64, err error) *MockClubRepository_CountClubs_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockClubRepository_CountClubs_Call) RunAndReturn(run func(ctx context.Context, filter bson.M) (int64, error)) *MockClubRepository_CountClubs_Call {
	_c.Call.Return(run)
	return _c
}

// CreateClub provides a mock function for the type MockClubRepository
func (_mock *MockClubRepository) CreateClub(ctx context.Context, club *models.Club) error {
	ret := _mock.Called(ctx, club)

	if len(ret) == 0 {
		panic("no return value specified for CreateClub")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.Club) error); ok {
		r0 = returnFunc(ctx, club)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockClubRepository_CreateClub_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateClub'
type MockClubRepository_CreateClub_Call struct {
	*mock.Call
}

// CreateClub is a helper method to define mock.On call
//   - ctx context.Context
//   - club *models.Club
func (_e *MockClubRepository_Expecter) CreateClub(ctx interface{}, club interface{}) *MockClubRepository_CreateClub_Call {
	return &MockClubRepository_CreateClub_Call{Call: _e.mock.On("CreateClub", ctx, club)}
}

func (_c *MockClubRepository_CreateClub_Call) Run(run func(ctx context.Context, club *models.Club)) *MockClubRepository_CreateClub_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.Club
		if args[1] != nil {
			arg1 = args[1].(*models.Club)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockClubRepository_CreateClub_Call) Return(err error) *MockClubRepository_CreateClub_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockClubRepository_CreateClub_Call) RunAndReturn(run func(ctx context.Context, club *models.Club) error) *MockClubRepository_CreateClub_Call {
	_c.Call.Return(run)
	return _c
}

// DecrementEventCount provides a mock function for the type MockClubRepository
func (_mock *MockClubRepository) DecrementEventCount(ctx context.Context, clubID primitive.ObjectID) error {
	ret := _mock.Called(ctx, clubID)

	if len(ret) == 0 {
		panic("no return value specified for DecrementEventCount")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, clubID)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockClubRepository_DecrementEventCount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DecrementEventCount'
type MockClubRepository_DecrementEventCount_Call struct {
	*mock.Call
}

// DecrementEventCount is a helper method to define mock.On call
//   - ctx context.Context
//   - clubID primitive.ObjectID
func (_e *MockClubRepository_Expecter) DecrementEventCount(ctx interface{}, clubID interface{}) *MockClubRepository_DecrementEventCount_Call {
	return &MockClubRepository_DecrementEventCount_Call{Call: _e.mock.On("DecrementEventCount", ctx, clubID)}
}

func (_c *MockClubRepository_DecrementEventCount_Call) Run(run func(ctx context.Context, clubID primitive.ObjectID)) *MockClubRepository_DecrementEventCount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockClubRepository_DecrementEventCount_Call) Return(err error) *MockClubRepository_DecrementEventCount_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockClubRepository_DecrementEventCount_Call) RunAndReturn(run func(ctx context.Context, clubID primitive.ObjectID) error) *MockClubRepository_DecrementEventCount_Call {
	_c.Call.Return(run)
	return _c
}

// DecrementMemberCount provides a mock function for the type MockClubRepository
func (_mock *MockClubRepository) DecrementMemberCount(ctx context.Context, clubID primitive.ObjectID) error {
	ret := _mock.Called(ctx, clubID)

	if len(ret) == 0 {
		panic("no return value specified for DecrementMemberCount")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, clubID)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockClubRepository_DecrementMemberCount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DecrementMemberCount'
type MockClubRepository_DecrementMemberCount_Call struct {
	*mock.Call
}

// DecrementMemberCount is a helper method to define mock.On call
//   - ctx context.Context
//   - clubID primitive.ObjectID
func (_e *MockClubRepository_Expecter) DecrementMemberCount(ctx interface{}, clubID interface{}) *MockClubRepository_DecrementMemberCount_Call {
	return &MockClubRepository_DecrementMemberCount_Call{Call: _e.mock.On("DecrementMemberCount", ctx, clubID)}
}

func (_c *MockClubRepository_DecrementMemberCount_Call) Run(run func(ctx context.Context, clubID primitive.ObjectID)) *MockClubRepository_DecrementMemberCount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockClubRepository_DecrementMemberCount_Call) Return(err error) *MockClubRepository_DecrementMemberCount_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockClubRepository_DecrementMemberCount_Call) RunAndReturn(run func(ctx context.Context, clubID primitive.ObjectID) error) *MockClubRepository_DecrementMemberCount_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteClub provides a mock function for the type MockClubRepository
func (_mock *MockClubRepository) DeleteClub(ctx context.Context, id primitive.ObjectID) error {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteClub")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, id)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockClubRepository_DeleteClub_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteClub'
type MockClubRepository_DeleteClub_Call struct {
	*mock.Call
}

// DeleteClub is a helper method to define mock.On call
//   - ctx context.Context
//   - id primitive.ObjectID
func (_e *MockClubRepository_Expecter) DeleteClub(ctx interface{}, id interface{}) *MockClubRepository_DeleteClub_Call {
	return &MockClubRepository_DeleteClub_Call{Call: _e.mock.On("DeleteClub", ctx, id)}
}

func (_c *MockClubRepository_DeleteClub_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockClubRepository_DeleteClub_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockClubRepository_DeleteClub_Call) Return(err error) *MockClubRepository_DeleteClub_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockClubRepository_DeleteClub_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) error) *MockClubRepository_DeleteClub_Call {
	_c.Call.Return(run)
	return _c
}

// FindClubByID provides a mock function for the type MockClubRepository
func (_mock *MockClubRepository) FindClubByID(ctx context.Context, id primitive.ObjectID) (*models.Club, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for FindClubByID")
	}

	var r0 *models.Club
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.Club, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.Club); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Club)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubRepository_FindClubByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindClubByID'
type MockClubRepository_FindClubByID_Call struct {
	*mock.Call
}

// FindClubByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id primitive.ObjectID
func (_e *MockClubRepository_Expecter) FindClubByID(ctx interface{}, id interface{}) *MockClubRepository_FindClubByID_Call {
	return &MockClubRepository_FindClubByID_Call{Call: _e.mock.On("FindClubByID", ctx, id)}
}

func (_c *MockClubRepository_FindClubByID_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockClubRepository_FindClubByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockClubRepository_FindClubByID_Call) Return(club *models.Club, err error) *MockClubRepository_FindClubByID_Call {
	_c.Call.Return(club, err)
	return _c
}

func (_c *MockClubRepository_FindClubByID_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (*models.Club, error)) *MockClubRepository_FindClubByID_Call {
	_c.Call.Return(run)
	return _c
}

// IncrementEventCount provides a mock function for the type MockClubRepository
func (_mock *MockClubRepository) IncrementEventCount(ctx context.Context, clubID primitive.ObjectID) error {
	ret := _mock.Called(ctx, clubID)

	if len(ret) == 0 {
		panic("no return value specified for IncrementEventCount")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, clubID)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockClubRepository_IncrementEventCount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IncrementEventCount'
type MockClubRepository_IncrementEventCount_Call struct {
	*mock.Call
}

// IncrementEventCount is a helper method to define mock.On call
//   - ctx context.Context
//   - clubID primitive.ObjectID
func (_e *MockClubRepository_Expecter) IncrementEventCount(ctx interface{}, clubID interface{}) *MockClubRepository_IncrementEventCount_Call {
	return &MockClubRepository_IncrementEventCount_Call{Call: _e.mock.On("IncrementEventCount", ctx, clubID)}
}

func (_c *MockClubRepository_IncrementEventCount_Call) Run(run func(ctx context.Context, clubID primitive.ObjectID)) *MockClubRepository_IncrementEventCount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockClubRepository_IncrementEventCount_Call) Return(err error) *MockClubRepository_IncrementEventCount_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockClubRepository_IncrementEventCount_Call) RunAndReturn(run func(ctx context.Context, clubID primitive.ObjectID) error) *MockClubRepository_IncrementEventCount_Call {
	_c.Call.Return(run)
	return _c
}

// IncrementMemberCount provides a mock function for the type MockClubRepository
func (_mock *MockClubRepository) IncrementMemberCount(ctx context.Context, clubID primitive.ObjectID) error {
	ret := _mock.Called(ctx, clubID)

	if len(ret) == 0 {
		panic("no return value specified for IncrementMemberCount")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, clubID)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockClubRepository_IncrementMemberCount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IncrementMemberCount'
type MockClubRepository_IncrementMemberCount_Call struct {
	*mock.Call
}

// IncrementMemberCount is a helper method to define mock.On call
//   - ctx context.Context
//   - clubID primitive.ObjectID
func (_e *MockClubRepository_Expecter) IncrementMemberCount(ctx interface{}, clubID interface{}) *MockClubRepository_IncrementMemberCount_Call {
	return &MockClubRepository_IncrementMemberCount_Call{Call: _e.mock.On("IncrementMemberCount", ctx, clubID)}
}

func (_c *MockClubRepository_IncrementMemberCount_Call) Run(run func(ctx context.Context, clubID primitive.ObjectID)) *MockClubRepository_IncrementMemberCount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockClubRepository_IncrementMemberCount_Call) Return(err error) *MockClubRepository_IncrementMemberCount_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockClubRepository_IncrementMemberCount_Call) RunAndReturn(run func(ctx context.Context, clubID primitive.ObjectID) error) *MockClubRepository_IncrementMemberCount_Call {
	_c.Call.Return(run)
	return _c
}

// ListClubs provides a mock function for the type MockClubRepository
func (_mock *MockClubRepository) ListClubs(ctx context.Context, filter bson.M, opts *options.FindOptions) ([]*models.Club, error) {
	ret := _mock.Called(ctx, filter, opts)

	if len(ret) == 0 {
		panic("no return value specified for ListClubs")
	}

	var r0 []*models.Club
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, *options.FindOptions) ([]*models.Club, error)); ok {
		return returnFunc(ctx, filter, opts)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, *options.FindOptions) []*models.Club); ok {
		r0 = returnFunc(ctx, filter, opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.Club)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, bson.M, *options.FindOptions) error); ok {
		r1 = returnFunc(ctx, filter, opts)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubRepository_ListClubs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListClubs'
type MockClubRepository_ListClubs_Call struct {
	*mock.Call
}

// ListClubs is a helper method to define mock.On call
//   - ctx context.Context
//   - filter bson.M
//   - opts *options.FindOptions
func (_e *MockClubRepository_Expecter) ListClubs(ctx interface{}, filter interface{}, opts interface{}) *MockClubRepository_ListClubs_Call {
	return &MockClubRepository_ListClubs_Call{Call: _e.mock.On("ListClubs", ctx, filter, opts)}
}

func (_c *MockClubRepository_ListClubs_Call) Run(run func(ctx context.Context, filter bson.M, opts *options.FindOptions)) *MockClubRepository_ListClubs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 bson.M
		if args[1] != nil {
			arg1 = args[1].(bson.M)
		}
		var arg2 *options.FindOptions
		if args[2] != nil {
			arg2 = args[2].(*options.FindOptions)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockClubRepository_ListClubs_Call) Return(clubs []*models.Club, err error) *MockClubRepository_ListClubs_Call {
	_c.Call.Return(clubs, err)
	return _c
}

func (_c *MockClubRepository_ListClubs_Call) RunAndReturn(run func(ctx context.Context, filter bson.M, opts *options.FindOptions) ([]*models.Club, error)) *MockClubRepository_ListClubs_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateClub provides a mock function for the type MockClubRepository
func (_mock *MockClubRepository) UpdateClub(ctx context.Context, id primitive.ObjectID, update bson.M) error {
	ret := _mock.Called(ctx, id, update)

	if len(ret) == 0 {
		panic("no return value specified for UpdateClub")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, bson.M) error); ok {
		r0 = returnFunc(ctx, id, update)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockClubRepository_UpdateClub_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateClub'
type MockClubRepository_UpdateClub_Call struct {
	*mock.Call
}

// UpdateClub is a helper method to define mock.On call
//   - ctx context.Context
//   - id primitive.ObjectID
//   - update bson.M
func (_e *MockClubRepository_Expecter) UpdateClub(ctx interface{}, id interface{}, update interface{}) *MockClubRepository_UpdateClub_Call {
	return &MockClubRepository_UpdateClub_Call{Call: _e.mock.On("UpdateClub", ctx, id, update)}
}

func (_c *MockClubRepository_UpdateClub_Call) Run(run func(ctx context.Context, id primitive.ObjectID, update bson.M)) *MockClubRepository_UpdateClub_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 bson.M
		if args[2] != nil {
			arg2 = args[2].(bson.M)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockClubRepository_UpdateClub_Call) Return(err error) *MockClubRepository_UpdateClub_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockClubRepository_UpdateClub_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID, update bson.M) error) *MockClubRepository_UpdateClub_Call {
	_c.Call.Return(run)
	return _c
}
