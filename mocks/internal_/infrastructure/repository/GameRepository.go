// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// NewMockGameRepository creates a new instance of MockGameRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockGameRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockGameRepository {
	mock := &MockGameRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockGameRepository is an autogenerated mock type for the GameRepository type
type MockGameRepository struct {
	mock.Mock
}

type MockGameRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockGameRepository) EXPECT() *MockGameRepository_Expecter {
	return &MockGameRepository_Expecter{mock: &_m.Mock}
}

// CancelGames provides a mock function for the type MockGameRepository
func (_mock *MockGameRepository) CancelGames(ctx context.Context, gameIDs []primitive.ObjectID) error {
	ret := _mock.Called(ctx, gameIDs)

	if len(ret) == 0 {
		panic("no return value specified for CancelGames")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, []primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, gameIDs)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockGameRepository_CancelGames_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CancelGames'
type MockGameRepository_CancelGames_Call struct {
	*mock.Call
}

// CancelGames is a helper method to define mock.On call
//   - ctx context.Context
//   - gameIDs []primitive.ObjectID
func (_e *MockGameRepository_Expecter) CancelGames(ctx interface{}, gameIDs interface{}) *MockGameRepository_CancelGames_Call {
	return &MockGameRepository_CancelGames_Call{Call: _e.mock.On("CancelGames", ctx, gameIDs)}
}

func (_c *MockGameRepository_CancelGames_Call) Run(run func(ctx context.Context, gameIDs []primitive.ObjectID)) *MockGameRepository_CancelGames_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 []primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].([]primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockGameRepository_CancelGames_Call) Return(err error) *MockGameRepository_CancelGames_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockGameRepository_CancelGames_Call) RunAndReturn(run func(ctx context.Context, gameIDs []primitive.ObjectID) error) *MockGameRepository_CancelGames_Call {
	_c.Call.Return(run)
	return _c
}

// CreateGame provides a mock function for the type MockGameRepository
func (_mock *MockGameRepository) CreateGame(ctx context.Context, game *models.Game) (*models.Game, error) {
	ret := _mock.Called(ctx, game)

	if len(ret) == 0 {
		panic("no return value specified for CreateGame")
	}

	var r0 *models.Game
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.Game) (*models.Game, error)); ok {
		return returnFunc(ctx, game)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.Game) *models.Game); ok {
		r0 = returnFunc(ctx, game)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Game)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.Game) error); ok {
		r1 = returnFunc(ctx, game)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameRepository_CreateGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateGame'
type MockGameRepository_CreateGame_Call struct {
	*mock.Call
}

// CreateGame is a helper method to define mock.On call
//   - ctx context.Context
//   - game *models.Game
func (_e *MockGameRepository_Expecter) CreateGame(ctx interface{}, game interface{}) *MockGameRepository_CreateGame_Call {
	return &MockGameRepository_CreateGame_Call{Call: _e.mock.On("CreateGame", ctx, game)}
}

func (_c *MockGameRepository_CreateGame_Call) Run(run func(ctx context.Context, game *models.Game)) *MockGameRepository_CreateGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.Game
		if args[1] != nil {
			arg1 = args[1].(*models.Game)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockGameRepository_CreateGame_Call) Return(game1 *models.Game, err error) *MockGameRepository_CreateGame_Call {
	_c.Call.Return(game1, err)
	return _c
}

func (_c *MockGameRepository_CreateGame_Call) RunAndReturn(run func(ctx context.Context, game *models.Game) (*models.Game, error)) *MockGameRepository_CreateGame_Call {
	_c.Call.Return(run)
	return _c
}

// CreateManyGames provides a mock function for the type MockGameRepository
func (_mock *MockGameRepository) CreateManyGames(ctx context.Context, games []*models.Game) error {
	ret := _mock.Called(ctx, games)

	if len(ret) == 0 {
		panic("no return value specified for CreateManyGames")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, []*models.Game) error); ok {
		r0 = returnFunc(ctx, games)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockGameRepository_CreateManyGames_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateManyGames'
type MockGameRepository_CreateManyGames_Call struct {
	*mock.Call
}

// CreateManyGames is a helper method to define mock.On call
//   - ctx context.Context
//   - games []*models.Game
func (_e *MockGameRepository_Expecter) CreateManyGames(ctx interface{}, games interface{}) *MockGameRepository_CreateManyGames_Call {
	return &MockGameRepository_CreateManyGames_Call{Call: _e.mock.On("CreateManyGames", ctx, games)}
}

func (_c *MockGameRepository_CreateManyGames_Call) Run(run func(ctx context.Context, games []*models.Game)) *MockGameRepository_CreateManyGames_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 []*models.Game
		if args[1] != nil {
			arg1 = args[1].([]*models.Game)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockGameRepository_CreateManyGames_Call) Return(err error) *MockGameRepository_CreateManyGames_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockGameRepository_CreateManyGames_Call) RunAndReturn(run func(ctx context.Context, games []*models.Game) error) *MockGameRepository_CreateManyGames_Call {
	_c.Call.Return(run)
	return _c
}

// EstimatedDocumentCount provides a mock function for the type MockGameRepository
func (_mock *MockGameRepository) EstimatedDocumentCount(ctx context.Context) (int64, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for EstimatedDocumentCount")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (int64, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) int64); ok {
		r0 = returnFunc(ctx)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameRepository_EstimatedDocumentCount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'EstimatedDocumentCount'
type MockGameRepository_EstimatedDocumentCount_Call struct {
	*mock.Call
}

// EstimatedDocumentCount is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockGameRepository_Expecter) EstimatedDocumentCount(ctx interface{}) *MockGameRepository_EstimatedDocumentCount_Call {
	return &MockGameRepository_EstimatedDocumentCount_Call{Call: _e.mock.On("EstimatedDocumentCount", ctx)}
}

func (_c *MockGameRepository_EstimatedDocumentCount_Call) Run(run func(ctx context.Context)) *MockGameRepository_EstimatedDocumentCount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockGameRepository_EstimatedDocumentCount_Call) Return(n int64, err error) *MockGameRepository_EstimatedDocumentCount_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockGameRepository_EstimatedDocumentCount_Call) RunAndReturn(run func(ctx context.Context) (int64, error)) *MockGameRepository_EstimatedDocumentCount_Call {
	_c.Call.Return(run)
	return _c
}

// Find provides a mock function for the type MockGameRepository
func (_mock *MockGameRepository) Find(ctx context.Context, filter bson.M, opts ...*options.FindOptions) ([]*models.Game, error) {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, filter, opts)
	} else {
		tmpRet = _mock.Called(ctx, filter)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for Find")
	}

	var r0 []*models.Game
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, ...*options.FindOptions) ([]*models.Game, error)); ok {
		return returnFunc(ctx, filter, opts...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, ...*options.FindOptions) []*models.Game); ok {
		r0 = returnFunc(ctx, filter, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.Game)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, bson.M, ...*options.FindOptions) error); ok {
		r1 = returnFunc(ctx, filter, opts...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameRepository_Find_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Find'
type MockGameRepository_Find_Call struct {
	*mock.Call
}

// Find is a helper method to define mock.On call
//   - ctx context.Context
//   - filter bson.M
//   - opts ...*options.FindOptions
func (_e *MockGameRepository_Expecter) Find(ctx interface{}, filter interface{}, opts ...interface{}) *MockGameRepository_Find_Call {
	return &MockGameRepository_Find_Call{Call: _e.mock.On("Find",
		append([]interface{}{ctx, filter}, opts...)...)}
}

func (_c *MockGameRepository_Find_Call) Run(run func(ctx context.Context, filter bson.M, opts ...*options.FindOptions)) *MockGameRepository_Find_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 bson.M
		if args[1] != nil {
			arg1 = args[1].(bson.M)
		}
		var arg2 []*options.FindOptions
		var variadicArgs []*options.FindOptions
		if len(args) > 2 {
			variadicArgs = args[2].([]*options.FindOptions)
		}
		arg2 = variadicArgs
		run(
			arg0,
			arg1,
			arg2...,
		)
	})
	return _c
}

func (_c *MockGameRepository_Find_Call) Return(games []*models.Game, err error) *MockGameRepository_Find_Call {
	_c.Call.Return(games, err)
	return _c
}

func (_c *MockGameRepository_Find_Call) RunAndReturn(run func(ctx context.Context, filter bson.M, opts ...*options.FindOptions) ([]*models.Game, error)) *MockGameRepository_Find_Call {
	_c.Call.Return(run)
	return _c
}

// FindActiveGamesByUserID provides a mock function for the type MockGameRepository
func (_mock *MockGameRepository) FindActiveGamesByUserID(ctx context.Context, userID primitive.ObjectID) ([]models.Game, error) {
	ret := _mock.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for FindActiveGamesByUserID")
	}

	var r0 []models.Game
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) ([]models.Game, error)); ok {
		return returnFunc(ctx, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) []models.Game); ok {
		r0 = returnFunc(ctx, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.Game)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameRepository_FindActiveGamesByUserID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindActiveGamesByUserID'
type MockGameRepository_FindActiveGamesByUserID_Call struct {
	*mock.Call
}

// FindActiveGamesByUserID is a helper method to define mock.On call
//   - ctx context.Context
//   - userID primitive.ObjectID
func (_e *MockGameRepository_Expecter) FindActiveGamesByUserID(ctx interface{}, userID interface{}) *MockGameRepository_FindActiveGamesByUserID_Call {
	return &MockGameRepository_FindActiveGamesByUserID_Call{Call: _e.mock.On("FindActiveGamesByUserID", ctx, userID)}
}

func (_c *MockGameRepository_FindActiveGamesByUserID_Call) Run(run func(ctx context.Context, userID primitive.ObjectID)) *MockGameRepository_FindActiveGamesByUserID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockGameRepository_FindActiveGamesByUserID_Call) Return(games []models.Game, err error) *MockGameRepository_FindActiveGamesByUserID_Call {
	_c.Call.Return(games, err)
	return _c
}

func (_c *MockGameRepository_FindActiveGamesByUserID_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID) ([]models.Game, error)) *MockGameRepository_FindActiveGamesByUserID_Call {
	_c.Call.Return(run)
	return _c
}

// FindOne provides a mock function for the type MockGameRepository
func (_mock *MockGameRepository) FindOne(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions) (*models.Game, error) {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, filter, opts)
	} else {
		tmpRet = _mock.Called(ctx, filter)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for FindOne")
	}

	var r0 *models.Game
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, ...*options.FindOneOptions) (*models.Game, error)); ok {
		return returnFunc(ctx, filter, opts...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, ...*options.FindOneOptions) *models.Game); ok {
		r0 = returnFunc(ctx, filter, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Game)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, bson.M, ...*options.FindOneOptions) error); ok {
		r1 = returnFunc(ctx, filter, opts...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameRepository_FindOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindOne'
type MockGameRepository_FindOne_Call struct {
	*mock.Call
}

// FindOne is a helper method to define mock.On call
//   - ctx context.Context
//   - filter bson.M
//   - opts ...*options.FindOneOptions
func (_e *MockGameRepository_Expecter) FindOne(ctx interface{}, filter interface{}, opts ...interface{}) *MockGameRepository_FindOne_Call {
	return &MockGameRepository_FindOne_Call{Call: _e.mock.On("FindOne",
		append([]interface{}{ctx, filter}, opts...)...)}
}

func (_c *MockGameRepository_FindOne_Call) Run(run func(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions)) *MockGameRepository_FindOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 bson.M
		if args[1] != nil {
			arg1 = args[1].(bson.M)
		}
		var arg2 []*options.FindOneOptions
		var variadicArgs []*options.FindOneOptions
		if len(args) > 2 {
			variadicArgs = args[2].([]*options.FindOneOptions)
		}
		arg2 = variadicArgs
		run(
			arg0,
			arg1,
			arg2...,
		)
	})
	return _c
}

func (_c *MockGameRepository_FindOne_Call) Return(game *models.Game, err error) *MockGameRepository_FindOne_Call {
	_c.Call.Return(game, err)
	return _c
}

func (_c *MockGameRepository_FindOne_Call) RunAndReturn(run func(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions) (*models.Game, error)) *MockGameRepository_FindOne_Call {
	_c.Call.Return(run)
	return _c
}

// GetGameByID provides a mock function for the type MockGameRepository
func (_mock *MockGameRepository) GetGameByID(ctx context.Context, gameID primitive.ObjectID) (*models.Game, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for GetGameByID")
	}

	var r0 *models.Game
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.Game, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.Game); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Game)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameRepository_GetGameByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetGameByID'
type MockGameRepository_GetGameByID_Call struct {
	*mock.Call
}

// GetGameByID is a helper method to define mock.On call
//   - ctx context.Context
//   - gameID primitive.ObjectID
func (_e *MockGameRepository_Expecter) GetGameByID(ctx interface{}, gameID interface{}) *MockGameRepository_GetGameByID_Call {
	return &MockGameRepository_GetGameByID_Call{Call: _e.mock.On("GetGameByID", ctx, gameID)}
}

func (_c *MockGameRepository_GetGameByID_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID)) *MockGameRepository_GetGameByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockGameRepository_GetGameByID_Call) Return(game *models.Game, err error) *MockGameRepository_GetGameByID_Call {
	_c.Call.Return(game, err)
	return _c
}

func (_c *MockGameRepository_GetGameByID_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID) (*models.Game, error)) *MockGameRepository_GetGameByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetMinifiedGames provides a mock function for the type MockGameRepository
func (_mock *MockGameRepository) GetMinifiedGames(ctx context.Context, filter bson.M, opts ...*options.FindOptions) ([]*models.MinifiedGame, error) {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, filter, opts)
	} else {
		tmpRet = _mock.Called(ctx, filter)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for GetMinifiedGames")
	}

	var r0 []*models.MinifiedGame
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, ...*options.FindOptions) ([]*models.MinifiedGame, error)); ok {
		return returnFunc(ctx, filter, opts...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, ...*options.FindOptions) []*models.MinifiedGame); ok {
		r0 = returnFunc(ctx, filter, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.MinifiedGame)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, bson.M, ...*options.FindOptions) error); ok {
		r1 = returnFunc(ctx, filter, opts...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameRepository_GetMinifiedGames_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMinifiedGames'
type MockGameRepository_GetMinifiedGames_Call struct {
	*mock.Call
}

// GetMinifiedGames is a helper method to define mock.On call
//   - ctx context.Context
//   - filter bson.M
//   - opts ...*options.FindOptions
func (_e *MockGameRepository_Expecter) GetMinifiedGames(ctx interface{}, filter interface{}, opts ...interface{}) *MockGameRepository_GetMinifiedGames_Call {
	return &MockGameRepository_GetMinifiedGames_Call{Call: _e.mock.On("GetMinifiedGames",
		append([]interface{}{ctx, filter}, opts...)...)}
}

func (_c *MockGameRepository_GetMinifiedGames_Call) Run(run func(ctx context.Context, filter bson.M, opts ...*options.FindOptions)) *MockGameRepository_GetMinifiedGames_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 bson.M
		if args[1] != nil {
			arg1 = args[1].(bson.M)
		}
		var arg2 []*options.FindOptions
		var variadicArgs []*options.FindOptions
		if len(args) > 2 {
			variadicArgs = args[2].([]*options.FindOptions)
		}
		arg2 = variadicArgs
		run(
			arg0,
			arg1,
			arg2...,
		)
	})
	return _c
}

func (_c *MockGameRepository_GetMinifiedGames_Call) Return(minifiedGames []*models.MinifiedGame, err error) *MockGameRepository_GetMinifiedGames_Call {
	_c.Call.Return(minifiedGames, err)
	return _c
}

func (_c *MockGameRepository_GetMinifiedGames_Call) RunAndReturn(run func(ctx context.Context, filter bson.M, opts ...*options.FindOptions) ([]*models.MinifiedGame, error)) *MockGameRepository_GetMinifiedGames_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateGame provides a mock function for the type MockGameRepository
func (_mock *MockGameRepository) UpdateGame(ctx context.Context, game *models.Game) error {
	ret := _mock.Called(ctx, game)

	if len(ret) == 0 {
		panic("no return value specified for UpdateGame")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.Game) error); ok {
		r0 = returnFunc(ctx, game)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockGameRepository_UpdateGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateGame'
type MockGameRepository_UpdateGame_Call struct {
	*mock.Call
}

// UpdateGame is a helper method to define mock.On call
//   - ctx context.Context
//   - game *models.Game
func (_e *MockGameRepository_Expecter) UpdateGame(ctx interface{}, game interface{}) *MockGameRepository_UpdateGame_Call {
	return &MockGameRepository_UpdateGame_Call{Call: _e.mock.On("UpdateGame", ctx, game)}
}

func (_c *MockGameRepository_UpdateGame_Call) Run(run func(ctx context.Context, game *models.Game)) *MockGameRepository_UpdateGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.Game
		if args[1] != nil {
			arg1 = args[1].(*models.Game)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockGameRepository_UpdateGame_Call) Return(err error) *MockGameRepository_UpdateGame_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockGameRepository_UpdateGame_Call) RunAndReturn(run func(ctx context.Context, game *models.Game) error) *MockGameRepository_UpdateGame_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateOne provides a mock function for the type MockGameRepository
func (_mock *MockGameRepository) UpdateOne(ctx context.Context, filter bson.M, update bson.M) error {
	ret := _mock.Called(ctx, filter, update)

	if len(ret) == 0 {
		panic("no return value specified for UpdateOne")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, bson.M) error); ok {
		r0 = returnFunc(ctx, filter, update)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockGameRepository_UpdateOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateOne'
type MockGameRepository_UpdateOne_Call struct {
	*mock.Call
}

// UpdateOne is a helper method to define mock.On call
//   - ctx context.Context
//   - filter bson.M
//   - update bson.M
func (_e *MockGameRepository_Expecter) UpdateOne(ctx interface{}, filter interface{}, update interface{}) *MockGameRepository_UpdateOne_Call {
	return &MockGameRepository_UpdateOne_Call{Call: _e.mock.On("UpdateOne", ctx, filter, update)}
}

func (_c *MockGameRepository_UpdateOne_Call) Run(run func(ctx context.Context, filter bson.M, update bson.M)) *MockGameRepository_UpdateOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 bson.M
		if args[1] != nil {
			arg1 = args[1].(bson.M)
		}
		var arg2 bson.M
		if args[2] != nil {
			arg2 = args[2].(bson.M)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockGameRepository_UpdateOne_Call) Return(err error) *MockGameRepository_UpdateOne_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockGameRepository_UpdateOne_Call) RunAndReturn(run func(ctx context.Context, filter bson.M, update bson.M) error) *MockGameRepository_UpdateOne_Call {
	_c.Call.Return(run)
	return _c
}
