// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockLeagueRepository creates a new instance of MockLeagueRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockLeagueRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockLeagueRepository {
	mock := &MockLeagueRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockLeagueRepository is an autogenerated mock type for the LeagueRepository type
type MockLeagueRepository struct {
	mock.Mock
}

type MockLeagueRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockLeagueRepository) EXPECT() *MockLeagueRepository_Expecter {
	return &MockLeagueRepository_Expecter{mock: &_m.Mock}
}

// CreateLeague provides a mock function for the type MockLeagueRepository
func (_mock *MockLeagueRepository) CreateLeague(ctx context.Context, league *models.League) (*models.League, error) {
	ret := _mock.Called(ctx, league)

	if len(ret) == 0 {
		panic("no return value specified for CreateLeague")
	}

	var r0 *models.League
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.League) (*models.League, error)); ok {
		return returnFunc(ctx, league)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.League) *models.League); ok {
		r0 = returnFunc(ctx, league)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.League)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.League) error); ok {
		r1 = returnFunc(ctx, league)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockLeagueRepository_CreateLeague_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateLeague'
type MockLeagueRepository_CreateLeague_Call struct {
	*mock.Call
}

// CreateLeague is a helper method to define mock.On call
//   - ctx context.Context
//   - league *models.League
func (_e *MockLeagueRepository_Expecter) CreateLeague(ctx interface{}, league interface{}) *MockLeagueRepository_CreateLeague_Call {
	return &MockLeagueRepository_CreateLeague_Call{Call: _e.mock.On("CreateLeague", ctx, league)}
}

func (_c *MockLeagueRepository_CreateLeague_Call) Run(run func(ctx context.Context, league *models.League)) *MockLeagueRepository_CreateLeague_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.League
		if args[1] != nil {
			arg1 = args[1].(*models.League)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockLeagueRepository_CreateLeague_Call) Return(league1 *models.League, err error) *MockLeagueRepository_CreateLeague_Call {
	_c.Call.Return(league1, err)
	return _c
}

func (_c *MockLeagueRepository_CreateLeague_Call) RunAndReturn(run func(ctx context.Context, league *models.League) (*models.League, error)) *MockLeagueRepository_CreateLeague_Call {
	_c.Call.Return(run)
	return _c
}

// GetLeagueByID provides a mock function for the type MockLeagueRepository
func (_mock *MockLeagueRepository) GetLeagueByID(ctx context.Context, id primitive.ObjectID) (*models.League, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetLeagueByID")
	}

	var r0 *models.League
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.League, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.League); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.League)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockLeagueRepository_GetLeagueByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetLeagueByID'
type MockLeagueRepository_GetLeagueByID_Call struct {
	*mock.Call
}

// GetLeagueByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id primitive.ObjectID
func (_e *MockLeagueRepository_Expecter) GetLeagueByID(ctx interface{}, id interface{}) *MockLeagueRepository_GetLeagueByID_Call {
	return &MockLeagueRepository_GetLeagueByID_Call{Call: _e.mock.On("GetLeagueByID", ctx, id)}
}

func (_c *MockLeagueRepository_GetLeagueByID_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockLeagueRepository_GetLeagueByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockLeagueRepository_GetLeagueByID_Call) Return(league *models.League, err error) *MockLeagueRepository_GetLeagueByID_Call {
	_c.Call.Return(league, err)
	return _c
}

func (_c *MockLeagueRepository_GetLeagueByID_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (*models.League, error)) *MockLeagueRepository_GetLeagueByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetLeagueLeaderboard provides a mock function for the type MockLeagueRepository
func (_mock *MockLeagueRepository) GetLeagueLeaderboard(ctx context.Context, leagueID primitive.ObjectID, page int, pageSize int) (*models.LeagueLeaderboardPage, error) {
	ret := _mock.Called(ctx, leagueID, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetLeagueLeaderboard")
	}

	var r0 *models.LeagueLeaderboardPage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int, int) (*models.LeagueLeaderboardPage, error)); ok {
		return returnFunc(ctx, leagueID, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int, int) *models.LeagueLeaderboardPage); ok {
		r0 = returnFunc(ctx, leagueID, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.LeagueLeaderboardPage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, int, int) error); ok {
		r1 = returnFunc(ctx, leagueID, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockLeagueRepository_GetLeagueLeaderboard_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetLeagueLeaderboard'
type MockLeagueRepository_GetLeagueLeaderboard_Call struct {
	*mock.Call
}

// GetLeagueLeaderboard is a helper method to define mock.On call
//   - ctx context.Context
//   - leagueID primitive.ObjectID
//   - page int
//   - pageSize int
func (_e *MockLeagueRepository_Expecter) GetLeagueLeaderboard(ctx interface{}, leagueID interface{}, page interface{}, pageSize interface{}) *MockLeagueRepository_GetLeagueLeaderboard_Call {
	return &MockLeagueRepository_GetLeagueLeaderboard_Call{Call: _e.mock.On("GetLeagueLeaderboard", ctx, leagueID, page, pageSize)}
}

func (_c *MockLeagueRepository_GetLeagueLeaderboard_Call) Run(run func(ctx context.Context, leagueID primitive.ObjectID, page int, pageSize int)) *MockLeagueRepository_GetLeagueLeaderboard_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 int
		if args[2] != nil {
			arg2 = args[2].(int)
		}
		var arg3 int
		if args[3] != nil {
			arg3 = args[3].(int)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockLeagueRepository_GetLeagueLeaderboard_Call) Return(leagueLeaderboardPage *models.LeagueLeaderboardPage, err error) *MockLeagueRepository_GetLeagueLeaderboard_Call {
	_c.Call.Return(leagueLeaderboardPage, err)
	return _c
}

func (_c *MockLeagueRepository_GetLeagueLeaderboard_Call) RunAndReturn(run func(ctx context.Context, leagueID primitive.ObjectID, page int, pageSize int) (*models.LeagueLeaderboardPage, error)) *MockLeagueRepository_GetLeagueLeaderboard_Call {
	_c.Call.Return(run)
	return _c
}

// GetLeaguesByStatus provides a mock function for the type MockLeagueRepository
func (_mock *MockLeagueRepository) GetLeaguesByStatus(ctx context.Context, statuses []models.LeagueStatus, page int, pageSize int, sortDirection string) (*models.PaginatedLeagues, error) {
	ret := _mock.Called(ctx, statuses, page, pageSize, sortDirection)

	if len(ret) == 0 {
		panic("no return value specified for GetLeaguesByStatus")
	}

	var r0 *models.PaginatedLeagues
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, []models.LeagueStatus, int, int, string) (*models.PaginatedLeagues, error)); ok {
		return returnFunc(ctx, statuses, page, pageSize, sortDirection)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, []models.LeagueStatus, int, int, string) *models.PaginatedLeagues); ok {
		r0 = returnFunc(ctx, statuses, page, pageSize, sortDirection)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PaginatedLeagues)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, []models.LeagueStatus, int, int, string) error); ok {
		r1 = returnFunc(ctx, statuses, page, pageSize, sortDirection)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockLeagueRepository_GetLeaguesByStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetLeaguesByStatus'
type MockLeagueRepository_GetLeaguesByStatus_Call struct {
	*mock.Call
}

// GetLeaguesByStatus is a helper method to define mock.On call
//   - ctx context.Context
//   - statuses []models.LeagueStatus
//   - page int
//   - pageSize int
//   - sortDirection string
func (_e *MockLeagueRepository_Expecter) GetLeaguesByStatus(ctx interface{}, statuses interface{}, page interface{}, pageSize interface{}, sortDirection interface{}) *MockLeagueRepository_GetLeaguesByStatus_Call {
	return &MockLeagueRepository_GetLeaguesByStatus_Call{Call: _e.mock.On("GetLeaguesByStatus", ctx, statuses, page, pageSize, sortDirection)}
}

func (_c *MockLeagueRepository_GetLeaguesByStatus_Call) Run(run func(ctx context.Context, statuses []models.LeagueStatus, page int, pageSize int, sortDirection string)) *MockLeagueRepository_GetLeaguesByStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 []models.LeagueStatus
		if args[1] != nil {
			arg1 = args[1].([]models.LeagueStatus)
		}
		var arg2 int
		if args[2] != nil {
			arg2 = args[2].(int)
		}
		var arg3 int
		if args[3] != nil {
			arg3 = args[3].(int)
		}
		var arg4 string
		if args[4] != nil {
			arg4 = args[4].(string)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
			arg4,
		)
	})
	return _c
}

func (_c *MockLeagueRepository_GetLeaguesByStatus_Call) Return(paginatedLeagues *models.PaginatedLeagues, err error) *MockLeagueRepository_GetLeaguesByStatus_Call {
	_c.Call.Return(paginatedLeagues, err)
	return _c
}

func (_c *MockLeagueRepository_GetLeaguesByStatus_Call) RunAndReturn(run func(ctx context.Context, statuses []models.LeagueStatus, page int, pageSize int, sortDirection string) (*models.PaginatedLeagues, error)) *MockLeagueRepository_GetLeaguesByStatus_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserLeagueResult provides a mock function for the type MockLeagueRepository
func (_mock *MockLeagueRepository) GetUserLeagueResult(ctx context.Context, leagueID primitive.ObjectID, userID primitive.ObjectID) (*models.LeagueLeaderboardEntry, error) {
	ret := _mock.Called(ctx, leagueID, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetUserLeagueResult")
	}

	var r0 *models.LeagueLeaderboardEntry
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) (*models.LeagueLeaderboardEntry, error)); ok {
		return returnFunc(ctx, leagueID, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) *models.LeagueLeaderboardEntry); ok {
		r0 = returnFunc(ctx, leagueID, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.LeagueLeaderboardEntry)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, leagueID, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockLeagueRepository_GetUserLeagueResult_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserLeagueResult'
type MockLeagueRepository_GetUserLeagueResult_Call struct {
	*mock.Call
}

// GetUserLeagueResult is a helper method to define mock.On call
//   - ctx context.Context
//   - leagueID primitive.ObjectID
//   - userID primitive.ObjectID
func (_e *MockLeagueRepository_Expecter) GetUserLeagueResult(ctx interface{}, leagueID interface{}, userID interface{}) *MockLeagueRepository_GetUserLeagueResult_Call {
	return &MockLeagueRepository_GetUserLeagueResult_Call{Call: _e.mock.On("GetUserLeagueResult", ctx, leagueID, userID)}
}

func (_c *MockLeagueRepository_GetUserLeagueResult_Call) Run(run func(ctx context.Context, leagueID primitive.ObjectID, userID primitive.ObjectID)) *MockLeagueRepository_GetUserLeagueResult_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockLeagueRepository_GetUserLeagueResult_Call) Return(leagueLeaderboardEntry *models.LeagueLeaderboardEntry, err error) *MockLeagueRepository_GetUserLeagueResult_Call {
	_c.Call.Return(leagueLeaderboardEntry, err)
	return _c
}

func (_c *MockLeagueRepository_GetUserLeagueResult_Call) RunAndReturn(run func(ctx context.Context, leagueID primitive.ObjectID, userID primitive.ObjectID) (*models.LeagueLeaderboardEntry, error)) *MockLeagueRepository_GetUserLeagueResult_Call {
	_c.Call.Return(run)
	return _c
}

// IsEmailRegisteredInLeague provides a mock function for the type MockLeagueRepository
func (_mock *MockLeagueRepository) IsEmailRegisteredInLeague(ctx context.Context, leagueID primitive.ObjectID, email string) (bool, error) {
	ret := _mock.Called(ctx, leagueID, email)

	if len(ret) == 0 {
		panic("no return value specified for IsEmailRegisteredInLeague")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, string) (bool, error)); ok {
		return returnFunc(ctx, leagueID, email)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, string) bool); ok {
		r0 = returnFunc(ctx, leagueID, email)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, string) error); ok {
		r1 = returnFunc(ctx, leagueID, email)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockLeagueRepository_IsEmailRegisteredInLeague_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsEmailRegisteredInLeague'
type MockLeagueRepository_IsEmailRegisteredInLeague_Call struct {
	*mock.Call
}

// IsEmailRegisteredInLeague is a helper method to define mock.On call
//   - ctx context.Context
//   - leagueID primitive.ObjectID
//   - email string
func (_e *MockLeagueRepository_Expecter) IsEmailRegisteredInLeague(ctx interface{}, leagueID interface{}, email interface{}) *MockLeagueRepository_IsEmailRegisteredInLeague_Call {
	return &MockLeagueRepository_IsEmailRegisteredInLeague_Call{Call: _e.mock.On("IsEmailRegisteredInLeague", ctx, leagueID, email)}
}

func (_c *MockLeagueRepository_IsEmailRegisteredInLeague_Call) Run(run func(ctx context.Context, leagueID primitive.ObjectID, email string)) *MockLeagueRepository_IsEmailRegisteredInLeague_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 string
		if args[2] != nil {
			arg2 = args[2].(string)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockLeagueRepository_IsEmailRegisteredInLeague_Call) Return(b bool, err error) *MockLeagueRepository_IsEmailRegisteredInLeague_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockLeagueRepository_IsEmailRegisteredInLeague_Call) RunAndReturn(run func(ctx context.Context, leagueID primitive.ObjectID, email string) (bool, error)) *MockLeagueRepository_IsEmailRegisteredInLeague_Call {
	_c.Call.Return(run)
	return _c
}

// RegisterUserForLeague provides a mock function for the type MockLeagueRepository
func (_mock *MockLeagueRepository) RegisterUserForLeague(ctx context.Context, leagueID primitive.ObjectID, userID primitive.ObjectID, registrationData []*models.RegistrationFieldData) error {
	ret := _mock.Called(ctx, leagueID, userID, registrationData)

	if len(ret) == 0 {
		panic("no return value specified for RegisterUserForLeague")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID, []*models.RegistrationFieldData) error); ok {
		r0 = returnFunc(ctx, leagueID, userID, registrationData)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockLeagueRepository_RegisterUserForLeague_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RegisterUserForLeague'
type MockLeagueRepository_RegisterUserForLeague_Call struct {
	*mock.Call
}

// RegisterUserForLeague is a helper method to define mock.On call
//   - ctx context.Context
//   - leagueID primitive.ObjectID
//   - userID primitive.ObjectID
//   - registrationData []*models.RegistrationFieldData
func (_e *MockLeagueRepository_Expecter) RegisterUserForLeague(ctx interface{}, leagueID interface{}, userID interface{}, registrationData interface{}) *MockLeagueRepository_RegisterUserForLeague_Call {
	return &MockLeagueRepository_RegisterUserForLeague_Call{Call: _e.mock.On("RegisterUserForLeague", ctx, leagueID, userID, registrationData)}
}

func (_c *MockLeagueRepository_RegisterUserForLeague_Call) Run(run func(ctx context.Context, leagueID primitive.ObjectID, userID primitive.ObjectID, registrationData []*models.RegistrationFieldData)) *MockLeagueRepository_RegisterUserForLeague_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(primitive.ObjectID)
		}
		var arg3 []*models.RegistrationFieldData
		if args[3] != nil {
			arg3 = args[3].([]*models.RegistrationFieldData)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockLeagueRepository_RegisterUserForLeague_Call) Return(err error) *MockLeagueRepository_RegisterUserForLeague_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockLeagueRepository_RegisterUserForLeague_Call) RunAndReturn(run func(ctx context.Context, leagueID primitive.ObjectID, userID primitive.ObjectID, registrationData []*models.RegistrationFieldData) error) *MockLeagueRepository_RegisterUserForLeague_Call {
	_c.Call.Return(run)
	return _c
}

// UnregisterUserFromLeague provides a mock function for the type MockLeagueRepository
func (_mock *MockLeagueRepository) UnregisterUserFromLeague(ctx context.Context, leagueID primitive.ObjectID, userID primitive.ObjectID) error {
	ret := _mock.Called(ctx, leagueID, userID)

	if len(ret) == 0 {
		panic("no return value specified for UnregisterUserFromLeague")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, leagueID, userID)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockLeagueRepository_UnregisterUserFromLeague_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UnregisterUserFromLeague'
type MockLeagueRepository_UnregisterUserFromLeague_Call struct {
	*mock.Call
}

// UnregisterUserFromLeague is a helper method to define mock.On call
//   - ctx context.Context
//   - leagueID primitive.ObjectID
//   - userID primitive.ObjectID
func (_e *MockLeagueRepository_Expecter) UnregisterUserFromLeague(ctx interface{}, leagueID interface{}, userID interface{}) *MockLeagueRepository_UnregisterUserFromLeague_Call {
	return &MockLeagueRepository_UnregisterUserFromLeague_Call{Call: _e.mock.On("UnregisterUserFromLeague", ctx, leagueID, userID)}
}

func (_c *MockLeagueRepository_UnregisterUserFromLeague_Call) Run(run func(ctx context.Context, leagueID primitive.ObjectID, userID primitive.ObjectID)) *MockLeagueRepository_UnregisterUserFromLeague_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockLeagueRepository_UnregisterUserFromLeague_Call) Return(err error) *MockLeagueRepository_UnregisterUserFromLeague_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockLeagueRepository_UnregisterUserFromLeague_Call) RunAndReturn(run func(ctx context.Context, leagueID primitive.ObjectID, userID primitive.ObjectID) error) *MockLeagueRepository_UnregisterUserFromLeague_Call {
	_c.Call.Return(run)
	return _c
}
