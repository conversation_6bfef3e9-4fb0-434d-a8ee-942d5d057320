// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// NewMockGameSeriesRepository creates a new instance of MockGameSeriesRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockGameSeriesRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockGameSeriesRepository {
	mock := &MockGameSeriesRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockGameSeriesRepository is an autogenerated mock type for the GameSeriesRepository type
type MockGameSeriesRepository struct {
	mock.Mock
}

type MockGameSeriesRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockGameSeriesRepository) EXPECT() *MockGameSeriesRepository_Expecter {
	return &MockGameSeriesRepository_Expecter{mock: &_m.Mock}
}

// Collection provides a mock function for the type MockGameSeriesRepository
func (_mock *MockGameSeriesRepository) Collection() *mongo.Collection {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for Collection")
	}

	var r0 *mongo.Collection
	if returnFunc, ok := ret.Get(0).(func() *mongo.Collection); ok {
		r0 = returnFunc()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*mongo.Collection)
		}
	}
	return r0
}

// MockGameSeriesRepository_Collection_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Collection'
type MockGameSeriesRepository_Collection_Call struct {
	*mock.Call
}

// Collection is a helper method to define mock.On call
func (_e *MockGameSeriesRepository_Expecter) Collection() *MockGameSeriesRepository_Collection_Call {
	return &MockGameSeriesRepository_Collection_Call{Call: _e.mock.On("Collection")}
}

func (_c *MockGameSeriesRepository_Collection_Call) Run(run func()) *MockGameSeriesRepository_Collection_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockGameSeriesRepository_Collection_Call) Return(collection *mongo.Collection) *MockGameSeriesRepository_Collection_Call {
	_c.Call.Return(collection)
	return _c
}

func (_c *MockGameSeriesRepository_Collection_Call) RunAndReturn(run func() *mongo.Collection) *MockGameSeriesRepository_Collection_Call {
	_c.Call.Return(run)
	return _c
}

// CreateGameSeries provides a mock function for the type MockGameSeriesRepository
func (_mock *MockGameSeriesRepository) CreateGameSeries(ctx context.Context, game *models.GameSeries) (*models.GameSeries, error) {
	ret := _mock.Called(ctx, game)

	if len(ret) == 0 {
		panic("no return value specified for CreateGameSeries")
	}

	var r0 *models.GameSeries
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.GameSeries) (*models.GameSeries, error)); ok {
		return returnFunc(ctx, game)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.GameSeries) *models.GameSeries); ok {
		r0 = returnFunc(ctx, game)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.GameSeries)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.GameSeries) error); ok {
		r1 = returnFunc(ctx, game)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameSeriesRepository_CreateGameSeries_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateGameSeries'
type MockGameSeriesRepository_CreateGameSeries_Call struct {
	*mock.Call
}

// CreateGameSeries is a helper method to define mock.On call
//   - ctx context.Context
//   - game *models.GameSeries
func (_e *MockGameSeriesRepository_Expecter) CreateGameSeries(ctx interface{}, game interface{}) *MockGameSeriesRepository_CreateGameSeries_Call {
	return &MockGameSeriesRepository_CreateGameSeries_Call{Call: _e.mock.On("CreateGameSeries", ctx, game)}
}

func (_c *MockGameSeriesRepository_CreateGameSeries_Call) Run(run func(ctx context.Context, game *models.GameSeries)) *MockGameSeriesRepository_CreateGameSeries_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.GameSeries
		if args[1] != nil {
			arg1 = args[1].(*models.GameSeries)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockGameSeriesRepository_CreateGameSeries_Call) Return(gameSeries *models.GameSeries, err error) *MockGameSeriesRepository_CreateGameSeries_Call {
	_c.Call.Return(gameSeries, err)
	return _c
}

func (_c *MockGameSeriesRepository_CreateGameSeries_Call) RunAndReturn(run func(ctx context.Context, game *models.GameSeries) (*models.GameSeries, error)) *MockGameSeriesRepository_CreateGameSeries_Call {
	_c.Call.Return(run)
	return _c
}

// GetGameSeriesByID provides a mock function for the type MockGameSeriesRepository
func (_mock *MockGameSeriesRepository) GetGameSeriesByID(ctx context.Context, gameSeriesID primitive.ObjectID) (*models.GameSeries, error) {
	ret := _mock.Called(ctx, gameSeriesID)

	if len(ret) == 0 {
		panic("no return value specified for GetGameSeriesByID")
	}

	var r0 *models.GameSeries
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.GameSeries, error)); ok {
		return returnFunc(ctx, gameSeriesID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.GameSeries); ok {
		r0 = returnFunc(ctx, gameSeriesID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.GameSeries)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameSeriesID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockGameSeriesRepository_GetGameSeriesByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetGameSeriesByID'
type MockGameSeriesRepository_GetGameSeriesByID_Call struct {
	*mock.Call
}

// GetGameSeriesByID is a helper method to define mock.On call
//   - ctx context.Context
//   - gameSeriesID primitive.ObjectID
func (_e *MockGameSeriesRepository_Expecter) GetGameSeriesByID(ctx interface{}, gameSeriesID interface{}) *MockGameSeriesRepository_GetGameSeriesByID_Call {
	return &MockGameSeriesRepository_GetGameSeriesByID_Call{Call: _e.mock.On("GetGameSeriesByID", ctx, gameSeriesID)}
}

func (_c *MockGameSeriesRepository_GetGameSeriesByID_Call) Run(run func(ctx context.Context, gameSeriesID primitive.ObjectID)) *MockGameSeriesRepository_GetGameSeriesByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockGameSeriesRepository_GetGameSeriesByID_Call) Return(gameSeries *models.GameSeries, err error) *MockGameSeriesRepository_GetGameSeriesByID_Call {
	_c.Call.Return(gameSeries, err)
	return _c
}

func (_c *MockGameSeriesRepository_GetGameSeriesByID_Call) RunAndReturn(run func(ctx context.Context, gameSeriesID primitive.ObjectID) (*models.GameSeries, error)) *MockGameSeriesRepository_GetGameSeriesByID_Call {
	_c.Call.Return(run)
	return _c
}

// Update provides a mock function for the type MockGameSeriesRepository
func (_mock *MockGameSeriesRepository) Update(ctx context.Context, gameSeries *models.GameSeries) error {
	ret := _mock.Called(ctx, gameSeries)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.GameSeries) error); ok {
		r0 = returnFunc(ctx, gameSeries)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockGameSeriesRepository_Update_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Update'
type MockGameSeriesRepository_Update_Call struct {
	*mock.Call
}

// Update is a helper method to define mock.On call
//   - ctx context.Context
//   - gameSeries *models.GameSeries
func (_e *MockGameSeriesRepository_Expecter) Update(ctx interface{}, gameSeries interface{}) *MockGameSeriesRepository_Update_Call {
	return &MockGameSeriesRepository_Update_Call{Call: _e.mock.On("Update", ctx, gameSeries)}
}

func (_c *MockGameSeriesRepository_Update_Call) Run(run func(ctx context.Context, gameSeries *models.GameSeries)) *MockGameSeriesRepository_Update_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.GameSeries
		if args[1] != nil {
			arg1 = args[1].(*models.GameSeries)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockGameSeriesRepository_Update_Call) Return(err error) *MockGameSeriesRepository_Update_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockGameSeriesRepository_Update_Call) RunAndReturn(run func(ctx context.Context, gameSeries *models.GameSeries) error) *MockGameSeriesRepository_Update_Call {
	_c.Call.Return(run)
	return _c
}
