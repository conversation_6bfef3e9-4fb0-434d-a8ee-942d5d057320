// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// NewMockClubAnnouncementRepository creates a new instance of MockClubAnnouncementRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockClubAnnouncementRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockClubAnnouncementRepository {
	mock := &MockClubAnnouncementRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockClubAnnouncementRepository is an autogenerated mock type for the ClubAnnouncementRepository type
type MockClubAnnouncementRepository struct {
	mock.Mock
}

type MockClubAnnouncementRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockClubAnnouncementRepository) EXPECT() *MockClubAnnouncementRepository_Expecter {
	return &MockClubAnnouncementRepository_Expecter{mock: &_m.Mock}
}

// Count provides a mock function for the type MockClubAnnouncementRepository
func (_mock *MockClubAnnouncementRepository) Count(ctx context.Context, filter bson.M) (int64, error) {
	ret := _mock.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for Count")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M) (int64, error)); ok {
		return returnFunc(ctx, filter)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M) int64); ok {
		r0 = returnFunc(ctx, filter)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, bson.M) error); ok {
		r1 = returnFunc(ctx, filter)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubAnnouncementRepository_Count_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Count'
type MockClubAnnouncementRepository_Count_Call struct {
	*mock.Call
}

// Count is a helper method to define mock.On call
//   - ctx context.Context
//   - filter bson.M
func (_e *MockClubAnnouncementRepository_Expecter) Count(ctx interface{}, filter interface{}) *MockClubAnnouncementRepository_Count_Call {
	return &MockClubAnnouncementRepository_Count_Call{Call: _e.mock.On("Count", ctx, filter)}
}

func (_c *MockClubAnnouncementRepository_Count_Call) Run(run func(ctx context.Context, filter bson.M)) *MockClubAnnouncementRepository_Count_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 bson.M
		if args[1] != nil {
			arg1 = args[1].(bson.M)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockClubAnnouncementRepository_Count_Call) Return(n int64, err error) *MockClubAnnouncementRepository_Count_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockClubAnnouncementRepository_Count_Call) RunAndReturn(run func(ctx context.Context, filter bson.M) (int64, error)) *MockClubAnnouncementRepository_Count_Call {
	_c.Call.Return(run)
	return _c
}

// CreateAnnouncement provides a mock function for the type MockClubAnnouncementRepository
func (_mock *MockClubAnnouncementRepository) CreateAnnouncement(ctx context.Context, event *models.ClubAnnouncement) error {
	ret := _mock.Called(ctx, event)

	if len(ret) == 0 {
		panic("no return value specified for CreateAnnouncement")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.ClubAnnouncement) error); ok {
		r0 = returnFunc(ctx, event)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockClubAnnouncementRepository_CreateAnnouncement_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateAnnouncement'
type MockClubAnnouncementRepository_CreateAnnouncement_Call struct {
	*mock.Call
}

// CreateAnnouncement is a helper method to define mock.On call
//   - ctx context.Context
//   - event *models.ClubAnnouncement
func (_e *MockClubAnnouncementRepository_Expecter) CreateAnnouncement(ctx interface{}, event interface{}) *MockClubAnnouncementRepository_CreateAnnouncement_Call {
	return &MockClubAnnouncementRepository_CreateAnnouncement_Call{Call: _e.mock.On("CreateAnnouncement", ctx, event)}
}

func (_c *MockClubAnnouncementRepository_CreateAnnouncement_Call) Run(run func(ctx context.Context, event *models.ClubAnnouncement)) *MockClubAnnouncementRepository_CreateAnnouncement_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.ClubAnnouncement
		if args[1] != nil {
			arg1 = args[1].(*models.ClubAnnouncement)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockClubAnnouncementRepository_CreateAnnouncement_Call) Return(err error) *MockClubAnnouncementRepository_CreateAnnouncement_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockClubAnnouncementRepository_CreateAnnouncement_Call) RunAndReturn(run func(ctx context.Context, event *models.ClubAnnouncement) error) *MockClubAnnouncementRepository_CreateAnnouncement_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteAnnouncement provides a mock function for the type MockClubAnnouncementRepository
func (_mock *MockClubAnnouncementRepository) DeleteAnnouncement(ctx context.Context, id primitive.ObjectID) error {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteAnnouncement")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, id)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockClubAnnouncementRepository_DeleteAnnouncement_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteAnnouncement'
type MockClubAnnouncementRepository_DeleteAnnouncement_Call struct {
	*mock.Call
}

// DeleteAnnouncement is a helper method to define mock.On call
//   - ctx context.Context
//   - id primitive.ObjectID
func (_e *MockClubAnnouncementRepository_Expecter) DeleteAnnouncement(ctx interface{}, id interface{}) *MockClubAnnouncementRepository_DeleteAnnouncement_Call {
	return &MockClubAnnouncementRepository_DeleteAnnouncement_Call{Call: _e.mock.On("DeleteAnnouncement", ctx, id)}
}

func (_c *MockClubAnnouncementRepository_DeleteAnnouncement_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockClubAnnouncementRepository_DeleteAnnouncement_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockClubAnnouncementRepository_DeleteAnnouncement_Call) Return(err error) *MockClubAnnouncementRepository_DeleteAnnouncement_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockClubAnnouncementRepository_DeleteAnnouncement_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) error) *MockClubAnnouncementRepository_DeleteAnnouncement_Call {
	_c.Call.Return(run)
	return _c
}

// Find provides a mock function for the type MockClubAnnouncementRepository
func (_mock *MockClubAnnouncementRepository) Find(ctx context.Context, filter bson.M, opts ...*options.FindOptions) ([]*models.ClubAnnouncement, error) {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, filter, opts)
	} else {
		tmpRet = _mock.Called(ctx, filter)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for Find")
	}

	var r0 []*models.ClubAnnouncement
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, ...*options.FindOptions) ([]*models.ClubAnnouncement, error)); ok {
		return returnFunc(ctx, filter, opts...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, ...*options.FindOptions) []*models.ClubAnnouncement); ok {
		r0 = returnFunc(ctx, filter, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.ClubAnnouncement)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, bson.M, ...*options.FindOptions) error); ok {
		r1 = returnFunc(ctx, filter, opts...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubAnnouncementRepository_Find_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Find'
type MockClubAnnouncementRepository_Find_Call struct {
	*mock.Call
}

// Find is a helper method to define mock.On call
//   - ctx context.Context
//   - filter bson.M
//   - opts ...*options.FindOptions
func (_e *MockClubAnnouncementRepository_Expecter) Find(ctx interface{}, filter interface{}, opts ...interface{}) *MockClubAnnouncementRepository_Find_Call {
	return &MockClubAnnouncementRepository_Find_Call{Call: _e.mock.On("Find",
		append([]interface{}{ctx, filter}, opts...)...)}
}

func (_c *MockClubAnnouncementRepository_Find_Call) Run(run func(ctx context.Context, filter bson.M, opts ...*options.FindOptions)) *MockClubAnnouncementRepository_Find_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 bson.M
		if args[1] != nil {
			arg1 = args[1].(bson.M)
		}
		var arg2 []*options.FindOptions
		var variadicArgs []*options.FindOptions
		if len(args) > 2 {
			variadicArgs = args[2].([]*options.FindOptions)
		}
		arg2 = variadicArgs
		run(
			arg0,
			arg1,
			arg2...,
		)
	})
	return _c
}

func (_c *MockClubAnnouncementRepository_Find_Call) Return(clubAnnouncements []*models.ClubAnnouncement, err error) *MockClubAnnouncementRepository_Find_Call {
	_c.Call.Return(clubAnnouncements, err)
	return _c
}

func (_c *MockClubAnnouncementRepository_Find_Call) RunAndReturn(run func(ctx context.Context, filter bson.M, opts ...*options.FindOptions) ([]*models.ClubAnnouncement, error)) *MockClubAnnouncementRepository_Find_Call {
	_c.Call.Return(run)
	return _c
}

// FindAnnouncementByID provides a mock function for the type MockClubAnnouncementRepository
func (_mock *MockClubAnnouncementRepository) FindAnnouncementByID(ctx context.Context, id primitive.ObjectID) (*models.ClubAnnouncement, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for FindAnnouncementByID")
	}

	var r0 *models.ClubAnnouncement
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.ClubAnnouncement, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.ClubAnnouncement); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ClubAnnouncement)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubAnnouncementRepository_FindAnnouncementByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindAnnouncementByID'
type MockClubAnnouncementRepository_FindAnnouncementByID_Call struct {
	*mock.Call
}

// FindAnnouncementByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id primitive.ObjectID
func (_e *MockClubAnnouncementRepository_Expecter) FindAnnouncementByID(ctx interface{}, id interface{}) *MockClubAnnouncementRepository_FindAnnouncementByID_Call {
	return &MockClubAnnouncementRepository_FindAnnouncementByID_Call{Call: _e.mock.On("FindAnnouncementByID", ctx, id)}
}

func (_c *MockClubAnnouncementRepository_FindAnnouncementByID_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockClubAnnouncementRepository_FindAnnouncementByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockClubAnnouncementRepository_FindAnnouncementByID_Call) Return(clubAnnouncement *models.ClubAnnouncement, err error) *MockClubAnnouncementRepository_FindAnnouncementByID_Call {
	_c.Call.Return(clubAnnouncement, err)
	return _c
}

func (_c *MockClubAnnouncementRepository_FindAnnouncementByID_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (*models.ClubAnnouncement, error)) *MockClubAnnouncementRepository_FindAnnouncementByID_Call {
	_c.Call.Return(run)
	return _c
}

// FindOne provides a mock function for the type MockClubAnnouncementRepository
func (_mock *MockClubAnnouncementRepository) FindOne(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions) (*models.ClubAnnouncement, error) {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, filter, opts)
	} else {
		tmpRet = _mock.Called(ctx, filter)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for FindOne")
	}

	var r0 *models.ClubAnnouncement
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, ...*options.FindOneOptions) (*models.ClubAnnouncement, error)); ok {
		return returnFunc(ctx, filter, opts...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, ...*options.FindOneOptions) *models.ClubAnnouncement); ok {
		r0 = returnFunc(ctx, filter, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ClubAnnouncement)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, bson.M, ...*options.FindOneOptions) error); ok {
		r1 = returnFunc(ctx, filter, opts...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubAnnouncementRepository_FindOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindOne'
type MockClubAnnouncementRepository_FindOne_Call struct {
	*mock.Call
}

// FindOne is a helper method to define mock.On call
//   - ctx context.Context
//   - filter bson.M
//   - opts ...*options.FindOneOptions
func (_e *MockClubAnnouncementRepository_Expecter) FindOne(ctx interface{}, filter interface{}, opts ...interface{}) *MockClubAnnouncementRepository_FindOne_Call {
	return &MockClubAnnouncementRepository_FindOne_Call{Call: _e.mock.On("FindOne",
		append([]interface{}{ctx, filter}, opts...)...)}
}

func (_c *MockClubAnnouncementRepository_FindOne_Call) Run(run func(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions)) *MockClubAnnouncementRepository_FindOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 bson.M
		if args[1] != nil {
			arg1 = args[1].(bson.M)
		}
		var arg2 []*options.FindOneOptions
		var variadicArgs []*options.FindOneOptions
		if len(args) > 2 {
			variadicArgs = args[2].([]*options.FindOneOptions)
		}
		arg2 = variadicArgs
		run(
			arg0,
			arg1,
			arg2...,
		)
	})
	return _c
}

func (_c *MockClubAnnouncementRepository_FindOne_Call) Return(clubAnnouncement *models.ClubAnnouncement, err error) *MockClubAnnouncementRepository_FindOne_Call {
	_c.Call.Return(clubAnnouncement, err)
	return _c
}

func (_c *MockClubAnnouncementRepository_FindOne_Call) RunAndReturn(run func(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions) (*models.ClubAnnouncement, error)) *MockClubAnnouncementRepository_FindOne_Call {
	_c.Call.Return(run)
	return _c
}

// ListAnnouncements provides a mock function for the type MockClubAnnouncementRepository
func (_mock *MockClubAnnouncementRepository) ListAnnouncements(ctx context.Context, clubID primitive.ObjectID, opts *options.FindOptions) ([]*models.ClubAnnouncement, error) {
	ret := _mock.Called(ctx, clubID, opts)

	if len(ret) == 0 {
		panic("no return value specified for ListAnnouncements")
	}

	var r0 []*models.ClubAnnouncement
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *options.FindOptions) ([]*models.ClubAnnouncement, error)); ok {
		return returnFunc(ctx, clubID, opts)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *options.FindOptions) []*models.ClubAnnouncement); ok {
		r0 = returnFunc(ctx, clubID, opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.ClubAnnouncement)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, *options.FindOptions) error); ok {
		r1 = returnFunc(ctx, clubID, opts)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockClubAnnouncementRepository_ListAnnouncements_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListAnnouncements'
type MockClubAnnouncementRepository_ListAnnouncements_Call struct {
	*mock.Call
}

// ListAnnouncements is a helper method to define mock.On call
//   - ctx context.Context
//   - clubID primitive.ObjectID
//   - opts *options.FindOptions
func (_e *MockClubAnnouncementRepository_Expecter) ListAnnouncements(ctx interface{}, clubID interface{}, opts interface{}) *MockClubAnnouncementRepository_ListAnnouncements_Call {
	return &MockClubAnnouncementRepository_ListAnnouncements_Call{Call: _e.mock.On("ListAnnouncements", ctx, clubID, opts)}
}

func (_c *MockClubAnnouncementRepository_ListAnnouncements_Call) Run(run func(ctx context.Context, clubID primitive.ObjectID, opts *options.FindOptions)) *MockClubAnnouncementRepository_ListAnnouncements_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 *options.FindOptions
		if args[2] != nil {
			arg2 = args[2].(*options.FindOptions)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockClubAnnouncementRepository_ListAnnouncements_Call) Return(clubAnnouncements []*models.ClubAnnouncement, err error) *MockClubAnnouncementRepository_ListAnnouncements_Call {
	_c.Call.Return(clubAnnouncements, err)
	return _c
}

func (_c *MockClubAnnouncementRepository_ListAnnouncements_Call) RunAndReturn(run func(ctx context.Context, clubID primitive.ObjectID, opts *options.FindOptions) ([]*models.ClubAnnouncement, error)) *MockClubAnnouncementRepository_ListAnnouncements_Call {
	_c.Call.Return(run)
	return _c
}
