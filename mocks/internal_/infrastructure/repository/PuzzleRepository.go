// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// NewMockPuzzleRepository creates a new instance of MockPuzzleRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockPuzzleRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockPuzzleRepository {
	mock := &MockPuzzleRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockPuzzleRepository is an autogenerated mock type for the PuzzleRepository type
type MockPuzzleRepository struct {
	mock.Mock
}

type MockPuzzleRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockPuzzleRepository) EXPECT() *MockPuzzleRepository_Expecter {
	return &MockPuzzleRepository_Expecter{mock: &_m.Mock}
}

// Create provides a mock function for the type MockPuzzleRepository
func (_mock *MockPuzzleRepository) Create(ctx context.Context, puzzle *models.Puzzle) error {
	ret := _mock.Called(ctx, puzzle)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.Puzzle) error); ok {
		r0 = returnFunc(ctx, puzzle)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockPuzzleRepository_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type MockPuzzleRepository_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx context.Context
//   - puzzle *models.Puzzle
func (_e *MockPuzzleRepository_Expecter) Create(ctx interface{}, puzzle interface{}) *MockPuzzleRepository_Create_Call {
	return &MockPuzzleRepository_Create_Call{Call: _e.mock.On("Create", ctx, puzzle)}
}

func (_c *MockPuzzleRepository_Create_Call) Run(run func(ctx context.Context, puzzle *models.Puzzle)) *MockPuzzleRepository_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.Puzzle
		if args[1] != nil {
			arg1 = args[1].(*models.Puzzle)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockPuzzleRepository_Create_Call) Return(err error) *MockPuzzleRepository_Create_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockPuzzleRepository_Create_Call) RunAndReturn(run func(ctx context.Context, puzzle *models.Puzzle) error) *MockPuzzleRepository_Create_Call {
	_c.Call.Return(run)
	return _c
}

// Delete provides a mock function for the type MockPuzzleRepository
func (_mock *MockPuzzleRepository) Delete(ctx context.Context, id primitive.ObjectID) error {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, id)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockPuzzleRepository_Delete_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Delete'
type MockPuzzleRepository_Delete_Call struct {
	*mock.Call
}

// Delete is a helper method to define mock.On call
//   - ctx context.Context
//   - id primitive.ObjectID
func (_e *MockPuzzleRepository_Expecter) Delete(ctx interface{}, id interface{}) *MockPuzzleRepository_Delete_Call {
	return &MockPuzzleRepository_Delete_Call{Call: _e.mock.On("Delete", ctx, id)}
}

func (_c *MockPuzzleRepository_Delete_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockPuzzleRepository_Delete_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockPuzzleRepository_Delete_Call) Return(err error) *MockPuzzleRepository_Delete_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockPuzzleRepository_Delete_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) error) *MockPuzzleRepository_Delete_Call {
	_c.Call.Return(run)
	return _c
}

// FindByID provides a mock function for the type MockPuzzleRepository
func (_mock *MockPuzzleRepository) FindByID(ctx context.Context, id primitive.ObjectID) (*models.Puzzle, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for FindByID")
	}

	var r0 *models.Puzzle
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.Puzzle, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.Puzzle); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Puzzle)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleRepository_FindByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindByID'
type MockPuzzleRepository_FindByID_Call struct {
	*mock.Call
}

// FindByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id primitive.ObjectID
func (_e *MockPuzzleRepository_Expecter) FindByID(ctx interface{}, id interface{}) *MockPuzzleRepository_FindByID_Call {
	return &MockPuzzleRepository_FindByID_Call{Call: _e.mock.On("FindByID", ctx, id)}
}

func (_c *MockPuzzleRepository_FindByID_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockPuzzleRepository_FindByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockPuzzleRepository_FindByID_Call) Return(puzzle *models.Puzzle, err error) *MockPuzzleRepository_FindByID_Call {
	_c.Call.Return(puzzle, err)
	return _c
}

func (_c *MockPuzzleRepository_FindByID_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (*models.Puzzle, error)) *MockPuzzleRepository_FindByID_Call {
	_c.Call.Return(run)
	return _c
}

// FindOne provides a mock function for the type MockPuzzleRepository
func (_mock *MockPuzzleRepository) FindOne(ctx context.Context, filter bson.M) (*models.Puzzle, error) {
	ret := _mock.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for FindOne")
	}

	var r0 *models.Puzzle
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M) (*models.Puzzle, error)); ok {
		return returnFunc(ctx, filter)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M) *models.Puzzle); ok {
		r0 = returnFunc(ctx, filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Puzzle)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, bson.M) error); ok {
		r1 = returnFunc(ctx, filter)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleRepository_FindOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindOne'
type MockPuzzleRepository_FindOne_Call struct {
	*mock.Call
}

// FindOne is a helper method to define mock.On call
//   - ctx context.Context
//   - filter bson.M
func (_e *MockPuzzleRepository_Expecter) FindOne(ctx interface{}, filter interface{}) *MockPuzzleRepository_FindOne_Call {
	return &MockPuzzleRepository_FindOne_Call{Call: _e.mock.On("FindOne", ctx, filter)}
}

func (_c *MockPuzzleRepository_FindOne_Call) Run(run func(ctx context.Context, filter bson.M)) *MockPuzzleRepository_FindOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 bson.M
		if args[1] != nil {
			arg1 = args[1].(bson.M)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockPuzzleRepository_FindOne_Call) Return(puzzle *models.Puzzle, err error) *MockPuzzleRepository_FindOne_Call {
	_c.Call.Return(puzzle, err)
	return _c
}

func (_c *MockPuzzleRepository_FindOne_Call) RunAndReturn(run func(ctx context.Context, filter bson.M) (*models.Puzzle, error)) *MockPuzzleRepository_FindOne_Call {
	_c.Call.Return(run)
	return _c
}

// List provides a mock function for the type MockPuzzleRepository
func (_mock *MockPuzzleRepository) List(ctx context.Context, filter bson.M, opts *options.FindOptions) ([]*models.Puzzle, error) {
	ret := _mock.Called(ctx, filter, opts)

	if len(ret) == 0 {
		panic("no return value specified for List")
	}

	var r0 []*models.Puzzle
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, *options.FindOptions) ([]*models.Puzzle, error)); ok {
		return returnFunc(ctx, filter, opts)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, *options.FindOptions) []*models.Puzzle); ok {
		r0 = returnFunc(ctx, filter, opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.Puzzle)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, bson.M, *options.FindOptions) error); ok {
		r1 = returnFunc(ctx, filter, opts)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleRepository_List_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'List'
type MockPuzzleRepository_List_Call struct {
	*mock.Call
}

// List is a helper method to define mock.On call
//   - ctx context.Context
//   - filter bson.M
//   - opts *options.FindOptions
func (_e *MockPuzzleRepository_Expecter) List(ctx interface{}, filter interface{}, opts interface{}) *MockPuzzleRepository_List_Call {
	return &MockPuzzleRepository_List_Call{Call: _e.mock.On("List", ctx, filter, opts)}
}

func (_c *MockPuzzleRepository_List_Call) Run(run func(ctx context.Context, filter bson.M, opts *options.FindOptions)) *MockPuzzleRepository_List_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 bson.M
		if args[1] != nil {
			arg1 = args[1].(bson.M)
		}
		var arg2 *options.FindOptions
		if args[2] != nil {
			arg2 = args[2].(*options.FindOptions)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockPuzzleRepository_List_Call) Return(puzzles []*models.Puzzle, err error) *MockPuzzleRepository_List_Call {
	_c.Call.Return(puzzles, err)
	return _c
}

func (_c *MockPuzzleRepository_List_Call) RunAndReturn(run func(ctx context.Context, filter bson.M, opts *options.FindOptions) ([]*models.Puzzle, error)) *MockPuzzleRepository_List_Call {
	_c.Call.Return(run)
	return _c
}

// Update provides a mock function for the type MockPuzzleRepository
func (_mock *MockPuzzleRepository) Update(ctx context.Context, puzzle *models.Puzzle) error {
	ret := _mock.Called(ctx, puzzle)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.Puzzle) error); ok {
		r0 = returnFunc(ctx, puzzle)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockPuzzleRepository_Update_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Update'
type MockPuzzleRepository_Update_Call struct {
	*mock.Call
}

// Update is a helper method to define mock.On call
//   - ctx context.Context
//   - puzzle *models.Puzzle
func (_e *MockPuzzleRepository_Expecter) Update(ctx interface{}, puzzle interface{}) *MockPuzzleRepository_Update_Call {
	return &MockPuzzleRepository_Update_Call{Call: _e.mock.On("Update", ctx, puzzle)}
}

func (_c *MockPuzzleRepository_Update_Call) Run(run func(ctx context.Context, puzzle *models.Puzzle)) *MockPuzzleRepository_Update_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.Puzzle
		if args[1] != nil {
			arg1 = args[1].(*models.Puzzle)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockPuzzleRepository_Update_Call) Return(err error) *MockPuzzleRepository_Update_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockPuzzleRepository_Update_Call) RunAndReturn(run func(ctx context.Context, puzzle *models.Puzzle) error) *MockPuzzleRepository_Update_Call {
	_c.Call.Return(run)
	return _c
}
