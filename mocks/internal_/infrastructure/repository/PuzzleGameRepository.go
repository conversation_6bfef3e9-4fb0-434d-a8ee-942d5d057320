// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// NewMockPuzzleGameRepository creates a new instance of MockPuzzleGameRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockPuzzleGameRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockPuzzleGameRepository {
	mock := &MockPuzzleGameRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockPuzzleGameRepository is an autogenerated mock type for the PuzzleGameRepository type
type MockPuzzleGameRepository struct {
	mock.Mock
}

type MockPuzzleGameRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockPuzzleGameRepository) EXPECT() *MockPuzzleGameRepository_Expecter {
	return &MockPuzzleGameRepository_Expecter{mock: &_m.Mock}
}

// CancelPuzzleGames provides a mock function for the type MockPuzzleGameRepository
func (_mock *MockPuzzleGameRepository) CancelPuzzleGames(ctx context.Context, gameIDs []primitive.ObjectID) error {
	ret := _mock.Called(ctx, gameIDs)

	if len(ret) == 0 {
		panic("no return value specified for CancelPuzzleGames")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, []primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, gameIDs)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockPuzzleGameRepository_CancelPuzzleGames_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CancelPuzzleGames'
type MockPuzzleGameRepository_CancelPuzzleGames_Call struct {
	*mock.Call
}

// CancelPuzzleGames is a helper method to define mock.On call
//   - ctx context.Context
//   - gameIDs []primitive.ObjectID
func (_e *MockPuzzleGameRepository_Expecter) CancelPuzzleGames(ctx interface{}, gameIDs interface{}) *MockPuzzleGameRepository_CancelPuzzleGames_Call {
	return &MockPuzzleGameRepository_CancelPuzzleGames_Call{Call: _e.mock.On("CancelPuzzleGames", ctx, gameIDs)}
}

func (_c *MockPuzzleGameRepository_CancelPuzzleGames_Call) Run(run func(ctx context.Context, gameIDs []primitive.ObjectID)) *MockPuzzleGameRepository_CancelPuzzleGames_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 []primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].([]primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockPuzzleGameRepository_CancelPuzzleGames_Call) Return(err error) *MockPuzzleGameRepository_CancelPuzzleGames_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockPuzzleGameRepository_CancelPuzzleGames_Call) RunAndReturn(run func(ctx context.Context, gameIDs []primitive.ObjectID) error) *MockPuzzleGameRepository_CancelPuzzleGames_Call {
	_c.Call.Return(run)
	return _c
}

// CreateManyPuzzleGames provides a mock function for the type MockPuzzleGameRepository
func (_mock *MockPuzzleGameRepository) CreateManyPuzzleGames(ctx context.Context, games []*models.PuzzleGame) error {
	ret := _mock.Called(ctx, games)

	if len(ret) == 0 {
		panic("no return value specified for CreateManyPuzzleGames")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, []*models.PuzzleGame) error); ok {
		r0 = returnFunc(ctx, games)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockPuzzleGameRepository_CreateManyPuzzleGames_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateManyPuzzleGames'
type MockPuzzleGameRepository_CreateManyPuzzleGames_Call struct {
	*mock.Call
}

// CreateManyPuzzleGames is a helper method to define mock.On call
//   - ctx context.Context
//   - games []*models.PuzzleGame
func (_e *MockPuzzleGameRepository_Expecter) CreateManyPuzzleGames(ctx interface{}, games interface{}) *MockPuzzleGameRepository_CreateManyPuzzleGames_Call {
	return &MockPuzzleGameRepository_CreateManyPuzzleGames_Call{Call: _e.mock.On("CreateManyPuzzleGames", ctx, games)}
}

func (_c *MockPuzzleGameRepository_CreateManyPuzzleGames_Call) Run(run func(ctx context.Context, games []*models.PuzzleGame)) *MockPuzzleGameRepository_CreateManyPuzzleGames_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 []*models.PuzzleGame
		if args[1] != nil {
			arg1 = args[1].([]*models.PuzzleGame)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockPuzzleGameRepository_CreateManyPuzzleGames_Call) Return(err error) *MockPuzzleGameRepository_CreateManyPuzzleGames_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockPuzzleGameRepository_CreateManyPuzzleGames_Call) RunAndReturn(run func(ctx context.Context, games []*models.PuzzleGame) error) *MockPuzzleGameRepository_CreateManyPuzzleGames_Call {
	_c.Call.Return(run)
	return _c
}

// CreatePuzzleGame provides a mock function for the type MockPuzzleGameRepository
func (_mock *MockPuzzleGameRepository) CreatePuzzleGame(ctx context.Context, game *models.PuzzleGame) (*models.PuzzleGame, error) {
	ret := _mock.Called(ctx, game)

	if len(ret) == 0 {
		panic("no return value specified for CreatePuzzleGame")
	}

	var r0 *models.PuzzleGame
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.PuzzleGame) (*models.PuzzleGame, error)); ok {
		return returnFunc(ctx, game)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.PuzzleGame) *models.PuzzleGame); ok {
		r0 = returnFunc(ctx, game)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PuzzleGame)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.PuzzleGame) error); ok {
		r1 = returnFunc(ctx, game)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleGameRepository_CreatePuzzleGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreatePuzzleGame'
type MockPuzzleGameRepository_CreatePuzzleGame_Call struct {
	*mock.Call
}

// CreatePuzzleGame is a helper method to define mock.On call
//   - ctx context.Context
//   - game *models.PuzzleGame
func (_e *MockPuzzleGameRepository_Expecter) CreatePuzzleGame(ctx interface{}, game interface{}) *MockPuzzleGameRepository_CreatePuzzleGame_Call {
	return &MockPuzzleGameRepository_CreatePuzzleGame_Call{Call: _e.mock.On("CreatePuzzleGame", ctx, game)}
}

func (_c *MockPuzzleGameRepository_CreatePuzzleGame_Call) Run(run func(ctx context.Context, game *models.PuzzleGame)) *MockPuzzleGameRepository_CreatePuzzleGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.PuzzleGame
		if args[1] != nil {
			arg1 = args[1].(*models.PuzzleGame)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockPuzzleGameRepository_CreatePuzzleGame_Call) Return(puzzleGame *models.PuzzleGame, err error) *MockPuzzleGameRepository_CreatePuzzleGame_Call {
	_c.Call.Return(puzzleGame, err)
	return _c
}

func (_c *MockPuzzleGameRepository_CreatePuzzleGame_Call) RunAndReturn(run func(ctx context.Context, game *models.PuzzleGame) (*models.PuzzleGame, error)) *MockPuzzleGameRepository_CreatePuzzleGame_Call {
	_c.Call.Return(run)
	return _c
}

// FindActivePuzzleGamesByUserID provides a mock function for the type MockPuzzleGameRepository
func (_mock *MockPuzzleGameRepository) FindActivePuzzleGamesByUserID(ctx context.Context, userID primitive.ObjectID) ([]models.PuzzleGame, error) {
	ret := _mock.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for FindActivePuzzleGamesByUserID")
	}

	var r0 []models.PuzzleGame
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) ([]models.PuzzleGame, error)); ok {
		return returnFunc(ctx, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) []models.PuzzleGame); ok {
		r0 = returnFunc(ctx, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.PuzzleGame)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleGameRepository_FindActivePuzzleGamesByUserID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindActivePuzzleGamesByUserID'
type MockPuzzleGameRepository_FindActivePuzzleGamesByUserID_Call struct {
	*mock.Call
}

// FindActivePuzzleGamesByUserID is a helper method to define mock.On call
//   - ctx context.Context
//   - userID primitive.ObjectID
func (_e *MockPuzzleGameRepository_Expecter) FindActivePuzzleGamesByUserID(ctx interface{}, userID interface{}) *MockPuzzleGameRepository_FindActivePuzzleGamesByUserID_Call {
	return &MockPuzzleGameRepository_FindActivePuzzleGamesByUserID_Call{Call: _e.mock.On("FindActivePuzzleGamesByUserID", ctx, userID)}
}

func (_c *MockPuzzleGameRepository_FindActivePuzzleGamesByUserID_Call) Run(run func(ctx context.Context, userID primitive.ObjectID)) *MockPuzzleGameRepository_FindActivePuzzleGamesByUserID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockPuzzleGameRepository_FindActivePuzzleGamesByUserID_Call) Return(puzzleGames []models.PuzzleGame, err error) *MockPuzzleGameRepository_FindActivePuzzleGamesByUserID_Call {
	_c.Call.Return(puzzleGames, err)
	return _c
}

func (_c *MockPuzzleGameRepository_FindActivePuzzleGamesByUserID_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID) ([]models.PuzzleGame, error)) *MockPuzzleGameRepository_FindActivePuzzleGamesByUserID_Call {
	_c.Call.Return(run)
	return _c
}

// FindMany provides a mock function for the type MockPuzzleGameRepository
func (_mock *MockPuzzleGameRepository) FindMany(ctx context.Context, filter bson.M, opts ...*options.FindOptions) ([]*models.PuzzleGame, error) {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, filter, opts)
	} else {
		tmpRet = _mock.Called(ctx, filter)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for FindMany")
	}

	var r0 []*models.PuzzleGame
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, ...*options.FindOptions) ([]*models.PuzzleGame, error)); ok {
		return returnFunc(ctx, filter, opts...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, ...*options.FindOptions) []*models.PuzzleGame); ok {
		r0 = returnFunc(ctx, filter, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.PuzzleGame)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, bson.M, ...*options.FindOptions) error); ok {
		r1 = returnFunc(ctx, filter, opts...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleGameRepository_FindMany_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindMany'
type MockPuzzleGameRepository_FindMany_Call struct {
	*mock.Call
}

// FindMany is a helper method to define mock.On call
//   - ctx context.Context
//   - filter bson.M
//   - opts ...*options.FindOptions
func (_e *MockPuzzleGameRepository_Expecter) FindMany(ctx interface{}, filter interface{}, opts ...interface{}) *MockPuzzleGameRepository_FindMany_Call {
	return &MockPuzzleGameRepository_FindMany_Call{Call: _e.mock.On("FindMany",
		append([]interface{}{ctx, filter}, opts...)...)}
}

func (_c *MockPuzzleGameRepository_FindMany_Call) Run(run func(ctx context.Context, filter bson.M, opts ...*options.FindOptions)) *MockPuzzleGameRepository_FindMany_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 bson.M
		if args[1] != nil {
			arg1 = args[1].(bson.M)
		}
		var arg2 []*options.FindOptions
		var variadicArgs []*options.FindOptions
		if len(args) > 2 {
			variadicArgs = args[2].([]*options.FindOptions)
		}
		arg2 = variadicArgs
		run(
			arg0,
			arg1,
			arg2...,
		)
	})
	return _c
}

func (_c *MockPuzzleGameRepository_FindMany_Call) Return(puzzleGames []*models.PuzzleGame, err error) *MockPuzzleGameRepository_FindMany_Call {
	_c.Call.Return(puzzleGames, err)
	return _c
}

func (_c *MockPuzzleGameRepository_FindMany_Call) RunAndReturn(run func(ctx context.Context, filter bson.M, opts ...*options.FindOptions) ([]*models.PuzzleGame, error)) *MockPuzzleGameRepository_FindMany_Call {
	_c.Call.Return(run)
	return _c
}

// FindOne provides a mock function for the type MockPuzzleGameRepository
func (_mock *MockPuzzleGameRepository) FindOne(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions) (*models.PuzzleGame, error) {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, filter, opts)
	} else {
		tmpRet = _mock.Called(ctx, filter)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for FindOne")
	}

	var r0 *models.PuzzleGame
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, ...*options.FindOneOptions) (*models.PuzzleGame, error)); ok {
		return returnFunc(ctx, filter, opts...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, ...*options.FindOneOptions) *models.PuzzleGame); ok {
		r0 = returnFunc(ctx, filter, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PuzzleGame)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, bson.M, ...*options.FindOneOptions) error); ok {
		r1 = returnFunc(ctx, filter, opts...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleGameRepository_FindOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindOne'
type MockPuzzleGameRepository_FindOne_Call struct {
	*mock.Call
}

// FindOne is a helper method to define mock.On call
//   - ctx context.Context
//   - filter bson.M
//   - opts ...*options.FindOneOptions
func (_e *MockPuzzleGameRepository_Expecter) FindOne(ctx interface{}, filter interface{}, opts ...interface{}) *MockPuzzleGameRepository_FindOne_Call {
	return &MockPuzzleGameRepository_FindOne_Call{Call: _e.mock.On("FindOne",
		append([]interface{}{ctx, filter}, opts...)...)}
}

func (_c *MockPuzzleGameRepository_FindOne_Call) Run(run func(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions)) *MockPuzzleGameRepository_FindOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 bson.M
		if args[1] != nil {
			arg1 = args[1].(bson.M)
		}
		var arg2 []*options.FindOneOptions
		var variadicArgs []*options.FindOneOptions
		if len(args) > 2 {
			variadicArgs = args[2].([]*options.FindOneOptions)
		}
		arg2 = variadicArgs
		run(
			arg0,
			arg1,
			arg2...,
		)
	})
	return _c
}

func (_c *MockPuzzleGameRepository_FindOne_Call) Return(puzzleGame *models.PuzzleGame, err error) *MockPuzzleGameRepository_FindOne_Call {
	_c.Call.Return(puzzleGame, err)
	return _c
}

func (_c *MockPuzzleGameRepository_FindOne_Call) RunAndReturn(run func(ctx context.Context, filter bson.M, opts ...*options.FindOneOptions) (*models.PuzzleGame, error)) *MockPuzzleGameRepository_FindOne_Call {
	_c.Call.Return(run)
	return _c
}

// GetMinifiedPuzzleGames provides a mock function for the type MockPuzzleGameRepository
func (_mock *MockPuzzleGameRepository) GetMinifiedPuzzleGames(ctx context.Context, filter bson.M, opts ...*options.FindOptions) ([]*models.MinifiedPuzzleGame, error) {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, filter, opts)
	} else {
		tmpRet = _mock.Called(ctx, filter)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for GetMinifiedPuzzleGames")
	}

	var r0 []*models.MinifiedPuzzleGame
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, ...*options.FindOptions) ([]*models.MinifiedPuzzleGame, error)); ok {
		return returnFunc(ctx, filter, opts...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, ...*options.FindOptions) []*models.MinifiedPuzzleGame); ok {
		r0 = returnFunc(ctx, filter, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.MinifiedPuzzleGame)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, bson.M, ...*options.FindOptions) error); ok {
		r1 = returnFunc(ctx, filter, opts...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleGameRepository_GetMinifiedPuzzleGames_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMinifiedPuzzleGames'
type MockPuzzleGameRepository_GetMinifiedPuzzleGames_Call struct {
	*mock.Call
}

// GetMinifiedPuzzleGames is a helper method to define mock.On call
//   - ctx context.Context
//   - filter bson.M
//   - opts ...*options.FindOptions
func (_e *MockPuzzleGameRepository_Expecter) GetMinifiedPuzzleGames(ctx interface{}, filter interface{}, opts ...interface{}) *MockPuzzleGameRepository_GetMinifiedPuzzleGames_Call {
	return &MockPuzzleGameRepository_GetMinifiedPuzzleGames_Call{Call: _e.mock.On("GetMinifiedPuzzleGames",
		append([]interface{}{ctx, filter}, opts...)...)}
}

func (_c *MockPuzzleGameRepository_GetMinifiedPuzzleGames_Call) Run(run func(ctx context.Context, filter bson.M, opts ...*options.FindOptions)) *MockPuzzleGameRepository_GetMinifiedPuzzleGames_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 bson.M
		if args[1] != nil {
			arg1 = args[1].(bson.M)
		}
		var arg2 []*options.FindOptions
		var variadicArgs []*options.FindOptions
		if len(args) > 2 {
			variadicArgs = args[2].([]*options.FindOptions)
		}
		arg2 = variadicArgs
		run(
			arg0,
			arg1,
			arg2...,
		)
	})
	return _c
}

func (_c *MockPuzzleGameRepository_GetMinifiedPuzzleGames_Call) Return(minifiedPuzzleGames []*models.MinifiedPuzzleGame, err error) *MockPuzzleGameRepository_GetMinifiedPuzzleGames_Call {
	_c.Call.Return(minifiedPuzzleGames, err)
	return _c
}

func (_c *MockPuzzleGameRepository_GetMinifiedPuzzleGames_Call) RunAndReturn(run func(ctx context.Context, filter bson.M, opts ...*options.FindOptions) ([]*models.MinifiedPuzzleGame, error)) *MockPuzzleGameRepository_GetMinifiedPuzzleGames_Call {
	_c.Call.Return(run)
	return _c
}

// GetPuzzleGameByID provides a mock function for the type MockPuzzleGameRepository
func (_mock *MockPuzzleGameRepository) GetPuzzleGameByID(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error) {
	ret := _mock.Called(ctx, gameID)

	if len(ret) == 0 {
		panic("no return value specified for GetPuzzleGameByID")
	}

	var r0 *models.PuzzleGame
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.PuzzleGame, error)); ok {
		return returnFunc(ctx, gameID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.PuzzleGame); ok {
		r0 = returnFunc(ctx, gameID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PuzzleGame)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, gameID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockPuzzleGameRepository_GetPuzzleGameByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPuzzleGameByID'
type MockPuzzleGameRepository_GetPuzzleGameByID_Call struct {
	*mock.Call
}

// GetPuzzleGameByID is a helper method to define mock.On call
//   - ctx context.Context
//   - gameID primitive.ObjectID
func (_e *MockPuzzleGameRepository_Expecter) GetPuzzleGameByID(ctx interface{}, gameID interface{}) *MockPuzzleGameRepository_GetPuzzleGameByID_Call {
	return &MockPuzzleGameRepository_GetPuzzleGameByID_Call{Call: _e.mock.On("GetPuzzleGameByID", ctx, gameID)}
}

func (_c *MockPuzzleGameRepository_GetPuzzleGameByID_Call) Run(run func(ctx context.Context, gameID primitive.ObjectID)) *MockPuzzleGameRepository_GetPuzzleGameByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockPuzzleGameRepository_GetPuzzleGameByID_Call) Return(puzzleGame *models.PuzzleGame, err error) *MockPuzzleGameRepository_GetPuzzleGameByID_Call {
	_c.Call.Return(puzzleGame, err)
	return _c
}

func (_c *MockPuzzleGameRepository_GetPuzzleGameByID_Call) RunAndReturn(run func(ctx context.Context, gameID primitive.ObjectID) (*models.PuzzleGame, error)) *MockPuzzleGameRepository_GetPuzzleGameByID_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateOne provides a mock function for the type MockPuzzleGameRepository
func (_mock *MockPuzzleGameRepository) UpdateOne(ctx context.Context, filter bson.M, update bson.M) error {
	ret := _mock.Called(ctx, filter, update)

	if len(ret) == 0 {
		panic("no return value specified for UpdateOne")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, bson.M) error); ok {
		r0 = returnFunc(ctx, filter, update)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockPuzzleGameRepository_UpdateOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateOne'
type MockPuzzleGameRepository_UpdateOne_Call struct {
	*mock.Call
}

// UpdateOne is a helper method to define mock.On call
//   - ctx context.Context
//   - filter bson.M
//   - update bson.M
func (_e *MockPuzzleGameRepository_Expecter) UpdateOne(ctx interface{}, filter interface{}, update interface{}) *MockPuzzleGameRepository_UpdateOne_Call {
	return &MockPuzzleGameRepository_UpdateOne_Call{Call: _e.mock.On("UpdateOne", ctx, filter, update)}
}

func (_c *MockPuzzleGameRepository_UpdateOne_Call) Run(run func(ctx context.Context, filter bson.M, update bson.M)) *MockPuzzleGameRepository_UpdateOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 bson.M
		if args[1] != nil {
			arg1 = args[1].(bson.M)
		}
		var arg2 bson.M
		if args[2] != nil {
			arg2 = args[2].(bson.M)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockPuzzleGameRepository_UpdateOne_Call) Return(err error) *MockPuzzleGameRepository_UpdateOne_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockPuzzleGameRepository_UpdateOne_Call) RunAndReturn(run func(ctx context.Context, filter bson.M, update bson.M) error) *MockPuzzleGameRepository_UpdateOne_Call {
	_c.Call.Return(run)
	return _c
}

// UpdatePuzzleGame provides a mock function for the type MockPuzzleGameRepository
func (_mock *MockPuzzleGameRepository) UpdatePuzzleGame(ctx context.Context, game *models.PuzzleGame) error {
	ret := _mock.Called(ctx, game)

	if len(ret) == 0 {
		panic("no return value specified for UpdatePuzzleGame")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.PuzzleGame) error); ok {
		r0 = returnFunc(ctx, game)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockPuzzleGameRepository_UpdatePuzzleGame_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdatePuzzleGame'
type MockPuzzleGameRepository_UpdatePuzzleGame_Call struct {
	*mock.Call
}

// UpdatePuzzleGame is a helper method to define mock.On call
//   - ctx context.Context
//   - game *models.PuzzleGame
func (_e *MockPuzzleGameRepository_Expecter) UpdatePuzzleGame(ctx interface{}, game interface{}) *MockPuzzleGameRepository_UpdatePuzzleGame_Call {
	return &MockPuzzleGameRepository_UpdatePuzzleGame_Call{Call: _e.mock.On("UpdatePuzzleGame", ctx, game)}
}

func (_c *MockPuzzleGameRepository_UpdatePuzzleGame_Call) Run(run func(ctx context.Context, game *models.PuzzleGame)) *MockPuzzleGameRepository_UpdatePuzzleGame_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.PuzzleGame
		if args[1] != nil {
			arg1 = args[1].(*models.PuzzleGame)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockPuzzleGameRepository_UpdatePuzzleGame_Call) Return(err error) *MockPuzzleGameRepository_UpdatePuzzleGame_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockPuzzleGameRepository_UpdatePuzzleGame_Call) RunAndReturn(run func(ctx context.Context, game *models.PuzzleGame) error) *MockPuzzleGameRepository_UpdatePuzzleGame_Call {
	_c.Call.Return(run)
	return _c
}
