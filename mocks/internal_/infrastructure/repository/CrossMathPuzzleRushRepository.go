// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockCrossMathPuzzleRushRepository creates a new instance of MockCrossMathPuzzleRushRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockCrossMathPuzzleRushRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockCrossMathPuzzleRushRepository {
	mock := &MockCrossMathPuzzleRushRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockCrossMathPuzzleRushRepository is an autogenerated mock type for the CrossMathPuzzleRushRepository type
type MockCrossMathPuzzleRushRepository struct {
	mock.Mock
}

type MockCrossMathPuzzleRushRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockCrossMathPuzzleRushRepository) EXPECT() *MockCrossMathPuzzleRushRepository_Expecter {
	return &MockCrossMathPuzzleRushRepository_Expecter{mock: &_m.Mock}
}

// FindUserRushStatsById provides a mock function for the type MockCrossMathPuzzleRushRepository
func (_mock *MockCrossMathPuzzleRushRepository) FindUserRushStatsById(ctx context.Context, userId primitive.ObjectID) (*models.CrossMathPuzzleRush, error) {
	ret := _mock.Called(ctx, userId)

	if len(ret) == 0 {
		panic("no return value specified for FindUserRushStatsById")
	}

	var r0 *models.CrossMathPuzzleRush
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.CrossMathPuzzleRush, error)); ok {
		return returnFunc(ctx, userId)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.CrossMathPuzzleRush); ok {
		r0 = returnFunc(ctx, userId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.CrossMathPuzzleRush)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, userId)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockCrossMathPuzzleRushRepository_FindUserRushStatsById_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindUserRushStatsById'
type MockCrossMathPuzzleRushRepository_FindUserRushStatsById_Call struct {
	*mock.Call
}

// FindUserRushStatsById is a helper method to define mock.On call
//   - ctx context.Context
//   - userId primitive.ObjectID
func (_e *MockCrossMathPuzzleRushRepository_Expecter) FindUserRushStatsById(ctx interface{}, userId interface{}) *MockCrossMathPuzzleRushRepository_FindUserRushStatsById_Call {
	return &MockCrossMathPuzzleRushRepository_FindUserRushStatsById_Call{Call: _e.mock.On("FindUserRushStatsById", ctx, userId)}
}

func (_c *MockCrossMathPuzzleRushRepository_FindUserRushStatsById_Call) Run(run func(ctx context.Context, userId primitive.ObjectID)) *MockCrossMathPuzzleRushRepository_FindUserRushStatsById_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockCrossMathPuzzleRushRepository_FindUserRushStatsById_Call) Return(crossMathPuzzleRush *models.CrossMathPuzzleRush, err error) *MockCrossMathPuzzleRushRepository_FindUserRushStatsById_Call {
	_c.Call.Return(crossMathPuzzleRush, err)
	return _c
}

func (_c *MockCrossMathPuzzleRushRepository_FindUserRushStatsById_Call) RunAndReturn(run func(ctx context.Context, userId primitive.ObjectID) (*models.CrossMathPuzzleRush, error)) *MockCrossMathPuzzleRushRepository_FindUserRushStatsById_Call {
	_c.Call.Return(run)
	return _c
}

// GetFriendsTop5InPuzzleRush provides a mock function for the type MockCrossMathPuzzleRushRepository
func (_mock *MockCrossMathPuzzleRushRepository) GetFriendsTop5InPuzzleRush(ctx context.Context, userID primitive.ObjectID) ([]*models.CrossMathPuzzleRushPlayerInfo, error) {
	ret := _mock.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetFriendsTop5InPuzzleRush")
	}

	var r0 []*models.CrossMathPuzzleRushPlayerInfo
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) ([]*models.CrossMathPuzzleRushPlayerInfo, error)); ok {
		return returnFunc(ctx, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) []*models.CrossMathPuzzleRushPlayerInfo); ok {
		r0 = returnFunc(ctx, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.CrossMathPuzzleRushPlayerInfo)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockCrossMathPuzzleRushRepository_GetFriendsTop5InPuzzleRush_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFriendsTop5InPuzzleRush'
type MockCrossMathPuzzleRushRepository_GetFriendsTop5InPuzzleRush_Call struct {
	*mock.Call
}

// GetFriendsTop5InPuzzleRush is a helper method to define mock.On call
//   - ctx context.Context
//   - userID primitive.ObjectID
func (_e *MockCrossMathPuzzleRushRepository_Expecter) GetFriendsTop5InPuzzleRush(ctx interface{}, userID interface{}) *MockCrossMathPuzzleRushRepository_GetFriendsTop5InPuzzleRush_Call {
	return &MockCrossMathPuzzleRushRepository_GetFriendsTop5InPuzzleRush_Call{Call: _e.mock.On("GetFriendsTop5InPuzzleRush", ctx, userID)}
}

func (_c *MockCrossMathPuzzleRushRepository_GetFriendsTop5InPuzzleRush_Call) Run(run func(ctx context.Context, userID primitive.ObjectID)) *MockCrossMathPuzzleRushRepository_GetFriendsTop5InPuzzleRush_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockCrossMathPuzzleRushRepository_GetFriendsTop5InPuzzleRush_Call) Return(crossMathPuzzleRushPlayerInfos []*models.CrossMathPuzzleRushPlayerInfo, err error) *MockCrossMathPuzzleRushRepository_GetFriendsTop5InPuzzleRush_Call {
	_c.Call.Return(crossMathPuzzleRushPlayerInfos, err)
	return _c
}

func (_c *MockCrossMathPuzzleRushRepository_GetFriendsTop5InPuzzleRush_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID) ([]*models.CrossMathPuzzleRushPlayerInfo, error)) *MockCrossMathPuzzleRushRepository_GetFriendsTop5InPuzzleRush_Call {
	_c.Call.Return(run)
	return _c
}

// GetGlobalTop5InPuzzleRush provides a mock function for the type MockCrossMathPuzzleRushRepository
func (_mock *MockCrossMathPuzzleRushRepository) GetGlobalTop5InPuzzleRush(ctx context.Context) ([]*models.CrossMathPuzzleRushPlayerInfo, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetGlobalTop5InPuzzleRush")
	}

	var r0 []*models.CrossMathPuzzleRushPlayerInfo
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) ([]*models.CrossMathPuzzleRushPlayerInfo, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) []*models.CrossMathPuzzleRushPlayerInfo); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.CrossMathPuzzleRushPlayerInfo)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockCrossMathPuzzleRushRepository_GetGlobalTop5InPuzzleRush_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetGlobalTop5InPuzzleRush'
type MockCrossMathPuzzleRushRepository_GetGlobalTop5InPuzzleRush_Call struct {
	*mock.Call
}

// GetGlobalTop5InPuzzleRush is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockCrossMathPuzzleRushRepository_Expecter) GetGlobalTop5InPuzzleRush(ctx interface{}) *MockCrossMathPuzzleRushRepository_GetGlobalTop5InPuzzleRush_Call {
	return &MockCrossMathPuzzleRushRepository_GetGlobalTop5InPuzzleRush_Call{Call: _e.mock.On("GetGlobalTop5InPuzzleRush", ctx)}
}

func (_c *MockCrossMathPuzzleRushRepository_GetGlobalTop5InPuzzleRush_Call) Run(run func(ctx context.Context)) *MockCrossMathPuzzleRushRepository_GetGlobalTop5InPuzzleRush_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockCrossMathPuzzleRushRepository_GetGlobalTop5InPuzzleRush_Call) Return(crossMathPuzzleRushPlayerInfos []*models.CrossMathPuzzleRushPlayerInfo, err error) *MockCrossMathPuzzleRushRepository_GetGlobalTop5InPuzzleRush_Call {
	_c.Call.Return(crossMathPuzzleRushPlayerInfos, err)
	return _c
}

func (_c *MockCrossMathPuzzleRushRepository_GetGlobalTop5InPuzzleRush_Call) RunAndReturn(run func(ctx context.Context) ([]*models.CrossMathPuzzleRushPlayerInfo, error)) *MockCrossMathPuzzleRushRepository_GetGlobalTop5InPuzzleRush_Call {
	_c.Call.Return(run)
	return _c
}

// GetMyGlobalRankInPuzzleRush provides a mock function for the type MockCrossMathPuzzleRushRepository
func (_mock *MockCrossMathPuzzleRushRepository) GetMyGlobalRankInPuzzleRush(ctx context.Context, userID primitive.ObjectID, score int) (int, error) {
	ret := _mock.Called(ctx, userID, score)

	if len(ret) == 0 {
		panic("no return value specified for GetMyGlobalRankInPuzzleRush")
	}

	var r0 int
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int) (int, error)); ok {
		return returnFunc(ctx, userID, score)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int) int); ok {
		r0 = returnFunc(ctx, userID, score)
	} else {
		r0 = ret.Get(0).(int)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, int) error); ok {
		r1 = returnFunc(ctx, userID, score)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockCrossMathPuzzleRushRepository_GetMyGlobalRankInPuzzleRush_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMyGlobalRankInPuzzleRush'
type MockCrossMathPuzzleRushRepository_GetMyGlobalRankInPuzzleRush_Call struct {
	*mock.Call
}

// GetMyGlobalRankInPuzzleRush is a helper method to define mock.On call
//   - ctx context.Context
//   - userID primitive.ObjectID
//   - score int
func (_e *MockCrossMathPuzzleRushRepository_Expecter) GetMyGlobalRankInPuzzleRush(ctx interface{}, userID interface{}, score interface{}) *MockCrossMathPuzzleRushRepository_GetMyGlobalRankInPuzzleRush_Call {
	return &MockCrossMathPuzzleRushRepository_GetMyGlobalRankInPuzzleRush_Call{Call: _e.mock.On("GetMyGlobalRankInPuzzleRush", ctx, userID, score)}
}

func (_c *MockCrossMathPuzzleRushRepository_GetMyGlobalRankInPuzzleRush_Call) Run(run func(ctx context.Context, userID primitive.ObjectID, score int)) *MockCrossMathPuzzleRushRepository_GetMyGlobalRankInPuzzleRush_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 int
		if args[2] != nil {
			arg2 = args[2].(int)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockCrossMathPuzzleRushRepository_GetMyGlobalRankInPuzzleRush_Call) Return(n int, err error) *MockCrossMathPuzzleRushRepository_GetMyGlobalRankInPuzzleRush_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockCrossMathPuzzleRushRepository_GetMyGlobalRankInPuzzleRush_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID, score int) (int, error)) *MockCrossMathPuzzleRushRepository_GetMyGlobalRankInPuzzleRush_Call {
	_c.Call.Return(run)
	return _c
}

// GetMyRankRelativeToFriend provides a mock function for the type MockCrossMathPuzzleRushRepository
func (_mock *MockCrossMathPuzzleRushRepository) GetMyRankRelativeToFriend(ctx context.Context, userID primitive.ObjectID, score int) (int, error) {
	ret := _mock.Called(ctx, userID, score)

	if len(ret) == 0 {
		panic("no return value specified for GetMyRankRelativeToFriend")
	}

	var r0 int
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int) (int, error)); ok {
		return returnFunc(ctx, userID, score)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int) int); ok {
		r0 = returnFunc(ctx, userID, score)
	} else {
		r0 = ret.Get(0).(int)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, int) error); ok {
		r1 = returnFunc(ctx, userID, score)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockCrossMathPuzzleRushRepository_GetMyRankRelativeToFriend_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMyRankRelativeToFriend'
type MockCrossMathPuzzleRushRepository_GetMyRankRelativeToFriend_Call struct {
	*mock.Call
}

// GetMyRankRelativeToFriend is a helper method to define mock.On call
//   - ctx context.Context
//   - userID primitive.ObjectID
//   - score int
func (_e *MockCrossMathPuzzleRushRepository_Expecter) GetMyRankRelativeToFriend(ctx interface{}, userID interface{}, score interface{}) *MockCrossMathPuzzleRushRepository_GetMyRankRelativeToFriend_Call {
	return &MockCrossMathPuzzleRushRepository_GetMyRankRelativeToFriend_Call{Call: _e.mock.On("GetMyRankRelativeToFriend", ctx, userID, score)}
}

func (_c *MockCrossMathPuzzleRushRepository_GetMyRankRelativeToFriend_Call) Run(run func(ctx context.Context, userID primitive.ObjectID, score int)) *MockCrossMathPuzzleRushRepository_GetMyRankRelativeToFriend_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 int
		if args[2] != nil {
			arg2 = args[2].(int)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockCrossMathPuzzleRushRepository_GetMyRankRelativeToFriend_Call) Return(n int, err error) *MockCrossMathPuzzleRushRepository_GetMyRankRelativeToFriend_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockCrossMathPuzzleRushRepository_GetMyRankRelativeToFriend_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID, score int) (int, error)) *MockCrossMathPuzzleRushRepository_GetMyRankRelativeToFriend_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserPuzzleRushStats provides a mock function for the type MockCrossMathPuzzleRushRepository
func (_mock *MockCrossMathPuzzleRushRepository) GetUserPuzzleRushStats(ctx context.Context, userID primitive.ObjectID) (*models.CrossMathPuzzleRush, error) {
	ret := _mock.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetUserPuzzleRushStats")
	}

	var r0 *models.CrossMathPuzzleRush
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.CrossMathPuzzleRush, error)); ok {
		return returnFunc(ctx, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.CrossMathPuzzleRush); ok {
		r0 = returnFunc(ctx, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.CrossMathPuzzleRush)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockCrossMathPuzzleRushRepository_GetUserPuzzleRushStats_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserPuzzleRushStats'
type MockCrossMathPuzzleRushRepository_GetUserPuzzleRushStats_Call struct {
	*mock.Call
}

// GetUserPuzzleRushStats is a helper method to define mock.On call
//   - ctx context.Context
//   - userID primitive.ObjectID
func (_e *MockCrossMathPuzzleRushRepository_Expecter) GetUserPuzzleRushStats(ctx interface{}, userID interface{}) *MockCrossMathPuzzleRushRepository_GetUserPuzzleRushStats_Call {
	return &MockCrossMathPuzzleRushRepository_GetUserPuzzleRushStats_Call{Call: _e.mock.On("GetUserPuzzleRushStats", ctx, userID)}
}

func (_c *MockCrossMathPuzzleRushRepository_GetUserPuzzleRushStats_Call) Run(run func(ctx context.Context, userID primitive.ObjectID)) *MockCrossMathPuzzleRushRepository_GetUserPuzzleRushStats_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockCrossMathPuzzleRushRepository_GetUserPuzzleRushStats_Call) Return(crossMathPuzzleRush *models.CrossMathPuzzleRush, err error) *MockCrossMathPuzzleRushRepository_GetUserPuzzleRushStats_Call {
	_c.Call.Return(crossMathPuzzleRush, err)
	return _c
}

func (_c *MockCrossMathPuzzleRushRepository_GetUserPuzzleRushStats_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID) (*models.CrossMathPuzzleRush, error)) *MockCrossMathPuzzleRushRepository_GetUserPuzzleRushStats_Call {
	_c.Call.Return(run)
	return _c
}

// InsertOne provides a mock function for the type MockCrossMathPuzzleRushRepository
func (_mock *MockCrossMathPuzzleRushRepository) InsertOne(ctx context.Context, crossMathPuzzleRush models.CrossMathPuzzleRush) error {
	ret := _mock.Called(ctx, crossMathPuzzleRush)

	if len(ret) == 0 {
		panic("no return value specified for InsertOne")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, models.CrossMathPuzzleRush) error); ok {
		r0 = returnFunc(ctx, crossMathPuzzleRush)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockCrossMathPuzzleRushRepository_InsertOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'InsertOne'
type MockCrossMathPuzzleRushRepository_InsertOne_Call struct {
	*mock.Call
}

// InsertOne is a helper method to define mock.On call
//   - ctx context.Context
//   - crossMathPuzzleRush models.CrossMathPuzzleRush
func (_e *MockCrossMathPuzzleRushRepository_Expecter) InsertOne(ctx interface{}, crossMathPuzzleRush interface{}) *MockCrossMathPuzzleRushRepository_InsertOne_Call {
	return &MockCrossMathPuzzleRushRepository_InsertOne_Call{Call: _e.mock.On("InsertOne", ctx, crossMathPuzzleRush)}
}

func (_c *MockCrossMathPuzzleRushRepository_InsertOne_Call) Run(run func(ctx context.Context, crossMathPuzzleRush models.CrossMathPuzzleRush)) *MockCrossMathPuzzleRushRepository_InsertOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 models.CrossMathPuzzleRush
		if args[1] != nil {
			arg1 = args[1].(models.CrossMathPuzzleRush)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockCrossMathPuzzleRushRepository_InsertOne_Call) Return(err error) *MockCrossMathPuzzleRushRepository_InsertOne_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockCrossMathPuzzleRushRepository_InsertOne_Call) RunAndReturn(run func(ctx context.Context, crossMathPuzzleRush models.CrossMathPuzzleRush) error) *MockCrossMathPuzzleRushRepository_InsertOne_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateOne provides a mock function for the type MockCrossMathPuzzleRushRepository
func (_mock *MockCrossMathPuzzleRushRepository) UpdateOne(ctx context.Context, filter bson.M, update bson.M) error {
	ret := _mock.Called(ctx, filter, update)

	if len(ret) == 0 {
		panic("no return value specified for UpdateOne")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, bson.M, bson.M) error); ok {
		r0 = returnFunc(ctx, filter, update)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockCrossMathPuzzleRushRepository_UpdateOne_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateOne'
type MockCrossMathPuzzleRushRepository_UpdateOne_Call struct {
	*mock.Call
}

// UpdateOne is a helper method to define mock.On call
//   - ctx context.Context
//   - filter bson.M
//   - update bson.M
func (_e *MockCrossMathPuzzleRushRepository_Expecter) UpdateOne(ctx interface{}, filter interface{}, update interface{}) *MockCrossMathPuzzleRushRepository_UpdateOne_Call {
	return &MockCrossMathPuzzleRushRepository_UpdateOne_Call{Call: _e.mock.On("UpdateOne", ctx, filter, update)}
}

func (_c *MockCrossMathPuzzleRushRepository_UpdateOne_Call) Run(run func(ctx context.Context, filter bson.M, update bson.M)) *MockCrossMathPuzzleRushRepository_UpdateOne_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 bson.M
		if args[1] != nil {
			arg1 = args[1].(bson.M)
		}
		var arg2 bson.M
		if args[2] != nil {
			arg2 = args[2].(bson.M)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockCrossMathPuzzleRushRepository_UpdateOne_Call) Return(err error) *MockCrossMathPuzzleRushRepository_UpdateOne_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockCrossMathPuzzleRushRepository_UpdateOne_Call) RunAndReturn(run func(ctx context.Context, filter bson.M, update bson.M) error) *MockCrossMathPuzzleRushRepository_UpdateOne_Call {
	_c.Call.Return(run)
	return _c
}
