// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"
	"time"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// NewMockUserDailyActivityRepository creates a new instance of MockUserDailyActivityRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockUserDailyActivityRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockUserDailyActivityRepository {
	mock := &MockUserDailyActivityRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockUserDailyActivityRepository is an autogenerated mock type for the UserDailyActivityRepository type
type MockUserDailyActivityRepository struct {
	mock.Mock
}

type MockUserDailyActivityRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockUserDailyActivityRepository) EXPECT() *MockUserDailyActivityRepository_Expecter {
	return &MockUserDailyActivityRepository_Expecter{mock: &_m.Mock}
}

// Aggregate provides a mock function for the type MockUserDailyActivityRepository
func (_mock *MockUserDailyActivityRepository) Aggregate(ctx context.Context, pipeline mongo.Pipeline, opts ...*options.AggregateOptions) ([]*models.UserDailyActivity, error) {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, pipeline, opts)
	} else {
		tmpRet = _mock.Called(ctx, pipeline)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for Aggregate")
	}

	var r0 []*models.UserDailyActivity
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, mongo.Pipeline, ...*options.AggregateOptions) ([]*models.UserDailyActivity, error)); ok {
		return returnFunc(ctx, pipeline, opts...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, mongo.Pipeline, ...*options.AggregateOptions) []*models.UserDailyActivity); ok {
		r0 = returnFunc(ctx, pipeline, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.UserDailyActivity)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, mongo.Pipeline, ...*options.AggregateOptions) error); ok {
		r1 = returnFunc(ctx, pipeline, opts...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserDailyActivityRepository_Aggregate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Aggregate'
type MockUserDailyActivityRepository_Aggregate_Call struct {
	*mock.Call
}

// Aggregate is a helper method to define mock.On call
//   - ctx context.Context
//   - pipeline mongo.Pipeline
//   - opts ...*options.AggregateOptions
func (_e *MockUserDailyActivityRepository_Expecter) Aggregate(ctx interface{}, pipeline interface{}, opts ...interface{}) *MockUserDailyActivityRepository_Aggregate_Call {
	return &MockUserDailyActivityRepository_Aggregate_Call{Call: _e.mock.On("Aggregate",
		append([]interface{}{ctx, pipeline}, opts...)...)}
}

func (_c *MockUserDailyActivityRepository_Aggregate_Call) Run(run func(ctx context.Context, pipeline mongo.Pipeline, opts ...*options.AggregateOptions)) *MockUserDailyActivityRepository_Aggregate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 mongo.Pipeline
		if args[1] != nil {
			arg1 = args[1].(mongo.Pipeline)
		}
		var arg2 []*options.AggregateOptions
		var variadicArgs []*options.AggregateOptions
		if len(args) > 2 {
			variadicArgs = args[2].([]*options.AggregateOptions)
		}
		arg2 = variadicArgs
		run(
			arg0,
			arg1,
			arg2...,
		)
	})
	return _c
}

func (_c *MockUserDailyActivityRepository_Aggregate_Call) Return(userDailyActivitys []*models.UserDailyActivity, err error) *MockUserDailyActivityRepository_Aggregate_Call {
	_c.Call.Return(userDailyActivitys, err)
	return _c
}

func (_c *MockUserDailyActivityRepository_Aggregate_Call) RunAndReturn(run func(ctx context.Context, pipeline mongo.Pipeline, opts ...*options.AggregateOptions) ([]*models.UserDailyActivity, error)) *MockUserDailyActivityRepository_Aggregate_Call {
	_c.Call.Return(run)
	return _c
}

// AggregateProjected provides a mock function for the type MockUserDailyActivityRepository
func (_mock *MockUserDailyActivityRepository) AggregateProjected(ctx context.Context, pipeline mongo.Pipeline, opts ...*options.AggregateOptions) (*mongo.Cursor, error) {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, pipeline, opts)
	} else {
		tmpRet = _mock.Called(ctx, pipeline)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for AggregateProjected")
	}

	var r0 *mongo.Cursor
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, mongo.Pipeline, ...*options.AggregateOptions) (*mongo.Cursor, error)); ok {
		return returnFunc(ctx, pipeline, opts...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, mongo.Pipeline, ...*options.AggregateOptions) *mongo.Cursor); ok {
		r0 = returnFunc(ctx, pipeline, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*mongo.Cursor)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, mongo.Pipeline, ...*options.AggregateOptions) error); ok {
		r1 = returnFunc(ctx, pipeline, opts...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserDailyActivityRepository_AggregateProjected_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AggregateProjected'
type MockUserDailyActivityRepository_AggregateProjected_Call struct {
	*mock.Call
}

// AggregateProjected is a helper method to define mock.On call
//   - ctx context.Context
//   - pipeline mongo.Pipeline
//   - opts ...*options.AggregateOptions
func (_e *MockUserDailyActivityRepository_Expecter) AggregateProjected(ctx interface{}, pipeline interface{}, opts ...interface{}) *MockUserDailyActivityRepository_AggregateProjected_Call {
	return &MockUserDailyActivityRepository_AggregateProjected_Call{Call: _e.mock.On("AggregateProjected",
		append([]interface{}{ctx, pipeline}, opts...)...)}
}

func (_c *MockUserDailyActivityRepository_AggregateProjected_Call) Run(run func(ctx context.Context, pipeline mongo.Pipeline, opts ...*options.AggregateOptions)) *MockUserDailyActivityRepository_AggregateProjected_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 mongo.Pipeline
		if args[1] != nil {
			arg1 = args[1].(mongo.Pipeline)
		}
		var arg2 []*options.AggregateOptions
		var variadicArgs []*options.AggregateOptions
		if len(args) > 2 {
			variadicArgs = args[2].([]*options.AggregateOptions)
		}
		arg2 = variadicArgs
		run(
			arg0,
			arg1,
			arg2...,
		)
	})
	return _c
}

func (_c *MockUserDailyActivityRepository_AggregateProjected_Call) Return(cursor *mongo.Cursor, err error) *MockUserDailyActivityRepository_AggregateProjected_Call {
	_c.Call.Return(cursor, err)
	return _c
}

func (_c *MockUserDailyActivityRepository_AggregateProjected_Call) RunAndReturn(run func(ctx context.Context, pipeline mongo.Pipeline, opts ...*options.AggregateOptions) (*mongo.Cursor, error)) *MockUserDailyActivityRepository_AggregateProjected_Call {
	_c.Call.Return(run)
	return _c
}

// FindByUserIDAndDate provides a mock function for the type MockUserDailyActivityRepository
func (_mock *MockUserDailyActivityRepository) FindByUserIDAndDate(ctx context.Context, userID primitive.ObjectID, date time.Time) (*models.UserDailyActivity, error) {
	ret := _mock.Called(ctx, userID, date)

	if len(ret) == 0 {
		panic("no return value specified for FindByUserIDAndDate")
	}

	var r0 *models.UserDailyActivity
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, time.Time) (*models.UserDailyActivity, error)); ok {
		return returnFunc(ctx, userID, date)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, time.Time) *models.UserDailyActivity); ok {
		r0 = returnFunc(ctx, userID, date)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserDailyActivity)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, time.Time) error); ok {
		r1 = returnFunc(ctx, userID, date)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserDailyActivityRepository_FindByUserIDAndDate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindByUserIDAndDate'
type MockUserDailyActivityRepository_FindByUserIDAndDate_Call struct {
	*mock.Call
}

// FindByUserIDAndDate is a helper method to define mock.On call
//   - ctx context.Context
//   - userID primitive.ObjectID
//   - date time.Time
func (_e *MockUserDailyActivityRepository_Expecter) FindByUserIDAndDate(ctx interface{}, userID interface{}, date interface{}) *MockUserDailyActivityRepository_FindByUserIDAndDate_Call {
	return &MockUserDailyActivityRepository_FindByUserIDAndDate_Call{Call: _e.mock.On("FindByUserIDAndDate", ctx, userID, date)}
}

func (_c *MockUserDailyActivityRepository_FindByUserIDAndDate_Call) Run(run func(ctx context.Context, userID primitive.ObjectID, date time.Time)) *MockUserDailyActivityRepository_FindByUserIDAndDate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 time.Time
		if args[2] != nil {
			arg2 = args[2].(time.Time)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockUserDailyActivityRepository_FindByUserIDAndDate_Call) Return(userDailyActivity *models.UserDailyActivity, err error) *MockUserDailyActivityRepository_FindByUserIDAndDate_Call {
	_c.Call.Return(userDailyActivity, err)
	return _c
}

func (_c *MockUserDailyActivityRepository_FindByUserIDAndDate_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID, date time.Time) (*models.UserDailyActivity, error)) *MockUserDailyActivityRepository_FindByUserIDAndDate_Call {
	_c.Call.Return(run)
	return _c
}

// GetTimeSpentByUser provides a mock function for the type MockUserDailyActivityRepository
func (_mock *MockUserDailyActivityRepository) GetTimeSpentByUser(ctx context.Context, userID primitive.ObjectID, dateString string) (*int64, error) {
	ret := _mock.Called(ctx, userID, dateString)

	if len(ret) == 0 {
		panic("no return value specified for GetTimeSpentByUser")
	}

	var r0 *int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, string) (*int64, error)); ok {
		return returnFunc(ctx, userID, dateString)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, string) *int64); ok {
		r0 = returnFunc(ctx, userID, dateString)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*int64)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, string) error); ok {
		r1 = returnFunc(ctx, userID, dateString)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockUserDailyActivityRepository_GetTimeSpentByUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTimeSpentByUser'
type MockUserDailyActivityRepository_GetTimeSpentByUser_Call struct {
	*mock.Call
}

// GetTimeSpentByUser is a helper method to define mock.On call
//   - ctx context.Context
//   - userID primitive.ObjectID
//   - dateString string
func (_e *MockUserDailyActivityRepository_Expecter) GetTimeSpentByUser(ctx interface{}, userID interface{}, dateString interface{}) *MockUserDailyActivityRepository_GetTimeSpentByUser_Call {
	return &MockUserDailyActivityRepository_GetTimeSpentByUser_Call{Call: _e.mock.On("GetTimeSpentByUser", ctx, userID, dateString)}
}

func (_c *MockUserDailyActivityRepository_GetTimeSpentByUser_Call) Run(run func(ctx context.Context, userID primitive.ObjectID, dateString string)) *MockUserDailyActivityRepository_GetTimeSpentByUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 string
		if args[2] != nil {
			arg2 = args[2].(string)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockUserDailyActivityRepository_GetTimeSpentByUser_Call) Return(n *int64, err error) *MockUserDailyActivityRepository_GetTimeSpentByUser_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockUserDailyActivityRepository_GetTimeSpentByUser_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID, dateString string) (*int64, error)) *MockUserDailyActivityRepository_GetTimeSpentByUser_Call {
	_c.Call.Return(run)
	return _c
}

// UpsertUserActivity provides a mock function for the type MockUserDailyActivityRepository
func (_mock *MockUserDailyActivityRepository) UpsertUserActivity(ctx context.Context, userID primitive.ObjectID, date time.Time, activityType string, duration int64, coins int) error {
	ret := _mock.Called(ctx, userID, date, activityType, duration, coins)

	if len(ret) == 0 {
		panic("no return value specified for UpsertUserActivity")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, time.Time, string, int64, int) error); ok {
		r0 = returnFunc(ctx, userID, date, activityType, duration, coins)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockUserDailyActivityRepository_UpsertUserActivity_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpsertUserActivity'
type MockUserDailyActivityRepository_UpsertUserActivity_Call struct {
	*mock.Call
}

// UpsertUserActivity is a helper method to define mock.On call
//   - ctx context.Context
//   - userID primitive.ObjectID
//   - date time.Time
//   - activityType string
//   - duration int64
//   - coins int
func (_e *MockUserDailyActivityRepository_Expecter) UpsertUserActivity(ctx interface{}, userID interface{}, date interface{}, activityType interface{}, duration interface{}, coins interface{}) *MockUserDailyActivityRepository_UpsertUserActivity_Call {
	return &MockUserDailyActivityRepository_UpsertUserActivity_Call{Call: _e.mock.On("UpsertUserActivity", ctx, userID, date, activityType, duration, coins)}
}

func (_c *MockUserDailyActivityRepository_UpsertUserActivity_Call) Run(run func(ctx context.Context, userID primitive.ObjectID, date time.Time, activityType string, duration int64, coins int)) *MockUserDailyActivityRepository_UpsertUserActivity_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 time.Time
		if args[2] != nil {
			arg2 = args[2].(time.Time)
		}
		var arg3 string
		if args[3] != nil {
			arg3 = args[3].(string)
		}
		var arg4 int64
		if args[4] != nil {
			arg4 = args[4].(int64)
		}
		var arg5 int
		if args[5] != nil {
			arg5 = args[5].(int)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
			arg4,
			arg5,
		)
	})
	return _c
}

func (_c *MockUserDailyActivityRepository_UpsertUserActivity_Call) Return(err error) *MockUserDailyActivityRepository_UpsertUserActivity_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockUserDailyActivityRepository_UpsertUserActivity_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID, date time.Time, activityType string, duration int64, coins int) error) *MockUserDailyActivityRepository_UpsertUserActivity_Call {
	_c.Call.Return(run)
	return _c
}
