// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockMessageGroupRepository creates a new instance of MockMessageGroupRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockMessageGroupRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockMessageGroupRepository {
	mock := &MockMessageGroupRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockMessageGroupRepository is an autogenerated mock type for the MessageGroupRepository type
type MockMessageGroupRepository struct {
	mock.Mock
}

type MockMessageGroupRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockMessageGroupRepository) EXPECT() *MockMessageGroupRepository_Expecter {
	return &MockMessageGroupRepository_Expecter{mock: &_m.Mock}
}

// AddParticipant provides a mock function for the type MockMessageGroupRepository
func (_mock *MockMessageGroupRepository) AddParticipant(ctx context.Context, groupID primitive.ObjectID, userID *primitive.ObjectID) error {
	ret := _mock.Called(ctx, groupID, userID)

	if len(ret) == 0 {
		panic("no return value specified for AddParticipant")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, groupID, userID)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockMessageGroupRepository_AddParticipant_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AddParticipant'
type MockMessageGroupRepository_AddParticipant_Call struct {
	*mock.Call
}

// AddParticipant is a helper method to define mock.On call
//   - ctx context.Context
//   - groupID primitive.ObjectID
//   - userID *primitive.ObjectID
func (_e *MockMessageGroupRepository_Expecter) AddParticipant(ctx interface{}, groupID interface{}, userID interface{}) *MockMessageGroupRepository_AddParticipant_Call {
	return &MockMessageGroupRepository_AddParticipant_Call{Call: _e.mock.On("AddParticipant", ctx, groupID, userID)}
}

func (_c *MockMessageGroupRepository_AddParticipant_Call) Run(run func(ctx context.Context, groupID primitive.ObjectID, userID *primitive.ObjectID)) *MockMessageGroupRepository_AddParticipant_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 *primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(*primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockMessageGroupRepository_AddParticipant_Call) Return(err error) *MockMessageGroupRepository_AddParticipant_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockMessageGroupRepository_AddParticipant_Call) RunAndReturn(run func(ctx context.Context, groupID primitive.ObjectID, userID *primitive.ObjectID) error) *MockMessageGroupRepository_AddParticipant_Call {
	_c.Call.Return(run)
	return _c
}

// Create provides a mock function for the type MockMessageGroupRepository
func (_mock *MockMessageGroupRepository) Create(ctx context.Context, messageGroup *models.MessageGroup) error {
	ret := _mock.Called(ctx, messageGroup)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.MessageGroup) error); ok {
		r0 = returnFunc(ctx, messageGroup)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockMessageGroupRepository_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type MockMessageGroupRepository_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx context.Context
//   - messageGroup *models.MessageGroup
func (_e *MockMessageGroupRepository_Expecter) Create(ctx interface{}, messageGroup interface{}) *MockMessageGroupRepository_Create_Call {
	return &MockMessageGroupRepository_Create_Call{Call: _e.mock.On("Create", ctx, messageGroup)}
}

func (_c *MockMessageGroupRepository_Create_Call) Run(run func(ctx context.Context, messageGroup *models.MessageGroup)) *MockMessageGroupRepository_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.MessageGroup
		if args[1] != nil {
			arg1 = args[1].(*models.MessageGroup)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockMessageGroupRepository_Create_Call) Return(err error) *MockMessageGroupRepository_Create_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockMessageGroupRepository_Create_Call) RunAndReturn(run func(ctx context.Context, messageGroup *models.MessageGroup) error) *MockMessageGroupRepository_Create_Call {
	_c.Call.Return(run)
	return _c
}

// GetByAlias provides a mock function for the type MockMessageGroupRepository
func (_mock *MockMessageGroupRepository) GetByAlias(ctx context.Context, alias string) (*models.MessageGroup, error) {
	ret := _mock.Called(ctx, alias)

	if len(ret) == 0 {
		panic("no return value specified for GetByAlias")
	}

	var r0 *models.MessageGroup
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (*models.MessageGroup, error)); ok {
		return returnFunc(ctx, alias)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) *models.MessageGroup); ok {
		r0 = returnFunc(ctx, alias)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.MessageGroup)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, alias)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMessageGroupRepository_GetByAlias_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetByAlias'
type MockMessageGroupRepository_GetByAlias_Call struct {
	*mock.Call
}

// GetByAlias is a helper method to define mock.On call
//   - ctx context.Context
//   - alias string
func (_e *MockMessageGroupRepository_Expecter) GetByAlias(ctx interface{}, alias interface{}) *MockMessageGroupRepository_GetByAlias_Call {
	return &MockMessageGroupRepository_GetByAlias_Call{Call: _e.mock.On("GetByAlias", ctx, alias)}
}

func (_c *MockMessageGroupRepository_GetByAlias_Call) Run(run func(ctx context.Context, alias string)) *MockMessageGroupRepository_GetByAlias_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockMessageGroupRepository_GetByAlias_Call) Return(messageGroup *models.MessageGroup, err error) *MockMessageGroupRepository_GetByAlias_Call {
	_c.Call.Return(messageGroup, err)
	return _c
}

func (_c *MockMessageGroupRepository_GetByAlias_Call) RunAndReturn(run func(ctx context.Context, alias string) (*models.MessageGroup, error)) *MockMessageGroupRepository_GetByAlias_Call {
	_c.Call.Return(run)
	return _c
}

// GetByID provides a mock function for the type MockMessageGroupRepository
func (_mock *MockMessageGroupRepository) GetByID(ctx context.Context, id primitive.ObjectID) (*models.MessageGroup, error) {
	ret := _mock.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetByID")
	}

	var r0 *models.MessageGroup
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.MessageGroup, error)); ok {
		return returnFunc(ctx, id)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.MessageGroup); ok {
		r0 = returnFunc(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.MessageGroup)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, id)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMessageGroupRepository_GetByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetByID'
type MockMessageGroupRepository_GetByID_Call struct {
	*mock.Call
}

// GetByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id primitive.ObjectID
func (_e *MockMessageGroupRepository_Expecter) GetByID(ctx interface{}, id interface{}) *MockMessageGroupRepository_GetByID_Call {
	return &MockMessageGroupRepository_GetByID_Call{Call: _e.mock.On("GetByID", ctx, id)}
}

func (_c *MockMessageGroupRepository_GetByID_Call) Run(run func(ctx context.Context, id primitive.ObjectID)) *MockMessageGroupRepository_GetByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockMessageGroupRepository_GetByID_Call) Return(messageGroup *models.MessageGroup, err error) *MockMessageGroupRepository_GetByID_Call {
	_c.Call.Return(messageGroup, err)
	return _c
}

func (_c *MockMessageGroupRepository_GetByID_Call) RunAndReturn(run func(ctx context.Context, id primitive.ObjectID) (*models.MessageGroup, error)) *MockMessageGroupRepository_GetByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetIndividualUsers provides a mock function for the type MockMessageGroupRepository
func (_mock *MockMessageGroupRepository) GetIndividualUsers(ctx context.Context, userID primitive.ObjectID, page int, pageSize int) ([]*models.MessageGroup, error) {
	ret := _mock.Called(ctx, userID, page, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetIndividualUsers")
	}

	var r0 []*models.MessageGroup
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int, int) ([]*models.MessageGroup, error)); ok {
		return returnFunc(ctx, userID, page, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, int, int) []*models.MessageGroup); ok {
		r0 = returnFunc(ctx, userID, page, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.MessageGroup)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, int, int) error); ok {
		r1 = returnFunc(ctx, userID, page, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockMessageGroupRepository_GetIndividualUsers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetIndividualUsers'
type MockMessageGroupRepository_GetIndividualUsers_Call struct {
	*mock.Call
}

// GetIndividualUsers is a helper method to define mock.On call
//   - ctx context.Context
//   - userID primitive.ObjectID
//   - page int
//   - pageSize int
func (_e *MockMessageGroupRepository_Expecter) GetIndividualUsers(ctx interface{}, userID interface{}, page interface{}, pageSize interface{}) *MockMessageGroupRepository_GetIndividualUsers_Call {
	return &MockMessageGroupRepository_GetIndividualUsers_Call{Call: _e.mock.On("GetIndividualUsers", ctx, userID, page, pageSize)}
}

func (_c *MockMessageGroupRepository_GetIndividualUsers_Call) Run(run func(ctx context.Context, userID primitive.ObjectID, page int, pageSize int)) *MockMessageGroupRepository_GetIndividualUsers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 int
		if args[2] != nil {
			arg2 = args[2].(int)
		}
		var arg3 int
		if args[3] != nil {
			arg3 = args[3].(int)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockMessageGroupRepository_GetIndividualUsers_Call) Return(messageGroups []*models.MessageGroup, err error) *MockMessageGroupRepository_GetIndividualUsers_Call {
	_c.Call.Return(messageGroups, err)
	return _c
}

func (_c *MockMessageGroupRepository_GetIndividualUsers_Call) RunAndReturn(run func(ctx context.Context, userID primitive.ObjectID, page int, pageSize int) ([]*models.MessageGroup, error)) *MockMessageGroupRepository_GetIndividualUsers_Call {
	_c.Call.Return(run)
	return _c
}

// RemoveParticipant provides a mock function for the type MockMessageGroupRepository
func (_mock *MockMessageGroupRepository) RemoveParticipant(ctx context.Context, groupID primitive.ObjectID, userID primitive.ObjectID) error {
	ret := _mock.Called(ctx, groupID, userID)

	if len(ret) == 0 {
		panic("no return value specified for RemoveParticipant")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, primitive.ObjectID) error); ok {
		r0 = returnFunc(ctx, groupID, userID)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockMessageGroupRepository_RemoveParticipant_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RemoveParticipant'
type MockMessageGroupRepository_RemoveParticipant_Call struct {
	*mock.Call
}

// RemoveParticipant is a helper method to define mock.On call
//   - ctx context.Context
//   - groupID primitive.ObjectID
//   - userID primitive.ObjectID
func (_e *MockMessageGroupRepository_Expecter) RemoveParticipant(ctx interface{}, groupID interface{}, userID interface{}) *MockMessageGroupRepository_RemoveParticipant_Call {
	return &MockMessageGroupRepository_RemoveParticipant_Call{Call: _e.mock.On("RemoveParticipant", ctx, groupID, userID)}
}

func (_c *MockMessageGroupRepository_RemoveParticipant_Call) Run(run func(ctx context.Context, groupID primitive.ObjectID, userID primitive.ObjectID)) *MockMessageGroupRepository_RemoveParticipant_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockMessageGroupRepository_RemoveParticipant_Call) Return(err error) *MockMessageGroupRepository_RemoveParticipant_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockMessageGroupRepository_RemoveParticipant_Call) RunAndReturn(run func(ctx context.Context, groupID primitive.ObjectID, userID primitive.ObjectID) error) *MockMessageGroupRepository_RemoveParticipant_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateLastMessage provides a mock function for the type MockMessageGroupRepository
func (_mock *MockMessageGroupRepository) UpdateLastMessage(ctx context.Context, groupID primitive.ObjectID, message *models.Message) error {
	ret := _mock.Called(ctx, groupID, message)

	if len(ret) == 0 {
		panic("no return value specified for UpdateLastMessage")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *models.Message) error); ok {
		r0 = returnFunc(ctx, groupID, message)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockMessageGroupRepository_UpdateLastMessage_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateLastMessage'
type MockMessageGroupRepository_UpdateLastMessage_Call struct {
	*mock.Call
}

// UpdateLastMessage is a helper method to define mock.On call
//   - ctx context.Context
//   - groupID primitive.ObjectID
//   - message *models.Message
func (_e *MockMessageGroupRepository_Expecter) UpdateLastMessage(ctx interface{}, groupID interface{}, message interface{}) *MockMessageGroupRepository_UpdateLastMessage_Call {
	return &MockMessageGroupRepository_UpdateLastMessage_Call{Call: _e.mock.On("UpdateLastMessage", ctx, groupID, message)}
}

func (_c *MockMessageGroupRepository_UpdateLastMessage_Call) Run(run func(ctx context.Context, groupID primitive.ObjectID, message *models.Message)) *MockMessageGroupRepository_UpdateLastMessage_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 *models.Message
		if args[2] != nil {
			arg2 = args[2].(*models.Message)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockMessageGroupRepository_UpdateLastMessage_Call) Return(err error) *MockMessageGroupRepository_UpdateLastMessage_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockMessageGroupRepository_UpdateLastMessage_Call) RunAndReturn(run func(ctx context.Context, groupID primitive.ObjectID, message *models.Message) error) *MockMessageGroupRepository_UpdateLastMessage_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateLastMessageRead provides a mock function for the type MockMessageGroupRepository
func (_mock *MockMessageGroupRepository) UpdateLastMessageRead(ctx context.Context, group *models.MessageGroup) error {
	ret := _mock.Called(ctx, group)

	if len(ret) == 0 {
		panic("no return value specified for UpdateLastMessageRead")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.MessageGroup) error); ok {
		r0 = returnFunc(ctx, group)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockMessageGroupRepository_UpdateLastMessageRead_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateLastMessageRead'
type MockMessageGroupRepository_UpdateLastMessageRead_Call struct {
	*mock.Call
}

// UpdateLastMessageRead is a helper method to define mock.On call
//   - ctx context.Context
//   - group *models.MessageGroup
func (_e *MockMessageGroupRepository_Expecter) UpdateLastMessageRead(ctx interface{}, group interface{}) *MockMessageGroupRepository_UpdateLastMessageRead_Call {
	return &MockMessageGroupRepository_UpdateLastMessageRead_Call{Call: _e.mock.On("UpdateLastMessageRead", ctx, group)}
}

func (_c *MockMessageGroupRepository_UpdateLastMessageRead_Call) Run(run func(ctx context.Context, group *models.MessageGroup)) *MockMessageGroupRepository_UpdateLastMessageRead_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.MessageGroup
		if args[1] != nil {
			arg1 = args[1].(*models.MessageGroup)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockMessageGroupRepository_UpdateLastMessageRead_Call) Return(err error) *MockMessageGroupRepository_UpdateLastMessageRead_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockMessageGroupRepository_UpdateLastMessageRead_Call) RunAndReturn(run func(ctx context.Context, group *models.MessageGroup) error) *MockMessageGroupRepository_UpdateLastMessageRead_Call {
	_c.Call.Return(run)
	return _c
}
