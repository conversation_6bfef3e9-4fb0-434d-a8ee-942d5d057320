// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package repository

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// NewMockFeedRepository creates a new instance of MockFeedRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockFeedRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockFeedRepository {
	mock := &MockFeedRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockFeedRepository is an autogenerated mock type for the FeedRepository type
type MockFeedRepository struct {
	mock.Mock
}

type MockFeedRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockFeedRepository) EXPECT() *MockFeedRepository_Expecter {
	return &MockFeedRepository_Expecter{mock: &_m.Mock}
}

// BatchInsertUserFeed provides a mock function for the type MockFeedRepository
func (_mock *MockFeedRepository) BatchInsertUserFeed(ctx context.Context, userFeeds []*models.UserFeed) error {
	ret := _mock.Called(ctx, userFeeds)

	if len(ret) == 0 {
		panic("no return value specified for BatchInsertUserFeed")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, []*models.UserFeed) error); ok {
		r0 = returnFunc(ctx, userFeeds)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockFeedRepository_BatchInsertUserFeed_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BatchInsertUserFeed'
type MockFeedRepository_BatchInsertUserFeed_Call struct {
	*mock.Call
}

// BatchInsertUserFeed is a helper method to define mock.On call
//   - ctx context.Context
//   - userFeeds []*models.UserFeed
func (_e *MockFeedRepository_Expecter) BatchInsertUserFeed(ctx interface{}, userFeeds interface{}) *MockFeedRepository_BatchInsertUserFeed_Call {
	return &MockFeedRepository_BatchInsertUserFeed_Call{Call: _e.mock.On("BatchInsertUserFeed", ctx, userFeeds)}
}

func (_c *MockFeedRepository_BatchInsertUserFeed_Call) Run(run func(ctx context.Context, userFeeds []*models.UserFeed)) *MockFeedRepository_BatchInsertUserFeed_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 []*models.UserFeed
		if args[1] != nil {
			arg1 = args[1].([]*models.UserFeed)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockFeedRepository_BatchInsertUserFeed_Call) Return(err error) *MockFeedRepository_BatchInsertUserFeed_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockFeedRepository_BatchInsertUserFeed_Call) RunAndReturn(run func(ctx context.Context, userFeeds []*models.UserFeed) error) *MockFeedRepository_BatchInsertUserFeed_Call {
	_c.Call.Return(run)
	return _c
}

// CreateUserFeed provides a mock function for the type MockFeedRepository
func (_mock *MockFeedRepository) CreateUserFeed(ctx context.Context, userFeed *models.UserFeed) error {
	ret := _mock.Called(ctx, userFeed)

	if len(ret) == 0 {
		panic("no return value specified for CreateUserFeed")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UserFeed) error); ok {
		r0 = returnFunc(ctx, userFeed)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockFeedRepository_CreateUserFeed_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateUserFeed'
type MockFeedRepository_CreateUserFeed_Call struct {
	*mock.Call
}

// CreateUserFeed is a helper method to define mock.On call
//   - ctx context.Context
//   - userFeed *models.UserFeed
func (_e *MockFeedRepository_Expecter) CreateUserFeed(ctx interface{}, userFeed interface{}) *MockFeedRepository_CreateUserFeed_Call {
	return &MockFeedRepository_CreateUserFeed_Call{Call: _e.mock.On("CreateUserFeed", ctx, userFeed)}
}

func (_c *MockFeedRepository_CreateUserFeed_Call) Run(run func(ctx context.Context, userFeed *models.UserFeed)) *MockFeedRepository_CreateUserFeed_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.UserFeed
		if args[1] != nil {
			arg1 = args[1].(*models.UserFeed)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockFeedRepository_CreateUserFeed_Call) Return(err error) *MockFeedRepository_CreateUserFeed_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockFeedRepository_CreateUserFeed_Call) RunAndReturn(run func(ctx context.Context, userFeed *models.UserFeed) error) *MockFeedRepository_CreateUserFeed_Call {
	_c.Call.Return(run)
	return _c
}

// GetFeedById provides a mock function for the type MockFeedRepository
func (_mock *MockFeedRepository) GetFeedById(ctx context.Context, feedId primitive.ObjectID) (*models.UserFeed, error) {
	ret := _mock.Called(ctx, feedId)

	if len(ret) == 0 {
		panic("no return value specified for GetFeedById")
	}

	var r0 *models.UserFeed
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) (*models.UserFeed, error)); ok {
		return returnFunc(ctx, feedId)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID) *models.UserFeed); ok {
		r0 = returnFunc(ctx, feedId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.UserFeed)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID) error); ok {
		r1 = returnFunc(ctx, feedId)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockFeedRepository_GetFeedById_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFeedById'
type MockFeedRepository_GetFeedById_Call struct {
	*mock.Call
}

// GetFeedById is a helper method to define mock.On call
//   - ctx context.Context
//   - feedId primitive.ObjectID
func (_e *MockFeedRepository_Expecter) GetFeedById(ctx interface{}, feedId interface{}) *MockFeedRepository_GetFeedById_Call {
	return &MockFeedRepository_GetFeedById_Call{Call: _e.mock.On("GetFeedById", ctx, feedId)}
}

func (_c *MockFeedRepository_GetFeedById_Call) Run(run func(ctx context.Context, feedId primitive.ObjectID)) *MockFeedRepository_GetFeedById_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockFeedRepository_GetFeedById_Call) Return(userFeed *models.UserFeed, err error) *MockFeedRepository_GetFeedById_Call {
	_c.Call.Return(userFeed, err)
	return _c
}

func (_c *MockFeedRepository_GetFeedById_Call) RunAndReturn(run func(ctx context.Context, feedId primitive.ObjectID) (*models.UserFeed, error)) *MockFeedRepository_GetFeedById_Call {
	_c.Call.Return(run)
	return _c
}

// GetUserFeed provides a mock function for the type MockFeedRepository
func (_mock *MockFeedRepository) GetUserFeed(ctx context.Context, userId primitive.ObjectID, lastId *primitive.ObjectID, pageSize int) ([]*models.UserFeed, error) {
	ret := _mock.Called(ctx, userId, lastId, pageSize)

	if len(ret) == 0 {
		panic("no return value specified for GetUserFeed")
	}

	var r0 []*models.UserFeed
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *primitive.ObjectID, int) ([]*models.UserFeed, error)); ok {
		return returnFunc(ctx, userId, lastId, pageSize)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, primitive.ObjectID, *primitive.ObjectID, int) []*models.UserFeed); ok {
		r0 = returnFunc(ctx, userId, lastId, pageSize)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.UserFeed)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, primitive.ObjectID, *primitive.ObjectID, int) error); ok {
		r1 = returnFunc(ctx, userId, lastId, pageSize)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockFeedRepository_GetUserFeed_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUserFeed'
type MockFeedRepository_GetUserFeed_Call struct {
	*mock.Call
}

// GetUserFeed is a helper method to define mock.On call
//   - ctx context.Context
//   - userId primitive.ObjectID
//   - lastId *primitive.ObjectID
//   - pageSize int
func (_e *MockFeedRepository_Expecter) GetUserFeed(ctx interface{}, userId interface{}, lastId interface{}, pageSize interface{}) *MockFeedRepository_GetUserFeed_Call {
	return &MockFeedRepository_GetUserFeed_Call{Call: _e.mock.On("GetUserFeed", ctx, userId, lastId, pageSize)}
}

func (_c *MockFeedRepository_GetUserFeed_Call) Run(run func(ctx context.Context, userId primitive.ObjectID, lastId *primitive.ObjectID, pageSize int)) *MockFeedRepository_GetUserFeed_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 primitive.ObjectID
		if args[1] != nil {
			arg1 = args[1].(primitive.ObjectID)
		}
		var arg2 *primitive.ObjectID
		if args[2] != nil {
			arg2 = args[2].(*primitive.ObjectID)
		}
		var arg3 int
		if args[3] != nil {
			arg3 = args[3].(int)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockFeedRepository_GetUserFeed_Call) Return(userFeeds []*models.UserFeed, err error) *MockFeedRepository_GetUserFeed_Call {
	_c.Call.Return(userFeeds, err)
	return _c
}

func (_c *MockFeedRepository_GetUserFeed_Call) RunAndReturn(run func(ctx context.Context, userId primitive.ObjectID, lastId *primitive.ObjectID, pageSize int) ([]*models.UserFeed, error)) *MockFeedRepository_GetUserFeed_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateFeed provides a mock function for the type MockFeedRepository
func (_mock *MockFeedRepository) UpdateFeed(ctx context.Context, userFeed *models.UserFeed) error {
	ret := _mock.Called(ctx, userFeed)

	if len(ret) == 0 {
		panic("no return value specified for UpdateFeed")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.UserFeed) error); ok {
		r0 = returnFunc(ctx, userFeed)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockFeedRepository_UpdateFeed_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateFeed'
type MockFeedRepository_UpdateFeed_Call struct {
	*mock.Call
}

// UpdateFeed is a helper method to define mock.On call
//   - ctx context.Context
//   - userFeed *models.UserFeed
func (_e *MockFeedRepository_Expecter) UpdateFeed(ctx interface{}, userFeed interface{}) *MockFeedRepository_UpdateFeed_Call {
	return &MockFeedRepository_UpdateFeed_Call{Call: _e.mock.On("UpdateFeed", ctx, userFeed)}
}

func (_c *MockFeedRepository_UpdateFeed_Call) Run(run func(ctx context.Context, userFeed *models.UserFeed)) *MockFeedRepository_UpdateFeed_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.UserFeed
		if args[1] != nil {
			arg1 = args[1].(*models.UserFeed)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockFeedRepository_UpdateFeed_Call) Return(err error) *MockFeedRepository_UpdateFeed_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockFeedRepository_UpdateFeed_Call) RunAndReturn(run func(ctx context.Context, userFeed *models.UserFeed) error) *MockFeedRepository_UpdateFeed_Call {
	_c.Call.Return(run)
	return _c
}
