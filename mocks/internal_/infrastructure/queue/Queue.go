// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package queue

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/models"

	mock "github.com/stretchr/testify/mock"
)

// NewMockQueue creates a new instance of MockQueue. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockQueue(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockQueue {
	mock := &MockQueue{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockQueue is an autogenerated mock type for the Queue type
type MockQueue struct {
	mock.Mock
}

type MockQueue_Expecter struct {
	mock *mock.Mock
}

func (_m *MockQueue) EXPECT() *MockQueue_Expecter {
	return &MockQueue_Expecter{mock: &_m.Mock}
}

// AddUser provides a mock function for the type MockQueue
func (_mock *MockQueue) AddUser(ctx context.Context, user *models.User, gameConfig models.GameConfigInterface) ([]*models.User, error) {
	ret := _mock.Called(ctx, user, gameConfig)

	if len(ret) == 0 {
		panic("no return value specified for AddUser")
	}

	var r0 []*models.User
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.User, models.GameConfigInterface) ([]*models.User, error)); ok {
		return returnFunc(ctx, user, gameConfig)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.User, models.GameConfigInterface) []*models.User); ok {
		r0 = returnFunc(ctx, user, gameConfig)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.User)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.User, models.GameConfigInterface) error); ok {
		r1 = returnFunc(ctx, user, gameConfig)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueue_AddUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AddUser'
type MockQueue_AddUser_Call struct {
	*mock.Call
}

// AddUser is a helper method to define mock.On call
//   - ctx context.Context
//   - user *models.User
//   - gameConfig models.GameConfigInterface
func (_e *MockQueue_Expecter) AddUser(ctx interface{}, user interface{}, gameConfig interface{}) *MockQueue_AddUser_Call {
	return &MockQueue_AddUser_Call{Call: _e.mock.On("AddUser", ctx, user, gameConfig)}
}

func (_c *MockQueue_AddUser_Call) Run(run func(ctx context.Context, user *models.User, gameConfig models.GameConfigInterface)) *MockQueue_AddUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.User
		if args[1] != nil {
			arg1 = args[1].(*models.User)
		}
		var arg2 models.GameConfigInterface
		if args[2] != nil {
			arg2 = args[2].(models.GameConfigInterface)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockQueue_AddUser_Call) Return(users []*models.User, err error) *MockQueue_AddUser_Call {
	_c.Call.Return(users, err)
	return _c
}

func (_c *MockQueue_AddUser_Call) RunAndReturn(run func(ctx context.Context, user *models.User, gameConfig models.GameConfigInterface) ([]*models.User, error)) *MockQueue_AddUser_Call {
	_c.Call.Return(run)
	return _c
}

// IsUserPresentInWaitingList provides a mock function for the type MockQueue
func (_mock *MockQueue) IsUserPresentInWaitingList(ctx context.Context, user *models.User) (bool, error) {
	ret := _mock.Called(ctx, user)

	if len(ret) == 0 {
		panic("no return value specified for IsUserPresentInWaitingList")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.User) (bool, error)); ok {
		return returnFunc(ctx, user)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *models.User) bool); ok {
		r0 = returnFunc(ctx, user)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *models.User) error); ok {
		r1 = returnFunc(ctx, user)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueue_IsUserPresentInWaitingList_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsUserPresentInWaitingList'
type MockQueue_IsUserPresentInWaitingList_Call struct {
	*mock.Call
}

// IsUserPresentInWaitingList is a helper method to define mock.On call
//   - ctx context.Context
//   - user *models.User
func (_e *MockQueue_Expecter) IsUserPresentInWaitingList(ctx interface{}, user interface{}) *MockQueue_IsUserPresentInWaitingList_Call {
	return &MockQueue_IsUserPresentInWaitingList_Call{Call: _e.mock.On("IsUserPresentInWaitingList", ctx, user)}
}

func (_c *MockQueue_IsUserPresentInWaitingList_Call) Run(run func(ctx context.Context, user *models.User)) *MockQueue_IsUserPresentInWaitingList_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *models.User
		if args[1] != nil {
			arg1 = args[1].(*models.User)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockQueue_IsUserPresentInWaitingList_Call) Return(b bool, err error) *MockQueue_IsUserPresentInWaitingList_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockQueue_IsUserPresentInWaitingList_Call) RunAndReturn(run func(ctx context.Context, user *models.User) (bool, error)) *MockQueue_IsUserPresentInWaitingList_Call {
	_c.Call.Return(run)
	return _c
}

// RemoveUser provides a mock function for the type MockQueue
func (_mock *MockQueue) RemoveUser(ctx context.Context, userID string) (bool, error) {
	ret := _mock.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for RemoveUser")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (bool, error)); ok {
		return returnFunc(ctx, userID)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) bool); ok {
		r0 = returnFunc(ctx, userID)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockQueue_RemoveUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RemoveUser'
type MockQueue_RemoveUser_Call struct {
	*mock.Call
}

// RemoveUser is a helper method to define mock.On call
//   - ctx context.Context
//   - userID string
func (_e *MockQueue_Expecter) RemoveUser(ctx interface{}, userID interface{}) *MockQueue_RemoveUser_Call {
	return &MockQueue_RemoveUser_Call{Call: _e.mock.On("RemoveUser", ctx, userID)}
}

func (_c *MockQueue_RemoveUser_Call) Run(run func(ctx context.Context, userID string)) *MockQueue_RemoveUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockQueue_RemoveUser_Call) Return(b bool, err error) *MockQueue_RemoveUser_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *MockQueue_RemoveUser_Call) RunAndReturn(run func(ctx context.Context, userID string) (bool, error)) *MockQueue_RemoveUser_Call {
	_c.Call.Return(run)
	return _c
}
