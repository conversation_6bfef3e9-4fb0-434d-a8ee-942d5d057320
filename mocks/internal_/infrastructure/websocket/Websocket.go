// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package websocket

import (
	"context"
	"matiksOfficial/matiks-server-go/internal/infrastructure/websocket"
	"net/http"

	mock "github.com/stretchr/testify/mock"
)

// NewMockWebsocket creates a new instance of MockWebsocket. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockWebsocket(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockWebsocket {
	mock := &MockWebsocket{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockWebsocket is an autogenerated mock type for the Websocket type
type MockWebsocket struct {
	mock.Mock
}

type MockWebsocket_Expecter struct {
	mock *mock.Mock
}

func (_m *MockWebsocket) EXPECT() *MockWebsocket_Expecter {
	return &MockWebsocket_Expecter{mock: &_m.Mock}
}

// Publish provides a mock function for the type MockWebsocket
func (_mock *MockWebsocket) Publish(ctx context.Context, channel string, message interface{}) error {
	ret := _mock.Called(ctx, channel, message)

	if len(ret) == 0 {
		panic("no return value specified for Publish")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, interface{}) error); ok {
		r0 = returnFunc(ctx, channel, message)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockWebsocket_Publish_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Publish'
type MockWebsocket_Publish_Call struct {
	*mock.Call
}

// Publish is a helper method to define mock.On call
//   - ctx context.Context
//   - channel string
//   - message interface{}
func (_e *MockWebsocket_Expecter) Publish(ctx interface{}, channel interface{}, message interface{}) *MockWebsocket_Publish_Call {
	return &MockWebsocket_Publish_Call{Call: _e.mock.On("Publish", ctx, channel, message)}
}

func (_c *MockWebsocket_Publish_Call) Run(run func(ctx context.Context, channel string, message interface{})) *MockWebsocket_Publish_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		var arg2 interface{}
		if args[2] != nil {
			arg2 = args[2].(interface{})
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockWebsocket_Publish_Call) Return(err error) *MockWebsocket_Publish_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockWebsocket_Publish_Call) RunAndReturn(run func(ctx context.Context, channel string, message interface{}) error) *MockWebsocket_Publish_Call {
	_c.Call.Return(run)
	return _c
}

// RegisterHandler provides a mock function for the type MockWebsocket
func (_mock *MockWebsocket) RegisterHandler(msgType websocket.MessageType, handler websocket.Handler) {
	_mock.Called(msgType, handler)
	return
}

// MockWebsocket_RegisterHandler_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RegisterHandler'
type MockWebsocket_RegisterHandler_Call struct {
	*mock.Call
}

// RegisterHandler is a helper method to define mock.On call
//   - msgType websocket.MessageType
//   - handler websocket.Handler
func (_e *MockWebsocket_Expecter) RegisterHandler(msgType interface{}, handler interface{}) *MockWebsocket_RegisterHandler_Call {
	return &MockWebsocket_RegisterHandler_Call{Call: _e.mock.On("RegisterHandler", msgType, handler)}
}

func (_c *MockWebsocket_RegisterHandler_Call) Run(run func(msgType websocket.MessageType, handler websocket.Handler)) *MockWebsocket_RegisterHandler_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 websocket.MessageType
		if args[0] != nil {
			arg0 = args[0].(websocket.MessageType)
		}
		var arg1 websocket.Handler
		if args[1] != nil {
			arg1 = args[1].(websocket.Handler)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockWebsocket_RegisterHandler_Call) Return() *MockWebsocket_RegisterHandler_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockWebsocket_RegisterHandler_Call) RunAndReturn(run func(msgType websocket.MessageType, handler websocket.Handler)) *MockWebsocket_RegisterHandler_Call {
	_c.Run(run)
	return _c
}

// ServeHTTP provides a mock function for the type MockWebsocket
func (_mock *MockWebsocket) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	_mock.Called(w, r)
	return
}

// MockWebsocket_ServeHTTP_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ServeHTTP'
type MockWebsocket_ServeHTTP_Call struct {
	*mock.Call
}

// ServeHTTP is a helper method to define mock.On call
//   - w http.ResponseWriter
//   - r *http.Request
func (_e *MockWebsocket_Expecter) ServeHTTP(w interface{}, r interface{}) *MockWebsocket_ServeHTTP_Call {
	return &MockWebsocket_ServeHTTP_Call{Call: _e.mock.On("ServeHTTP", w, r)}
}

func (_c *MockWebsocket_ServeHTTP_Call) Run(run func(w http.ResponseWriter, r *http.Request)) *MockWebsocket_ServeHTTP_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 http.ResponseWriter
		if args[0] != nil {
			arg0 = args[0].(http.ResponseWriter)
		}
		var arg1 *http.Request
		if args[1] != nil {
			arg1 = args[1].(*http.Request)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockWebsocket_ServeHTTP_Call) Return() *MockWebsocket_ServeHTTP_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockWebsocket_ServeHTTP_Call) RunAndReturn(run func(w http.ResponseWriter, r *http.Request)) *MockWebsocket_ServeHTTP_Call {
	_c.Run(run)
	return _c
}

// Shutdown provides a mock function for the type MockWebsocket
func (_mock *MockWebsocket) Shutdown(ctx context.Context) error {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for Shutdown")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = returnFunc(ctx)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockWebsocket_Shutdown_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Shutdown'
type MockWebsocket_Shutdown_Call struct {
	*mock.Call
}

// Shutdown is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockWebsocket_Expecter) Shutdown(ctx interface{}) *MockWebsocket_Shutdown_Call {
	return &MockWebsocket_Shutdown_Call{Call: _e.mock.On("Shutdown", ctx)}
}

func (_c *MockWebsocket_Shutdown_Call) Run(run func(ctx context.Context)) *MockWebsocket_Shutdown_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockWebsocket_Shutdown_Call) Return(err error) *MockWebsocket_Shutdown_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockWebsocket_Shutdown_Call) RunAndReturn(run func(ctx context.Context) error) *MockWebsocket_Shutdown_Call {
	_c.Call.Return(run)
	return _c
}

// Subscribe provides a mock function for the type MockWebsocket
func (_mock *MockWebsocket) Subscribe(ctx context.Context, channel string) (<-chan interface{}, error) {
	ret := _mock.Called(ctx, channel)

	if len(ret) == 0 {
		panic("no return value specified for Subscribe")
	}

	var r0 <-chan interface{}
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (<-chan interface{}, error)); ok {
		return returnFunc(ctx, channel)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) <-chan interface{}); ok {
		r0 = returnFunc(ctx, channel)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(<-chan interface{})
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, channel)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockWebsocket_Subscribe_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Subscribe'
type MockWebsocket_Subscribe_Call struct {
	*mock.Call
}

// Subscribe is a helper method to define mock.On call
//   - ctx context.Context
//   - channel string
func (_e *MockWebsocket_Expecter) Subscribe(ctx interface{}, channel interface{}) *MockWebsocket_Subscribe_Call {
	return &MockWebsocket_Subscribe_Call{Call: _e.mock.On("Subscribe", ctx, channel)}
}

func (_c *MockWebsocket_Subscribe_Call) Run(run func(ctx context.Context, channel string)) *MockWebsocket_Subscribe_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockWebsocket_Subscribe_Call) Return(ifaceValCh <-chan interface{}, err error) *MockWebsocket_Subscribe_Call {
	_c.Call.Return(ifaceValCh, err)
	return _c
}

func (_c *MockWebsocket_Subscribe_Call) RunAndReturn(run func(ctx context.Context, channel string) (<-chan interface{}, error)) *MockWebsocket_Subscribe_Call {
	_c.Call.Return(run)
	return _c
}
