// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package cdn

import (
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewMockCDNClient creates a new instance of MockCD<PERSON>lient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockCDNClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockCDNClient {
	mock := &MockCDNClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockCDNClient is an autogenerated mock type for the CDNClient type
type MockCDNClient struct {
	mock.Mock
}

type MockCDNClient_Expecter struct {
	mock *mock.Mock
}

func (_m *MockCDNClient) EXPECT() *MockCDNClient_Expecter {
	return &MockCDNClient_Expecter{mock: &_m.Mock}
}

// ExtractPathFromURL provides a mock function for the type MockCDNClient
func (_mock *MockCDNClient) ExtractPathFromURL(url string) string {
	ret := _mock.Called(url)

	if len(ret) == 0 {
		panic("no return value specified for ExtractPathFromURL")
	}

	var r0 string
	if returnFunc, ok := ret.Get(0).(func(string) string); ok {
		r0 = returnFunc(url)
	} else {
		r0 = ret.Get(0).(string)
	}
	return r0
}

// MockCDNClient_ExtractPathFromURL_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ExtractPathFromURL'
type MockCDNClient_ExtractPathFromURL_Call struct {
	*mock.Call
}

// ExtractPathFromURL is a helper method to define mock.On call
//   - url string
func (_e *MockCDNClient_Expecter) ExtractPathFromURL(url interface{}) *MockCDNClient_ExtractPathFromURL_Call {
	return &MockCDNClient_ExtractPathFromURL_Call{Call: _e.mock.On("ExtractPathFromURL", url)}
}

func (_c *MockCDNClient_ExtractPathFromURL_Call) Run(run func(url string)) *MockCDNClient_ExtractPathFromURL_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockCDNClient_ExtractPathFromURL_Call) Return(s string) *MockCDNClient_ExtractPathFromURL_Call {
	_c.Call.Return(s)
	return _c
}

func (_c *MockCDNClient_ExtractPathFromURL_Call) RunAndReturn(run func(url string) string) *MockCDNClient_ExtractPathFromURL_Call {
	_c.Call.Return(run)
	return _c
}

// InvalidateCache provides a mock function for the type MockCDNClient
func (_mock *MockCDNClient) InvalidateCache(ctx context.Context, path string) error {
	ret := _mock.Called(ctx, path)

	if len(ret) == 0 {
		panic("no return value specified for InvalidateCache")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = returnFunc(ctx, path)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockCDNClient_InvalidateCache_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'InvalidateCache'
type MockCDNClient_InvalidateCache_Call struct {
	*mock.Call
}

// InvalidateCache is a helper method to define mock.On call
//   - ctx context.Context
//   - path string
func (_e *MockCDNClient_Expecter) InvalidateCache(ctx interface{}, path interface{}) *MockCDNClient_InvalidateCache_Call {
	return &MockCDNClient_InvalidateCache_Call{Call: _e.mock.On("InvalidateCache", ctx, path)}
}

func (_c *MockCDNClient_InvalidateCache_Call) Run(run func(ctx context.Context, path string)) *MockCDNClient_InvalidateCache_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockCDNClient_InvalidateCache_Call) Return(err error) *MockCDNClient_InvalidateCache_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockCDNClient_InvalidateCache_Call) RunAndReturn(run func(ctx context.Context, path string) error) *MockCDNClient_InvalidateCache_Call {
	_c.Call.Return(run)
	return _c
}

// InvalidateCacheWithHost provides a mock function for the type MockCDNClient
func (_mock *MockCDNClient) InvalidateCacheWithHost(ctx context.Context, host string, path string) error {
	ret := _mock.Called(ctx, host, path)

	if len(ret) == 0 {
		panic("no return value specified for InvalidateCacheWithHost")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string) error); ok {
		r0 = returnFunc(ctx, host, path)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockCDNClient_InvalidateCacheWithHost_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'InvalidateCacheWithHost'
type MockCDNClient_InvalidateCacheWithHost_Call struct {
	*mock.Call
}

// InvalidateCacheWithHost is a helper method to define mock.On call
//   - ctx context.Context
//   - host string
//   - path string
func (_e *MockCDNClient_Expecter) InvalidateCacheWithHost(ctx interface{}, host interface{}, path interface{}) *MockCDNClient_InvalidateCacheWithHost_Call {
	return &MockCDNClient_InvalidateCacheWithHost_Call{Call: _e.mock.On("InvalidateCacheWithHost", ctx, host, path)}
}

func (_c *MockCDNClient_InvalidateCacheWithHost_Call) Run(run func(ctx context.Context, host string, path string)) *MockCDNClient_InvalidateCacheWithHost_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		var arg2 string
		if args[2] != nil {
			arg2 = args[2].(string)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockCDNClient_InvalidateCacheWithHost_Call) Return(err error) *MockCDNClient_InvalidateCacheWithHost_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockCDNClient_InvalidateCacheWithHost_Call) RunAndReturn(run func(ctx context.Context, host string, path string) error) *MockCDNClient_InvalidateCacheWithHost_Call {
	_c.Call.Return(run)
	return _c
}
