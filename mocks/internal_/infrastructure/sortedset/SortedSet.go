// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package sortedset

import (
	"context"

	"github.com/redis/go-redis/v9"
	mock "github.com/stretchr/testify/mock"
)

// NewMockSortedSet creates a new instance of MockSortedSet. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockSortedSet(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockSortedSet {
	mock := &MockSortedSet{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockSortedSet is an autogenerated mock type for the SortedSet type
type MockSortedSet struct {
	mock.Mock
}

type MockSortedSet_Expecter struct {
	mock *mock.Mock
}

func (_m *MockSortedSet) EXPECT() *MockSortedSet_Expecter {
	return &MockSortedSet_Expecter{mock: &_m.<PERSON>}
}

// ZAdd provides a mock function for the type MockSortedSet
func (_mock *MockSortedSet) ZAdd(ctx context.Context, key string, members ...redis.Z) error {
	var tmpRet mock.Arguments
	if len(members) > 0 {
		tmpRet = _mock.Called(ctx, key, members)
	} else {
		tmpRet = _mock.Called(ctx, key)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for ZAdd")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, ...redis.Z) error); ok {
		r0 = returnFunc(ctx, key, members...)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockSortedSet_ZAdd_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ZAdd'
type MockSortedSet_ZAdd_Call struct {
	*mock.Call
}

// ZAdd is a helper method to define mock.On call
//   - ctx context.Context
//   - key string
//   - members ...redis.Z
func (_e *MockSortedSet_Expecter) ZAdd(ctx interface{}, key interface{}, members ...interface{}) *MockSortedSet_ZAdd_Call {
	return &MockSortedSet_ZAdd_Call{Call: _e.mock.On("ZAdd",
		append([]interface{}{ctx, key}, members...)...)}
}

func (_c *MockSortedSet_ZAdd_Call) Run(run func(ctx context.Context, key string, members ...redis.Z)) *MockSortedSet_ZAdd_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		var arg2 []redis.Z
		var variadicArgs []redis.Z
		if len(args) > 2 {
			variadicArgs = args[2].([]redis.Z)
		}
		arg2 = variadicArgs
		run(
			arg0,
			arg1,
			arg2...,
		)
	})
	return _c
}

func (_c *MockSortedSet_ZAdd_Call) Return(err error) *MockSortedSet_ZAdd_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockSortedSet_ZAdd_Call) RunAndReturn(run func(ctx context.Context, key string, members ...redis.Z) error) *MockSortedSet_ZAdd_Call {
	_c.Call.Return(run)
	return _c
}

// ZCard provides a mock function for the type MockSortedSet
func (_mock *MockSortedSet) ZCard(ctx context.Context, key string) (int64, error) {
	ret := _mock.Called(ctx, key)

	if len(ret) == 0 {
		panic("no return value specified for ZCard")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (int64, error)); ok {
		return returnFunc(ctx, key)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) int64); ok {
		r0 = returnFunc(ctx, key)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, key)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockSortedSet_ZCard_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ZCard'
type MockSortedSet_ZCard_Call struct {
	*mock.Call
}

// ZCard is a helper method to define mock.On call
//   - ctx context.Context
//   - key string
func (_e *MockSortedSet_Expecter) ZCard(ctx interface{}, key interface{}) *MockSortedSet_ZCard_Call {
	return &MockSortedSet_ZCard_Call{Call: _e.mock.On("ZCard", ctx, key)}
}

func (_c *MockSortedSet_ZCard_Call) Run(run func(ctx context.Context, key string)) *MockSortedSet_ZCard_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockSortedSet_ZCard_Call) Return(n int64, err error) *MockSortedSet_ZCard_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockSortedSet_ZCard_Call) RunAndReturn(run func(ctx context.Context, key string) (int64, error)) *MockSortedSet_ZCard_Call {
	_c.Call.Return(run)
	return _c
}

// ZCount provides a mock function for the type MockSortedSet
func (_mock *MockSortedSet) ZCount(ctx context.Context, key string, min string, max string) (int64, error) {
	ret := _mock.Called(ctx, key, min, max)

	if len(ret) == 0 {
		panic("no return value specified for ZCount")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string, string) (int64, error)); ok {
		return returnFunc(ctx, key, min, max)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string, string) int64); ok {
		r0 = returnFunc(ctx, key, min, max)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, string, string) error); ok {
		r1 = returnFunc(ctx, key, min, max)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockSortedSet_ZCount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ZCount'
type MockSortedSet_ZCount_Call struct {
	*mock.Call
}

// ZCount is a helper method to define mock.On call
//   - ctx context.Context
//   - key string
//   - min string
//   - max string
func (_e *MockSortedSet_Expecter) ZCount(ctx interface{}, key interface{}, min interface{}, max interface{}) *MockSortedSet_ZCount_Call {
	return &MockSortedSet_ZCount_Call{Call: _e.mock.On("ZCount", ctx, key, min, max)}
}

func (_c *MockSortedSet_ZCount_Call) Run(run func(ctx context.Context, key string, min string, max string)) *MockSortedSet_ZCount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		var arg2 string
		if args[2] != nil {
			arg2 = args[2].(string)
		}
		var arg3 string
		if args[3] != nil {
			arg3 = args[3].(string)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockSortedSet_ZCount_Call) Return(n int64, err error) *MockSortedSet_ZCount_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockSortedSet_ZCount_Call) RunAndReturn(run func(ctx context.Context, key string, min string, max string) (int64, error)) *MockSortedSet_ZCount_Call {
	_c.Call.Return(run)
	return _c
}

// ZRangeByScore provides a mock function for the type MockSortedSet
func (_mock *MockSortedSet) ZRangeByScore(ctx context.Context, key string, opt *redis.ZRangeBy) ([]string, error) {
	ret := _mock.Called(ctx, key, opt)

	if len(ret) == 0 {
		panic("no return value specified for ZRangeByScore")
	}

	var r0 []string
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, *redis.ZRangeBy) ([]string, error)); ok {
		return returnFunc(ctx, key, opt)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, *redis.ZRangeBy) []string); ok {
		r0 = returnFunc(ctx, key, opt)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, *redis.ZRangeBy) error); ok {
		r1 = returnFunc(ctx, key, opt)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockSortedSet_ZRangeByScore_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ZRangeByScore'
type MockSortedSet_ZRangeByScore_Call struct {
	*mock.Call
}

// ZRangeByScore is a helper method to define mock.On call
//   - ctx context.Context
//   - key string
//   - opt *redis.ZRangeBy
func (_e *MockSortedSet_Expecter) ZRangeByScore(ctx interface{}, key interface{}, opt interface{}) *MockSortedSet_ZRangeByScore_Call {
	return &MockSortedSet_ZRangeByScore_Call{Call: _e.mock.On("ZRangeByScore", ctx, key, opt)}
}

func (_c *MockSortedSet_ZRangeByScore_Call) Run(run func(ctx context.Context, key string, opt *redis.ZRangeBy)) *MockSortedSet_ZRangeByScore_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		var arg2 *redis.ZRangeBy
		if args[2] != nil {
			arg2 = args[2].(*redis.ZRangeBy)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockSortedSet_ZRangeByScore_Call) Return(strings []string, err error) *MockSortedSet_ZRangeByScore_Call {
	_c.Call.Return(strings, err)
	return _c
}

func (_c *MockSortedSet_ZRangeByScore_Call) RunAndReturn(run func(ctx context.Context, key string, opt *redis.ZRangeBy) ([]string, error)) *MockSortedSet_ZRangeByScore_Call {
	_c.Call.Return(run)
	return _c
}

// ZRangeWithScores provides a mock function for the type MockSortedSet
func (_mock *MockSortedSet) ZRangeWithScores(ctx context.Context, key string, start int64, stop int64) ([]redis.Z, error) {
	ret := _mock.Called(ctx, key, start, stop)

	if len(ret) == 0 {
		panic("no return value specified for ZRangeWithScores")
	}

	var r0 []redis.Z
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, int64, int64) ([]redis.Z, error)); ok {
		return returnFunc(ctx, key, start, stop)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, int64, int64) []redis.Z); ok {
		r0 = returnFunc(ctx, key, start, stop)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]redis.Z)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, int64, int64) error); ok {
		r1 = returnFunc(ctx, key, start, stop)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockSortedSet_ZRangeWithScores_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ZRangeWithScores'
type MockSortedSet_ZRangeWithScores_Call struct {
	*mock.Call
}

// ZRangeWithScores is a helper method to define mock.On call
//   - ctx context.Context
//   - key string
//   - start int64
//   - stop int64
func (_e *MockSortedSet_Expecter) ZRangeWithScores(ctx interface{}, key interface{}, start interface{}, stop interface{}) *MockSortedSet_ZRangeWithScores_Call {
	return &MockSortedSet_ZRangeWithScores_Call{Call: _e.mock.On("ZRangeWithScores", ctx, key, start, stop)}
}

func (_c *MockSortedSet_ZRangeWithScores_Call) Run(run func(ctx context.Context, key string, start int64, stop int64)) *MockSortedSet_ZRangeWithScores_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		var arg2 int64
		if args[2] != nil {
			arg2 = args[2].(int64)
		}
		var arg3 int64
		if args[3] != nil {
			arg3 = args[3].(int64)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockSortedSet_ZRangeWithScores_Call) Return(zs []redis.Z, err error) *MockSortedSet_ZRangeWithScores_Call {
	_c.Call.Return(zs, err)
	return _c
}

func (_c *MockSortedSet_ZRangeWithScores_Call) RunAndReturn(run func(ctx context.Context, key string, start int64, stop int64) ([]redis.Z, error)) *MockSortedSet_ZRangeWithScores_Call {
	_c.Call.Return(run)
	return _c
}

// ZRem provides a mock function for the type MockSortedSet
func (_mock *MockSortedSet) ZRem(ctx context.Context, key string, entry string) error {
	ret := _mock.Called(ctx, key, entry)

	if len(ret) == 0 {
		panic("no return value specified for ZRem")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string) error); ok {
		r0 = returnFunc(ctx, key, entry)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockSortedSet_ZRem_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ZRem'
type MockSortedSet_ZRem_Call struct {
	*mock.Call
}

// ZRem is a helper method to define mock.On call
//   - ctx context.Context
//   - key string
//   - entry string
func (_e *MockSortedSet_Expecter) ZRem(ctx interface{}, key interface{}, entry interface{}) *MockSortedSet_ZRem_Call {
	return &MockSortedSet_ZRem_Call{Call: _e.mock.On("ZRem", ctx, key, entry)}
}

func (_c *MockSortedSet_ZRem_Call) Run(run func(ctx context.Context, key string, entry string)) *MockSortedSet_ZRem_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		var arg2 string
		if args[2] != nil {
			arg2 = args[2].(string)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockSortedSet_ZRem_Call) Return(err error) *MockSortedSet_ZRem_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockSortedSet_ZRem_Call) RunAndReturn(run func(ctx context.Context, key string, entry string) error) *MockSortedSet_ZRem_Call {
	_c.Call.Return(run)
	return _c
}

// ZRemRangeByScore provides a mock function for the type MockSortedSet
func (_mock *MockSortedSet) ZRemRangeByScore(ctx context.Context, key string, min string, max string) error {
	ret := _mock.Called(ctx, key, min, max)

	if len(ret) == 0 {
		panic("no return value specified for ZRemRangeByScore")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string, string) error); ok {
		r0 = returnFunc(ctx, key, min, max)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockSortedSet_ZRemRangeByScore_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ZRemRangeByScore'
type MockSortedSet_ZRemRangeByScore_Call struct {
	*mock.Call
}

// ZRemRangeByScore is a helper method to define mock.On call
//   - ctx context.Context
//   - key string
//   - min string
//   - max string
func (_e *MockSortedSet_Expecter) ZRemRangeByScore(ctx interface{}, key interface{}, min interface{}, max interface{}) *MockSortedSet_ZRemRangeByScore_Call {
	return &MockSortedSet_ZRemRangeByScore_Call{Call: _e.mock.On("ZRemRangeByScore", ctx, key, min, max)}
}

func (_c *MockSortedSet_ZRemRangeByScore_Call) Run(run func(ctx context.Context, key string, min string, max string)) *MockSortedSet_ZRemRangeByScore_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		var arg2 string
		if args[2] != nil {
			arg2 = args[2].(string)
		}
		var arg3 string
		if args[3] != nil {
			arg3 = args[3].(string)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockSortedSet_ZRemRangeByScore_Call) Return(err error) *MockSortedSet_ZRemRangeByScore_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockSortedSet_ZRemRangeByScore_Call) RunAndReturn(run func(ctx context.Context, key string, min string, max string) error) *MockSortedSet_ZRemRangeByScore_Call {
	_c.Call.Return(run)
	return _c
}

// ZRevRangeByScoreWithScores provides a mock function for the type MockSortedSet
func (_mock *MockSortedSet) ZRevRangeByScoreWithScores(ctx context.Context, key string, opt *redis.ZRangeBy) ([]redis.Z, error) {
	ret := _mock.Called(ctx, key, opt)

	if len(ret) == 0 {
		panic("no return value specified for ZRevRangeByScoreWithScores")
	}

	var r0 []redis.Z
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, *redis.ZRangeBy) ([]redis.Z, error)); ok {
		return returnFunc(ctx, key, opt)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, *redis.ZRangeBy) []redis.Z); ok {
		r0 = returnFunc(ctx, key, opt)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]redis.Z)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, *redis.ZRangeBy) error); ok {
		r1 = returnFunc(ctx, key, opt)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockSortedSet_ZRevRangeByScoreWithScores_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ZRevRangeByScoreWithScores'
type MockSortedSet_ZRevRangeByScoreWithScores_Call struct {
	*mock.Call
}

// ZRevRangeByScoreWithScores is a helper method to define mock.On call
//   - ctx context.Context
//   - key string
//   - opt *redis.ZRangeBy
func (_e *MockSortedSet_Expecter) ZRevRangeByScoreWithScores(ctx interface{}, key interface{}, opt interface{}) *MockSortedSet_ZRevRangeByScoreWithScores_Call {
	return &MockSortedSet_ZRevRangeByScoreWithScores_Call{Call: _e.mock.On("ZRevRangeByScoreWithScores", ctx, key, opt)}
}

func (_c *MockSortedSet_ZRevRangeByScoreWithScores_Call) Run(run func(ctx context.Context, key string, opt *redis.ZRangeBy)) *MockSortedSet_ZRevRangeByScoreWithScores_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		var arg2 *redis.ZRangeBy
		if args[2] != nil {
			arg2 = args[2].(*redis.ZRangeBy)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockSortedSet_ZRevRangeByScoreWithScores_Call) Return(zs []redis.Z, err error) *MockSortedSet_ZRevRangeByScoreWithScores_Call {
	_c.Call.Return(zs, err)
	return _c
}

func (_c *MockSortedSet_ZRevRangeByScoreWithScores_Call) RunAndReturn(run func(ctx context.Context, key string, opt *redis.ZRangeBy) ([]redis.Z, error)) *MockSortedSet_ZRevRangeByScoreWithScores_Call {
	_c.Call.Return(run)
	return _c
}

// ZRevRangeWithScores provides a mock function for the type MockSortedSet
func (_mock *MockSortedSet) ZRevRangeWithScores(ctx context.Context, key string, start int64, stop int64) ([]redis.Z, error) {
	ret := _mock.Called(ctx, key, start, stop)

	if len(ret) == 0 {
		panic("no return value specified for ZRevRangeWithScores")
	}

	var r0 []redis.Z
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, int64, int64) ([]redis.Z, error)); ok {
		return returnFunc(ctx, key, start, stop)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, int64, int64) []redis.Z); ok {
		r0 = returnFunc(ctx, key, start, stop)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]redis.Z)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, int64, int64) error); ok {
		r1 = returnFunc(ctx, key, start, stop)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockSortedSet_ZRevRangeWithScores_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ZRevRangeWithScores'
type MockSortedSet_ZRevRangeWithScores_Call struct {
	*mock.Call
}

// ZRevRangeWithScores is a helper method to define mock.On call
//   - ctx context.Context
//   - key string
//   - start int64
//   - stop int64
func (_e *MockSortedSet_Expecter) ZRevRangeWithScores(ctx interface{}, key interface{}, start interface{}, stop interface{}) *MockSortedSet_ZRevRangeWithScores_Call {
	return &MockSortedSet_ZRevRangeWithScores_Call{Call: _e.mock.On("ZRevRangeWithScores", ctx, key, start, stop)}
}

func (_c *MockSortedSet_ZRevRangeWithScores_Call) Run(run func(ctx context.Context, key string, start int64, stop int64)) *MockSortedSet_ZRevRangeWithScores_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		var arg2 int64
		if args[2] != nil {
			arg2 = args[2].(int64)
		}
		var arg3 int64
		if args[3] != nil {
			arg3 = args[3].(int64)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
		)
	})
	return _c
}

func (_c *MockSortedSet_ZRevRangeWithScores_Call) Return(zs []redis.Z, err error) *MockSortedSet_ZRevRangeWithScores_Call {
	_c.Call.Return(zs, err)
	return _c
}

func (_c *MockSortedSet_ZRevRangeWithScores_Call) RunAndReturn(run func(ctx context.Context, key string, start int64, stop int64) ([]redis.Z, error)) *MockSortedSet_ZRevRangeWithScores_Call {
	_c.Call.Return(run)
	return _c
}

// ZRevRank provides a mock function for the type MockSortedSet
func (_mock *MockSortedSet) ZRevRank(ctx context.Context, key string, entry string) (int64, error) {
	ret := _mock.Called(ctx, key, entry)

	if len(ret) == 0 {
		panic("no return value specified for ZRevRank")
	}

	var r0 int64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string) (int64, error)); ok {
		return returnFunc(ctx, key, entry)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string) int64); ok {
		r0 = returnFunc(ctx, key, entry)
	} else {
		r0 = ret.Get(0).(int64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = returnFunc(ctx, key, entry)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockSortedSet_ZRevRank_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ZRevRank'
type MockSortedSet_ZRevRank_Call struct {
	*mock.Call
}

// ZRevRank is a helper method to define mock.On call
//   - ctx context.Context
//   - key string
//   - entry string
func (_e *MockSortedSet_Expecter) ZRevRank(ctx interface{}, key interface{}, entry interface{}) *MockSortedSet_ZRevRank_Call {
	return &MockSortedSet_ZRevRank_Call{Call: _e.mock.On("ZRevRank", ctx, key, entry)}
}

func (_c *MockSortedSet_ZRevRank_Call) Run(run func(ctx context.Context, key string, entry string)) *MockSortedSet_ZRevRank_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		var arg2 string
		if args[2] != nil {
			arg2 = args[2].(string)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockSortedSet_ZRevRank_Call) Return(n int64, err error) *MockSortedSet_ZRevRank_Call {
	_c.Call.Return(n, err)
	return _c
}

func (_c *MockSortedSet_ZRevRank_Call) RunAndReturn(run func(ctx context.Context, key string, entry string) (int64, error)) *MockSortedSet_ZRevRank_Call {
	_c.Call.Return(run)
	return _c
}

// ZScore provides a mock function for the type MockSortedSet
func (_mock *MockSortedSet) ZScore(ctx context.Context, key string, member string) (float64, error) {
	ret := _mock.Called(ctx, key, member)

	if len(ret) == 0 {
		panic("no return value specified for ZScore")
	}

	var r0 float64
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string) (float64, error)); ok {
		return returnFunc(ctx, key, member)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, string) float64); ok {
		r0 = returnFunc(ctx, key, member)
	} else {
		r0 = ret.Get(0).(float64)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = returnFunc(ctx, key, member)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockSortedSet_ZScore_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ZScore'
type MockSortedSet_ZScore_Call struct {
	*mock.Call
}

// ZScore is a helper method to define mock.On call
//   - ctx context.Context
//   - key string
//   - member string
func (_e *MockSortedSet_Expecter) ZScore(ctx interface{}, key interface{}, member interface{}) *MockSortedSet_ZScore_Call {
	return &MockSortedSet_ZScore_Call{Call: _e.mock.On("ZScore", ctx, key, member)}
}

func (_c *MockSortedSet_ZScore_Call) Run(run func(ctx context.Context, key string, member string)) *MockSortedSet_ZScore_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		var arg2 string
		if args[2] != nil {
			arg2 = args[2].(string)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockSortedSet_ZScore_Call) Return(f float64, err error) *MockSortedSet_ZScore_Call {
	_c.Call.Return(f, err)
	return _c
}

func (_c *MockSortedSet_ZScore_Call) RunAndReturn(run func(ctx context.Context, key string, member string) (float64, error)) *MockSortedSet_ZScore_Call {
	_c.Call.Return(run)
	return _c
}
