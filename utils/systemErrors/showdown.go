package systemErrors

import "errors"

var (
	ErrShowdownCannotBeNil              = errors.New("showdown cannot be nil")
	ErrShowdownNotFound                 = errors.New("showdown not found")
	ErrParticipantNotFound              = errors.New("participant not found")
	ErrInvalidRoundInputInFixture       = errors.New("round input in fixture is invalid")
	ErrMatchMakingResponseNotFound      = errors.New("match making response not found")
	ErrParticipantsPayloadMapNotFound   = errors.New("participants payload map not found")
	ErrMatchMakingResponsePairsNotFound = errors.New("match making response pairs not found")
	ErrMatchMakingPairNotFound          = errors.New("match making pair not found")
	ErrMatchMakingPairEntityNotFound    = errors.New("match making pair entity not found")
	ErrShowdownParticipantNotFound      = errors.New("showdown participant not found")
	ErrInitialGameIDNotFound            = errors.New("initial game id not found")
	ErrFixturesNotFound                 = errors.New("fixtures not found")
)
