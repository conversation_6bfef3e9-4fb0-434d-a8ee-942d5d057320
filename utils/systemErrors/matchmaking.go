package systemErrors

import "errors"

var (
	ErrInvalidMatchMakingType                    = errors.New("invalid match making type")
	ErrNoEntities                                = errors.New("no entities")
	ErrPayloadIsNil                              = errors.New("payload is nil")
	ErrMatchMakingScoreIsNil                     = errors.New("match making score is nil")
	ErrOddNumberOfEntitiesAfterRemovingByeEntity = errors.New("found odd number of entities after removing bye entity")
	ErrByeEntityNotFound                         = errors.New("bye entity not found")
	ErrOddNumberOfEntities                       = errors.New("found odd number of entities")
)
