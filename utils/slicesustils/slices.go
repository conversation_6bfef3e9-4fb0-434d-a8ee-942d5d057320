package slicesustils

import (
	"cmp"
	"fmt"
	"math/rand/v2"
	"sort"
)

// Contains checks if a slice contains a specific element
func Contains[T comparable](slice []T, element T) bool {
	for _, v := range slice {
		if v == element {
			return true
		}
	}
	return false
}

func RemoveMultiple[T any](slice []T, indices []int) []T {
	newSlice := make([]T, 0, len(slice)-len(indices))
	j := 0
	for i := range len(slice) {
		if !Contains(indices, i) {
			newSlice = append(newSlice, slice[j])
			j++
		}
	}
	return newSlice
}

func RemoveAt[T any](slice []T, index int) []T {
	return append(slice[:index], slice[index+1:]...)
}

// ContainsBy checks if a slice contains an element that satisfies the predicate
func ContainsBy[T any](slice []T, predicate func(T) bool) bool {
	for _, v := range slice {
		if predicate(v) {
			return true
		}
	}
	return false
}

func SplitSlice[T any](slice []T, chunkSize int) [][]T {
	if chunkSize <= 0 {
		chunkSize = 1
	}

	sliceLength := len(slice)
	numOfChunks := sliceLength / chunkSize
	if sliceLength%chunkSize != 0 {
		numOfChunks++
	}

	var chunks [][]T = make([][]T, numOfChunks)
	for i := 0; i < sliceLength; i += chunkSize {
		end := min(i+chunkSize, sliceLength)
		chunks[i/chunkSize] = slice[i:end]
	}
	return chunks
}

// Filter returns a new slice with elements that satisfy the predicate
func Filter[T any](slice []T, predicate func(T) bool) []T {
	var result []T
	for _, v := range slice {
		if predicate(v) {
			result = append(result, v)
		}
	}
	return result
}

// Map transforms a slice by applying a mapping function to each element
func Map[T, R any](slice []T, mapper func(T) R) []R {
	result := make([]R, len(slice))
	for i, v := range slice {
		result[i] = mapper(v)
	}
	return result
}

// Reduce reduces a slice to a single value by applying a reduction function
func Reduce[T, R any](slice []T, initialValue R, reducer func(R, T) R) R {
	accumulator := initialValue
	for _, v := range slice {
		accumulator = reducer(accumulator, v)
	}
	return accumulator
}

// Find returns the first element that satisfies the predicate and a boolean indicating if found
func Find[T any](slice []T, predicate func(T) bool) (T, bool) {
	var zero T
	for _, v := range slice {
		if predicate(v) {
			return v, true
		}
	}
	return zero, false
}

// FindIndex returns the index of the first element that satisfies the predicate
func FindIndex[T any](slice []T, predicate func(T) bool) int {
	for i, v := range slice {
		if predicate(v) {
			return i
		}
	}
	return -1
}

// Uniq returns a new slice with duplicate elements removed
func Uniq[T comparable](slice []T) []T {
	seen := make(map[T]bool)
	var result []T
	for _, v := range slice {
		if !seen[v] {
			seen[v] = true
			result = append(result, v)
		}
	}
	return result
}

// UniqBy returns a new slice with duplicate elements removed based on a key function
func UniqBy[T any, K comparable](slice []T, key func(T) K) []T {
	seen := make(map[K]bool)
	var result []T
	for _, v := range slice {
		k := key(v)
		if !seen[k] {
			seen[k] = true
			result = append(result, v)
		}
	}
	return result
}

// GroupBy groups slice elements by a key function
func GroupBy[T any, K comparable](slice []T, key func(T) K) map[K][]T {
	groups := make(map[K][]T)
	for _, v := range slice {
		k := key(v)
		groups[k] = append(groups[k], v)
	}
	return groups
}

// Chunk splits a slice into chunks of a specified size
func Chunk[T any](slice []T, size int) ([][]T, error) {
	if size <= 0 {
		return nil, fmt.Errorf("chunk size must be positive")
	}

	var chunks [][]T
	for i := 0; i < len(slice); i += size {
		end := i + size
		if end > len(slice) {
			end = len(slice)
		}
		chunks = append(chunks, slice[i:end])
	}
	return chunks, nil
}

// Flatten flattens a slice of slices into a single slice
func Flatten[T any](slice [][]T) []T {
	var result []T
	for _, subSlice := range slice {
		result = append(result, subSlice...)
	}
	return result
}

// Intersection returns a slice of unique elements common to all input slices
func Intersection[T comparable](slices ...[]T) []T {
	if len(slices) == 0 {
		return nil
	}

	// Count occurrences of elements in all slices
	counts := make(map[T]int)
	for _, slice := range slices {
		seen := make(map[T]bool)
		for _, v := range slice {
			if !seen[v] {
				counts[v]++
				seen[v] = true
			}
		}
	}

	// Filter elements that appear in all slices
	var result []T
	for v, count := range counts {
		if count == len(slices) {
			result = append(result, v)
		}
	}
	return result
}

// Difference returns elements in the first slice that are not in subsequent slices
func Difference[T comparable](slice []T, excludeSlices ...[]T) []T {
	excluded := make(map[T]bool)
	for _, excludeSlice := range excludeSlices {
		for _, v := range excludeSlice {
			excluded[v] = true
		}
	}

	var result []T
	for _, v := range slice {
		if !excluded[v] {
			result = append(result, v)
		}
	}
	return result
}

// Shuffle randomly reorders the elements of a slice
func Shuffle[T any](slice []T) []T {
	result := make([]T, len(slice))
	copy(result, slice)

	for i := len(result) - 1; i > 0; i-- {
		j := RandInt(0, i+1)
		result[i], result[j] = result[j], result[i]
	}
	return result
}

// Sort sorts a slice in ascending order
func Sort[T cmp.Ordered](slice []T) []T {
	result := make([]T, len(slice))
	copy(result, slice)
	sort.Slice(result, func(i, j int) bool {
		return result[i] < result[j]
	})
	return result
}

// SortBy sorts a slice using a custom comparison function
func SortBy[T any](slice []T, less func(T, T) bool) []T {
	result := make([]T, len(slice))
	copy(result, slice)
	sort.Slice(result, func(i, j int) bool {
		return less(result[i], result[j])
	})
	return result
}

// Partition splits a slice into two slices based on a predicate
func Partition[T any](slice []T, predicate func(T) bool) ([]T, []T) {
	var pass, fail []T
	for _, v := range slice {
		if predicate(v) {
			pass = append(pass, v)
		} else {
			fail = append(fail, v)
		}
	}
	return pass, fail
}

// Without returns a slice excluding specified elements
func Without[T comparable](slice []T, exclude ...T) []T {
	excludeMap := make(map[T]bool)
	for _, v := range exclude {
		excludeMap[v] = true
	}

	var result []T
	for _, v := range slice {
		if !excludeMap[v] {
			result = append(result, v)
		}
	}
	return result
}

// Zip combines multiple slices element-wise
func Zip[T any](slices ...[]T) [][]T {
	if len(slices) == 0 {
		return nil
	}

	// Find the length of the shortest slice
	minLen := len(slices[0])
	for _, slice := range slices[1:] {
		if len(slice) < minLen {
			minLen = len(slice)
		}
	}

	// Zip slices
	result := make([][]T, minLen)
	for i := 0; i < minLen; i++ {
		row := make([]T, len(slices))
		for j, slice := range slices {
			row[j] = slice[i]
		}
		result[i] = row
	}
	return result
}

// Some checks if at least one element satisfies the predicate
func Some[T any](slice []T, predicate func(T) bool) bool {
	for _, v := range slice {
		if predicate(v) {
			return true
		}
	}
	return false
}

// Every checks if all elements satisfy the predicate
func Every[T any](slice []T, predicate func(T) bool) bool {
	for _, v := range slice {
		if !predicate(v) {
			return false
		}
	}
	return true
}

// Concat combines multiple slices into a single slice
func Concat[T any](slices ...[]T) []T {
	var totalLen int
	for _, slice := range slices {
		totalLen += len(slice)
	}

	result := make([]T, 0, totalLen)
	for _, slice := range slices {
		result = append(result, slice...)
	}
	return result
}

// Sample returns a random element from the slice
func Sample[T any](slice []T) (T, bool) {
	var zero T
	if len(slice) == 0 {
		return zero, false
	}
	return slice[RandInt(0, len(slice))], true
}

// RandInt returns a random integer in the range [min, max)
func RandInt(min, max int) int {
	return min + rand.IntN(max-min)
}

// ToMap converts a slice to a map using a key function
func ToMap[T any, K comparable](slice []T, key func(T) K) map[K]T {
	result := make(map[K]T)
	for _, v := range slice {
		result[key(v)] = v
	}
	return result
}

// Compact removes zero values from a slice
func Compact[T comparable](slice []T) []T {
	var zero T
	return Filter(slice, func(v T) bool {
		return v != zero
	})
}

// Range generates a slice of integers
func Range(start, end, step int) []int {
	if step == 0 {
		panic("step cannot be zero")
	}

	var result []int
	if step > 0 {
		for i := start; i < end; i += step {
			result = append(result, i)
		}
	} else {
		for i := start; i > end; i += step {
			result = append(result, i)
		}
	}
	return result
}
